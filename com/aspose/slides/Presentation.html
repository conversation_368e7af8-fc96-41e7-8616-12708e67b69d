<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_421) on Fri Jul 11 05:32:43 CDT 2025 -->
<title>Presentation (Aspose.Slides for Java (25.7) API Documentation)</title>
<meta name="date" content="2025-07-11">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Presentation (Aspose.Slides for Java (25.7) API Documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10,"i26":10,"i27":10,"i28":10,"i29":10,"i30":10,"i31":10,"i32":10,"i33":10,"i34":10,"i35":42,"i36":42,"i37":42,"i38":42,"i39":42,"i40":42,"i41":10,"i42":10,"i43":10,"i44":10,"i45":10,"i46":10,"i47":10,"i48":10,"i49":10,"i50":10,"i51":10,"i52":10,"i53":10,"i54":10,"i55":10,"i56":10,"i57":10,"i58":10,"i59":10,"i60":10,"i61":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"],32:["t6","Deprecated Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/PptxUnsupportedFormatException.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/PresentationAnimationsGenerator.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/Presentation.html" target="_top">Frames</a></li>
<li><a href="Presentation.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.aspose.slides</div>
<h2 title="Class Presentation" class="title">Class Presentation</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.aspose.slides.Presentation</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd>com.aspose.ms.System.IDisposable, <a href="../../../com/aspose/slides/IPresentation.html" title="interface in com.aspose.slides">IPresentation</a>, <a href="../../../com/aspose/slides/IPresentationComponent.html" title="interface in com.aspose.slides">IPresentationComponent</a></dd>
</dl>
<hr>
<br>
<pre>public final class <span class="typeNameLabel">Presentation</span>
extends java.lang.Object
implements <a href="../../../com/aspose/slides/IPresentation.html" title="interface in com.aspose.slides">IPresentation</a></pre>
<div class="block"><p>
  Represents a Microsoft PowerPoint presentation.
 </p><p><hr><blockquote><pre>
  The following example shows how to create PowerPoint Presentation.
  <pre>
 // Instantiate a Presentation object that represents a presentation file
 Presentation pres = new Presentation();
 try {
     // Get the first slide
     ISlide slide = pres.getSlides().get_Item(0);
     // Add an autoshape of type line
     slide.getShapes().addAutoShape(ShapeType.Line, 50, 150, 300, 0);
     // Save the presentation file.
     pres.save("NewPresentation_out.pptx", SaveFormat.Pptx);
 } finally {
     if (pres != null) pres.dispose();
 }
  </pre>
  The following example shows how to open and save Presentation.
  <pre>
 // Load any supported file in Presentation e.g. ppt, pptx, odp etc.
 Presentation pres = new Presentation("Sample.odp");
 try {
     // Save the presentation file.
     pres.save("OutputPresenation.pptx", SaveFormat.Pptx);
 } finally {
     if (pres != null) pres.dispose();
 }
  </pre>
  </pre></blockquote></hr></p></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Presentation.html#Presentation--">Presentation</a></span>()</code>
<div class="block">
 This constructor creates new presentation from scratch.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Presentation.html#Presentation-java.io.InputStream-">Presentation</a></span>(java.io.InputStream&nbsp;stream)</code>
<div class="block">
 This constructor is the primary mechanism for reading an existing Presentation.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Presentation.html#Presentation-java.io.InputStream-com.aspose.slides.LoadOptions-">Presentation</a></span>(java.io.InputStream&nbsp;stream,
            <a href="../../../com/aspose/slides/LoadOptions.html" title="class in com.aspose.slides">LoadOptions</a>&nbsp;loadOptions)</code>
<div class="block">
 This constructor is the primary mechanism for reading an existing Presentation.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Presentation.html#Presentation-com.aspose.slides.LoadOptions-">Presentation</a></span>(<a href="../../../com/aspose/slides/LoadOptions.html" title="class in com.aspose.slides">LoadOptions</a>&nbsp;loadOptions)</code>
<div class="block">
 This constructor creates new presentation from scratch.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Presentation.html#Presentation-java.lang.String-">Presentation</a></span>(java.lang.String&nbsp;file)</code>
<div class="block">
 This constructor gets a source file path from which
 the contents of the Presentation are read.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Presentation.html#Presentation-java.lang.String-com.aspose.slides.LoadOptions-">Presentation</a></span>(java.lang.String&nbsp;file,
            <a href="../../../com/aspose/slides/LoadOptions.html" title="class in com.aspose.slides">LoadOptions</a>&nbsp;loadOptions)</code>
<div class="block">
 This constructor gets a source file path from which
 the contents of the Presentation are read.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t6" class="tableTab"><span><a href="javascript:show(32);">Deprecated Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Presentation.html#dispose--">dispose</a></span>()</code>
<div class="block">
 Releases all resources used by this Presentation object.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/ICustomXmlPart.html" title="interface in com.aspose.slides">ICustomXmlPart</a>[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Presentation.html#getAllCustomXmlParts--">getAllCustomXmlParts</a></span>()</code>
<div class="block">
 Returns all custom data parts in the presentaion.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IAudioCollection.html" title="interface in com.aspose.slides">IAudioCollection</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Presentation.html#getAudios--">getAudios</a></span>()</code>
<div class="block">
 Returns the collection of all embedded audio files in the presentation.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/ICommentAuthorCollection.html" title="interface in com.aspose.slides">ICommentAuthorCollection</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Presentation.html#getCommentAuthors--">getCommentAuthors</a></span>()</code>
<div class="block">
 Returns the collection of comments autors.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>java.util.Date</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Presentation.html#getCurrentDateTime--">getCurrentDateTime</a></span>()</code>
<div class="block">
 Returns or sets date and time which will substitute content of datetime fields.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/ICustomData.html" title="interface in com.aspose.slides">ICustomData</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Presentation.html#getCustomData--">getCustomData</a></span>()</code>
<div class="block">
 Returns the presentation's custom data.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/ITextStyle.html" title="interface in com.aspose.slides">ITextStyle</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Presentation.html#getDefaultTextStyle--">getDefaultTextStyle</a></span>()</code>
<div class="block">
 Returns default text style for shapes.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IDigitalSignatureCollection.html" title="interface in com.aspose.slides">IDigitalSignatureCollection</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Presentation.html#getDigitalSignatures--">getDigitalSignatures</a></span>()</code>
<div class="block">
 Returns the collection of signatures used to sign the presentation.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IDocumentProperties.html" title="interface in com.aspose.slides">IDocumentProperties</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Presentation.html#getDocumentProperties--">getDocumentProperties</a></span>()</code>
<div class="block">
 Returns DocumentProperties object which contains standard and custom document properties.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Presentation.html#getFirstSlideNumber--">getFirstSlideNumber</a></span>()</code>
<div class="block">
 Represents the first slide number in the presentation</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IFontsManager.html" title="interface in com.aspose.slides">IFontsManager</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Presentation.html#getFontsManager--">getFontsManager</a></span>()</code>
<div class="block">
 Returns fonts manager.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IPresentationHeaderFooterManager.html" title="interface in com.aspose.slides">IPresentationHeaderFooterManager</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Presentation.html#getHeaderFooterManager--">getHeaderFooterManager</a></span>()</code>
<div class="block">
 Returns actual HeaderFooter manager.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IHyperlinkQueries.html" title="interface in com.aspose.slides">IHyperlinkQueries</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Presentation.html#getHyperlinkQueries--">getHyperlinkQueries</a></span>()</code>
<div class="block">
 Provides easy access to all hyperlinks contained in all presentation slides (not in master, layout, notes slides).</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IImageCollection.html" title="interface in com.aspose.slides">IImageCollection</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Presentation.html#getImages--">getImages</a></span>()</code>
<div class="block">
 Returns the collection of all images in the presentation.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IImage.html" title="interface in com.aspose.slides">IImage</a>[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Presentation.html#getImages-com.aspose.slides.IRenderingOptions-">getImages</a></span>(<a href="../../../com/aspose/slides/IRenderingOptions.html" title="interface in com.aspose.slides">IRenderingOptions</a>&nbsp;options)</code>
<div class="block">
 Returns a Image objects for all slides of a presentation.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IImage.html" title="interface in com.aspose.slides">IImage</a>[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Presentation.html#getImages-com.aspose.slides.IRenderingOptions-java.awt.Dimension-">getImages</a></span>(<a href="../../../com/aspose/slides/IRenderingOptions.html" title="interface in com.aspose.slides">IRenderingOptions</a>&nbsp;options,
         java.awt.Dimension&nbsp;imageSize)</code>
<div class="block">
 Returns a Thumbnail Image objects for all slides of a presentation with specified size.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IImage.html" title="interface in com.aspose.slides">IImage</a>[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Presentation.html#getImages-com.aspose.slides.IRenderingOptions-float-float-">getImages</a></span>(<a href="../../../com/aspose/slides/IRenderingOptions.html" title="interface in com.aspose.slides">IRenderingOptions</a>&nbsp;options,
         float&nbsp;scaleX,
         float&nbsp;scaleY)</code>
<div class="block">
 Returns a Thumbnail Image objects for all slides of a presentation with custom scaling.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IImage.html" title="interface in com.aspose.slides">IImage</a>[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Presentation.html#getImages-com.aspose.slides.IRenderingOptions-int:A-">getImages</a></span>(<a href="../../../com/aspose/slides/IRenderingOptions.html" title="interface in com.aspose.slides">IRenderingOptions</a>&nbsp;options,
         int[]&nbsp;slides)</code>
<div class="block">
 Returns a Thumbnail Image objects for specified slides of a presentation.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IImage.html" title="interface in com.aspose.slides">IImage</a>[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Presentation.html#getImages-com.aspose.slides.IRenderingOptions-int:A-java.awt.Dimension-">getImages</a></span>(<a href="../../../com/aspose/slides/IRenderingOptions.html" title="interface in com.aspose.slides">IRenderingOptions</a>&nbsp;options,
         int[]&nbsp;slides,
         java.awt.Dimension&nbsp;imageSize)</code>
<div class="block">
 Returns a Thumbnail Image objects for specified slides of a presentation with specified size.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IImage.html" title="interface in com.aspose.slides">IImage</a>[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Presentation.html#getImages-com.aspose.slides.IRenderingOptions-int:A-float-float-">getImages</a></span>(<a href="../../../com/aspose/slides/IRenderingOptions.html" title="interface in com.aspose.slides">IRenderingOptions</a>&nbsp;options,
         int[]&nbsp;slides,
         float&nbsp;scaleX,
         float&nbsp;scaleY)</code>
<div class="block">
 Returns a Thumbnail Image objects for specified slides of a presentation with custom scaling.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IGlobalLayoutSlideCollection.html" title="interface in com.aspose.slides">IGlobalLayoutSlideCollection</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Presentation.html#getLayoutSlides--">getLayoutSlides</a></span>()</code>
<div class="block">
 Returns a list of all layout slides that are defined in the presentation.</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IMasterHandoutSlideManager.html" title="interface in com.aspose.slides">IMasterHandoutSlideManager</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Presentation.html#getMasterHandoutSlideManager--">getMasterHandoutSlideManager</a></span>()</code>
<div class="block">
 Returns handout master manager.</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IMasterNotesSlideManager.html" title="interface in com.aspose.slides">IMasterNotesSlideManager</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Presentation.html#getMasterNotesSlideManager--">getMasterNotesSlideManager</a></span>()</code>
<div class="block">
 Returns notes master manager.</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IMasterSlideCollection.html" title="interface in com.aspose.slides">IMasterSlideCollection</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Presentation.html#getMasters--">getMasters</a></span>()</code>
<div class="block">
 Returns a list of all master slides that are defined in the presentation.</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IMasterTheme.html" title="interface in com.aspose.slides">IMasterTheme</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Presentation.html#getMasterTheme--">getMasterTheme</a></span>()</code>
<div class="block">
 Returns master theme.</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/INotesSize.html" title="interface in com.aspose.slides">INotesSize</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Presentation.html#getNotesSize--">getNotesSize</a></span>()</code>
<div class="block">
 Returns notes slide size object.</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>com.aspose.slides.IDOMObject</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Presentation.html#getParent_Immediate--">getParent_Immediate</a></span>()</code>
<div class="block">
 Returns Parent_Immediate object.</div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IPresentation.html" title="interface in com.aspose.slides">IPresentation</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Presentation.html#getPresentation--">getPresentation</a></span>()</code>
<div class="block">
 Returns the parent presentation of a text.</div>
</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IProtectionManager.html" title="interface in com.aspose.slides">IProtectionManager</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Presentation.html#getProtectionManager--">getProtectionManager</a></span>()</code>
<div class="block">
 Gets manager of the permissions for this presentation.</div>
</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/ISectionCollection.html" title="interface in com.aspose.slides">ISectionCollection</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Presentation.html#getSections--">getSections</a></span>()</code>
<div class="block">
 Returns a list of all slides sections that are defined in the presentation.</div>
</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IBaseSlide.html" title="interface in com.aspose.slides">IBaseSlide</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Presentation.html#getSlideById-long-">getSlideById</a></span>(long&nbsp;id)</code>
<div class="block">
 Returns a Slide, MasterSlide or LayoutSlide by Id.</div>
</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/ISlideCollection.html" title="interface in com.aspose.slides">ISlideCollection</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Presentation.html#getSlides--">getSlides</a></span>()</code>
<div class="block">
 Returns a list of all slides that are defined in the presentation.</div>
</td>
</tr>
<tr id="i32" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/SlideShowSettings.html" title="class in com.aspose.slides">SlideShowSettings</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Presentation.html#getSlideShowSettings--">getSlideShowSettings</a></span>()</code>
<div class="block">
 Returns the slide show settings for the presentation.</div>
</td>
</tr>
<tr id="i33" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/ISlideSize.html" title="interface in com.aspose.slides">ISlideSize</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Presentation.html#getSlideSize--">getSlideSize</a></span>()</code>
<div class="block">
 Returns slide size object.</div>
</td>
</tr>
<tr id="i34" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Presentation.html#getSourceFormat--">getSourceFormat</a></span>()</code>
<div class="block">
 Returns information about from which format presentation was loaded.</div>
</td>
</tr>
<tr id="i35" class="rowColor">
<td class="colFirst"><code>java.awt.image.BufferedImage[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Presentation.html#getThumbnails-com.aspose.slides.IRenderingOptions-">getThumbnails</a></span>(<a href="../../../com/aspose/slides/IRenderingOptions.html" title="interface in com.aspose.slides">IRenderingOptions</a>&nbsp;options)</code>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;
<div class="block"><span class="deprecationComment">Use Presentation.GetThumbnails(IRenderingOptions) instead. The method will be removed after release of version 21.4.</span></div>
</div>
</td>
</tr>
<tr id="i36" class="altColor">
<td class="colFirst"><code>java.awt.image.BufferedImage[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Presentation.html#getThumbnails-com.aspose.slides.IRenderingOptions-java.awt.Dimension-">getThumbnails</a></span>(<a href="../../../com/aspose/slides/IRenderingOptions.html" title="interface in com.aspose.slides">IRenderingOptions</a>&nbsp;options,
             java.awt.Dimension&nbsp;imageSize)</code>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;
<div class="block"><span class="deprecationComment">Use GetImages(IRenderingOptions options, Size imageSize) method instead. The method will be removed after release of version 24.7.</span></div>
</div>
</td>
</tr>
<tr id="i37" class="rowColor">
<td class="colFirst"><code>java.awt.image.BufferedImage[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Presentation.html#getThumbnails-com.aspose.slides.IRenderingOptions-float-float-">getThumbnails</a></span>(<a href="../../../com/aspose/slides/IRenderingOptions.html" title="interface in com.aspose.slides">IRenderingOptions</a>&nbsp;options,
             float&nbsp;scaleX,
             float&nbsp;scaleY)</code>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;
<div class="block"><span class="deprecationComment">Use GetImages(IRenderingOptions options, float scaleX, float scaleY) method instead. The method will be removed after release of version 24.7.</span></div>
</div>
</td>
</tr>
<tr id="i38" class="altColor">
<td class="colFirst"><code>java.awt.image.BufferedImage[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Presentation.html#getThumbnails-com.aspose.slides.IRenderingOptions-int:A-">getThumbnails</a></span>(<a href="../../../com/aspose/slides/IRenderingOptions.html" title="interface in com.aspose.slides">IRenderingOptions</a>&nbsp;options,
             int[]&nbsp;slides)</code>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;
<div class="block"><span class="deprecationComment">Use GetImages(IRenderingOptions options, int[] slides) method instead. The method will be removed after release of version 24.7.</span></div>
</div>
</td>
</tr>
<tr id="i39" class="rowColor">
<td class="colFirst"><code>java.awt.image.BufferedImage[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Presentation.html#getThumbnails-com.aspose.slides.IRenderingOptions-int:A-java.awt.Dimension-">getThumbnails</a></span>(<a href="../../../com/aspose/slides/IRenderingOptions.html" title="interface in com.aspose.slides">IRenderingOptions</a>&nbsp;options,
             int[]&nbsp;slides,
             java.awt.Dimension&nbsp;imageSize)</code>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;
<div class="block"><span class="deprecationComment">Use GetImages(IRenderingOptions options, int[] slides, Size imageSize) method instead. The method will be removed after release of version 24.7.</span></div>
</div>
</td>
</tr>
<tr id="i40" class="altColor">
<td class="colFirst"><code>java.awt.image.BufferedImage[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Presentation.html#getThumbnails-com.aspose.slides.IRenderingOptions-int:A-float-float-">getThumbnails</a></span>(<a href="../../../com/aspose/slides/IRenderingOptions.html" title="interface in com.aspose.slides">IRenderingOptions</a>&nbsp;options,
             int[]&nbsp;slides,
             float&nbsp;scaleX,
             float&nbsp;scaleY)</code>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;
<div class="block"><span class="deprecationComment">Use GetImages(IRenderingOptions options, int[] slides, float scaleX, float scaleY) method instead. The method will be removed after release of version 24.7.</span></div>
</div>
</td>
</tr>
<tr id="i41" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IVbaProject.html" title="interface in com.aspose.slides">IVbaProject</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Presentation.html#getVbaProject--">getVbaProject</a></span>()</code>
<div class="block">
 Gets or sets VBA project with presentation macros.</div>
</td>
</tr>
<tr id="i42" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IVideoCollection.html" title="interface in com.aspose.slides">IVideoCollection</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Presentation.html#getVideos--">getVideos</a></span>()</code>
<div class="block">
 Returns the collection of all embedded video files in the presentation.</div>
</td>
</tr>
<tr id="i43" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IViewProperties.html" title="interface in com.aspose.slides">IViewProperties</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Presentation.html#getViewProperties--">getViewProperties</a></span>()</code>
<div class="block">
 Gets presentation wide view properties.</div>
</td>
</tr>
<tr id="i44" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Presentation.html#highlightRegex-java.util.regex.Pattern-java.awt.Color-com.aspose.slides.IFindResultCallback-">highlightRegex</a></span>(java.util.regex.Pattern&nbsp;regex,
              java.awt.Color&nbsp;highlightColor,
              <a href="../../../com/aspose/slides/IFindResultCallback.html" title="interface in com.aspose.slides">IFindResultCallback</a>&nbsp;callback)</code>
<div class="block">
 Highlights all matches of the regular expression with the specified color.</div>
</td>
</tr>
<tr id="i45" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Presentation.html#highlightText-java.lang.String-java.awt.Color-">highlightText</a></span>(java.lang.String&nbsp;text,
             java.awt.Color&nbsp;highlightColor)</code>
<div class="block">
 Highlights all matches of the sample text with the specified color.</div>
</td>
</tr>
<tr id="i46" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Presentation.html#highlightText-java.lang.String-java.awt.Color-com.aspose.slides.ITextSearchOptions-com.aspose.slides.IFindResultCallback-">highlightText</a></span>(java.lang.String&nbsp;text,
             java.awt.Color&nbsp;highlightColor,
             <a href="../../../com/aspose/slides/ITextSearchOptions.html" title="interface in com.aspose.slides">ITextSearchOptions</a>&nbsp;options,
             <a href="../../../com/aspose/slides/IFindResultCallback.html" title="interface in com.aspose.slides">IFindResultCallback</a>&nbsp;callback)</code>
<div class="block">
 Highlights all matches of the sample text with the specified color.</div>
</td>
</tr>
<tr id="i47" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Presentation.html#joinPortionsWithSameFormatting--">joinPortionsWithSameFormatting</a></span>()</code>
<div class="block">
 Joins runs with same formatting in all paragraphs in all acceptable shapes in all slides.</div>
</td>
</tr>
<tr id="i48" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Presentation.html#replaceRegex-java.util.regex.Pattern-java.lang.String-com.aspose.slides.IFindResultCallback-">replaceRegex</a></span>(java.util.regex.Pattern&nbsp;regex,
            java.lang.String&nbsp;newText,
            <a href="../../../com/aspose/slides/IFindResultCallback.html" title="interface in com.aspose.slides">IFindResultCallback</a>&nbsp;callback)</code>
<div class="block">
 Replaces all matches of the regular expression with the specified string.</div>
</td>
</tr>
<tr id="i49" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Presentation.html#replaceText-java.lang.String-java.lang.String-com.aspose.slides.ITextSearchOptions-com.aspose.slides.IFindResultCallback-">replaceText</a></span>(java.lang.String&nbsp;oldText,
           java.lang.String&nbsp;newText,
           <a href="../../../com/aspose/slides/ITextSearchOptions.html" title="interface in com.aspose.slides">ITextSearchOptions</a>&nbsp;options,
           <a href="../../../com/aspose/slides/IFindResultCallback.html" title="interface in com.aspose.slides">IFindResultCallback</a>&nbsp;callback)</code>
<div class="block">
 Replaces all occurrences of the specified text with another specified text.</div>
</td>
</tr>
<tr id="i50" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Presentation.html#save-com.aspose.slides.IXamlOptions-">save</a></span>(<a href="../../../com/aspose/slides/IXamlOptions.html" title="interface in com.aspose.slides">IXamlOptions</a>&nbsp;options)</code>
<div class="block">
 Saves all slides of a presentation to a set of files representing XAML markup.</div>
</td>
</tr>
<tr id="i51" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Presentation.html#save-java.io.OutputStream-int-">save</a></span>(java.io.OutputStream&nbsp;stream,
    int&nbsp;format)</code>
<div class="block">
 Saves all slides of a presentation to a stream in the specified format.</div>
</td>
</tr>
<tr id="i52" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Presentation.html#save-java.io.OutputStream-int:A-int-">save</a></span>(java.io.OutputStream&nbsp;stream,
    int[]&nbsp;slides,
    int&nbsp;format)</code>
<div class="block">
 Saves specified slides of a presentation to a stream in the specified format with page number keeping.</div>
</td>
</tr>
<tr id="i53" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Presentation.html#save-java.io.OutputStream-int:A-int-com.aspose.slides.ISaveOptions-">save</a></span>(java.io.OutputStream&nbsp;stream,
    int[]&nbsp;slides,
    int&nbsp;format,
    <a href="../../../com/aspose/slides/ISaveOptions.html" title="interface in com.aspose.slides">ISaveOptions</a>&nbsp;options)</code>
<div class="block">
 Saves specified slides of a presentation to a stream in the specified format with page number keeping.</div>
</td>
</tr>
<tr id="i54" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Presentation.html#save-java.io.OutputStream-int-com.aspose.slides.ISaveOptions-">save</a></span>(java.io.OutputStream&nbsp;stream,
    int&nbsp;format,
    <a href="../../../com/aspose/slides/ISaveOptions.html" title="interface in com.aspose.slides">ISaveOptions</a>&nbsp;options)</code>
<div class="block">
 Saves all slides of a presentation to a stream in the specified format and with additional options.</div>
</td>
</tr>
<tr id="i55" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Presentation.html#save-java.lang.String-int-">save</a></span>(java.lang.String&nbsp;fname,
    int&nbsp;format)</code>
<div class="block">
 Saves all slides of a presentation to a file with the specified format.</div>
</td>
</tr>
<tr id="i56" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Presentation.html#save-java.lang.String-int:A-int-">save</a></span>(java.lang.String&nbsp;fname,
    int[]&nbsp;slides,
    int&nbsp;format)</code>
<div class="block">
 Saves specified slides of a presentation to a file with the specified format with page number keeping.</div>
</td>
</tr>
<tr id="i57" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Presentation.html#save-java.lang.String-int:A-int-com.aspose.slides.ISaveOptions-">save</a></span>(java.lang.String&nbsp;fname,
    int[]&nbsp;slides,
    int&nbsp;format,
    <a href="../../../com/aspose/slides/ISaveOptions.html" title="interface in com.aspose.slides">ISaveOptions</a>&nbsp;options)</code>
<div class="block">
 Saves specified slides of a presentation to a file with the specified format with page number keeping.</div>
</td>
</tr>
<tr id="i58" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Presentation.html#save-java.lang.String-int-com.aspose.slides.ISaveOptions-">save</a></span>(java.lang.String&nbsp;fname,
    int&nbsp;format,
    <a href="../../../com/aspose/slides/ISaveOptions.html" title="interface in com.aspose.slides">ISaveOptions</a>&nbsp;options)</code>
<div class="block">
 Saves all slides of a presentation to a file with the specified format and with additional options.</div>
</td>
</tr>
<tr id="i59" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Presentation.html#setCurrentDateTime-java.util.Date-">setCurrentDateTime</a></span>(java.util.Date&nbsp;value)</code>
<div class="block">
 Returns or sets date and time which will substitute content of datetime fields.</div>
</td>
</tr>
<tr id="i60" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Presentation.html#setFirstSlideNumber-int-">setFirstSlideNumber</a></span>(int&nbsp;value)</code>
<div class="block">
 Represents the first slide number in the presentation</div>
</td>
</tr>
<tr id="i61" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Presentation.html#setVbaProject-com.aspose.slides.IVbaProject-">setVbaProject</a></span>(<a href="../../../com/aspose/slides/IVbaProject.html" title="interface in com.aspose.slides">IVbaProject</a>&nbsp;value)</code>
<div class="block">
 Gets or sets VBA project with presentation macros.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="Presentation--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Presentation</h4>
<pre>public&nbsp;Presentation()</pre>
<div class="block"><p>
 This constructor creates new presentation from scratch.
 Created presentation has one empty slide.
 </p></div>
</li>
</ul>
<a name="Presentation-com.aspose.slides.LoadOptions-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Presentation</h4>
<pre>public&nbsp;Presentation(<a href="../../../com/aspose/slides/LoadOptions.html" title="class in com.aspose.slides">LoadOptions</a>&nbsp;loadOptions)</pre>
<div class="block"><p>
 This constructor creates new presentation from scratch.
 Created presentation has one empty slide.
 </p></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>loadOptions</code> - Additional load options.</dd>
</dl>
</li>
</ul>
<a name="Presentation-java.io.InputStream-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Presentation</h4>
<pre>public&nbsp;Presentation(java.io.InputStream&nbsp;stream)</pre>
<div class="block"><p>
 This constructor is the primary mechanism for reading an existing Presentation.
 </p><p><hr><blockquote><pre>
 <pre>
 FileInputStream fis = new FileInputStream("demo.pptx");
 Presentation pres = new Presentation(fis);
 fis.close();
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>stream</code> - Input stream.</dd>
</dl>
</li>
</ul>
<a name="Presentation-java.io.InputStream-com.aspose.slides.LoadOptions-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Presentation</h4>
<pre>public&nbsp;Presentation(java.io.InputStream&nbsp;stream,
                    <a href="../../../com/aspose/slides/LoadOptions.html" title="class in com.aspose.slides">LoadOptions</a>&nbsp;loadOptions)</pre>
<div class="block"><p>
 This constructor is the primary mechanism for reading an existing Presentation.
 </p></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>stream</code> - Input stream.</dd>
<dd><code>loadOptions</code> - Additional load options.</dd>
</dl>
</li>
</ul>
<a name="Presentation-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Presentation</h4>
<pre>public&nbsp;Presentation(java.lang.String&nbsp;file)</pre>
<div class="block"><p>
 This constructor gets a source file path from which
 the contents of the Presentation are read.
 </p><p><hr><blockquote><pre>
 <pre>
 Presentation pres = new Presentation("demo.pptx");
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>file</code> - Input file.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>com.aspose.ms.System.ArgumentException</code> - Thrown when input file has zero length</dd>
</dl>
</li>
</ul>
<a name="Presentation-java.lang.String-com.aspose.slides.LoadOptions-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>Presentation</h4>
<pre>public&nbsp;Presentation(java.lang.String&nbsp;file,
                    <a href="../../../com/aspose/slides/LoadOptions.html" title="class in com.aspose.slides">LoadOptions</a>&nbsp;loadOptions)</pre>
<div class="block"><p>
 This constructor gets a source file path from which
 the contents of the Presentation are read.
 </p></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>file</code> - Input file.</dd>
<dd><code>loadOptions</code> - Additional load options.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>com.aspose.ms.System.ArgumentException</code> - Thrown when input file has zero length</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getCurrentDateTime--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCurrentDateTime</h4>
<pre>public final&nbsp;java.util.Date&nbsp;getCurrentDateTime()</pre>
<div class="block"><p>
 Returns or sets date and time which will substitute content of datetime fields.
 Time of this Presentation object creation by default.
 Read/write <code>Date</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPresentation.html#getCurrentDateTime--">getCurrentDateTime</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPresentation.html" title="interface in com.aspose.slides">IPresentation</a></code></dd>
</dl>
</li>
</ul>
<a name="setCurrentDateTime-java.util.Date-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCurrentDateTime</h4>
<pre>public final&nbsp;void&nbsp;setCurrentDateTime(java.util.Date&nbsp;value)</pre>
<div class="block"><p>
 Returns or sets date and time which will substitute content of datetime fields.
 Time of this Presentation object creation by default.
 Read/write <code>Date</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPresentation.html#setCurrentDateTime-java.util.Date-">setCurrentDateTime</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPresentation.html" title="interface in com.aspose.slides">IPresentation</a></code></dd>
</dl>
</li>
</ul>
<a name="getParent_Immediate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getParent_Immediate</h4>
<pre>public final&nbsp;com.aspose.slides.IDOMObject&nbsp;getParent_Immediate()</pre>
<div class="block"><p>
 Returns Parent_Immediate object.
 Read-only <code>IDOMObject</code>.
 </p></div>
</li>
</ul>
<a name="getHeaderFooterManager--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHeaderFooterManager</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IPresentationHeaderFooterManager.html" title="interface in com.aspose.slides">IPresentationHeaderFooterManager</a>&nbsp;getHeaderFooterManager()</pre>
<div class="block"><p>
 Returns actual HeaderFooter manager.
 Read-only <a href="../../../com/aspose/slides/IPresentationHeaderFooterManager.html" title="interface in com.aspose.slides"><code>IPresentationHeaderFooterManager</code></a>.
 </p><p><hr><blockquote><pre>
 The following example shows how to set footer visibility inside Slide of PowerPoint Presentation.
 <pre>
 Presentation pres = new Presentation("presentation.ppt");
 try
 {
     IBaseSlideHeaderFooterManager headerFooterManager = pres.getSlides().get_Item(0).getHeaderFooterManager();
     if (!headerFooterManager.isFooterVisible()) // Property IsFooterVisible is used for indicating that a slide footer placeholder is not present.
     {
         headerFooterManager.setFooterVisibility(true); // Method SetFooterVisibility is used for making a slide footer placeholder visible.
     }
     if (!headerFooterManager.isSlideNumberVisible()) // Property IsSlideNumberVisible is used for indicating that a slide page number placeholder is not present.
     {
         headerFooterManager.setSlideNumberVisibility(true); // Method SetSlideNumberVisibility is used for making a slide page number placeholder visible.
     }
     if (!headerFooterManager.isDateTimeVisible()) // Property IsDateTimeVisible is used for indicating that a slide date-time placeholder is not present.
     {
         headerFooterManager.setDateTimeVisibility(true); // Method SetFooterVisibility is used for making a slide date-time placeholder visible.
     }
     headerFooterManager.setFooterText("Footer text"); // Method SetFooterText is used for setting text to slide footer placeholder.
     headerFooterManager.setDateTimeText("Date and time text"); // Method SetDateTimeText is used for setting text to slide date-time placeholder.
     pres.save("Presentation.ppt", SaveFormat.Ppt);
 }
 finally
 {
     if (pres != null) pres.dispose();
 }
 </pre>
 The following example shows how to set child footer visibility inside Slide.
 <pre>
 Presentation pres = new Presentation("presentation.ppt");
 try
 {
     IMasterSlideHeaderFooterManager headerFooterManager = pres.getMasters().get_Item(0).getHeaderFooterManager();
     headerFooterManager.setFooterAndChildFootersVisibility(true); // Method SetFooterAndChildFootersVisibility is used for making a master slide and all child footer placeholders visible.
     headerFooterManager.setSlideNumberAndChildSlideNumbersVisibility(true); // Method SetSlideNumberAndChildSlideNumbersVisibility is used for making a master slide and all child page number placeholders visible.
     headerFooterManager.setDateTimeAndChildDateTimesVisibility(true); // Method SetDateTimeAndChildDateTimesVisibility is used for making a master slide and all child date-time placeholders visible.

     headerFooterManager.setFooterAndChildFootersText("Footer text"); // Method SetFooterAndChildFootersText is used for setting text to master slide and all child footer placeholders.
     headerFooterManager.setDateTimeAndChildDateTimesText("Date and time text"); // Method SetDateTimeAndChildDateTimesText is used for setting text to master slide and all child date-time placeholders.
 }
 finally
 {
     if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPresentation.html#getHeaderFooterManager--">getHeaderFooterManager</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPresentation.html" title="interface in com.aspose.slides">IPresentation</a></code></dd>
</dl>
</li>
</ul>
<a name="getProtectionManager--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProtectionManager</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IProtectionManager.html" title="interface in com.aspose.slides">IProtectionManager</a>&nbsp;getProtectionManager()</pre>
<div class="block"><p>
 Gets manager of the permissions for this presentation.
 Read-only <a href="../../../com/aspose/slides/IProtectionManager.html" title="interface in com.aspose.slides"><code>IProtectionManager</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPresentation.html#getProtectionManager--">getProtectionManager</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPresentation.html" title="interface in com.aspose.slides">IPresentation</a></code></dd>
</dl>
</li>
</ul>
<a name="getSlides--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSlides</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/ISlideCollection.html" title="interface in com.aspose.slides">ISlideCollection</a>&nbsp;getSlides()</pre>
<div class="block"><p>
 Returns a list of all slides that are defined in the presentation.
 Read-only <a href="../../../com/aspose/slides/ISlideCollection.html" title="interface in com.aspose.slides"><code>ISlideCollection</code></a>.
 </p><p><hr><blockquote><pre>
 The following example shows how to set slides' background color of PowerPoint Presentation.
 <pre>
 // Instantiate the Presentation class that represents the presentation file
 Presentation pres = new Presentation();
 try
 {
     // Set the background color of the first ISlide to Blue
     pres.getSlides().get_Item(0).getBackground().setType(BackgroundType.OwnBackground);
     pres.getSlides().get_Item(0).getBackground().getFillFormat().setFillType(FillType.Solid);
     pres.getSlides().get_Item(0).getBackground().getFillFormat().getSolidFillColor().setColor(Color.BLUE);
     pres.save("ContentBG_out.pptx", SaveFormat.Pptx);
 }
 finally
 {
     if (pres != null) pres.dispose();
 }
 </pre>
 The following example shows how to set slides' background image of PowerPoint Presentation.
 <pre>
 // Instantiate the Presentation class that represents the presentation file
 Presentation pres = new Presentation("SetImageAsBackground.pptx");
 try {
     // Set the background with Image
     pres.getSlides().get_Item(0).getBackground().setType(BackgroundType.OwnBackground);
     pres.getSlides().get_Item(0).getBackground().getFillFormat().setFillType(FillType.Picture);
     pres.getSlides().get_Item(0).getBackground().getFillFormat().getPictureFillFormat().setPictureFillMode(PictureFillMode.Stretch);
     // Set the picture
     BufferedImage img = ImageIO.read(new File("Tulips.jpg"));
     // Add image to presentation's images collection
     IPPImage imgx = pres.getImages().addImage(img);
     pres.getSlides().get_Item(0).getBackground().getFillFormat().getPictureFillFormat().getPicture().setImage(imgx);
     // Write the presentation to disk
     pres.save("ContentBG_Img_out.pptx", SaveFormat.Pptx);
 } catch (IOException e) { }
 finally
 {
     if (pres != null) pres.dispose();
 }
 </pre>
 The following example shows how to add slide transition Presentation.
 <pre>
 // Instantiate Presentation class to load the source presentation file
 Presentation pres = new Presentation("AccessSlides.pptx");
 try
 {
     // Apply circle type transition on slide 1
     pres.getSlides().get_Item(0).getSlideShowTransition().setType(TransitionType.Circle);
     // Apply comb type transition on slide 2
     pres.getSlides().get_Item(1).getSlideShowTransition().setType(TransitionType.Comb);
     // Write the presentation to disk
     pres.save("SampleTransition_out.pptx", SaveFormat.Pptx);
 }
 finally
 {
     if (pres != null) pres.dispose();
 }
 </pre>
 The following example shows how to add advanced slide Transition.
 <pre>
 // Instantiate Presentation class that represents a presentation file
 Presentation pres = new Presentation("BetterSlideTransitions.pptx");
 try
 {
     // Apply circle type transition on slide 1
     pres.getSlides().get_Item(0).getSlideShowTransition().setType(TransitionType.Circle);
     // Set the transition time of 3 seconds
     pres.getSlides().get_Item(0).getSlideShowTransition().setAdvanceOnClick(true);
     pres.getSlides().get_Item(0).getSlideShowTransition().setAdvanceAfterTime(3000);
     // Apply comb type transition on slide 2
     pres.getSlides().get_Item(1).getSlideShowTransition().setType(TransitionType.Comb);
     // Set the transition time of 5 seconds
     pres.getSlides().get_Item(1).getSlideShowTransition().setAdvanceOnClick(true);
     pres.getSlides().get_Item(1).getSlideShowTransition().setAdvanceAfterTime(5000);
     // Apply zoom type transition on slide 3
     pres.getSlides().get_Item(2).getSlideShowTransition().setType(TransitionType.Zoom);
     // Set the transition time of 7 seconds
     pres.getSlides().get_Item(2).getSlideShowTransition().setAdvanceOnClick(true);
     pres.getSlides().get_Item(2).getSlideShowTransition().setAdvanceAfterTime(7000);
     // Write the presentation to disk
     pres.save("SampleTransition_out.pptx", SaveFormat.Pptx);
 }
 finally
 {
     if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPresentation.html#getSlides--">getSlides</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPresentation.html" title="interface in com.aspose.slides">IPresentation</a></code></dd>
</dl>
</li>
</ul>
<a name="getSections--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSections</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/ISectionCollection.html" title="interface in com.aspose.slides">ISectionCollection</a>&nbsp;getSections()</pre>
<div class="block"><p>
 Returns a list of all slides sections that are defined in the presentation.
 Read-only <a href="../../../com/aspose/slides/ISectionCollection.html" title="interface in com.aspose.slides"><code>ISectionCollection</code></a>.
 </p><p><hr><blockquote><pre>
 The following examples shows how to create Sections in a PowerPoint Presentation.
 <pre>
 Presentation pres = new Presentation();
 try {
     ISlide defaultSlide = pres.getSlides().get_Item(0);
     ISlide newSlide1 = pres.getSlides().addEmptySlide(pres.getLayoutSlides().get_Item(0));
     ISlide newSlide2 = pres.getSlides().addEmptySlide(pres.getLayoutSlides().get_Item(0));
     ISlide newSlide3 = pres.getSlides().addEmptySlide(pres.getLayoutSlides().get_Item(0));
     ISlide newSlide4 = pres.getSlides().addEmptySlide(pres.getLayoutSlides().get_Item(0));
     ISection section1 = pres.getSections().addSection("Section 1", newSlide1);
     // section1 will be ended at newSlide2 and after it section2 will start
     ISection section2 = pres.getSections().addSection("Section 2", newSlide3);
     pres.save("pres-sections.pptx", SaveFormat.Pptx);
     pres.getSections().reorderSectionWithSlides(section2, 0);
     pres.save("pres-sections-moved.pptx", SaveFormat.Pptx);
     pres.getSections().removeSectionWithSlides(section2);
     pres.getSections().appendEmptySection("Last empty section");
     pres.save("pres-section-with-empty.pptx",SaveFormat.Pptx);
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 The following examples shows how to changing the names of Sections.
 <pre>
 Presentation pres = new Presentation("pres.pptx");
 try {
     ISection section = pres.getSections().get_Item(0);
     section.setName("My section");
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPresentation.html#getSections--">getSections</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPresentation.html" title="interface in com.aspose.slides">IPresentation</a></code></dd>
</dl>
</li>
</ul>
<a name="getSlideSize--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSlideSize</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/ISlideSize.html" title="interface in com.aspose.slides">ISlideSize</a>&nbsp;getSlideSize()</pre>
<div class="block"><p>
 Returns slide size object.
 Read-only <a href="../../../com/aspose/slides/ISlideSize.html" title="interface in com.aspose.slides"><code>ISlideSize</code></a>.
 </p><p><hr><blockquote><pre>
 The following example shows how to change the slide size in a PowerPoint Presentation.
 <pre>
 Presentation pres = new Presentation("pres-4x3-aspect-ratio.pptx");
 try {
     pres.getSlideSize().setSize(SlideSizeType.OnScreen16x9, SlideSizeScaleType.DoNotScale);
     pres.save("pres-4x3-aspect-ratio.pptx", SaveFormat.Pptx);
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 The following example shows how to set slide size with respect to content scaling for a PowerPoint Presentation.
 <pre>
 // Instantiate a Presentation object that represents a presentation file
 Presentation presentation = new Presentation("AccessSlides.pptx");
 try {
     Presentation auxPresentation = new Presentation();
     try {
         ISlide slide = presentation.getSlides().get_Item(0);
         // Set the slide size of generated presentations to that of source
         presentation.getSlideSize().setSize(540, 720, SlideSizeScaleType.EnsureFit); // Method SetSize is used for set slide size with scale content to ensure fit
         presentation.getSlideSize().setSize(SlideSizeType.A4Paper, SlideSizeScaleType.Maximize); // Method SetSize is used for set slide size with maximize size of content
         // Save Presentation to disk
         auxPresentation.save("Set_Size&amp;Type_out.pptx", SaveFormat.Pptx);
     } finally {
         if (auxPresentation != null) auxPresentation.dispose();
     }
 } finally {
     if (presentation != null) presentation.dispose();
 }
 </pre>
 The following example shows how to specifying custom slide sizes in a PowerPoint Presentation.
 <pre>
 Presentation pres = new Presentation("pres.pptx");
 try {
     pres.getSlideSize().setSize(780, 540, SlideSizeScaleType.DoNotScale); // A4 paper size
     pres.save("pres-a4-slide-size.pptx", SaveFormat.Pptx);
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPresentation.html#getSlideSize--">getSlideSize</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPresentation.html" title="interface in com.aspose.slides">IPresentation</a></code></dd>
</dl>
</li>
</ul>
<a name="getNotesSize--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNotesSize</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/INotesSize.html" title="interface in com.aspose.slides">INotesSize</a>&nbsp;getNotesSize()</pre>
<div class="block"><p>
 Returns notes slide size object.
 Read-only <a href="../../../com/aspose/slides/INotesSize.html" title="interface in com.aspose.slides"><code>INotesSize</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPresentation.html#getNotesSize--">getNotesSize</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPresentation.html" title="interface in com.aspose.slides">IPresentation</a></code></dd>
</dl>
</li>
</ul>
<a name="getLayoutSlides--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLayoutSlides</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IGlobalLayoutSlideCollection.html" title="interface in com.aspose.slides">IGlobalLayoutSlideCollection</a>&nbsp;getLayoutSlides()</pre>
<div class="block"><p>
 Returns a list of all layout slides that are defined in the presentation.
 Read-only <a href="../../../com/aspose/slides/IGlobalLayoutSlideCollection.html" title="interface in com.aspose.slides"><code>IGlobalLayoutSlideCollection</code></a>.
 </p><p><hr>
 You can access to alternative API for adding/inserting/removing/cloning layout slides 
 by using IMasterSlide.LayoutSlides property.
 </hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPresentation.html#getLayoutSlides--">getLayoutSlides</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPresentation.html" title="interface in com.aspose.slides">IPresentation</a></code></dd>
</dl>
</li>
</ul>
<a name="getMasters--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMasters</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IMasterSlideCollection.html" title="interface in com.aspose.slides">IMasterSlideCollection</a>&nbsp;getMasters()</pre>
<div class="block"><p>
 Returns a list of all master slides that are defined in the presentation.
 Read-only <a href="../../../com/aspose/slides/IMasterSlideCollection.html" title="interface in com.aspose.slides"><code>IMasterSlideCollection</code></a>.
 </p><p><hr><blockquote><pre>
 The following examples shows how to adding Images to Master Slides of PowerPoint Presentation.
 <pre>
 Presentation pres = new Presentation();
 try {
     ISlide slide = pres.getSlides().get_Item(0);
     IMasterSlide masterSlide = slide.getLayoutSlide().getMasterSlide();
     IPPImage image = pres.getImages().addImage(Files.readAllBytes(Paths.get("image.png")));
     masterSlide.getShapes().addPictureFrame(ShapeType.Rectangle, 10, 10, 100, 100, image);
     pres.save("pres.pptx", SaveFormat.Pptx);
 } catch(IOException e) {
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 The following examples shows how to change the background color of the master slide of PowerPoint Presentation.
 <pre>
 // Instantiate the Presentation class that represents the presentation file
 Presentation pres = new Presentation();
 try
 {
     // Set the background color of the Master ISlide to Forest Green
     pres.getMasters().get_Item(0).getBackground().setType(BackgroundType.OwnBackground);
     pres.getMasters().get_Item(0).getBackground().getFillFormat().setFillType(FillType.Solid);
     pres.getMasters().get_Item(0).getBackground().getFillFormat().getSolidFillColor().setColor(Color.GREEN);
     // Write the presentation to disk
     pres.save("SetSlideBackgroundMaster_out.pptx", SaveFormat.Pptx);
 }
 finally
 {
     if (pres != null) pres.dispose();
 }
 </pre>
 The following examples shows how to add slide layout to PowerPoint Presentation.
 <pre>
 // Instantiate Presentation class that represents the presentation file
 Presentation presentation = new Presentation("AccessSlides.pptx");
 try
 {
     // Try to search by layout slide type
     IMasterLayoutSlideCollection layoutSlides = presentation.getMasters().get_Item(0).getLayoutSlides();
     ILayoutSlide layoutSlide = null;
     if (layoutSlides.getByType(SlideLayoutType.TitleAndObject) != null)
         layoutSlide = layoutSlides.getByType(SlideLayoutType.TitleAndObject);
     else
         layoutSlide = layoutSlides.getByType(SlideLayoutType.Title);

     if (layoutSlide == null)
     {
         // The situation when a presentation doesn't contain some type of layouts.
         // presentation File only contains Blank and Custom layout types.
         // But layout slides with Custom types has different slide names,
         // like "Title", "Title and Content", etc. And it is possible to use these
         // names for layout slide selection.
         // Also it is possible to use the set of placeholder shape types. For example,
         // Title slide should have only Title pleceholder type, etc.
         for (ILayoutSlide titleAndObjectLayoutSlide : (Iterable<ILayoutSlide>) layoutSlides)
         {
             if ("Title and Object".equals(titleAndObjectLayoutSlide.getName()))
             {
                 layoutSlide = titleAndObjectLayoutSlide;
                 break;
             }
         }
         if (layoutSlide == null)
         {
             for (ILayoutSlide titleLayoutSlide : (Iterable<ILayoutSlide>) layoutSlides)
             {
                 if ("Title".equals(titleLayoutSlide.getName()))
                 {
                     layoutSlide = titleLayoutSlide;
                     break;
                 }
             }
             if (layoutSlide == null)
             {
                 layoutSlide = layoutSlides.getByType(SlideLayoutType.Blank);
                 if (layoutSlide == null)
                 {
                     layoutSlide = layoutSlides.add(SlideLayoutType.TitleAndObject, "Title and Object");
                 }
             }
         }
     }
     // Adding empty slide with added layout slide
     presentation.getSlides().insertEmptySlide(0, layoutSlide);
     // Save presentation
     presentation.save("AddLayoutSlides_out.pptx", SaveFormat.Pptx);
 }
 finally
 {
     if (presentation != null) presentation.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPresentation.html#getMasters--">getMasters</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPresentation.html" title="interface in com.aspose.slides">IPresentation</a></code></dd>
</dl>
</li>
</ul>
<a name="getMasterNotesSlideManager--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMasterNotesSlideManager</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IMasterNotesSlideManager.html" title="interface in com.aspose.slides">IMasterNotesSlideManager</a>&nbsp;getMasterNotesSlideManager()</pre>
<div class="block"><p>
 Returns notes master manager.
 Read-only <a href="../../../com/aspose/slides/IMasterNotesSlideManager.html" title="interface in com.aspose.slides"><code>IMasterNotesSlideManager</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPresentation.html#getMasterNotesSlideManager--">getMasterNotesSlideManager</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPresentation.html" title="interface in com.aspose.slides">IPresentation</a></code></dd>
</dl>
</li>
</ul>
<a name="getMasterHandoutSlideManager--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMasterHandoutSlideManager</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IMasterHandoutSlideManager.html" title="interface in com.aspose.slides">IMasterHandoutSlideManager</a>&nbsp;getMasterHandoutSlideManager()</pre>
<div class="block"><p>
 Returns handout master manager.
 Read-only <a href="../../../com/aspose/slides/IMasterHandoutSlideManager.html" title="interface in com.aspose.slides"><code>IMasterHandoutSlideManager</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPresentation.html#getMasterHandoutSlideManager--">getMasterHandoutSlideManager</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPresentation.html" title="interface in com.aspose.slides">IPresentation</a></code></dd>
</dl>
</li>
</ul>
<a name="getFontsManager--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFontsManager</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IFontsManager.html" title="interface in com.aspose.slides">IFontsManager</a>&nbsp;getFontsManager()</pre>
<div class="block"><p>
 Returns fonts manager.
 Read-only <a href="../../../com/aspose/slides/IFontsManager.html" title="interface in com.aspose.slides"><code>IFontsManager</code></a>.
 </p><p><hr><blockquote><pre>
 The following example shows how to add embedded fonts to PowerPoint Presentation.
 <pre>
 // Load presentation
 Presentation pres = new Presentation("Fonts.pptx");
 try {
     // Load source font to be replaced
     IFontData sourceFont = new FontData("Arial");
     IFontData[] allFonts = pres.getFontsManager().getFonts();
     for (IFontData font : allFonts)
     {
         boolean fontAlreadyEmbedded = false;
         IFontData[] embeddedFonts = pres.getFontsManager().getEmbeddedFonts();
         for (int i = 0; i &lt; embeddedFonts.length; i++)
         {
             if (embeddedFonts[i].equals(font))
             {
                 fontAlreadyEmbedded = true;
                 break;
             }
         }
         if (!fontAlreadyEmbedded) {
             pres.getFontsManager().addEmbeddedFont(font, EmbedFontCharacters.All);
         }
     }
     // Save the presentation
     pres.save("AddEmbeddedFont_out.pptx", SaveFormat.Pptx);
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPresentation.html#getFontsManager--">getFontsManager</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPresentation.html" title="interface in com.aspose.slides">IPresentation</a></code></dd>
</dl>
</li>
</ul>
<a name="getDefaultTextStyle--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDefaultTextStyle</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/ITextStyle.html" title="interface in com.aspose.slides">ITextStyle</a>&nbsp;getDefaultTextStyle()</pre>
<div class="block"><p>
 Returns default text style for shapes.
 Read-only <a href="../../../com/aspose/slides/ITextStyle.html" title="interface in com.aspose.slides"><code>ITextStyle</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPresentation.html#getDefaultTextStyle--">getDefaultTextStyle</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPresentation.html" title="interface in com.aspose.slides">IPresentation</a></code></dd>
</dl>
</li>
</ul>
<a name="getCommentAuthors--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCommentAuthors</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/ICommentAuthorCollection.html" title="interface in com.aspose.slides">ICommentAuthorCollection</a>&nbsp;getCommentAuthors()</pre>
<div class="block"><p>
 Returns the collection of comments autors.
 Read-only <a href="../../../com/aspose/slides/ICommentAuthorCollection.html" title="interface in com.aspose.slides"><code>ICommentAuthorCollection</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPresentation.html#getCommentAuthors--">getCommentAuthors</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPresentation.html" title="interface in com.aspose.slides">IPresentation</a></code></dd>
</dl>
</li>
</ul>
<a name="getDocumentProperties--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDocumentProperties</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IDocumentProperties.html" title="interface in com.aspose.slides">IDocumentProperties</a>&nbsp;getDocumentProperties()</pre>
<div class="block"><p>
 Returns DocumentProperties object which contains standard and custom document properties.
 Read-only <a href="../../../com/aspose/slides/IDocumentProperties.html" title="interface in com.aspose.slides"><code>IDocumentProperties</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPresentation.html#getDocumentProperties--">getDocumentProperties</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPresentation.html" title="interface in com.aspose.slides">IPresentation</a></code></dd>
</dl>
</li>
</ul>
<a name="getImages--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getImages</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IImageCollection.html" title="interface in com.aspose.slides">IImageCollection</a>&nbsp;getImages()</pre>
<div class="block"><p>
 Returns the collection of all images in the presentation.
 Read-only <a href="../../../com/aspose/slides/IImageCollection.html" title="interface in com.aspose.slides"><code>IImageCollection</code></a>.
 </p><p><hr><blockquote><pre>
 The following examples shows how to add image as BLOB in PowerPoint Presentation.
 <pre>
 // creates a new presentation to which the image will be added.
 Presentation pres = new Presentation();
 try
 {
     // supposed we have the large image file we want to include into the presentation
     FileInputStream fip = new FileInputStream("large_image.jpg");
     try
     {
         // Let's add the image to the presentation - we choose KeepLocked behavior because we do
         // NOT intend to access the "largeImage.png" file.
         IPPImage img = pres.getImages().addImage(fip, LoadingStreamBehavior.KeepLocked);
         pres.getSlides().get_Item(0).getShapes().addPictureFrame(ShapeType.Rectangle, 0, 0, 300, 200, img);
         // Saves the presentation. While a large presentation gets outputted, the memory consumption
         // stays low through the pres object's lifecycle
         pres.save("presentationWithLargeImage.pptx", SaveFormat.Pptx);
     }
     finally
     {
         fip.close();
     }
 }
 catch (java.io.IOException e) { }
 finally
 {
     pres.dispose();
 }
 </pre>
 The following examples add a hyperlink to an image in a PowerPoint Presentation.
 <pre>
 Presentation pres = new Presentation();
 try {
     // Adds image to presentation
     IPPImage image = pres.getImages().addImage(Files.readAllBytes(Paths.get("image.png")));
     // Creates picture frame on slide 1 based on previously added image
     IPictureFrame pictureFrame = pres.getSlides().get_Item(0).getShapes().addPictureFrame(ShapeType.Rectangle, 10, 10, 100, 100, image);
     pictureFrame.setHyperlinkClick(new Hyperlink("https://www.aspose.com/"));
     pictureFrame.getHyperlinkClick().setTooltip("More than 70% Fortune 100 companies trust Aspose APIs");
     pres.save("pres-out.pptx", SaveFormat.Pptx);
 } catch (IOException e){ }
 finally
 {
     if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPresentation.html#getImages--">getImages</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPresentation.html" title="interface in com.aspose.slides">IPresentation</a></code></dd>
</dl>
</li>
</ul>
<a name="getAudios--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAudios</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IAudioCollection.html" title="interface in com.aspose.slides">IAudioCollection</a>&nbsp;getAudios()</pre>
<div class="block"><p>
 Returns the collection of all embedded audio files in the presentation.
 Read-only <a href="../../../com/aspose/slides/IAudioCollection.html" title="interface in com.aspose.slides"><code>IAudioCollection</code></a>.
 </p><p><hr><blockquote><pre>
 The following examples shows how to add a hyperlink to an audio file.
 <pre>
 Presentation pres = new Presentation();
 try {
     IAudio audio = pres.getAudios().addAudio(Files.readAllBytes(Paths.get("audio.mp3")));
     IAudioFrame audioFrame = pres.getSlides().get_Item(0).getShapes().addAudioFrameEmbedded(10, 10, 100, 100, audio);
     audioFrame.setHyperlinkClick(new Hyperlink("https://www.aspose.com/"));
     audioFrame.getHyperlinkClick().setTooltip("More than 70% Fortune 100 companies trust Aspose APIs");
     pres.save("pres-out.pptx", SaveFormat.Pptx);
 }
 catch (IOException e) {}
 finally
 {
     if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPresentation.html#getAudios--">getAudios</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPresentation.html" title="interface in com.aspose.slides">IPresentation</a></code></dd>
</dl>
</li>
</ul>
<a name="getVideos--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVideos</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IVideoCollection.html" title="interface in com.aspose.slides">IVideoCollection</a>&nbsp;getVideos()</pre>
<div class="block"><p>
 Returns the collection of all embedded video files in the presentation.
 Read-only <a href="../../../com/aspose/slides/IVideoCollection.html" title="interface in com.aspose.slides"><code>IVideoCollection</code></a>.
 </p><p><hr><blockquote><pre>
 The following examples shows how to create embedded Video Frame in a PowerPoint Presentation.
 <pre>
 // Instantiate Presentation class that represents the PPTX
 Presentation pres = new Presentation();
 try {
     // Get the first slide
     ISlide sld = pres.getSlides().get_Item(0);
     // Embedd vide inside presentation
     IVideo vid = pres.getVideos().addVideo(new FileInputStream("Wildlife.mp4"));
     // Add Video Frame
     IVideoFrame vf = sld.getShapes().addVideoFrame(50, 150, 300, 350, vid);
     // Set video to Video Frame
     vf.setEmbeddedVideo(vid);
     // Set Play Mode and Volume of the Video
     vf.setPlayMode(VideoPlayModePreset.Auto);
     vf.setVolume(AudioVolumeMode.Loud);
     // Write the PPTX file to disk
     pres.save("VideoFrame_out.pptx", SaveFormat.Pptx);
 } catch(IOException e) {
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 The following examples shows how to add a video passing path to the video file directly into AddVideoFrame method for PowerPoint Presentation.
 <pre>
 Presentation pres = new Presentation();
 try {
     ISlide sld = pres.getSlides().get_Item(0);
     IVideoFrame vf = sld.getShapes().addVideoFrame(50, 150, 300, 150, "video1.avi");
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 The following examples shows how to add large file through BLOB to a Presentation.
 <pre>
 // Creates a new presentation to which the video will be added
 Presentation pres = new Presentation();
 try {
     FileInputStream fileStream = new FileInputStream("veryLargeVideo.avi");
     try {
         // Let's add the video to the presentation - we chose the KeepLocked behavior because we do
         //not intend to access the "veryLargeVideo.avi" file.
         IVideo video = pres.getVideos().addVideo(fileStream, LoadingStreamBehavior.KeepLocked);
         pres.getSlides().get_Item(0).getShapes().addVideoFrame(0, 0, 480, 270, video);
         // Saves the presentation. While a large presentation gets outputted, the memory consumption
         // stays low through the pres object's lifecycle
         pres.save("presentationWithLargeVideo.pptx", SaveFormat.Pptx);
     } finally {
         if (fileStream != null) fileStream.close();
     }
 } catch(IOException e) {
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 The following examples shows how to export large file through BLOB from PowerPoint Presentation.
 <pre>
 LoadOptions loadOptions = new LoadOptions();
 // Locks the source file and does NOT load it into memory
 loadOptions.getBlobManagementOptions().setPresentationLockingBehavior(PresentationLockingBehavior.KeepLocked);
 // Creates a Presentation's instance, locks the "hugePresentationWithAudiosAndVideos.pptx" file.
 Presentation pres = new Presentation("Large  Video File Test1.pptx", loadOptions);
 try {
     // Let's save each video to a file. To prevent high memory usage, we need a buffer that will be used
     // to transfer the data from the presentation's video stream to a stream for a newly created video file.
     byte[] buffer = new byte[81024];
     // Iterates through the videos
     for (int index = 0; index &lt; pres.getVideos().size(); index++) {
         IVideo video = pres.getVideos().get_Item(index);
         // Opens the presentation video stream. Please, note that we intentionally avoided accessing properties
         // like video.BinaryData - because this property returns a byte array containing a full video, which then
         // causes bytes to be loaded into memory. We use video.GetStream, which will return Stream - and does NOT
         //  require us to load the whole video into the memory.
         InputStream presVideoStream = video.getStream();
         try {
             FileOutputStream outputFileStream = new FileOutputStream("video{index}.avi");
             try {
                 int bytesRead;
                 while ((bytesRead = presVideoStream.read(buffer, 0, buffer.length)) &gt; 0) {
                     outputFileStream.write(buffer, 0, bytesRead);
                 }
             } finally {
                 if (outputFileStream != null) outputFileStream.close();
             }
         } finally {
             if (presVideoStream != null) presVideoStream.close();
         }
         // Memory consumption will remain low regardless of the size of the video or presentation,
     }
     // If necessary, you can apply the same steps for audio files.
 } catch(IOException e) {
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 The following examples shows how to add a hyperlink to a video in a PowerPoint Presentation.
 <pre>
 Presentation pres = new Presentation();
 try {
     IVideo video = pres.getVideos().addVideo(Files.readAllBytes(Paths.get("video.avi")));
     IVideoFrame videoFrame = pres.getSlides().get_Item(0).getShapes().addVideoFrame(10, 10, 100, 100, video);
     videoFrame.setHyperlinkClick(new Hyperlink("https://www.aspose.com/"));
     videoFrame.getHyperlinkClick().setTooltip("More than 70% Fortune 100 companies trust Aspose APIs");
     pres.save("pres-out.pptx", SaveFormat.Pptx);
 } catch(IOException e) {
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 The following examples shows how to create Video Frame with Video from Web Source in a PowerPoint Presentation.
 <pre>
 public static void run()
 {
     Presentation pres = new Presentation();
     try {
         addVideoFromYouTube(pres, "Tj75Arhq5ho");
         pres.save("AddVideoFrameFromWebSource_out.pptx", SaveFormat.Pptx);
     } catch(IOException e) {
     } finally {
         if (pres != null) pres.dispose();
     }
 }
 private static void addVideoFromYouTube(Presentation pres, String videoId) throws IOException
 {
     //add videoFrame
     IVideoFrame videoFrame = pres.getSlides().get_Item(0).getShapes().addVideoFrame(10, 10, 427, 240, "https://www.youtube.com/embed/" + videoId);
     videoFrame.setPlayMode(VideoPlayModePreset.Auto);

     //load thumbnail
     String thumbnailUri = "http://img.youtube.com/vi/" + videoId + "/hqdefault.jpg";
     URL url = new URL(thumbnailUri);
     URLConnection connection = url.openConnection();
     connection.setConnectTimeout(5000);
     connection.setReadTimeout(10000);
     InputStream input = connection.getInputStream();
     ByteArrayOutputStream output = new ByteArrayOutputStream();
     try
     {
         byte[] buffer = new byte[8192];
         for (int count; (count = input.read(buffer)) &gt; 0; )
         {
             output.write(buffer, 0, count);
         }
         videoFrame.getPictureFormat().getPicture().setImage(pres.getImages().addImage(output.toByteArray()));
     } finally {
         if (input != null) input.close();
         if (output != null) output.close();
     }
 }
 </pre>
 The following examples shows how to extract Video from slide of PowerPoint Presentation.
 <pre>
 // Instantiate a Presentation object that represents a presentation file
 Presentation presentation = new Presentation("Video.pptx");
 try {
     for (ISlide slide : presentation.getSlides())
     {
         for (IShape shape : presentation.getSlides().get_Item(0).getShapes())
         {
             if (shape instanceof VideoFrame)
             {
                 IVideoFrame vf = (IVideoFrame) shape;
                 String type = vf.getEmbeddedVideo().getContentType();
                 int ss = type.lastIndexOf('/');
                 type = type.substring(ss + 1);
                 byte[] buffer = vf.getEmbeddedVideo().getBinaryData();
                 FileOutputStream fop = new FileOutputStream("NewVideo_out." + type);
                 try
                 {
                     fop.write(buffer);
                     fop.flush();
                     fop.close();
                 }
                 finally
                 {
                     if (presentation != null) presentation.dispose();
                 }
             }
         }
     }
 } catch(IOException e) {
 } finally {
     if (presentation != null) presentation.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPresentation.html#getVideos--">getVideos</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPresentation.html" title="interface in com.aspose.slides">IPresentation</a></code></dd>
</dl>
</li>
</ul>
<a name="getSlideShowSettings--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSlideShowSettings</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/SlideShowSettings.html" title="class in com.aspose.slides">SlideShowSettings</a>&nbsp;getSlideShowSettings()</pre>
<div class="block"><p>
 Returns the slide show settings for the presentation.
 </p></div>
</li>
</ul>
<a name="getDigitalSignatures--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDigitalSignatures</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IDigitalSignatureCollection.html" title="interface in com.aspose.slides">IDigitalSignatureCollection</a>&nbsp;getDigitalSignatures()</pre>
<div class="block"><p>
 Returns the collection of signatures used to sign the presentation.
 Read-only <a href="../../../com/aspose/slides/IDigitalSignatureCollection.html" title="interface in com.aspose.slides"><code>IDigitalSignatureCollection</code></a>.
 </p><p><hr><blockquote><pre>
 <pre>
 Presentation pres = new Presentation("SomePresentationSigned.pptx");
 try
 {
     if (pres.getDigitalSignatures().size() &gt; 0)
     {
         boolean allSignaturesAreValid = true;
         System.out.println("Signatures used to sign the presentation: ");
         for (IDigitalSignature signature : pres.getDigitalSignatures())
         {
            System.out.println(signature.getCertificate().hashCode() + ", "
                  + signature.getSignTime().toString() + " -- " + (signature.isValid() ? "VALID" : "INVALID"));
            allSignaturesAreValid &amp;= signature.isValid();
         }
         if (allSignaturesAreValid)
            System.out.println("Presentation is genuine, all signatures are valid.");
         else
            System.out.println("Presentation has been modified since signing.");
     }
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPresentation.html#getDigitalSignatures--">getDigitalSignatures</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPresentation.html" title="interface in com.aspose.slides">IPresentation</a></code></dd>
</dl>
</li>
</ul>
<a name="getCustomData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCustomData</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/ICustomData.html" title="interface in com.aspose.slides">ICustomData</a>&nbsp;getCustomData()</pre>
<div class="block"><p>
 Returns the presentation's custom data.
 Read-only <a href="../../../com/aspose/slides/ICustomData.html" title="interface in com.aspose.slides"><code>ICustomData</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPresentation.html#getCustomData--">getCustomData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPresentation.html" title="interface in com.aspose.slides">IPresentation</a></code></dd>
</dl>
</li>
</ul>
<a name="getAllCustomXmlParts--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAllCustomXmlParts</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/ICustomXmlPart.html" title="interface in com.aspose.slides">ICustomXmlPart</a>[]&nbsp;getAllCustomXmlParts()</pre>
<div class="block"><p>
 Returns all custom data parts in the presentaion.
 Read-only <code>ICustomXmlPart[]</code>.
 </p><p><hr><blockquote><pre>
 The following examples show how to clear all custom xml parts from PowerPoint Presentation.
 <pre>
 Presentation pres = new Presentation("PresentationWithCustomXml.pptx");
 try {
     // Iterate all custom XML Parts
     for (ICustomXmlPart item : pres.getAllCustomXmlParts())
     {
         item.remove();
     }
     pres.save("out.pptx", SaveFormat.Pptx);
 }
 finally
 {
     if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPresentation.html#getAllCustomXmlParts--">getAllCustomXmlParts</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPresentation.html" title="interface in com.aspose.slides">IPresentation</a></code></dd>
</dl>
</li>
</ul>
<a name="getVbaProject--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVbaProject</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IVbaProject.html" title="interface in com.aspose.slides">IVbaProject</a>&nbsp;getVbaProject()</pre>
<div class="block"><p>
 Gets or sets VBA project with presentation macros.
 Read/write <a href="../../../com/aspose/slides/IVbaProject.html" title="interface in com.aspose.slides"><code>IVbaProject</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPresentation.html#getVbaProject--">getVbaProject</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPresentation.html" title="interface in com.aspose.slides">IPresentation</a></code></dd>
</dl>
</li>
</ul>
<a name="setVbaProject-com.aspose.slides.IVbaProject-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setVbaProject</h4>
<pre>public final&nbsp;void&nbsp;setVbaProject(<a href="../../../com/aspose/slides/IVbaProject.html" title="interface in com.aspose.slides">IVbaProject</a>&nbsp;value)</pre>
<div class="block"><p>
 Gets or sets VBA project with presentation macros.
 Read/write <a href="../../../com/aspose/slides/IVbaProject.html" title="interface in com.aspose.slides"><code>IVbaProject</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPresentation.html#setVbaProject-com.aspose.slides.IVbaProject-">setVbaProject</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPresentation.html" title="interface in com.aspose.slides">IPresentation</a></code></dd>
</dl>
</li>
</ul>
<a name="getHyperlinkQueries--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHyperlinkQueries</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IHyperlinkQueries.html" title="interface in com.aspose.slides">IHyperlinkQueries</a>&nbsp;getHyperlinkQueries()</pre>
<div class="block"><p>
 Provides easy access to all hyperlinks contained in all presentation slides (not in master, layout, notes slides).
 Read-only <a href="../../../com/aspose/slides/IHyperlinkQueries.html" title="interface in com.aspose.slides"><code>IHyperlinkQueries</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPresentation.html#getHyperlinkQueries--">getHyperlinkQueries</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPresentation.html" title="interface in com.aspose.slides">IPresentation</a></code></dd>
</dl>
</li>
</ul>
<a name="getViewProperties--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getViewProperties</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IViewProperties.html" title="interface in com.aspose.slides">IViewProperties</a>&nbsp;getViewProperties()</pre>
<div class="block"><p>
 Gets presentation wide view properties.
 Read-only <a href="../../../com/aspose/slides/IViewProperties.html" title="interface in com.aspose.slides"><code>IViewProperties</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPresentation.html#getViewProperties--">getViewProperties</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPresentation.html" title="interface in com.aspose.slides">IPresentation</a></code></dd>
</dl>
</li>
</ul>
<a name="getFirstSlideNumber--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFirstSlideNumber</h4>
<pre>public final&nbsp;int&nbsp;getFirstSlideNumber()</pre>
<div class="block"><p>
 Represents the first slide number in the presentation
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPresentation.html#getFirstSlideNumber--">getFirstSlideNumber</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPresentation.html" title="interface in com.aspose.slides">IPresentation</a></code></dd>
</dl>
</li>
</ul>
<a name="setFirstSlideNumber-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFirstSlideNumber</h4>
<pre>public final&nbsp;void&nbsp;setFirstSlideNumber(int&nbsp;value)</pre>
<div class="block"><p>
 Represents the first slide number in the presentation
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPresentation.html#setFirstSlideNumber-int-">setFirstSlideNumber</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPresentation.html" title="interface in com.aspose.slides">IPresentation</a></code></dd>
</dl>
</li>
</ul>
<a name="getSlideById-long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSlideById</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IBaseSlide.html" title="interface in com.aspose.slides">IBaseSlide</a>&nbsp;getSlideById(long&nbsp;id)</pre>
<div class="block"><p>
 Returns a Slide, MasterSlide or LayoutSlide by Id.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPresentation.html#getSlideById-long-">getSlideById</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPresentation.html" title="interface in com.aspose.slides">IPresentation</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>id</code> - Id of a slide.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>IBaseSlide object.</dd>
</dl>
</li>
</ul>
<a name="getSourceFormat--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSourceFormat</h4>
<pre>public final&nbsp;int&nbsp;getSourceFormat()</pre>
<div class="block"><p>
 Returns information about from which format presentation was loaded.
 Read-only <a href="../../../com/aspose/slides/SourceFormat.html" title="class in com.aspose.slides"><code>SourceFormat</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPresentation.html#getSourceFormat--">getSourceFormat</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPresentation.html" title="interface in com.aspose.slides">IPresentation</a></code></dd>
</dl>
</li>
</ul>
<a name="getMasterTheme--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMasterTheme</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IMasterTheme.html" title="interface in com.aspose.slides">IMasterTheme</a>&nbsp;getMasterTheme()</pre>
<div class="block"><p>
 Returns master theme.
 Read-only <a href="../../../com/aspose/slides/IMasterTheme.html" title="interface in com.aspose.slides"><code>IMasterTheme</code></a>.
 </p><p><hr><blockquote><pre>
 The following examples shows how to change a theme effect by altering parts of elements of PowerPoint Presentation.
 <pre>
 //Instantiate a presentation object that represents a presentation file
 Presentation pres = new Presentation("Subtle_Moderate_Intense.pptx");
 try {
     pres.getMasterTheme().getFormatScheme().getLineStyles().get_Item(0).getFillFormat().getSolidFillColor().setColor(Color.RED);
     ((FillFormat)pres.getMasterTheme().getFormatScheme().getLineStyles().get_Item(2)).setFillType(FillType.Solid);
     ((FillFormat)pres.getMasterTheme().getFormatScheme().getLineStyles().get_Item(2)).getSolidFillColor().setColor(Color.GREEN);
     ((EffectStyle)pres.getMasterTheme().getFormatScheme().getLineStyles().get_Item(2)).getEffectFormat().getOuterShadowEffect().setDistance(10f);
     pres.save("Design_04_Subtle_Moderate_Intense-out.pptx", SaveFormat.Pptx);
 }
 finally
 {
     if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPresentation.html#getMasterTheme--">getMasterTheme</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPresentation.html" title="interface in com.aspose.slides">IPresentation</a></code></dd>
</dl>
</li>
</ul>
<a name="save-java.lang.String-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>save</h4>
<pre>public final&nbsp;void&nbsp;save(java.lang.String&nbsp;fname,
                       int&nbsp;format)</pre>
<div class="block"><p>
 Saves all slides of a presentation to a file with the specified format.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPresentation.html#save-java.lang.String-int-">save</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPresentation.html" title="interface in com.aspose.slides">IPresentation</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>fname</code> - Path to the created file.</dd>
<dd><code>format</code> - Format of the exported data.</dd>
</dl>
</li>
</ul>
<a name="save-java.io.OutputStream-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>save</h4>
<pre>public final&nbsp;void&nbsp;save(java.io.OutputStream&nbsp;stream,
                       int&nbsp;format)</pre>
<div class="block"><p>
 Saves all slides of a presentation to a stream in the specified format.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPresentation.html#save-java.io.OutputStream-int-">save</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPresentation.html" title="interface in com.aspose.slides">IPresentation</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>stream</code> - Output stream.</dd>
<dd><code>format</code> - Format of the exported data.</dd>
</dl>
</li>
</ul>
<a name="save-java.lang.String-int-com.aspose.slides.ISaveOptions-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>save</h4>
<pre>public final&nbsp;void&nbsp;save(java.lang.String&nbsp;fname,
                       int&nbsp;format,
                       <a href="../../../com/aspose/slides/ISaveOptions.html" title="interface in com.aspose.slides">ISaveOptions</a>&nbsp;options)</pre>
<div class="block"><p>
 Saves all slides of a presentation to a file with the specified format and with additional options.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPresentation.html#save-java.lang.String-int-com.aspose.slides.ISaveOptions-">save</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPresentation.html" title="interface in com.aspose.slides">IPresentation</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>fname</code> - Path to the created file.</dd>
<dd><code>format</code> - Format of the exported data.</dd>
<dd><code>options</code> - Additional format options.</dd>
</dl>
</li>
</ul>
<a name="save-java.io.OutputStream-int-com.aspose.slides.ISaveOptions-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>save</h4>
<pre>public final&nbsp;void&nbsp;save(java.io.OutputStream&nbsp;stream,
                       int&nbsp;format,
                       <a href="../../../com/aspose/slides/ISaveOptions.html" title="interface in com.aspose.slides">ISaveOptions</a>&nbsp;options)</pre>
<div class="block"><p>
 Saves all slides of a presentation to a stream in the specified format and with additional options.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPresentation.html#save-java.io.OutputStream-int-com.aspose.slides.ISaveOptions-">save</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPresentation.html" title="interface in com.aspose.slides">IPresentation</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>stream</code> - Output stream.</dd>
<dd><code>format</code> - Format of the exported data.</dd>
<dd><code>options</code> - Additional format options.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>com.aspose.ms.System.NotSupportedException</code> - If you try to save encrypted file in 
 none Office 2007-2010 format</dd>
</dl>
</li>
</ul>
<a name="save-com.aspose.slides.IXamlOptions-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>save</h4>
<pre>public final&nbsp;void&nbsp;save(<a href="../../../com/aspose/slides/IXamlOptions.html" title="interface in com.aspose.slides">IXamlOptions</a>&nbsp;options)</pre>
<div class="block"><p>
 Saves all slides of a presentation to a set of files representing XAML markup.
 </p><p><hr><blockquote><pre>
 <pre>
 Presentation pres = new Presentation("pres.pptx");
 try {
     XamlOptions xamlOptions = new XamlOptions();
     xamlOptions.setExportHiddenSlides(true);

     pres.save(xamlOptions);
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPresentation.html#save-com.aspose.slides.IXamlOptions-">save</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPresentation.html" title="interface in com.aspose.slides">IPresentation</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>options</code> - The XAML format options.</dd>
</dl>
</li>
</ul>
<a name="getThumbnails-com.aspose.slides.IRenderingOptions-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getThumbnails</h4>
<pre>@Deprecated
public final&nbsp;java.awt.image.BufferedImage[]&nbsp;getThumbnails(<a href="../../../com/aspose/slides/IRenderingOptions.html" title="interface in com.aspose.slides">IRenderingOptions</a>&nbsp;options)</pre>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;<span class="deprecationComment">Use Presentation.GetThumbnails(IRenderingOptions) instead. The method will be removed after release of version 21.4.</span></div>
<div class="block"><p>
 Returns a Thumbnail BufferedImage objects for all slides of a presentation.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPresentation.html#getThumbnails-com.aspose.slides.IRenderingOptions-">getThumbnails</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPresentation.html" title="interface in com.aspose.slides">IPresentation</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>options</code> - Tiff options.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>BufferedImage objects.</dd>
</dl>
</li>
</ul>
<a name="getImages-com.aspose.slides.IRenderingOptions-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getImages</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IImage.html" title="interface in com.aspose.slides">IImage</a>[]&nbsp;getImages(<a href="../../../com/aspose/slides/IRenderingOptions.html" title="interface in com.aspose.slides">IRenderingOptions</a>&nbsp;options)</pre>
<div class="block"><p>
 Returns a Image objects for all slides of a presentation.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPresentation.html#getImages-com.aspose.slides.IRenderingOptions-">getImages</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPresentation.html" title="interface in com.aspose.slides">IPresentation</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>options</code> - Tiff options.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Image objects.</dd>
</dl>
</li>
</ul>
<a name="getThumbnails-com.aspose.slides.IRenderingOptions-int:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getThumbnails</h4>
<pre>@Deprecated
public final&nbsp;java.awt.image.BufferedImage[]&nbsp;getThumbnails(<a href="../../../com/aspose/slides/IRenderingOptions.html" title="interface in com.aspose.slides">IRenderingOptions</a>&nbsp;options,
                                                                       int[]&nbsp;slides)</pre>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;<span class="deprecationComment">Use GetImages(IRenderingOptions options, int[] slides) method instead. The method will be removed after release of version 24.7.</span></div>
<div class="block"><p>
 Returns a Thumbnail BufferedImage objects for specified slides of a presentation.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPresentation.html#getThumbnails-com.aspose.slides.IRenderingOptions-int:A-">getThumbnails</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPresentation.html" title="interface in com.aspose.slides">IPresentation</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>options</code> - Tiff options.</dd>
<dd><code>slides</code> - Array with slide positions, starting from 1.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>BufferedImage objects.</dd>
</dl>
</li>
</ul>
<a name="getImages-com.aspose.slides.IRenderingOptions-int:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getImages</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IImage.html" title="interface in com.aspose.slides">IImage</a>[]&nbsp;getImages(<a href="../../../com/aspose/slides/IRenderingOptions.html" title="interface in com.aspose.slides">IRenderingOptions</a>&nbsp;options,
                                int[]&nbsp;slides)</pre>
<div class="block"><p>
 Returns a Thumbnail Image objects for specified slides of a presentation.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPresentation.html#getImages-com.aspose.slides.IRenderingOptions-int:A-">getImages</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPresentation.html" title="interface in com.aspose.slides">IPresentation</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>options</code> - Tiff options.</dd>
<dd><code>slides</code> - Array with slide positions, starting from 1.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Image objects.</dd>
</dl>
</li>
</ul>
<a name="getThumbnails-com.aspose.slides.IRenderingOptions-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getThumbnails</h4>
<pre>@Deprecated
public final&nbsp;java.awt.image.BufferedImage[]&nbsp;getThumbnails(<a href="../../../com/aspose/slides/IRenderingOptions.html" title="interface in com.aspose.slides">IRenderingOptions</a>&nbsp;options,
                                                                       float&nbsp;scaleX,
                                                                       float&nbsp;scaleY)</pre>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;<span class="deprecationComment">Use GetImages(IRenderingOptions options, float scaleX, float scaleY) method instead. The method will be removed after release of version 24.7.</span></div>
<div class="block"><p>
 Returns a Thumbnail BufferedImage objects for all slides of a presentation with custom scaling.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPresentation.html#getThumbnails-com.aspose.slides.IRenderingOptions-float-float-">getThumbnails</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPresentation.html" title="interface in com.aspose.slides">IPresentation</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>options</code> - Tiff options.</dd>
<dd><code>scaleX</code> - The value by which to scale this Thumbnail in the x-axis direction.</dd>
<dd><code>scaleY</code> - The value by which to scale this Thumbnail in the y-axis direction.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>BufferedImage objects.</dd>
</dl>
</li>
</ul>
<a name="getImages-com.aspose.slides.IRenderingOptions-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getImages</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IImage.html" title="interface in com.aspose.slides">IImage</a>[]&nbsp;getImages(<a href="../../../com/aspose/slides/IRenderingOptions.html" title="interface in com.aspose.slides">IRenderingOptions</a>&nbsp;options,
                                float&nbsp;scaleX,
                                float&nbsp;scaleY)</pre>
<div class="block"><p>
 Returns a Thumbnail Image objects for all slides of a presentation with custom scaling.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPresentation.html#getImages-com.aspose.slides.IRenderingOptions-float-float-">getImages</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPresentation.html" title="interface in com.aspose.slides">IPresentation</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>options</code> - Tiff options.</dd>
<dd><code>scaleX</code> - The value by which to scale this Thumbnail in the x-axis direction.</dd>
<dd><code>scaleY</code> - The value by which to scale this Thumbnail in the y-axis direction.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Image objects.</dd>
</dl>
</li>
</ul>
<a name="getThumbnails-com.aspose.slides.IRenderingOptions-int:A-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getThumbnails</h4>
<pre>@Deprecated
public final&nbsp;java.awt.image.BufferedImage[]&nbsp;getThumbnails(<a href="../../../com/aspose/slides/IRenderingOptions.html" title="interface in com.aspose.slides">IRenderingOptions</a>&nbsp;options,
                                                                       int[]&nbsp;slides,
                                                                       float&nbsp;scaleX,
                                                                       float&nbsp;scaleY)</pre>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;<span class="deprecationComment">Use GetImages(IRenderingOptions options, int[] slides, float scaleX, float scaleY) method instead. The method will be removed after release of version 24.7.</span></div>
<div class="block"><p>
 Returns a Thumbnail BufferedImage objects for specified slides of a presentation with custom scaling.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPresentation.html#getThumbnails-com.aspose.slides.IRenderingOptions-int:A-float-float-">getThumbnails</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPresentation.html" title="interface in com.aspose.slides">IPresentation</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>options</code> - Tiff options.</dd>
<dd><code>slides</code> - Array with slide positions, starting from 1.</dd>
<dd><code>scaleX</code> - The value by which to scale this Thumbnail in the x-axis direction.</dd>
<dd><code>scaleY</code> - The value by which to scale this Thumbnail in the y-axis direction.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>BufferedImage objects.</dd>
</dl>
</li>
</ul>
<a name="getImages-com.aspose.slides.IRenderingOptions-int:A-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getImages</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IImage.html" title="interface in com.aspose.slides">IImage</a>[]&nbsp;getImages(<a href="../../../com/aspose/slides/IRenderingOptions.html" title="interface in com.aspose.slides">IRenderingOptions</a>&nbsp;options,
                                int[]&nbsp;slides,
                                float&nbsp;scaleX,
                                float&nbsp;scaleY)</pre>
<div class="block"><p>
 Returns a Thumbnail Image objects for specified slides of a presentation with custom scaling.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPresentation.html#getImages-com.aspose.slides.IRenderingOptions-int:A-float-float-">getImages</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPresentation.html" title="interface in com.aspose.slides">IPresentation</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>options</code> - Tiff options.</dd>
<dd><code>slides</code> - Array with slide positions, starting from 1.</dd>
<dd><code>scaleX</code> - The value by which to scale this Thumbnail in the x-axis direction.</dd>
<dd><code>scaleY</code> - The value by which to scale this Thumbnail in the y-axis direction.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Image objects.</dd>
</dl>
</li>
</ul>
<a name="getThumbnails-com.aspose.slides.IRenderingOptions-java.awt.Dimension-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getThumbnails</h4>
<pre>@Deprecated
public final&nbsp;java.awt.image.BufferedImage[]&nbsp;getThumbnails(<a href="../../../com/aspose/slides/IRenderingOptions.html" title="interface in com.aspose.slides">IRenderingOptions</a>&nbsp;options,
                                                                       java.awt.Dimension&nbsp;imageSize)</pre>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;<span class="deprecationComment">Use GetImages(IRenderingOptions options, Size imageSize) method instead. The method will be removed after release of version 24.7.</span></div>
<div class="block"><p>
 Returns a Thumbnail BufferedImage objects for all slides of a presentation with specified size.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPresentation.html#getThumbnails-com.aspose.slides.IRenderingOptions-java.awt.Dimension-">getThumbnails</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPresentation.html" title="interface in com.aspose.slides">IPresentation</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>options</code> - Tiff options.</dd>
<dd><code>imageSize</code> - Size of the image to create.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>BufferedImage objects.</dd>
</dl>
</li>
</ul>
<a name="getImages-com.aspose.slides.IRenderingOptions-java.awt.Dimension-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getImages</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IImage.html" title="interface in com.aspose.slides">IImage</a>[]&nbsp;getImages(<a href="../../../com/aspose/slides/IRenderingOptions.html" title="interface in com.aspose.slides">IRenderingOptions</a>&nbsp;options,
                                java.awt.Dimension&nbsp;imageSize)</pre>
<div class="block"><p>
 Returns a Thumbnail Image objects for all slides of a presentation with specified size.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPresentation.html#getImages-com.aspose.slides.IRenderingOptions-java.awt.Dimension-">getImages</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPresentation.html" title="interface in com.aspose.slides">IPresentation</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>options</code> - Tiff options.</dd>
<dd><code>imageSize</code> - Size of the image to create.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Image objects.</dd>
</dl>
</li>
</ul>
<a name="getThumbnails-com.aspose.slides.IRenderingOptions-int:A-java.awt.Dimension-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getThumbnails</h4>
<pre>@Deprecated
public final&nbsp;java.awt.image.BufferedImage[]&nbsp;getThumbnails(<a href="../../../com/aspose/slides/IRenderingOptions.html" title="interface in com.aspose.slides">IRenderingOptions</a>&nbsp;options,
                                                                       int[]&nbsp;slides,
                                                                       java.awt.Dimension&nbsp;imageSize)</pre>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;<span class="deprecationComment">Use GetImages(IRenderingOptions options, int[] slides, Size imageSize) method instead. The method will be removed after release of version 24.7.</span></div>
<div class="block"><p>
 Returns a Thumbnail BufferedImage objects for specified slides of a presentation with specified size.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPresentation.html#getThumbnails-com.aspose.slides.IRenderingOptions-int:A-java.awt.Dimension-">getThumbnails</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPresentation.html" title="interface in com.aspose.slides">IPresentation</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>options</code> - Tiff options.</dd>
<dd><code>slides</code> - Array with slide positions, starting from 1.</dd>
<dd><code>imageSize</code> - Size of the image to create.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>BufferedImage objects.</dd>
</dl>
</li>
</ul>
<a name="getImages-com.aspose.slides.IRenderingOptions-int:A-java.awt.Dimension-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getImages</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IImage.html" title="interface in com.aspose.slides">IImage</a>[]&nbsp;getImages(<a href="../../../com/aspose/slides/IRenderingOptions.html" title="interface in com.aspose.slides">IRenderingOptions</a>&nbsp;options,
                                int[]&nbsp;slides,
                                java.awt.Dimension&nbsp;imageSize)</pre>
<div class="block"><p>
 Returns a Thumbnail Image objects for specified slides of a presentation with specified size.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPresentation.html#getImages-com.aspose.slides.IRenderingOptions-int:A-java.awt.Dimension-">getImages</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPresentation.html" title="interface in com.aspose.slides">IPresentation</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>options</code> - Tiff options.</dd>
<dd><code>slides</code> - Array with slide positions, starting from 1.</dd>
<dd><code>imageSize</code> - Size of the image to create.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Image objects.</dd>
</dl>
</li>
</ul>
<a name="save-java.lang.String-int:A-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>save</h4>
<pre>public final&nbsp;void&nbsp;save(java.lang.String&nbsp;fname,
                       int[]&nbsp;slides,
                       int&nbsp;format)</pre>
<div class="block"><p>
 Saves specified slides of a presentation to a file with the specified format with page number keeping.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPresentation.html#save-java.lang.String-int:A-int-">save</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPresentation.html" title="interface in com.aspose.slides">IPresentation</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>fname</code> - Path to the created file.</dd>
<dd><code>slides</code> - Array with slide positions, starting from 1.</dd>
<dd><code>format</code> - Format of the exported data.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>com.aspose.ms.System.ArgumentNullException</code> - When stream or slides parameter is null.</dd>
<dd><code>com.aspose.ms.System.ArgumentOutOfRangeException</code> - When slides parameter contains wrong page numbers.</dd>
<dd><code>com.aspose.ms.System.InvalidOperationException</code> - When an unsupported SaveFormat is used, e.g. PPTX, PPTM, PPSX, PPSM, POTX, POTM, PPT, ODP.</dd>
</dl>
</li>
</ul>
<a name="save-java.lang.String-int:A-int-com.aspose.slides.ISaveOptions-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>save</h4>
<pre>public final&nbsp;void&nbsp;save(java.lang.String&nbsp;fname,
                       int[]&nbsp;slides,
                       int&nbsp;format,
                       <a href="../../../com/aspose/slides/ISaveOptions.html" title="interface in com.aspose.slides">ISaveOptions</a>&nbsp;options)</pre>
<div class="block"><p>
 Saves specified slides of a presentation to a file with the specified format with page number keeping.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPresentation.html#save-java.lang.String-int:A-int-com.aspose.slides.ISaveOptions-">save</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPresentation.html" title="interface in com.aspose.slides">IPresentation</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>fname</code> - Path to the created file.</dd>
<dd><code>slides</code> - Array with slide positions, starting from 1.</dd>
<dd><code>format</code> - Format of the exported data.</dd>
<dd><code>options</code> - Additional format options.</dd>
</dl>
</li>
</ul>
<a name="save-java.io.OutputStream-int:A-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>save</h4>
<pre>public final&nbsp;void&nbsp;save(java.io.OutputStream&nbsp;stream,
                       int[]&nbsp;slides,
                       int&nbsp;format)</pre>
<div class="block"><p>
 Saves specified slides of a presentation to a stream in the specified format with page number keeping.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPresentation.html#save-java.io.OutputStream-int:A-int-">save</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPresentation.html" title="interface in com.aspose.slides">IPresentation</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>stream</code> - Output stream.</dd>
<dd><code>slides</code> - Array with slide positions, starting from 1.</dd>
<dd><code>format</code> - Format of the exported data.</dd>
</dl>
</li>
</ul>
<a name="save-java.io.OutputStream-int:A-int-com.aspose.slides.ISaveOptions-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>save</h4>
<pre>public final&nbsp;void&nbsp;save(java.io.OutputStream&nbsp;stream,
                       int[]&nbsp;slides,
                       int&nbsp;format,
                       <a href="../../../com/aspose/slides/ISaveOptions.html" title="interface in com.aspose.slides">ISaveOptions</a>&nbsp;options)</pre>
<div class="block"><p>
 Saves specified slides of a presentation to a stream in the specified format with page number keeping.
 </p><p><hr><blockquote><pre>
 The following example shows how to convert PowerPoint to PNG.
 <pre>
 Presentation pres = new Presentation("pres.pptx");
 try {
     for (int index = 0; index &lt; pres.getSlides().size(); index++) {
         ISlide slide = pres.getSlides().get_Item(index);
         ImageIO.write(slide.getThumbnail(), "PNG", new java.io.File("slide_" + index + ".png"));
     }
 } catch(IOException e) {
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 The following example shows how to convert PowerPoint to PNG with custom dimensions.
 <pre>
 Presentation pres = new Presentation("pres.pptx");
 try {
     float scaleX = 2f;
     float scaleY = 2f;
     for (int index = 0; index &lt; pres.getSlides().size(); index++) {
         ISlide slide = pres.getSlides().get_Item(index);
         ImageIO.write(slide.getThumbnail(scaleX, scaleY), "PNG", new java.io.File("slide_" + index + ".png"));
     }
 } catch(IOException e) {
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 The following example shows how to convert PowerPoint to PNG with custom size.
 <pre>
 Presentation pres = new Presentation("pres.pptx");
 try {
     Dimension size = new Dimension(960, 720);
     for (int index = 0; index &lt; pres.getSlides().size(); index++) {
         ISlide slide = pres.getSlides().get_Item(index);
         ImageIO.write(slide.getThumbnail(size), "PNG", new java.io.File("slide_" + index + ".png"));
     }
 } catch(IOException e) {
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPresentation.html#save-java.io.OutputStream-int:A-int-com.aspose.slides.ISaveOptions-">save</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPresentation.html" title="interface in com.aspose.slides">IPresentation</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>stream</code> - Output stream.</dd>
<dd><code>slides</code> - Array with slide positions, starting from 1.</dd>
<dd><code>format</code> - Format of the exported data.</dd>
<dd><code>options</code> - Additional format options.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>com.aspose.ms.System.ArgumentNullException</code> - When stream or slides parameter is null.</dd>
<dd><code>com.aspose.ms.System.ArgumentOutOfRangeException</code> - When slides parameter contains wrong page numbers.</dd>
<dd><code>com.aspose.ms.System.InvalidOperationException</code> - When an unsupported SaveFormat is used, e.g. PPTX, PPTM, PPSX, PPSM, POTX, POTM, PPT, ODP.</dd>
</dl>
</li>
</ul>
<a name="joinPortionsWithSameFormatting--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>joinPortionsWithSameFormatting</h4>
<pre>public final&nbsp;void&nbsp;joinPortionsWithSameFormatting()</pre>
<div class="block"><p>
 Joins runs with same formatting in all paragraphs in all acceptable shapes in all slides.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPresentation.html#joinPortionsWithSameFormatting--">joinPortionsWithSameFormatting</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPresentation.html" title="interface in com.aspose.slides">IPresentation</a></code></dd>
</dl>
</li>
</ul>
<a name="dispose--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>dispose</h4>
<pre>public final&nbsp;void&nbsp;dispose()</pre>
<div class="block"><p>
 Releases all resources used by this Presentation object.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>dispose</code>&nbsp;in interface&nbsp;<code>com.aspose.ms.System.IDisposable</code></dd>
</dl>
</li>
</ul>
<a name="getPresentation--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPresentation</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IPresentation.html" title="interface in com.aspose.slides">IPresentation</a>&nbsp;getPresentation()</pre>
<div class="block"><p>
 Returns the parent presentation of a text.
 Read-only <a href="../../../com/aspose/slides/IPresentation.html" title="interface in com.aspose.slides"><code>IPresentation</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPresentationComponent.html#getPresentation--">getPresentation</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPresentationComponent.html" title="interface in com.aspose.slides">IPresentationComponent</a></code></dd>
</dl>
</li>
</ul>
<a name="highlightText-java.lang.String-java.awt.Color-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>highlightText</h4>
<pre>public final&nbsp;void&nbsp;highlightText(java.lang.String&nbsp;text,
                                java.awt.Color&nbsp;highlightColor)</pre>
<div class="block"><p>
 Highlights all matches of the sample text with the specified color.
 </p><p><hr><blockquote><pre>
 The following code sample shows how to highlight text in a PowerPoint presentation.
 <pre>
 Presentation presentation = new Presentation("SomePresentation.pptx");
 try {
     // highlighting all separate 'the' occurrences
     presentation.highlightText("the", Color.MAGENTA);
     presentation.save("SomePresentation-out2.pptx", SaveFormat.Pptx);
 } finally {
     if (presentation != null) presentation.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPresentation.html#highlightText-java.lang.String-java.awt.Color-">highlightText</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPresentation.html" title="interface in com.aspose.slides">IPresentation</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>text</code> - The text to highlight.</dd>
<dd><code>highlightColor</code> - The color to highlight the text.</dd>
</dl>
</li>
</ul>
<a name="highlightText-java.lang.String-java.awt.Color-com.aspose.slides.ITextSearchOptions-com.aspose.slides.IFindResultCallback-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>highlightText</h4>
<pre>public final&nbsp;void&nbsp;highlightText(java.lang.String&nbsp;text,
                                java.awt.Color&nbsp;highlightColor,
                                <a href="../../../com/aspose/slides/ITextSearchOptions.html" title="interface in com.aspose.slides">ITextSearchOptions</a>&nbsp;options,
                                <a href="../../../com/aspose/slides/IFindResultCallback.html" title="interface in com.aspose.slides">IFindResultCallback</a>&nbsp;callback)</pre>
<div class="block"><p>
 Highlights all matches of the sample text with the specified color.
 </p><p><hr><blockquote><pre>
 The following code sample shows how to highlight text in a PowerPoint presentation.
 <pre>
 Presentation presentation = new Presentation("SomePresentation.pptx");
 try {
     TextSearchOptions textSearchOptions = new TextSearchOptions();
     textSearchOptions.setWholeWordsOnly(true);
     // highlighting all separate 'the' occurrences
     presentation.highlightText("the", Color.MAGENTA, textSearchOptions, null);
     presentation.save("SomePresentation-out2.pptx", SaveFormat.Pptx);
 } finally {
     if (presentation != null) presentation.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPresentation.html#highlightText-java.lang.String-java.awt.Color-com.aspose.slides.ITextSearchOptions-com.aspose.slides.IFindResultCallback-">highlightText</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPresentation.html" title="interface in com.aspose.slides">IPresentation</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>text</code> - The text to highlight.</dd>
<dd><code>highlightColor</code> - The color to highlight the text.</dd>
<dd><code>options</code> - Text search options <a href="../../../com/aspose/slides/ITextSearchOptions.html" title="interface in com.aspose.slides"><code>ITextSearchOptions</code></a>.</dd>
<dd><code>callback</code> - The callback object for receiving search results <a href="../../../com/aspose/slides/IFindResultCallback.html" title="interface in com.aspose.slides"><code>IFindResultCallback</code></a>.</dd>
</dl>
</li>
</ul>
<a name="highlightRegex-java.util.regex.Pattern-java.awt.Color-com.aspose.slides.IFindResultCallback-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>highlightRegex</h4>
<pre>public final&nbsp;void&nbsp;highlightRegex(java.util.regex.Pattern&nbsp;regex,
                                 java.awt.Color&nbsp;highlightColor,
                                 <a href="../../../com/aspose/slides/IFindResultCallback.html" title="interface in com.aspose.slides">IFindResultCallback</a>&nbsp;callback)</pre>
<div class="block"><p>
 Highlights all matches of the regular expression with the specified color.
 </p><p><hr><blockquote><pre>
 The following code sample shows how to highlight text in a PowerPoint Presentation using a regular expression.
 <pre>
 Presentation presentation = new Presentation("SomePresentation.pptx");
 try {
     Pattern regex = Pattern.compile("\\b[^\\s]{10,}\\b");
     // highlighting all words with 10 symbols or longer
     presentation.highlightRegex(regex, Color.BLUE, null);
     presentation.save("SomePresentation-out.pptx", SaveFormat.Pptx);
 } finally {
     if (presentation != null) presentation.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPresentation.html#highlightRegex-java.util.regex.Pattern-java.awt.Color-com.aspose.slides.IFindResultCallback-">highlightRegex</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPresentation.html" title="interface in com.aspose.slides">IPresentation</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>regex</code> - The regular expression <code>Pattern</code> to get strings to highlight.</dd>
<dd><code>highlightColor</code> - The color to highlight the text.</dd>
<dd><code>callback</code> - The callback object for receiving search results <a href="../../../com/aspose/slides/IFindResultCallback.html" title="interface in com.aspose.slides"><code>IFindResultCallback</code></a>.</dd>
</dl>
</li>
</ul>
<a name="replaceText-java.lang.String-java.lang.String-com.aspose.slides.ITextSearchOptions-com.aspose.slides.IFindResultCallback-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>replaceText</h4>
<pre>public final&nbsp;void&nbsp;replaceText(java.lang.String&nbsp;oldText,
                              java.lang.String&nbsp;newText,
                              <a href="../../../com/aspose/slides/ITextSearchOptions.html" title="interface in com.aspose.slides">ITextSearchOptions</a>&nbsp;options,
                              <a href="../../../com/aspose/slides/IFindResultCallback.html" title="interface in com.aspose.slides">IFindResultCallback</a>&nbsp;callback)</pre>
<div class="block"><p>
 Replaces all occurrences of the specified text with another specified text.
 </p><p><hr><blockquote><pre>
 The following sample code shows how to replace one specified string with another specified string.
 <pre>
 Presentation presentation = new Presentation("SomePresentation.pptx")
 try {
     TextSearchOptions textSearchOptions = new TextSearchOptions();
     textSearchOptions.setWholeWordsOnly(true);
     // Replace all separate 'the' occurrences with '***'
     presentation.replaceText("the", "***", textSearchOptions, null);
     presentation.save("SomePresentation-out2.pptx", SaveFormat.Pptx);
 } finally {
     if (presentation != null) presentation.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPresentation.html#replaceText-java.lang.String-java.lang.String-com.aspose.slides.ITextSearchOptions-com.aspose.slides.IFindResultCallback-">replaceText</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPresentation.html" title="interface in com.aspose.slides">IPresentation</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>oldText</code> - The string to be replaced.</dd>
<dd><code>newText</code> - The string to replace all occurrences of oldText.</dd>
<dd><code>options</code> - Text search options <a href="../../../com/aspose/slides/ITextSearchOptions.html" title="interface in com.aspose.slides"><code>ITextSearchOptions</code></a>.</dd>
<dd><code>callback</code> - The callback object for receiving search results <a href="../../../com/aspose/slides/IFindResultCallback.html" title="interface in com.aspose.slides"><code>IFindResultCallback</code></a>.</dd>
</dl>
</li>
</ul>
<a name="replaceRegex-java.util.regex.Pattern-java.lang.String-com.aspose.slides.IFindResultCallback-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>replaceRegex</h4>
<pre>public final&nbsp;void&nbsp;replaceRegex(java.util.regex.Pattern&nbsp;regex,
                               java.lang.String&nbsp;newText,
                               <a href="../../../com/aspose/slides/IFindResultCallback.html" title="interface in com.aspose.slides">IFindResultCallback</a>&nbsp;callback)</pre>
<div class="block"><p>
 Replaces all matches of the regular expression with the specified string.
 </p><p><hr><blockquote><pre>
 The following code sample shows how to replace text using regular expression with the specified string.
 Presentation presentation = new Presentation("SomePresentation.pptx");
 try {
     Pattern regex = Pattern.compile("\\b[^\\s]{10,}\\b");
     // Replace all words with 10 symbols or longer with '***'
     presentation.replaceRegex(regex, "***", null);
     presentation.save("SomePresentation-out.pptx", SaveFormat.Pptx);
 } finally {
     if (presentation != null) presentation.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPresentation.html#replaceRegex-java.util.regex.Pattern-java.lang.String-com.aspose.slides.IFindResultCallback-">replaceRegex</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPresentation.html" title="interface in com.aspose.slides">IPresentation</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>regex</code> - The regular expression <code>Pattern</code> to get strings to replace.</dd>
<dd><code>newText</code> - The string to replace all occurrences of the strings to be replaced.</dd>
<dd><code>callback</code> - The callback object for receiving search results <a href="../../../com/aspose/slides/IFindResultCallback.html" title="interface in com.aspose.slides"><code>IFindResultCallback</code></a>.</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/PptxUnsupportedFormatException.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/PresentationAnimationsGenerator.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/Presentation.html" target="_top">Frames</a></li>
<li><a href="Presentation.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><i>Copyright &copy; 2004-2025 Aspose Pty Ltd. All Rights Reserved.</i></small></p>
</body>
</html>
