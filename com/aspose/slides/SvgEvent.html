<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_421) on Fri Jul 11 05:32:44 CDT 2025 -->
<title>SvgEvent (Aspose.Slides for Java (25.7) API Documentation)</title>
<meta name="date" content="2025-07-11">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="SvgEvent (Aspose.Slides for Java (25.7) API Documentation)";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/SvgCoordinateUnit.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/SvgExternalFontsHandling.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/SvgEvent.html" target="_top">Frames</a></li>
<li><a href="SvgEvent.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.classes.inherited.from.class.com.aspose.ms.System.Enum">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#methods.inherited.from.class.com.aspose.ms.System.Enum">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li>Method</li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.aspose.slides</div>
<h2 title="Class SvgEvent" class="title">Class SvgEvent</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.aspose.ms.System.ValueType&lt;com.aspose.ms.System.Enum&gt;</li>
<li>
<ul class="inheritance">
<li>com.aspose.ms.System.Enum</li>
<li>
<ul class="inheritance">
<li>com.aspose.slides.SvgEvent</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public final class <span class="typeNameLabel">SvgEvent</span>
extends com.aspose.ms.System.Enum</pre>
<div class="block"><p>
 Represents options for SVG shape.
 </p></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.com.aspose.ms.System.Enum">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from class&nbsp;com.aspose.ms.System.Enum</h3>
<code>com.aspose.ms.System.Enum.AbstractEnum, com.aspose.ms.System.Enum.FlaggedEnum, com.aspose.ms.System.Enum.ObjectEnum, com.aspose.ms.System.Enum.SimpleEnum</code></li>
</ul>
</li>
</ul>
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SvgEvent.html#OnAbort">OnAbort</a></span></code>
<div class="block">
 Occurs when page loading is stopped before an element has been allowed to load completely.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SvgEvent.html#OnActivate">OnActivate</a></span></code>
<div class="block">
 Occurs when an element is activated, for instance, through a mouse click or a keypress.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SvgEvent.html#OnBegin">OnBegin</a></span></code>
<div class="block">
 Occurs when an animation element begins.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SvgEvent.html#OnClick">OnClick</a></span></code>
<div class="block">
 Occurs when the pointing device button is clicked over an element.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SvgEvent.html#OnEnd">OnEnd</a></span></code>
<div class="block">
 Occurs when an animation element ends.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SvgEvent.html#OnError">OnError</a></span></code>
<div class="block">
 Occurs when an element does not load properly or when an error occurs during script execution.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SvgEvent.html#OnFocusIn">OnFocusIn</a></span></code>
<div class="block">
 Occurs when an element receives focus, such as when a text becomes selected.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SvgEvent.html#OnFocusOut">OnFocusOut</a></span></code>
<div class="block">
 Occurs when an element loses focus, such as when a text becomes unselected.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SvgEvent.html#OnLoad">OnLoad</a></span></code>
<div class="block">
 Occurs when the user agent has fully parsed the element and its descendants and all referrenced resurces, required to render it.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SvgEvent.html#OnMouseDown">OnMouseDown</a></span></code>
<div class="block">
 Occurs when the pointing device button is pressed over an element.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SvgEvent.html#OnMouseMove">OnMouseMove</a></span></code>
<div class="block">
 Occurs when the pointing device is moved while it is over an element.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SvgEvent.html#OnMouseOut">OnMouseOut</a></span></code>
<div class="block">
 Occurs when the pointing device is moved away from an element.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SvgEvent.html#OnMouseOver">OnMouseOver</a></span></code>
<div class="block">
 Occurs when the pointing device is moved onto an element.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SvgEvent.html#OnMouseUp">OnMouseUp</a></span></code>
<div class="block">
 Occurs when the pointing device button is released over an element.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SvgEvent.html#OnRepeat">OnRepeat</a></span></code>
<div class="block">
 Occurs when an animation element repeats.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SvgEvent.html#OnResize">OnResize</a></span></code>
<div class="block">
 Occurs when a document view is being resized.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SvgEvent.html#OnScroll">OnScroll</a></span></code>
<div class="block">
 Occurs when a document view is being shifted along the X or Y or both axis.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SvgEvent.html#OnUnload">OnUnload</a></span></code>
<div class="block">
 Occurs when the DOM implementation removes a document from a window or frame.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SvgEvent.html#OnZoom">OnZoom</a></span></code>
<div class="block">
 Occurs when the zoom level of a document view is being changed.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.com.aspose.ms.System.Enum">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;com.aspose.ms.System.Enum</h3>
<code>EnumSeparatorCharArray</code></li>
</ul>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.aspose.ms.System.Enum">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;com.aspose.ms.System.Enum</h3>
<code>Clone, CloneTo, format, format, get_Caption, get_Value, getName, getName, getNames, getNames, getUnderlyingType, getUnderlyingType, getValue, getValues, isDefined, isDefined, isDefined, isDefined, parse, parse, parse, parse, register, toObject, toString</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="OnFocusIn">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OnFocusIn</h4>
<pre>public static final&nbsp;int OnFocusIn</pre>
<div class="block"><p>
 Occurs when an element receives focus, such as when a text becomes selected.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SvgEvent.OnFocusIn">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="OnFocusOut">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OnFocusOut</h4>
<pre>public static final&nbsp;int OnFocusOut</pre>
<div class="block"><p>
 Occurs when an element loses focus, such as when a text becomes unselected.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SvgEvent.OnFocusOut">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="OnActivate">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OnActivate</h4>
<pre>public static final&nbsp;int OnActivate</pre>
<div class="block"><p>
 Occurs when an element is activated, for instance, through a mouse click or a keypress. 
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SvgEvent.OnActivate">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="OnClick">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OnClick</h4>
<pre>public static final&nbsp;int OnClick</pre>
<div class="block"><p>
 Occurs when the pointing device button is clicked over an element.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SvgEvent.OnClick">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="OnMouseDown">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OnMouseDown</h4>
<pre>public static final&nbsp;int OnMouseDown</pre>
<div class="block"><p>
 Occurs when the pointing device button is pressed over an element.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SvgEvent.OnMouseDown">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="OnMouseUp">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OnMouseUp</h4>
<pre>public static final&nbsp;int OnMouseUp</pre>
<div class="block"><p>
 Occurs when the pointing device button is released over an element.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SvgEvent.OnMouseUp">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="OnMouseOver">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OnMouseOver</h4>
<pre>public static final&nbsp;int OnMouseOver</pre>
<div class="block"><p>
 Occurs when the pointing device is moved onto an element.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SvgEvent.OnMouseOver">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="OnMouseMove">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OnMouseMove</h4>
<pre>public static final&nbsp;int OnMouseMove</pre>
<div class="block"><p>
 Occurs when the pointing device is moved while it is over an element.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SvgEvent.OnMouseMove">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="OnMouseOut">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OnMouseOut</h4>
<pre>public static final&nbsp;int OnMouseOut</pre>
<div class="block"><p>
 Occurs when the pointing device is moved away from an element.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SvgEvent.OnMouseOut">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="OnLoad">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OnLoad</h4>
<pre>public static final&nbsp;int OnLoad</pre>
<div class="block"><p>
 Occurs when the user agent has fully parsed the element and its descendants and all referrenced resurces, required to render it.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SvgEvent.OnLoad">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="OnUnload">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OnUnload</h4>
<pre>public static final&nbsp;int OnUnload</pre>
<div class="block"><p>
 Occurs when the DOM implementation removes a document from a window or frame. Only applicable to outermost svg elements.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SvgEvent.OnUnload">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="OnAbort">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OnAbort</h4>
<pre>public static final&nbsp;int OnAbort</pre>
<div class="block"><p>
 Occurs when page loading is stopped before an element has been allowed to load completely.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SvgEvent.OnAbort">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="OnError">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OnError</h4>
<pre>public static final&nbsp;int OnError</pre>
<div class="block"><p>
 Occurs when an element does not load properly or when an error occurs during script execution.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SvgEvent.OnError">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="OnResize">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OnResize</h4>
<pre>public static final&nbsp;int OnResize</pre>
<div class="block"><p>
 Occurs when a document view is being resized. Only applicable to outermost svg elements.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SvgEvent.OnResize">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="OnScroll">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OnScroll</h4>
<pre>public static final&nbsp;int OnScroll</pre>
<div class="block"><p>
 Occurs when a document view is being shifted along the X or Y or both axis. Only applicable to outermost svg elements.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SvgEvent.OnScroll">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="OnZoom">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OnZoom</h4>
<pre>public static final&nbsp;int OnZoom</pre>
<div class="block"><p>
 Occurs when the zoom level of a document view is being changed. Only applicable to outermost svg elements.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SvgEvent.OnZoom">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="OnBegin">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OnBegin</h4>
<pre>public static final&nbsp;int OnBegin</pre>
<div class="block"><p>
 Occurs when an animation element begins.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SvgEvent.OnBegin">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="OnEnd">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OnEnd</h4>
<pre>public static final&nbsp;int OnEnd</pre>
<div class="block"><p>
 Occurs when an animation element ends.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SvgEvent.OnEnd">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="OnRepeat">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>OnRepeat</h4>
<pre>public static final&nbsp;int OnRepeat</pre>
<div class="block"><p>
 Occurs when an animation element repeats.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SvgEvent.OnRepeat">Constant Field Values</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/SvgCoordinateUnit.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/SvgExternalFontsHandling.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/SvgEvent.html" target="_top">Frames</a></li>
<li><a href="SvgEvent.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.classes.inherited.from.class.com.aspose.ms.System.Enum">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#methods.inherited.from.class.com.aspose.ms.System.Enum">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li>Method</li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><i>Copyright &copy; 2004-2025 Aspose Pty Ltd. All Rights Reserved.</i></small></p>
</body>
</html>
