<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_421) on Fri Jul 11 05:32:44 CDT 2025 -->
<title>SlideCollection (Aspose.Slides for Java (25.7) API Documentation)</title>
<meta name="date" content="2025-07-11">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="SlideCollection (Aspose.Slides for Java (25.7) API Documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10,"i26":10,"i27":10,"i28":10,"i29":10,"i30":10,"i31":10,"i32":10,"i33":10,"i34":10,"i35":10,"i36":10,"i37":10,"i38":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/Slide.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/SlideHeaderFooterManager.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/SlideCollection.html" target="_top">Frames</a></li>
<li><a href="SlideCollection.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.aspose.slides</div>
<h2 title="Class SlideCollection" class="title">Class SlideCollection</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../com/aspose/slides/DomObject.html" title="class in com.aspose.slides">com.aspose.slides.DomObject</a>&lt;<a href="../../../com/aspose/slides/Presentation.html" title="class in com.aspose.slides">Presentation</a>&gt;</li>
<li>
<ul class="inheritance">
<li>com.aspose.slides.SlideCollection</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd>com.aspose.ms.System.Collections.Generic.IGenericEnumerable&lt;<a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a>&gt;, com.aspose.ms.System.Collections.ICollection&lt;<a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a>&gt;, com.aspose.ms.System.Collections.IEnumerable&lt;<a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a>&gt;, <a href="../../../com/aspose/slides/IGenericCollection.html" title="interface in com.aspose.slides">IGenericCollection</a>&lt;<a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a>&gt;, <a href="../../../com/aspose/slides/ISlideCollection.html" title="interface in com.aspose.slides">ISlideCollection</a>, java.lang.Iterable&lt;<a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a>&gt;</dd>
</dl>
<hr>
<br>
<pre>public final class <span class="typeNameLabel">SlideCollection</span>
extends <a href="../../../com/aspose/slides/DomObject.html" title="class in com.aspose.slides">DomObject</a>&lt;<a href="../../../com/aspose/slides/Presentation.html" title="class in com.aspose.slides">Presentation</a>&gt;
implements <a href="../../../com/aspose/slides/ISlideCollection.html" title="interface in com.aspose.slides">ISlideCollection</a></pre>
<div class="block"><p>
 Represents a collection of a slides.
 </p></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideCollection.html#addClone-com.aspose.slides.ISlide-">addClone</a></span>(<a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a>&nbsp;sourceSlide)</code>
<div class="block">
 Adds a copy of a specified slide to the end of the collection.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideCollection.html#addClone-com.aspose.slides.ISlide-com.aspose.slides.ILayoutSlide-">addClone</a></span>(<a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a>&nbsp;sourceSlide,
        <a href="../../../com/aspose/slides/ILayoutSlide.html" title="interface in com.aspose.slides">ILayoutSlide</a>&nbsp;destLayout)</code>
<div class="block">
 Adds a copy of a specified slide to the end of the collection.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideCollection.html#addClone-com.aspose.slides.ISlide-com.aspose.slides.IMasterSlide-boolean-">addClone</a></span>(<a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a>&nbsp;sourceSlide,
        <a href="../../../com/aspose/slides/IMasterSlide.html" title="interface in com.aspose.slides">IMasterSlide</a>&nbsp;destMaster,
        boolean&nbsp;allowCloneMissingLayout)</code>
<div class="block">
 Adds a copy of a specified source slide to the end of the collection.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideCollection.html#addClone-com.aspose.slides.ISlide-com.aspose.slides.ISection-">addClone</a></span>(<a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a>&nbsp;sourceSlide,
        <a href="../../../com/aspose/slides/ISection.html" title="interface in com.aspose.slides">ISection</a>&nbsp;section)</code>
<div class="block">
 Adds a copy of a specified slide to the end of the specified section.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideCollection.html#addEmptySlide-com.aspose.slides.ILayoutSlide-">addEmptySlide</a></span>(<a href="../../../com/aspose/slides/ILayoutSlide.html" title="interface in com.aspose.slides">ILayoutSlide</a>&nbsp;layout)</code>
<div class="block">
 Adds a new empty slide to the end of the collection.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a>[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideCollection.html#addFromHtml-java.io.InputStream-">addFromHtml</a></span>(java.io.InputStream&nbsp;htmlStream)</code>
<div class="block">
 Creates slides from HTML text and adds them to the end of the collection.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a>[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideCollection.html#addFromHtml-java.io.InputStream-com.aspose.slides.IExternalResourceResolver-java.lang.String-">addFromHtml</a></span>(java.io.InputStream&nbsp;htmlStream,
           <a href="../../../com/aspose/slides/IExternalResourceResolver.html" title="interface in com.aspose.slides">IExternalResourceResolver</a>&nbsp;resolver,
           java.lang.String&nbsp;uri)</code>
<div class="block">
 Creates slides from HTML text and adds them to the end of the collection.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a>[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideCollection.html#addFromHtml-java.lang.String-">addFromHtml</a></span>(java.lang.String&nbsp;htmlText)</code>
<div class="block">
 Creates slides from HTML text and adds them to the end of the collection.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a>[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideCollection.html#addFromHtml-java.lang.String-com.aspose.slides.IExternalResourceResolver-java.lang.String-">addFromHtml</a></span>(java.lang.String&nbsp;htmlText,
           <a href="../../../com/aspose/slides/IExternalResourceResolver.html" title="interface in com.aspose.slides">IExternalResourceResolver</a>&nbsp;resolver,
           java.lang.String&nbsp;uri)</code>
<div class="block">
 Creates slides from HTML text and adds them to the end of the collection.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a>[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideCollection.html#addFromPdf-java.io.InputStream-">addFromPdf</a></span>(java.io.InputStream&nbsp;pdfStream)</code>
<div class="block">
 Creates slides from the PDF document and adds them to the end of the collection.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a>[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideCollection.html#addFromPdf-java.io.InputStream-com.aspose.slides.PdfImportOptions-">addFromPdf</a></span>(java.io.InputStream&nbsp;pdfStream,
          <a href="../../../com/aspose/slides/PdfImportOptions.html" title="class in com.aspose.slides">PdfImportOptions</a>&nbsp;pdfImportOptions)</code>
<div class="block">
 Creates slides from the PDF document and adds them to the end of the collection.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a>[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideCollection.html#addFromPdf-java.lang.String-">addFromPdf</a></span>(java.lang.String&nbsp;path)</code>
<div class="block">
 Creates slides from the PDF document and adds them to the end of the collection.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a>[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideCollection.html#addFromPdf-java.lang.String-com.aspose.slides.PdfImportOptions-">addFromPdf</a></span>(java.lang.String&nbsp;path,
          <a href="../../../com/aspose/slides/PdfImportOptions.html" title="class in com.aspose.slides">PdfImportOptions</a>&nbsp;pdfImportOptions)</code>
<div class="block">
 Creates slides from the PDF document and adds them to the end of the collection considering the pdf import options.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideCollection.html#copyTo-com.aspose.ms.System.Array-int-">copyTo</a></span>(com.aspose.ms.System.Array&nbsp;array,
      int&nbsp;index)</code>
<div class="block">
 Copies all elements from the collection to the specified array.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideCollection.html#get_Item-int-">get_Item</a></span>(int&nbsp;index)</code>
<div class="block">
 Gets the element at the specified index.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>java.lang.Object</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideCollection.html#getSyncRoot--">getSyncRoot</a></span>()</code>
<div class="block">
 Returns a synchronization root.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideCollection.html#indexOf-com.aspose.slides.ISlide-">indexOf</a></span>(<a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a>&nbsp;slide)</code>
<div class="block">
 Returns an index of the specified slide in the collection.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideCollection.html#insertClone-int-com.aspose.slides.ISlide-">insertClone</a></span>(int&nbsp;index,
           <a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a>&nbsp;sourceSlide)</code>
<div class="block">
 Inserts a copy of a specified slide to specified position of the collection.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideCollection.html#insertClone-int-com.aspose.slides.ISlide-com.aspose.slides.ILayoutSlide-">insertClone</a></span>(int&nbsp;index,
           <a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a>&nbsp;sourceSlide,
           <a href="../../../com/aspose/slides/ILayoutSlide.html" title="interface in com.aspose.slides">ILayoutSlide</a>&nbsp;destLayout)</code>
<div class="block">
 Inserts a copy of a specified slide to specified position of the collection.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideCollection.html#insertClone-int-com.aspose.slides.ISlide-com.aspose.slides.IMasterSlide-boolean-">insertClone</a></span>(int&nbsp;index,
           <a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a>&nbsp;sourceSlide,
           <a href="../../../com/aspose/slides/IMasterSlide.html" title="interface in com.aspose.slides">IMasterSlide</a>&nbsp;destMaster,
           boolean&nbsp;allowCloneMissingLayout)</code>
<div class="block">
 Inserts a copy of a specified source slide to specified position of the collection.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideCollection.html#insertEmptySlide-int-com.aspose.slides.ILayoutSlide-">insertEmptySlide</a></span>(int&nbsp;index,
                <a href="../../../com/aspose/slides/ILayoutSlide.html" title="interface in com.aspose.slides">ILayoutSlide</a>&nbsp;layout)</code>
<div class="block">
 Inserts a copy of a specified slide to specified position of the collection.</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a>[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideCollection.html#insertFromHtml-int-java.io.InputStream-">insertFromHtml</a></span>(int&nbsp;index,
              java.io.InputStream&nbsp;htmlStream)</code>
<div class="block">
 Creates slides from HTML text and inserts them to the collection at the specified position.</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a>[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideCollection.html#insertFromHtml-int-java.io.InputStream-boolean-">insertFromHtml</a></span>(int&nbsp;index,
              java.io.InputStream&nbsp;htmlStream,
              boolean&nbsp;useSlideWithIndexAsStart)</code>
<div class="block">
 Creates slides from HTML text and inserts them to the collection at the specified position.</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a>[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideCollection.html#insertFromHtml-int-java.io.InputStream-com.aspose.slides.IExternalResourceResolver-java.lang.String-">insertFromHtml</a></span>(int&nbsp;index,
              java.io.InputStream&nbsp;htmlStream,
              <a href="../../../com/aspose/slides/IExternalResourceResolver.html" title="interface in com.aspose.slides">IExternalResourceResolver</a>&nbsp;resolver,
              java.lang.String&nbsp;uri)</code>
<div class="block">
 Creates slides from HTML text and inserts them to the collection at the specified position.</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a>[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideCollection.html#insertFromHtml-int-java.io.InputStream-com.aspose.slides.IExternalResourceResolver-java.lang.String-boolean-">insertFromHtml</a></span>(int&nbsp;index,
              java.io.InputStream&nbsp;htmlStream,
              <a href="../../../com/aspose/slides/IExternalResourceResolver.html" title="interface in com.aspose.slides">IExternalResourceResolver</a>&nbsp;resolver,
              java.lang.String&nbsp;uri,
              boolean&nbsp;useSlideWithIndexAsStart)</code>
<div class="block">
 Creates slides from HTML text and inserts them to the collection at the specified position.</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a>[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideCollection.html#insertFromHtml-int-java.lang.String-">insertFromHtml</a></span>(int&nbsp;index,
              java.lang.String&nbsp;htmlText)</code>
<div class="block">
 Creates slides from HTML text and inserts them to the collection at the specified position.</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a>[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideCollection.html#insertFromHtml-int-java.lang.String-boolean-">insertFromHtml</a></span>(int&nbsp;index,
              java.lang.String&nbsp;htmlText,
              boolean&nbsp;useSlideWithIndexAsStart)</code>
<div class="block">
 Creates slides from HTML text and inserts them to the collection at the specified position.</div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a>[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideCollection.html#insertFromHtml-int-java.lang.String-com.aspose.slides.IExternalResourceResolver-java.lang.String-">insertFromHtml</a></span>(int&nbsp;index,
              java.lang.String&nbsp;htmlText,
              <a href="../../../com/aspose/slides/IExternalResourceResolver.html" title="interface in com.aspose.slides">IExternalResourceResolver</a>&nbsp;resolver,
              java.lang.String&nbsp;uri)</code>
<div class="block">
 Creates slides from HTML text and inserts them to the collection at the specified position.</div>
</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a>[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideCollection.html#insertFromHtml-int-java.lang.String-com.aspose.slides.IExternalResourceResolver-java.lang.String-boolean-">insertFromHtml</a></span>(int&nbsp;index,
              java.lang.String&nbsp;htmlText,
              <a href="../../../com/aspose/slides/IExternalResourceResolver.html" title="interface in com.aspose.slides">IExternalResourceResolver</a>&nbsp;resolver,
              java.lang.String&nbsp;uri,
              boolean&nbsp;useSlideWithIndexAsStart)</code>
<div class="block">
 Creates slides from HTML text and inserts them to the collection at the specified position.</div>
</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideCollection.html#isSynchronized--">isSynchronized</a></span>()</code>
<div class="block">
 Returns a value indicating whether access to the collection is synchronized (thread-safe).</div>
</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code>com.aspose.ms.System.Collections.Generic.IGenericEnumerator&lt;<a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideCollection.html#iterator--">iterator</a></span>()</code>
<div class="block">
 Returns an enumerator that iterates through the collection.</div>
</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code>com.aspose.ms.System.Collections.Generic.IGenericEnumerator&lt;<a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideCollection.html#iteratorJava--">iteratorJava</a></span>()</code>
<div class="block">
 Returns a java iterator for the entire collection.</div>
</td>
</tr>
<tr id="i32" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideCollection.html#remove-com.aspose.slides.ISlide-">remove</a></span>(<a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a>&nbsp;value)</code>
<div class="block">
 Removes the first occurrence of a specific object from the collection.</div>
</td>
</tr>
<tr id="i33" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideCollection.html#removeAt-int-">removeAt</a></span>(int&nbsp;index)</code>
<div class="block">
 Removes the element at the specified index of the collection.</div>
</td>
</tr>
<tr id="i34" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideCollection.html#reorder-int-com.aspose.slides.ISlide...-">reorder</a></span>(int&nbsp;index,
       <a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a>...&nbsp;slides)</code>
<div class="block">
 Moves slides from the collection to the specified position.</div>
</td>
</tr>
<tr id="i35" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideCollection.html#reorder-int-com.aspose.slides.ISlide-">reorder</a></span>(int&nbsp;index,
       <a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a>&nbsp;slide)</code>
<div class="block">
 Moves slide from the collection to the specified position.</div>
</td>
</tr>
<tr id="i36" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideCollection.html#size--">size</a></span>()</code>
<div class="block">
 Gets the number of elements actually contained in the collection.</div>
</td>
</tr>
<tr id="i37" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a>[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideCollection.html#toArray--">toArray</a></span>()</code>
<div class="block">
 Creates and returns an array with all slides in it.</div>
</td>
</tr>
<tr id="i38" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a>[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideCollection.html#toArray-int-int-">toArray</a></span>(int&nbsp;startIndex,
       int&nbsp;count)</code>
<div class="block">
 Creates and returns an array with all slides from the specified range in it.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.aspose.slides.DomObject">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;com.aspose.slides.<a href="../../../com/aspose/slides/DomObject.html" title="class in com.aspose.slides">DomObject</a></h3>
<code><a href="../../../com/aspose/slides/DomObject.html#getParent_Immediate--">getParent_Immediate</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Iterable">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;java.lang.Iterable</h3>
<code>forEach, spliterator</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="size--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>size</h4>
<pre>public final&nbsp;int&nbsp;size()</pre>
<div class="block"><p>
 Gets the number of elements actually contained in the collection.
 Read-only int.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>size</code>&nbsp;in interface&nbsp;<code>com.aspose.ms.System.Collections.ICollection&lt;<a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a>&gt;</code></dd>
</dl>
</li>
</ul>
<a name="get_Item-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_Item</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a>&nbsp;get_Item(int&nbsp;index)</pre>
<div class="block"><p>
 Gets the element at the specified index.
 Read-only <a href="../../../com/aspose/slides/Slide.html" title="class in com.aspose.slides"><code>Slide</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISlideCollection.html#get_Item-int-">get_Item</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISlideCollection.html" title="interface in com.aspose.slides">ISlideCollection</a></code></dd>
</dl>
</li>
</ul>
<a name="addClone-com.aspose.slides.ISlide-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addClone</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a>&nbsp;addClone(<a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a>&nbsp;sourceSlide)</pre>
<div class="block"><p>
 Adds a copy of a specified slide to the end of the collection.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISlideCollection.html#addClone-com.aspose.slides.ISlide-">addClone</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISlideCollection.html" title="interface in com.aspose.slides">ISlideCollection</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>sourceSlide</code> - Slide to clone.
 <p><hr>
 When cloning a slide between different presentations slide's master can be cloned too.
 Internal registry is used to track automatically cloned masters to prevent creation of 
 multiple clones of the same master slide.
 Manual cloning of master slides will be neither prevented nor registered.
 If you need more control over cloning process use
 <a href="../../../com/aspose/slides/SlideCollection.html#addClone-com.aspose.slides.ISlide-com.aspose.slides.ILayoutSlide-"><code>addClone(ISlide,ILayoutSlide)</code></a> or
 <a href="../../../com/aspose/slides/SlideCollection.html#addClone-com.aspose.slides.ISlide-com.aspose.slides.IMasterSlide-boolean-"><code>addClone(ISlide,IMasterSlide,boolean)</code></a> for cloning slides,
 <a href="../../../com/aspose/slides/IGlobalLayoutSlideCollection.html#addClone-com.aspose.slides.ILayoutSlide-"><code>IGlobalLayoutSlideCollection.addClone(ILayoutSlide)</code></a> or
 <a href="../../../com/aspose/slides/IGlobalLayoutSlideCollection.html#addClone-com.aspose.slides.ILayoutSlide-com.aspose.slides.IMasterSlide-"><code>IGlobalLayoutSlideCollection.addClone(ILayoutSlide,IMasterSlide)</code></a> for cloning layouts and
 <a href="../../../com/aspose/slides/IMasterSlideCollection.html#addClone-com.aspose.slides.IMasterSlide-"><code>IMasterSlideCollection.addClone(IMasterSlide)</code></a> for cloning masters.
 </hr></p></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>New slide.</dd>
</dl>
</li>
</ul>
<a name="addClone-com.aspose.slides.ISlide-com.aspose.slides.ISection-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addClone</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a>&nbsp;addClone(<a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a>&nbsp;sourceSlide,
                             <a href="../../../com/aspose/slides/ISection.html" title="interface in com.aspose.slides">ISection</a>&nbsp;section)</pre>
<div class="block"><p>
 Adds a copy of a specified slide to the end of the specified section.
 </p><p><hr><blockquote><pre>
 <pre>
 IPresentation presentation = new Presentation();
 try
 {
     presentation.getSlides().get_Item(0).getShapes().addAutoShape(ShapeType.Rectangle, 200, 50, 300, 100);
     presentation.getSections().addSection("Section 1", presentation.getSlides().get_Item(0));
     
     ISection section2 = presentation.getSections().appendEmptySection("Section 2");
     presentation.getSlides().addClone(presentation.getSlides().get_Item(0), section2);
     
     // Now the second section contains a copy of the first slide.
 } finally {
     if (presentation != null) presentation.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISlideCollection.html#addClone-com.aspose.slides.ISlide-com.aspose.slides.ISection-">addClone</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISlideCollection.html" title="interface in com.aspose.slides">ISlideCollection</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>sourceSlide</code> - Slide to clone.</dd>
<dd><code>section</code> - Section for a new slide.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>New slide.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>com.aspose.ms.System.ArgumentNullException</code> - When sourceSlide or section parameter is null.</dd>
<dd><code><a href="../../../com/aspose/slides/PptxEditException.html" title="class in com.aspose.slides">PptxEditException</a></code> - When section parameter contains wrong or invalid value.</dd>
</dl>
</li>
</ul>
<a name="insertClone-int-com.aspose.slides.ISlide-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>insertClone</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a>&nbsp;insertClone(int&nbsp;index,
                                <a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a>&nbsp;sourceSlide)</pre>
<div class="block"><p>
 Inserts a copy of a specified slide to specified position of the collection.
 </p><p><hr><blockquote><pre>
 The following example shows how to clone at another position within Presentation.
 <pre>
 // Instantiate Presentation class that represents a presentation file
 Presentation pres = new Presentation("CloneWithInSamePresentation.pptx");
 try {
     // Clone the desired slide to the end of the collection of slides in the same presentation
     ISlideCollection slds = pres.getSlides();
     // Clone the desired slide to the specified index in the same presentation
     slds.insertClone(2, pres.getSlides().get_Item(1));
     // Write the modified presentation to disk
     pres.save("Aspose_CloneWithInSamePresentation_out.pptx", SaveFormat.Pptx);
 }
 finally
 {
     if (pres != null) pres.dispose();
 }
 </pre>
 The following example shows how to clone at another position within Presentation.
 <pre>
 // Instantiate Presentation class to load the source presentation file
 Presentation srcPres = new Presentation("CloneAtEndOfAnother.pptx");
 try {
     // Instantiate Presentation class for destination PPTX (where slide is to be cloned)
     Presentation destPres = new Presentation();
     try {
         ISlideCollection slds = destPres.getSlides();
         slds.insertClone(2, srcPres.getSlides().get_Item(0));
         // Write the destination presentation to disk
         destPres.save("Aspose2_out.pptx", SaveFormat.Pptx);
     } finally {
         if (destPres != null) destPres.dispose();
     }
 } finally {
     if (srcPres != null) srcPres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISlideCollection.html#insertClone-int-com.aspose.slides.ISlide-">insertClone</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISlideCollection.html" title="interface in com.aspose.slides">ISlideCollection</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - Index of new slide.</dd>
<dd><code>sourceSlide</code> - Slide to clone.
 <p><hr>
 When cloning a slide between different presentations slide's master can be cloned too.
 Internal registry is used to track automatically cloned masters to prevent creation of multiple clones of the same master slide.
 Manual cloning of master slides will be neither prevented nor registered.
 If you need more control over cloning process use
 <a href="../../../com/aspose/slides/SlideCollection.html#insertClone-int-com.aspose.slides.ISlide-com.aspose.slides.ILayoutSlide-"><code>insertClone(int,ISlide,ILayoutSlide)</code></a> or
 <a href="../../../com/aspose/slides/SlideCollection.html#insertClone-int-com.aspose.slides.ISlide-com.aspose.slides.IMasterSlide-boolean-"><code>insertClone(int,ISlide,IMasterSlide,boolean)</code></a> for cloning slides and
 <a href="../../../com/aspose/slides/IMasterSlideCollection.html#addClone-com.aspose.slides.IMasterSlide-"><code>IMasterSlideCollection.addClone(IMasterSlide)</code></a> for cloning masters.
 </hr></p></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Inserted slide.</dd>
</dl>
</li>
</ul>
<a name="addEmptySlide-com.aspose.slides.ILayoutSlide-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addEmptySlide</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a>&nbsp;addEmptySlide(<a href="../../../com/aspose/slides/ILayoutSlide.html" title="interface in com.aspose.slides">ILayoutSlide</a>&nbsp;layout)</pre>
<div class="block"><p>
 Adds a new empty slide to the end of the collection.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISlideCollection.html#addEmptySlide-com.aspose.slides.ILayoutSlide-">addEmptySlide</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISlideCollection.html" title="interface in com.aspose.slides">ISlideCollection</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>layout</code> - Layout for a slide.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Added slide.</dd>
</dl>
</li>
</ul>
<a name="insertEmptySlide-int-com.aspose.slides.ILayoutSlide-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>insertEmptySlide</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a>&nbsp;insertEmptySlide(int&nbsp;index,
                                     <a href="../../../com/aspose/slides/ILayoutSlide.html" title="interface in com.aspose.slides">ILayoutSlide</a>&nbsp;layout)</pre>
<div class="block"><p>
 Inserts a copy of a specified slide to specified position of the collection.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISlideCollection.html#insertEmptySlide-int-com.aspose.slides.ILayoutSlide-">insertEmptySlide</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISlideCollection.html" title="interface in com.aspose.slides">ISlideCollection</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - Index of a new slide.</dd>
<dd><code>layout</code> - Layout for a slide.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Inserted slide.</dd>
</dl>
</li>
</ul>
<a name="addClone-com.aspose.slides.ISlide-com.aspose.slides.ILayoutSlide-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addClone</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a>&nbsp;addClone(<a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a>&nbsp;sourceSlide,
                             <a href="../../../com/aspose/slides/ILayoutSlide.html" title="interface in com.aspose.slides">ILayoutSlide</a>&nbsp;destLayout)</pre>
<div class="block"><p>
 Adds a copy of a specified slide to the end of the collection.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISlideCollection.html#addClone-com.aspose.slides.ISlide-com.aspose.slides.ILayoutSlide-">addClone</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISlideCollection.html" title="interface in com.aspose.slides">ISlideCollection</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>sourceSlide</code> - Slide to clone.</dd>
<dd><code>destLayout</code> - Layout slide for a new slide.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>New slide.</dd>
</dl>
</li>
</ul>
<a name="insertClone-int-com.aspose.slides.ISlide-com.aspose.slides.ILayoutSlide-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>insertClone</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a>&nbsp;insertClone(int&nbsp;index,
                                <a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a>&nbsp;sourceSlide,
                                <a href="../../../com/aspose/slides/ILayoutSlide.html" title="interface in com.aspose.slides">ILayoutSlide</a>&nbsp;destLayout)</pre>
<div class="block"><p>
 Inserts a copy of a specified slide to specified position of the collection.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISlideCollection.html#insertClone-int-com.aspose.slides.ISlide-com.aspose.slides.ILayoutSlide-">insertClone</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISlideCollection.html" title="interface in com.aspose.slides">ISlideCollection</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - Index of new slide.</dd>
<dd><code>sourceSlide</code> - Slide to clone.</dd>
<dd><code>destLayout</code> - Layout slide for a new slide.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Inserted slide.</dd>
</dl>
</li>
</ul>
<a name="addClone-com.aspose.slides.ISlide-com.aspose.slides.IMasterSlide-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addClone</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a>&nbsp;addClone(<a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a>&nbsp;sourceSlide,
                             <a href="../../../com/aspose/slides/IMasterSlide.html" title="interface in com.aspose.slides">IMasterSlide</a>&nbsp;destMaster,
                             boolean&nbsp;allowCloneMissingLayout)</pre>
<div class="block"><p>
 Adds a copy of a specified source slide to the end of the collection.
 Appropriate layout will be selected automatically from the specified 
 master (appropriate layout is the layout with the same Type or Name as 
 of layout of the source slide). If there is no appropriate layout then
 layout of the source slide will be cloned (if allowCloneMissingLayout 
 is true) or PptxEditException will be thrown (if allowCloneMissingLayout
 is false).
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISlideCollection.html#addClone-com.aspose.slides.ISlide-com.aspose.slides.IMasterSlide-boolean-">addClone</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISlideCollection.html" title="interface in com.aspose.slides">ISlideCollection</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>sourceSlide</code> - Slide to clone.</dd>
<dd><code>destMaster</code> - Master slide for a new slide.</dd>
<dd><code>allowCloneMissingLayout</code> - If there is no appropriate layout in specified master then layout of the 
 source slide will be cloned (if allowCloneMissingLayout is true) or 
 PptxEditException will be thrown (if allowCloneMissingLayout is false).</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>New slide.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../com/aspose/slides/PptxEditException.html" title="class in com.aspose.slides">PptxEditException</a></code> - Thrown if there is no appropriate layout in specified master and 
 allowCloneMissingLayout is false.</dd>
</dl>
</li>
</ul>
<a name="insertClone-int-com.aspose.slides.ISlide-com.aspose.slides.IMasterSlide-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>insertClone</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a>&nbsp;insertClone(int&nbsp;index,
                                <a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a>&nbsp;sourceSlide,
                                <a href="../../../com/aspose/slides/IMasterSlide.html" title="interface in com.aspose.slides">IMasterSlide</a>&nbsp;destMaster,
                                boolean&nbsp;allowCloneMissingLayout)</pre>
<div class="block"><p>
 Inserts a copy of a specified source slide to specified position of the collection.
 Appropriate layout will be selected automatically from the specified 
 master (appropriate layout is the layout with the same Type or Name as 
 of layout of the source slide). If there is no appropriate layout then
 layout of the source slide will be cloned (if allowCloneMissingLayout 
 is true) or PptxEditException will be thrown (if allowCloneMissingLayout
 is false).
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISlideCollection.html#insertClone-int-com.aspose.slides.ISlide-com.aspose.slides.IMasterSlide-boolean-">insertClone</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISlideCollection.html" title="interface in com.aspose.slides">ISlideCollection</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - Index of new slide.</dd>
<dd><code>sourceSlide</code> - Slide to clone.</dd>
<dd><code>destMaster</code> - Master slide for a new slide.</dd>
<dd><code>allowCloneMissingLayout</code> - If there is no appropriate layout in specified master then layout of the 
 source slide will be cloned (if allowCloneMissingLayout is true) or 
 PptxEditException will be thrown (if allowCloneMissingLayout is false).</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Inserted slide.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../com/aspose/slides/PptxEditException.html" title="class in com.aspose.slides">PptxEditException</a></code> - Thrown if there is no appropriate layout in specified master and 
 allowCloneMissingLayout is false.</dd>
</dl>
</li>
</ul>
<a name="remove-com.aspose.slides.ISlide-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>remove</h4>
<pre>public final&nbsp;void&nbsp;remove(<a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a>&nbsp;value)</pre>
<div class="block"><p>
 Removes the first occurrence of a specific object from the collection.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISlideCollection.html#remove-com.aspose.slides.ISlide-">remove</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISlideCollection.html" title="interface in com.aspose.slides">ISlideCollection</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - The slide to remove from the collection.</dd>
</dl>
</li>
</ul>
<a name="removeAt-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>removeAt</h4>
<pre>public final&nbsp;void&nbsp;removeAt(int&nbsp;index)</pre>
<div class="block"><p>
 Removes the element at the specified index of the collection.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISlideCollection.html#removeAt-int-">removeAt</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISlideCollection.html" title="interface in com.aspose.slides">ISlideCollection</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - The zero-based index of the element to remove.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>com.aspose.ms.System.ArgumentOutOfRangeException</code> - When index parameter contains wrong section number.</dd>
</dl>
</li>
</ul>
<a name="iterator--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>iterator</h4>
<pre>public final&nbsp;com.aspose.ms.System.Collections.Generic.IGenericEnumerator&lt;<a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a>&gt;&nbsp;iterator()</pre>
<div class="block"><p>
 Returns an enumerator that iterates through the collection.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>iterator</code>&nbsp;in interface&nbsp;<code>com.aspose.ms.System.Collections.Generic.IGenericEnumerable&lt;<a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a>&gt;</code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>iterator</code>&nbsp;in interface&nbsp;<code>com.aspose.ms.System.Collections.IEnumerable&lt;<a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a>&gt;</code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>iterator</code>&nbsp;in interface&nbsp;<code>java.lang.Iterable&lt;<a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a>&gt;</code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>A <code>IGenericEnumerator</code> that can be used to iterate through the collection.</dd>
</dl>
</li>
</ul>
<a name="iteratorJava--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>iteratorJava</h4>
<pre>public final&nbsp;com.aspose.ms.System.Collections.Generic.IGenericEnumerator&lt;<a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a>&gt;&nbsp;iteratorJava()</pre>
<div class="block"><p>
 Returns a java iterator for the entire collection.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IGenericCollection.html#iteratorJava--">iteratorJava</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IGenericCollection.html" title="interface in com.aspose.slides">IGenericCollection</a>&lt;<a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a>&gt;</code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>An <code>Iterator</code> for the entire collection.</dd>
</dl>
</li>
</ul>
<a name="toArray--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>toArray</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a>[]&nbsp;toArray()</pre>
<div class="block"><p>
 Creates and returns an array with all slides in it.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISlideCollection.html#toArray--">toArray</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISlideCollection.html" title="interface in com.aspose.slides">ISlideCollection</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Array of <a href="../../../com/aspose/slides/Slide.html" title="class in com.aspose.slides"><code>Slide</code></a></dd>
</dl>
</li>
</ul>
<a name="toArray-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>toArray</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a>[]&nbsp;toArray(int&nbsp;startIndex,
                              int&nbsp;count)</pre>
<div class="block"><p>
 Creates and returns an array with all slides from the specified range in it.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISlideCollection.html#toArray-int-int-">toArray</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISlideCollection.html" title="interface in com.aspose.slides">ISlideCollection</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>startIndex</code> - An index of a first slide to add.</dd>
<dd><code>count</code> - A number of slides to add.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Array of <a href="../../../com/aspose/slides/Slide.html" title="class in com.aspose.slides"><code>Slide</code></a></dd>
</dl>
</li>
</ul>
<a name="reorder-int-com.aspose.slides.ISlide-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>reorder</h4>
<pre>public final&nbsp;void&nbsp;reorder(int&nbsp;index,
                          <a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a>&nbsp;slide)</pre>
<div class="block"><p>
 Moves slide from the collection to the specified position.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISlideCollection.html#reorder-int-com.aspose.slides.ISlide-">reorder</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISlideCollection.html" title="interface in com.aspose.slides">ISlideCollection</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - Target index.</dd>
<dd><code>slide</code> - Slide to move.</dd>
</dl>
</li>
</ul>
<a name="reorder-int-com.aspose.slides.ISlide...-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>reorder</h4>
<pre>public final&nbsp;void&nbsp;reorder(int&nbsp;index,
                          <a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a>...&nbsp;slides)</pre>
<div class="block"><p>
 Moves slides from the collection to the specified position.
 Slides will be placed starting from index in order they appear in list.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISlideCollection.html#reorder-int-com.aspose.slides.ISlide...-">reorder</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISlideCollection.html" title="interface in com.aspose.slides">ISlideCollection</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - Target index.</dd>
<dd><code>slides</code> - Slides to move.</dd>
</dl>
</li>
</ul>
<a name="indexOf-com.aspose.slides.ISlide-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>indexOf</h4>
<pre>public final&nbsp;int&nbsp;indexOf(<a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a>&nbsp;slide)</pre>
<div class="block"><p>
 Returns an index of the specified slide in the collection.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISlideCollection.html#indexOf-com.aspose.slides.ISlide-">indexOf</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISlideCollection.html" title="interface in com.aspose.slides">ISlideCollection</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>slide</code> - Slide to find.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Index of a slide or -1 if slide not from this collection.</dd>
</dl>
</li>
</ul>
<a name="addFromPdf-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addFromPdf</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a>[]&nbsp;addFromPdf(java.lang.String&nbsp;path)</pre>
<div class="block"><p>
 Creates slides from the PDF document and adds them to the end of the collection.
 </p><p><hr><blockquote><pre>Example:
 <pre>
 Presentation pres = new Presentation();
 try {
     pres.getSlides().addFromPdf("document.pdf");
     pres.save("fromPdfDocument.pptx", SaveFormat.Pptx);
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISlideCollection.html#addFromPdf-java.lang.String-">addFromPdf</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISlideCollection.html" title="interface in com.aspose.slides">ISlideCollection</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>path</code> - A path to the PDF document</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Added slides</dd>
</dl>
</li>
</ul>
<a name="addFromPdf-java.lang.String-com.aspose.slides.PdfImportOptions-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addFromPdf</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a>[]&nbsp;addFromPdf(java.lang.String&nbsp;path,
                                 <a href="../../../com/aspose/slides/PdfImportOptions.html" title="class in com.aspose.slides">PdfImportOptions</a>&nbsp;pdfImportOptions)</pre>
<div class="block"><p>
 Creates slides from the PDF document and adds them to the end of the collection considering the pdf import options.
 </p><p><hr><blockquote><pre>Example:
 <pre>
 Presentation pres = new Presentation();
 try {
     PdfImportOptions pdfImportOptions = new PdfImportOptions();
     pdfImportOptions.setDetectTables(true);
     pres.getSlides().addFromPdf("document.pdf", pdfImportOptions);
     pres.save("fromPdfDocument.pptx", SaveFormat.Pptx);
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISlideCollection.html#addFromPdf-java.lang.String-com.aspose.slides.PdfImportOptions-">addFromPdf</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISlideCollection.html" title="interface in com.aspose.slides">ISlideCollection</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>path</code> - A path to the PDF document</dd>
<dd><code>pdfImportOptions</code> - Options for pdf import</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Added slides</dd>
</dl>
</li>
</ul>
<a name="addFromPdf-java.io.InputStream-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addFromPdf</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a>[]&nbsp;addFromPdf(java.io.InputStream&nbsp;pdfStream)</pre>
<div class="block"><p>
 Creates slides from the PDF document and adds them to the end of the collection.
 </p><p><hr><blockquote><pre>Example:
 <pre>
 Presentation pres = new Presentation();
 try {
     FileInputStream stream = new FileInputStream("document.pdf");
     pres.getSlides().addFromPdf(stream);

     pres.save("fromPdfDocument.pptx", SaveFormat.Pptx);
 } catch (IOException e) {
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISlideCollection.html#addFromPdf-java.io.InputStream-">addFromPdf</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISlideCollection.html" title="interface in com.aspose.slides">ISlideCollection</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>pdfStream</code> - A stream which will be used as a source of the PDF document</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Added slides</dd>
</dl>
</li>
</ul>
<a name="addFromPdf-java.io.InputStream-com.aspose.slides.PdfImportOptions-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addFromPdf</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a>[]&nbsp;addFromPdf(java.io.InputStream&nbsp;pdfStream,
                                 <a href="../../../com/aspose/slides/PdfImportOptions.html" title="class in com.aspose.slides">PdfImportOptions</a>&nbsp;pdfImportOptions)</pre>
<div class="block"><p>
 Creates slides from the PDF document and adds them to the end of the collection.
 </p><p><hr><blockquote><pre>Example:
 <pre>
 Presentation pres = new Presentation();
 try {
     PdfImportOptions pdfImportOptions = new PdfImportOptions();
     pdfImportOptions.setDetectTables(true);

     FileInputStream stream = new FileInputStream("document.pdf");
     pres.getSlides().addFromPdf(stream, pdfImportOptions);

     pres.save("fromPdfDocument.pptx", SaveFormat.Pptx);
 } catch (IOException e) {
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISlideCollection.html#addFromPdf-java.io.InputStream-com.aspose.slides.PdfImportOptions-">addFromPdf</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISlideCollection.html" title="interface in com.aspose.slides">ISlideCollection</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>pdfStream</code> - A stream which will be used as a source of the PDF document</dd>
<dd><code>pdfImportOptions</code> - Options for pdf import</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Added slides</dd>
</dl>
</li>
</ul>
<a name="addFromHtml-java.lang.String-com.aspose.slides.IExternalResourceResolver-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addFromHtml</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a>[]&nbsp;addFromHtml(java.lang.String&nbsp;htmlText,
                                  <a href="../../../com/aspose/slides/IExternalResourceResolver.html" title="interface in com.aspose.slides">IExternalResourceResolver</a>&nbsp;resolver,
                                  java.lang.String&nbsp;uri)</pre>
<div class="block"><p>
 Creates slides from HTML text and adds them to the end of the collection.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISlideCollection.html#addFromHtml-java.lang.String-com.aspose.slides.IExternalResourceResolver-java.lang.String-">addFromHtml</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISlideCollection.html" title="interface in com.aspose.slides">ISlideCollection</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>htmlText</code> - Html to add.</dd>
<dd><code>resolver</code> - A callback object used to fetch external objects. If this parameter is null all external objects will be ignored.</dd>
<dd><code>uri</code> - An URI of the specified HTML. Used to resolve relative links.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Added slides.</dd>
</dl>
</li>
</ul>
<a name="addFromHtml-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addFromHtml</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a>[]&nbsp;addFromHtml(java.lang.String&nbsp;htmlText)</pre>
<div class="block"><p>
 Creates slides from HTML text and adds them to the end of the collection.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISlideCollection.html#addFromHtml-java.lang.String-">addFromHtml</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISlideCollection.html" title="interface in com.aspose.slides">ISlideCollection</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>htmlText</code> - Html to add.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Added slides</dd>
</dl>
</li>
</ul>
<a name="addFromHtml-java.io.InputStream-com.aspose.slides.IExternalResourceResolver-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addFromHtml</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a>[]&nbsp;addFromHtml(java.io.InputStream&nbsp;htmlStream,
                                  <a href="../../../com/aspose/slides/IExternalResourceResolver.html" title="interface in com.aspose.slides">IExternalResourceResolver</a>&nbsp;resolver,
                                  java.lang.String&nbsp;uri)</pre>
<div class="block"><p>
 Creates slides from HTML text and adds them to the end of the collection.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISlideCollection.html#addFromHtml-java.io.InputStream-com.aspose.slides.IExternalResourceResolver-java.lang.String-">addFromHtml</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISlideCollection.html" title="interface in com.aspose.slides">ISlideCollection</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>htmlStream</code> - A Stream object which will be used as a source of a HTML file.</dd>
<dd><code>resolver</code> - A callback object used to fetch external objects. If this parameter is null all external objects will be ignored.</dd>
<dd><code>uri</code> - An URI of the specified HTML. Used to resolve relative links.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Added slides.</dd>
</dl>
</li>
</ul>
<a name="addFromHtml-java.io.InputStream-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addFromHtml</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a>[]&nbsp;addFromHtml(java.io.InputStream&nbsp;htmlStream)</pre>
<div class="block"><p>
 Creates slides from HTML text and adds them to the end of the collection.
 </p><p><hr><blockquote><pre>
 <pre>
 // Create an instance of the Presentation class.
 Presentation pres = new Presentation();
 try {
     String html = new String(Files.readAllBytes(Paths.get("file.html")));
     // Call the AddFromHtml method and pass the HTML file.
     pres.getSlides().addFromHtml(html);
     // Use the Save method to save the file as a PowerPoint document.
     pres.save("MyPresentation.pptx", SaveFormat.Pptx);
 } catch(IOException e) {
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISlideCollection.html#addFromHtml-java.io.InputStream-">addFromHtml</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISlideCollection.html" title="interface in com.aspose.slides">ISlideCollection</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>htmlStream</code> - A Stream object which will be used as a source of a HTML file.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Added slides</dd>
</dl>
</li>
</ul>
<a name="insertFromHtml-int-java.lang.String-com.aspose.slides.IExternalResourceResolver-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>insertFromHtml</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a>[]&nbsp;insertFromHtml(int&nbsp;index,
                                     java.lang.String&nbsp;htmlText,
                                     <a href="../../../com/aspose/slides/IExternalResourceResolver.html" title="interface in com.aspose.slides">IExternalResourceResolver</a>&nbsp;resolver,
                                     java.lang.String&nbsp;uri)</pre>
<div class="block"><p>
 Creates slides from HTML text and inserts them to the collection at the specified position.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISlideCollection.html#insertFromHtml-int-java.lang.String-com.aspose.slides.IExternalResourceResolver-java.lang.String-">insertFromHtml</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISlideCollection.html" title="interface in com.aspose.slides">ISlideCollection</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - Position to insert.</dd>
<dd><code>htmlText</code> - Html to add.</dd>
<dd><code>resolver</code> - A callback object used to fetch external objects. If this parameter is null all external objects will be ignored.</dd>
<dd><code>uri</code> - An URI of the specified HTML. Used to resolve relative links.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Added slides.</dd>
</dl>
</li>
</ul>
<a name="insertFromHtml-int-java.lang.String-com.aspose.slides.IExternalResourceResolver-java.lang.String-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>insertFromHtml</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a>[]&nbsp;insertFromHtml(int&nbsp;index,
                                     java.lang.String&nbsp;htmlText,
                                     <a href="../../../com/aspose/slides/IExternalResourceResolver.html" title="interface in com.aspose.slides">IExternalResourceResolver</a>&nbsp;resolver,
                                     java.lang.String&nbsp;uri,
                                     boolean&nbsp;useSlideWithIndexAsStart)</pre>
<div class="block"><p>
 Creates slides from HTML text and inserts them to the collection at the specified position.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISlideCollection.html#insertFromHtml-int-java.lang.String-com.aspose.slides.IExternalResourceResolver-java.lang.String-boolean-">insertFromHtml</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISlideCollection.html" title="interface in com.aspose.slides">ISlideCollection</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - Position to insert.</dd>
<dd><code>htmlText</code> - Html to add.</dd>
<dd><code>resolver</code> - A callback object used to fetch external objects. If this parameter is null all external objects will be ignored.</dd>
<dd><code>uri</code> - An URI of the specified HTML. Used to resolve relative links.</dd>
<dd><code>useSlideWithIndexAsStart</code> - This flag determines how to start insertion: from a new slide or from the slide with the specified index.
 If <b>true</b>, then data insertion will start from an empty space on the slide with the specified index.
 If <b>false</b>, then data will be added to the created slides.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Added slides.</dd>
</dl>
</li>
</ul>
<a name="insertFromHtml-int-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>insertFromHtml</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a>[]&nbsp;insertFromHtml(int&nbsp;index,
                                     java.lang.String&nbsp;htmlText)</pre>
<div class="block"><p>
 Creates slides from HTML text and inserts them to the collection at the specified position.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISlideCollection.html#insertFromHtml-int-java.lang.String-">insertFromHtml</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISlideCollection.html" title="interface in com.aspose.slides">ISlideCollection</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - Position to insert.</dd>
<dd><code>htmlText</code> - Html to add.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Added slides</dd>
</dl>
</li>
</ul>
<a name="insertFromHtml-int-java.lang.String-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>insertFromHtml</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a>[]&nbsp;insertFromHtml(int&nbsp;index,
                                     java.lang.String&nbsp;htmlText,
                                     boolean&nbsp;useSlideWithIndexAsStart)</pre>
<div class="block"><p>
 Creates slides from HTML text and inserts them to the collection at the specified position.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISlideCollection.html#insertFromHtml-int-java.lang.String-boolean-">insertFromHtml</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISlideCollection.html" title="interface in com.aspose.slides">ISlideCollection</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - Position to insert.</dd>
<dd><code>htmlText</code> - Html to add.</dd>
<dd><code>useSlideWithIndexAsStart</code> - This flag determines how to start insertion: from a new slide or from the slide with the specified index.
 If <b>true</b>, then data insertion will start from an empty space on the slide with the specified index.
 If <b>false</b>, then data will be added to the created slides.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Added slides</dd>
</dl>
</li>
</ul>
<a name="insertFromHtml-int-java.io.InputStream-com.aspose.slides.IExternalResourceResolver-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>insertFromHtml</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a>[]&nbsp;insertFromHtml(int&nbsp;index,
                                     java.io.InputStream&nbsp;htmlStream,
                                     <a href="../../../com/aspose/slides/IExternalResourceResolver.html" title="interface in com.aspose.slides">IExternalResourceResolver</a>&nbsp;resolver,
                                     java.lang.String&nbsp;uri)</pre>
<div class="block"><p>
 Creates slides from HTML text and inserts them to the collection at the specified position.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISlideCollection.html#insertFromHtml-int-java.io.InputStream-com.aspose.slides.IExternalResourceResolver-java.lang.String-">insertFromHtml</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISlideCollection.html" title="interface in com.aspose.slides">ISlideCollection</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - Position to insert.</dd>
<dd><code>htmlStream</code> - A Stream object which will be used as a source of a HTML file.</dd>
<dd><code>resolver</code> - A callback object used to fetch external objects. If this parameter is null all external objects will be ignored.</dd>
<dd><code>uri</code> - An URI of the specified HTML. Used to resolve relative links.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Added slides.</dd>
</dl>
</li>
</ul>
<a name="insertFromHtml-int-java.io.InputStream-com.aspose.slides.IExternalResourceResolver-java.lang.String-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>insertFromHtml</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a>[]&nbsp;insertFromHtml(int&nbsp;index,
                                     java.io.InputStream&nbsp;htmlStream,
                                     <a href="../../../com/aspose/slides/IExternalResourceResolver.html" title="interface in com.aspose.slides">IExternalResourceResolver</a>&nbsp;resolver,
                                     java.lang.String&nbsp;uri,
                                     boolean&nbsp;useSlideWithIndexAsStart)</pre>
<div class="block"><p>
 Creates slides from HTML text and inserts them to the collection at the specified position.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISlideCollection.html#insertFromHtml-int-java.io.InputStream-com.aspose.slides.IExternalResourceResolver-java.lang.String-boolean-">insertFromHtml</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISlideCollection.html" title="interface in com.aspose.slides">ISlideCollection</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - Position to insert.</dd>
<dd><code>htmlStream</code> - A Stream object which will be used as a source of a HTML file.</dd>
<dd><code>resolver</code> - A callback object used to fetch external objects. If this parameter is null all external objects will be ignored.</dd>
<dd><code>uri</code> - An URI of the specified HTML. Used to resolve relative links.</dd>
<dd><code>useSlideWithIndexAsStart</code> - This flag determines how to start insertion: from a new slide or from the slide with the specified index.
 If <b>true</b>, then data insertion will start from an empty space on the slide with the specified index.
 If <b>false</b>, then data will be added to the created slides.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Added slides.</dd>
</dl>
</li>
</ul>
<a name="insertFromHtml-int-java.io.InputStream-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>insertFromHtml</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a>[]&nbsp;insertFromHtml(int&nbsp;index,
                                     java.io.InputStream&nbsp;htmlStream)</pre>
<div class="block"><p>
 Creates slides from HTML text and inserts them to the collection at the specified position.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISlideCollection.html#insertFromHtml-int-java.io.InputStream-">insertFromHtml</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISlideCollection.html" title="interface in com.aspose.slides">ISlideCollection</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - Position to insert.</dd>
<dd><code>htmlStream</code> - A Stream object which will be used as a source of a HTML file.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Added slides</dd>
</dl>
</li>
</ul>
<a name="insertFromHtml-int-java.io.InputStream-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>insertFromHtml</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a>[]&nbsp;insertFromHtml(int&nbsp;index,
                                     java.io.InputStream&nbsp;htmlStream,
                                     boolean&nbsp;useSlideWithIndexAsStart)</pre>
<div class="block"><p>
 Creates slides from HTML text and inserts them to the collection at the specified position.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISlideCollection.html#insertFromHtml-int-java.io.InputStream-boolean-">insertFromHtml</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISlideCollection.html" title="interface in com.aspose.slides">ISlideCollection</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - Position to insert.</dd>
<dd><code>htmlStream</code> - A Stream object which will be used as a source of a HTML file.</dd>
<dd><code>useSlideWithIndexAsStart</code> - This flag determines how to start insertion: from a new slide or from the slide with the specified index.
 If <b>true</b>, then data insertion will start from an empty space on the slide with the specified index.
 If <b>false</b>, then data will be added to the created slides.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Added slides</dd>
</dl>
</li>
</ul>
<a name="copyTo-com.aspose.ms.System.Array-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>copyTo</h4>
<pre>public final&nbsp;void&nbsp;copyTo(com.aspose.ms.System.Array&nbsp;array,
                         int&nbsp;index)</pre>
<div class="block"><p>
 Copies all elements from the collection to the specified array.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>copyTo</code>&nbsp;in interface&nbsp;<code>com.aspose.ms.System.Collections.ICollection&lt;<a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a>&gt;</code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>array</code> - Target array.</dd>
<dd><code>index</code> - Starting index in the target array.</dd>
</dl>
</li>
</ul>
<a name="isSynchronized--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isSynchronized</h4>
<pre>public final&nbsp;boolean&nbsp;isSynchronized()</pre>
<div class="block"><p>
 Returns a value indicating whether access to the collection is synchronized (thread-safe).
 Read-only boolean.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>isSynchronized</code>&nbsp;in interface&nbsp;<code>com.aspose.ms.System.Collections.ICollection&lt;<a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a>&gt;</code></dd>
</dl>
</li>
</ul>
<a name="getSyncRoot--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getSyncRoot</h4>
<pre>public final&nbsp;java.lang.Object&nbsp;getSyncRoot()</pre>
<div class="block"><p>
 Returns a synchronization root.
 Read-only <code>Object</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>getSyncRoot</code>&nbsp;in interface&nbsp;<code>com.aspose.ms.System.Collections.ICollection&lt;<a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a>&gt;</code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/Slide.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/SlideHeaderFooterManager.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/SlideCollection.html" target="_top">Frames</a></li>
<li><a href="SlideCollection.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><i>Copyright &copy; 2004-2025 Aspose Pty Ltd. All Rights Reserved.</i></small></p>
</body>
</html>
