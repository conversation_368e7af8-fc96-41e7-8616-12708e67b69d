<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_421) on Fri Jul 11 05:32:43 CDT 2025 -->
<title>PresentationInfo (Aspose.Slides for Java (25.7) API Documentation)</title>
<meta name="date" content="2025-07-11">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="PresentationInfo (Aspose.Slides for Java (25.7) API Documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/PresentationHeaderFooterManager.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/PresentationLockingBehavior.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/PresentationInfo.html" target="_top">Frames</a></li>
<li><a href="PresentationInfo.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.aspose.slides</div>
<h2 title="Class PresentationInfo" class="title">Class PresentationInfo</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.aspose.slides.PresentationInfo</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../com/aspose/slides/IPresentationInfo.html" title="interface in com.aspose.slides">IPresentationInfo</a></dd>
</dl>
<hr>
<br>
<pre>public final class <span class="typeNameLabel">PresentationInfo</span>
extends java.lang.Object
implements <a href="../../../com/aspose/slides/IPresentationInfo.html" title="interface in com.aspose.slides">IPresentationInfo</a></pre>
<div class="block"><p>
 Information about presentation file
 </p></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresentationInfo.html#checkPassword-java.lang.String-">checkPassword</a></span>(java.lang.String&nbsp;password)</code>
<div class="block">
 Checks whether a password is correct for a presentation protected with open password.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresentationInfo.html#checkWriteProtection-java.lang.String-">checkWriteProtection</a></span>(java.lang.String&nbsp;password)</code>
<div class="block">
 Checks whether a password to modify is correct for a write protected presentation.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresentationInfo.html#getLoadFormat--">getLoadFormat</a></span>()</code>
<div class="block">
 Gets format of the binded presentation.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresentationInfo.html#isEncrypted--">isEncrypted</a></span>()</code>
<div class="block">
 Gets True if binded presentation is encrypted, otherwise False.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresentationInfo.html#isPasswordProtected--">isPasswordProtected</a></span>()</code>
<div class="block">
 Gets a value that indicates whether a binded presentation is protected by a password to open.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>byte</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresentationInfo.html#isWriteProtected--">isWriteProtected</a></span>()</code>
<div class="block">
 Gets a value that indicates whether a binded presentation is write protected.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IDocumentProperties.html" title="interface in com.aspose.slides">IDocumentProperties</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresentationInfo.html#readDocumentProperties--">readDocumentProperties</a></span>()</code>
<div class="block">
 Gets document properties of binded presentation.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresentationInfo.html#updateDocumentProperties-com.aspose.slides.IDocumentProperties-">updateDocumentProperties</a></span>(<a href="../../../com/aspose/slides/IDocumentProperties.html" title="interface in com.aspose.slides">IDocumentProperties</a>&nbsp;documentProperties)</code>
<div class="block">
 Updates properties of binded presentation.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresentationInfo.html#writeBindedPresentation-java.io.OutputStream-">writeBindedPresentation</a></span>(java.io.OutputStream&nbsp;stream)</code>
<div class="block">
 Writes binded presentation to stream.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresentationInfo.html#writeBindedPresentation-java.lang.String-">writeBindedPresentation</a></span>(java.lang.String&nbsp;file)</code>
<div class="block">
 Writes binded presentation to file.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="isEncrypted--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isEncrypted</h4>
<pre>public final&nbsp;boolean&nbsp;isEncrypted()</pre>
<div class="block"><p>
 Gets True if binded presentation is encrypted, otherwise False.
 Read-only <code>boolean</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPresentationInfo.html#isEncrypted--">isEncrypted</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPresentationInfo.html" title="interface in com.aspose.slides">IPresentationInfo</a></code></dd>
</dl>
</li>
</ul>
<a name="isPasswordProtected--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isPasswordProtected</h4>
<pre>public final&nbsp;boolean&nbsp;isPasswordProtected()</pre>
<div class="block"><p>
 Gets a value that indicates whether a binded presentation is protected by a password to open.
 </p><p><hr><blockquote><pre>
 <pre>
 IPresentationInfo info = PresentationFactory.getInstance().getPresentationInfo(presentationFilePath);
 if (info.isPasswordProtected())
 {
     System.out.println("The presentation '" + presentationFilePath + "' is protected by password to open.");
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPresentationInfo.html#isPasswordProtected--">isPasswordProtected</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPresentationInfo.html" title="interface in com.aspose.slides">IPresentationInfo</a></code></dd>
</dl>
</li>
</ul>
<a name="isWriteProtected--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isWriteProtected</h4>
<pre>public final&nbsp;byte&nbsp;isWriteProtected()</pre>
<div class="block"><p>
 Gets a value that indicates whether a binded presentation is write protected.
 </p><p><hr><blockquote><pre>
 <pre>
 IPresentationInfo info = PresentationFactory.getInstance().getPresentationInfo(presentationFilePath);
 if (info.isWriteProtected() == NullableBool.True)
 {
     System.out.println("The presentation '" + presentationFilePath + "' is protected by password to open.");
 }
 </pre>
 </pre></blockquote></hr></p><p><hr>
 If the presentation is protected by a password to open, the property value equals NotDefined.
 </hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPresentationInfo.html#isWriteProtected--">isWriteProtected</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPresentationInfo.html" title="interface in com.aspose.slides">IPresentationInfo</a></code></dd>
</dl>
</li>
</ul>
<a name="getLoadFormat--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLoadFormat</h4>
<pre>public final&nbsp;int&nbsp;getLoadFormat()</pre>
<div class="block"><p>
 Gets format of the binded presentation.
 Read-only <a href="../../../com/aspose/slides/LoadFormat.html" title="class in com.aspose.slides"><code>LoadFormat</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPresentationInfo.html#getLoadFormat--">getLoadFormat</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPresentationInfo.html" title="interface in com.aspose.slides">IPresentationInfo</a></code></dd>
</dl>
</li>
</ul>
<a name="checkPassword-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>checkPassword</h4>
<pre>public final&nbsp;boolean&nbsp;checkPassword(java.lang.String&nbsp;password)</pre>
<div class="block"><p>
 Checks whether a password is correct for a presentation protected with open password.
 </p><p><hr><blockquote><pre>
 <pre>
 IPresentationInfo info = PresentationFactory.getInstance().getPresentationInfo(presentationFilePath);
 boolean isPasswordCorrect = info.checkPassword("my_password");
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPresentationInfo.html#checkPassword-java.lang.String-">checkPassword</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPresentationInfo.html" title="interface in com.aspose.slides">IPresentationInfo</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>password</code> - The password to check.
 <p><hr>
 When the password is null or empty, this method returns false.
 </hr></p></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>True if the presentation is protected with open password and the password is correct and false otherwise.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>com.aspose.ms.System.InvalidOperationException</code> - if an unknown presentation format is encountered.</dd>
<dd><code>com.aspose.ms.System.NotSupportedException</code> - if format is not supported to check passwords.</dd>
</dl>
</li>
</ul>
<a name="checkWriteProtection-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>checkWriteProtection</h4>
<pre>public final&nbsp;boolean&nbsp;checkWriteProtection(java.lang.String&nbsp;password)</pre>
<div class="block"><p>
 Checks whether a password to modify is correct for a write protected presentation.
 </p><p><hr><blockquote><pre>
 <pre>
 IPresentationInfo info = PresentationFactory.getInstance().getPresentationInfo(presentationFilePath);
 if (info.isWriteProtected() == NullableBool.True)
 {
     boolean isWriteProtectedByPassword = info.checkWriteProtection("my_password");
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPresentationInfo.html#checkWriteProtection-java.lang.String-">checkWriteProtection</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPresentationInfo.html" title="interface in com.aspose.slides">IPresentationInfo</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>password</code> - The password to check.
 <p><hr>
 1. You should check the (<a href="../../../com/aspose/slides/PresentationInfo.html#isWriteProtected--"><code>isWriteProtected()</code></a>) property before calling this method.
 2. When password is null or empty, this method returns false.
 </hr></p></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>True if the presentation is write protected and the password is correct. False otherwise.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>com.aspose.ms.System.InvalidOperationException</code> - If a presentation is protected by a password to open or format does not support write protection</dd>
</dl>
</li>
</ul>
<a name="readDocumentProperties--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>readDocumentProperties</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IDocumentProperties.html" title="interface in com.aspose.slides">IDocumentProperties</a>&nbsp;readDocumentProperties()</pre>
<div class="block"><p>
 Gets document properties of binded presentation.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPresentationInfo.html#readDocumentProperties--">readDocumentProperties</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPresentationInfo.html" title="interface in com.aspose.slides">IPresentationInfo</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Document properties <a href="../../../com/aspose/slides/IDocumentProperties.html" title="interface in com.aspose.slides"><code>IDocumentProperties</code></a></dd>
</dl>
</li>
</ul>
<a name="updateDocumentProperties-com.aspose.slides.IDocumentProperties-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>updateDocumentProperties</h4>
<pre>public final&nbsp;void&nbsp;updateDocumentProperties(<a href="../../../com/aspose/slides/IDocumentProperties.html" title="interface in com.aspose.slides">IDocumentProperties</a>&nbsp;documentProperties)</pre>
<div class="block"><p>
 Updates properties of binded presentation.
 </p><p><hr><blockquote><pre>
 This sample shows how to call the <a href="../../../com/aspose/slides/PresentationInfo.html#updateDocumentProperties-com.aspose.slides.IDocumentProperties-"><code>updateDocumentProperties(IDocumentProperties)</code></a> method to
 update the document properties returned by call of the <a href="../../../com/aspose/slides/PresentationInfo.html#readDocumentProperties--"><code>readDocumentProperties()</code></a> method.
 <pre>
 IPresentationInfo info = PresentationFactory.getInstance().getPresentationInfo("pres.pptx");
 IDocumentProperties props = info.readDocumentProperties();
 props.setSubject("New subject");
 props.setLastSavedTime(Calendar.getInstance().getTime());
 info.updateDocumentProperties(props);
 info.writeBindedPresentation("new_pres.pptx");
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPresentationInfo.html#updateDocumentProperties-com.aspose.slides.IDocumentProperties-">updateDocumentProperties</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPresentationInfo.html" title="interface in com.aspose.slides">IPresentationInfo</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>documentProperties</code> - Document properties <a href="../../../com/aspose/slides/IDocumentProperties.html" title="interface in com.aspose.slides"><code>IDocumentProperties</code></a></dd>
</dl>
</li>
</ul>
<a name="writeBindedPresentation-java.io.OutputStream-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>writeBindedPresentation</h4>
<pre>public final&nbsp;void&nbsp;writeBindedPresentation(java.io.OutputStream&nbsp;stream)</pre>
<div class="block"><p>
 Writes binded presentation to stream.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPresentationInfo.html#writeBindedPresentation-java.io.OutputStream-">writeBindedPresentation</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPresentationInfo.html" title="interface in com.aspose.slides">IPresentationInfo</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>stream</code> - The stream must be seekable and writable.</dd>
</dl>
</li>
</ul>
<a name="writeBindedPresentation-java.lang.String-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>writeBindedPresentation</h4>
<pre>public final&nbsp;void&nbsp;writeBindedPresentation(java.lang.String&nbsp;file)</pre>
<div class="block"><p>
 Writes binded presentation to file.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPresentationInfo.html#writeBindedPresentation-java.lang.String-">writeBindedPresentation</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPresentationInfo.html" title="interface in com.aspose.slides">IPresentationInfo</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>file</code> - Presentation file.</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/PresentationHeaderFooterManager.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/PresentationLockingBehavior.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/PresentationInfo.html" target="_top">Frames</a></li>
<li><a href="PresentationInfo.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><i>Copyright &copy; 2004-2025 Aspose Pty Ltd. All Rights Reserved.</i></small></p>
</body>
</html>
