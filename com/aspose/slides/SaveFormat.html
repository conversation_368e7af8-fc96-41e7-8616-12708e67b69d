<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_421) on Fri Jul 11 05:32:44 CDT 2025 -->
<title>SaveFormat (Aspose.Slides for Java (25.7) API Documentation)</title>
<meta name="date" content="2025-07-11">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="SaveFormat (Aspose.Slides for Java (25.7) API Documentation)";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/RowFormat.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/SaveOptions.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/SaveFormat.html" target="_top">Frames</a></li>
<li><a href="SaveFormat.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.classes.inherited.from.class.com.aspose.ms.System.Enum">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#methods.inherited.from.class.com.aspose.ms.System.Enum">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li>Method</li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.aspose.slides</div>
<h2 title="Class SaveFormat" class="title">Class SaveFormat</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.aspose.ms.System.ValueType&lt;com.aspose.ms.System.Enum&gt;</li>
<li>
<ul class="inheritance">
<li>com.aspose.ms.System.Enum</li>
<li>
<ul class="inheritance">
<li>com.aspose.slides.SaveFormat</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public final class <span class="typeNameLabel">SaveFormat</span>
extends com.aspose.ms.System.Enum</pre>
<div class="block"><p>
 Constants which define the format of a saved presentation.
 </p></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.com.aspose.ms.System.Enum">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from class&nbsp;com.aspose.ms.System.Enum</h3>
<code>com.aspose.ms.System.Enum.AbstractEnum, com.aspose.ms.System.Enum.FlaggedEnum, com.aspose.ms.System.Enum.ObjectEnum, com.aspose.ms.System.Enum.SimpleEnum</code></li>
</ul>
</li>
</ul>
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SaveFormat.html#Fodp">Fodp</a></span></code>
<div class="block">
 Save presentation in FODP format.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SaveFormat.html#Gif">Gif</a></span></code>
<div class="block">
 Save presentation in GIF format.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SaveFormat.html#Html">Html</a></span></code>
<div class="block">
 Save presentation in HTML format.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SaveFormat.html#Html5">Html5</a></span></code>
<div class="block">
 Save presentation in HTML format using new HTML5 templating system.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SaveFormat.html#Md">Md</a></span></code>
<div class="block">
 Save presentation in Markdown format</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SaveFormat.html#Odp">Odp</a></span></code>
<div class="block">
 Save presentation in ODP format.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SaveFormat.html#Otp">Otp</a></span></code>
<div class="block">
 Save presentation in OTP (presentation template) format.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SaveFormat.html#Pdf">Pdf</a></span></code>
<div class="block">
 Save presentation in PDF format.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SaveFormat.html#Pot">Pot</a></span></code>
<div class="block">
 Save presentation in POT format.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SaveFormat.html#Potm">Potm</a></span></code>
<div class="block">
 Save presentation in POTM (macro-enabled template) format.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SaveFormat.html#Potx">Potx</a></span></code>
<div class="block">
 Save presentation in POTX (template) format.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SaveFormat.html#Pps">Pps</a></span></code>
<div class="block">
 Save presentation in PPS format.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SaveFormat.html#Ppsm">Ppsm</a></span></code>
<div class="block">
 Save presentation in PPSM (macro-enabled slideshow) format.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SaveFormat.html#Ppsx">Ppsx</a></span></code>
<div class="block">
 Save presentation in PPSX (slideshow) format.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SaveFormat.html#Ppt">Ppt</a></span></code>
<div class="block">
 Save presentation in PPT format.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SaveFormat.html#Pptm">Pptm</a></span></code>
<div class="block">
 Save presentation in PPTM (macro-enabled presentation) format.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SaveFormat.html#Pptx">Pptx</a></span></code>
<div class="block">
 Save presentation in PPTX format.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SaveFormat.html#Swf">Swf</a></span></code>
<div class="block">
 Save presentation in SWF format.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SaveFormat.html#Tiff">Tiff</a></span></code>
<div class="block">
 Save presentation as multi-page TIFF image.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SaveFormat.html#Xml">Xml</a></span></code>
<div class="block">
 Save presentation in PowerPoint XML Presentation format.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SaveFormat.html#Xps">Xps</a></span></code>
<div class="block">
 Save presentation in XPS format.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.com.aspose.ms.System.Enum">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;com.aspose.ms.System.Enum</h3>
<code>EnumSeparatorCharArray</code></li>
</ul>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.aspose.ms.System.Enum">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;com.aspose.ms.System.Enum</h3>
<code>Clone, CloneTo, format, format, get_Caption, get_Value, getName, getName, getNames, getNames, getUnderlyingType, getUnderlyingType, getValue, getValues, isDefined, isDefined, isDefined, isDefined, parse, parse, parse, parse, register, toObject, toString</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="Ppt">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Ppt</h4>
<pre>public static final&nbsp;int Ppt</pre>
<div class="block"><p>
 Save presentation in PPT format.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SaveFormat.Ppt">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Pdf">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Pdf</h4>
<pre>public static final&nbsp;int Pdf</pre>
<div class="block"><p>
 Save presentation in PDF format.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SaveFormat.Pdf">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Xps">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Xps</h4>
<pre>public static final&nbsp;int Xps</pre>
<div class="block"><p>
 Save presentation in XPS format.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SaveFormat.Xps">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Pptx">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Pptx</h4>
<pre>public static final&nbsp;int Pptx</pre>
<div class="block"><p>
 Save presentation in PPTX format.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SaveFormat.Pptx">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Ppsx">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Ppsx</h4>
<pre>public static final&nbsp;int Ppsx</pre>
<div class="block"><p>
 Save presentation in PPSX (slideshow) format.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SaveFormat.Ppsx">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Tiff">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Tiff</h4>
<pre>public static final&nbsp;int Tiff</pre>
<div class="block"><p>
 Save presentation as multi-page TIFF image.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SaveFormat.Tiff">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Odp">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Odp</h4>
<pre>public static final&nbsp;int Odp</pre>
<div class="block"><p>
 Save presentation in ODP format.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SaveFormat.Odp">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Pptm">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Pptm</h4>
<pre>public static final&nbsp;int Pptm</pre>
<div class="block"><p>
 Save presentation in PPTM (macro-enabled presentation) format.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SaveFormat.Pptm">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Ppsm">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Ppsm</h4>
<pre>public static final&nbsp;int Ppsm</pre>
<div class="block"><p>
 Save presentation in PPSM (macro-enabled slideshow) format.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SaveFormat.Ppsm">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Potx">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Potx</h4>
<pre>public static final&nbsp;int Potx</pre>
<div class="block"><p>
 Save presentation in POTX (template) format.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SaveFormat.Potx">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Potm">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Potm</h4>
<pre>public static final&nbsp;int Potm</pre>
<div class="block"><p>
 Save presentation in POTM (macro-enabled template) format.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SaveFormat.Potm">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Html">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Html</h4>
<pre>public static final&nbsp;int Html</pre>
<div class="block"><p>
 Save presentation in HTML format.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SaveFormat.Html">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Swf">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Swf</h4>
<pre>public static final&nbsp;int Swf</pre>
<div class="block"><p>
 Save presentation in SWF format.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SaveFormat.Swf">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Otp">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Otp</h4>
<pre>public static final&nbsp;int Otp</pre>
<div class="block"><p>
 Save presentation in OTP (presentation template) format.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SaveFormat.Otp">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Pps">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Pps</h4>
<pre>public static final&nbsp;int Pps</pre>
<div class="block"><p>
 Save presentation in PPS format.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SaveFormat.Pps">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Pot">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Pot</h4>
<pre>public static final&nbsp;int Pot</pre>
<div class="block"><p>
 Save presentation in POT format.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SaveFormat.Pot">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Fodp">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Fodp</h4>
<pre>public static final&nbsp;int Fodp</pre>
<div class="block"><p>
 Save presentation in FODP format.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SaveFormat.Fodp">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Gif">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Gif</h4>
<pre>public static final&nbsp;int Gif</pre>
<div class="block"><p>
 Save presentation in GIF format.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SaveFormat.Gif">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Html5">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Html5</h4>
<pre>public static final&nbsp;int Html5</pre>
<div class="block"><p>
 Save presentation in HTML format using new HTML5 templating system.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SaveFormat.Html5">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Md">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Md</h4>
<pre>public static final&nbsp;int Md</pre>
<div class="block"><p>
 Save presentation in Markdown format
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SaveFormat.Md">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Xml">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>Xml</h4>
<pre>public static final&nbsp;int Xml</pre>
<div class="block"><p>
 Save presentation in PowerPoint XML Presentation format.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SaveFormat.Xml">Constant Field Values</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/RowFormat.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/SaveOptions.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/SaveFormat.html" target="_top">Frames</a></li>
<li><a href="SaveFormat.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.classes.inherited.from.class.com.aspose.ms.System.Enum">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#methods.inherited.from.class.com.aspose.ms.System.Enum">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li>Method</li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><i>Copyright &copy; 2004-2025 Aspose Pty Ltd. All Rights Reserved.</i></small></p>
</body>
</html>
