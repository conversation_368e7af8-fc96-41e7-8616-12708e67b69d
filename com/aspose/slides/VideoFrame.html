<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_421) on Fri Jul 11 05:32:45 CDT 2025 -->
<title>VideoFrame (Aspose.Slides for Java (25.7) API Documentation)</title>
<meta name="date" content="2025-07-11">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="VideoFrame (Aspose.Slides for Java (25.7) API Documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/VideoCollection.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/VideoPlayerHtmlController.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/VideoFrame.html" target="_top">Frames</a></li>
<li><a href="VideoFrame.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.aspose.slides</div>
<h2 title="Class VideoFrame" class="title">Class VideoFrame</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../com/aspose/slides/Shape.html" title="class in com.aspose.slides">com.aspose.slides.Shape</a></li>
<li>
<ul class="inheritance">
<li><a href="../../../com/aspose/slides/GeometryShape.html" title="class in com.aspose.slides">com.aspose.slides.GeometryShape</a></li>
<li>
<ul class="inheritance">
<li><a href="../../../com/aspose/slides/PictureFrame.html" title="class in com.aspose.slides">com.aspose.slides.PictureFrame</a></li>
<li>
<ul class="inheritance">
<li>com.aspose.slides.VideoFrame</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../com/aspose/slides/IGeometryShape.html" title="interface in com.aspose.slides">IGeometryShape</a>, <a href="../../../com/aspose/slides/IHyperlinkContainer.html" title="interface in com.aspose.slides">IHyperlinkContainer</a>, <a href="../../../com/aspose/slides/IPictureFrame.html" title="interface in com.aspose.slides">IPictureFrame</a>, <a href="../../../com/aspose/slides/IPresentationComponent.html" title="interface in com.aspose.slides">IPresentationComponent</a>, <a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a>, <a href="../../../com/aspose/slides/ISlideComponent.html" title="interface in com.aspose.slides">ISlideComponent</a>, <a href="../../../com/aspose/slides/IVideoFrame.html" title="interface in com.aspose.slides">IVideoFrame</a></dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">VideoFrame</span>
extends <a href="../../../com/aspose/slides/PictureFrame.html" title="class in com.aspose.slides">PictureFrame</a>
implements <a href="../../../com/aspose/slides/IVideoFrame.html" title="interface in com.aspose.slides">IVideoFrame</a></pre>
<div class="block"><p>
  Represents a video clip on a slide.
 </p></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/ICaptionsCollection.html" title="interface in com.aspose.slides">ICaptionsCollection</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/VideoFrame.html#getCaptionTracks--">getCaptionTracks</a></span>()</code>
<div class="block">
  Returns the closed captions collection of the video.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IVideo.html" title="interface in com.aspose.slides">IVideo</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/VideoFrame.html#getEmbeddedVideo--">getEmbeddedVideo</a></span>()</code>
<div class="block">
 Returns or sets embedded video object.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/VideoFrame.html#getFullScreenMode--">getFullScreenMode</a></span>()</code>
<div class="block">
 Determines whether a video is shown in full screen mode.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/VideoFrame.html#getHideAtShowing--">getHideAtShowing</a></span>()</code>
<div class="block">
 Determines whether a VideoFrame is hidden.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/VideoFrame.html#getLinkPathLong--">getLinkPathLong</a></span>()</code>
<div class="block">
 Returns or sets the name of an video file which is linked to a VideoFrame.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/VideoFrame.html#getPlayLoopMode--">getPlayLoopMode</a></span>()</code>
<div class="block">
 Determines whether a video is looped.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/VideoFrame.html#getPlayMode--">getPlayMode</a></span>()</code>
<div class="block">
 Returns or sets the video play mode.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/VideoFrame.html#getRewindVideo--">getRewindVideo</a></span>()</code>
<div class="block">
 Determines whether a video is automatically rewinded to start
 as soon as the movie has finished playing.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/VideoFrame.html#getTrimFromEnd--">getTrimFromEnd</a></span>()</code>
<div class="block">
 Trim end [ms]</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/VideoFrame.html#getTrimFromStart--">getTrimFromStart</a></span>()</code>
<div class="block">
 Trim start [ms]</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/VideoFrame.html#getVolume--">getVolume</a></span>()</code>
<div class="block">
 Returns or sets the audio volume.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/VideoFrame.html#setEmbeddedVideo-com.aspose.slides.IVideo-">setEmbeddedVideo</a></span>(<a href="../../../com/aspose/slides/IVideo.html" title="interface in com.aspose.slides">IVideo</a>&nbsp;value)</code>
<div class="block">
 Returns or sets embedded video object.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/VideoFrame.html#setFullScreenMode-boolean-">setFullScreenMode</a></span>(boolean&nbsp;value)</code>
<div class="block">
 Determines whether a video is shown in full screen mode.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/VideoFrame.html#setHideAtShowing-boolean-">setHideAtShowing</a></span>(boolean&nbsp;value)</code>
<div class="block">
 Determines whether a VideoFrame is hidden.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/VideoFrame.html#setLinkPathLong-java.lang.String-">setLinkPathLong</a></span>(java.lang.String&nbsp;value)</code>
<div class="block">
 Returns or sets the name of an video file which is linked to a VideoFrame.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/VideoFrame.html#setPlayLoopMode-boolean-">setPlayLoopMode</a></span>(boolean&nbsp;value)</code>
<div class="block">
 Determines whether a video is looped.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/VideoFrame.html#setPlayMode-int-">setPlayMode</a></span>(int&nbsp;value)</code>
<div class="block">
 Returns or sets the video play mode.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/VideoFrame.html#setRewindVideo-boolean-">setRewindVideo</a></span>(boolean&nbsp;value)</code>
<div class="block">
 Determines whether a video is automatically rewinded to start
 as soon as the movie has finished playing.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/VideoFrame.html#setTrimFromEnd-float-">setTrimFromEnd</a></span>(float&nbsp;value)</code>
<div class="block">
 Trim end [ms]</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/VideoFrame.html#setTrimFromStart-float-">setTrimFromStart</a></span>(float&nbsp;value)</code>
<div class="block">
 Trim start [ms]</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/VideoFrame.html#setVolume-int-">setVolume</a></span>(int&nbsp;value)</code>
<div class="block">
 Returns or sets the audio volume.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.aspose.slides.PictureFrame">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;com.aspose.slides.<a href="../../../com/aspose/slides/PictureFrame.html" title="class in com.aspose.slides">PictureFrame</a></h3>
<code><a href="../../../com/aspose/slides/PictureFrame.html#getPictureFormat--">getPictureFormat</a>, <a href="../../../com/aspose/slides/PictureFrame.html#getPictureFrameLock--">getPictureFrameLock</a>, <a href="../../../com/aspose/slides/PictureFrame.html#getRelativeScaleHeight--">getRelativeScaleHeight</a>, <a href="../../../com/aspose/slides/PictureFrame.html#getRelativeScaleWidth--">getRelativeScaleWidth</a>, <a href="../../../com/aspose/slides/PictureFrame.html#getShapeType--">getShapeType</a>, <a href="../../../com/aspose/slides/PictureFrame.html#isCameo--">isCameo</a>, <a href="../../../com/aspose/slides/PictureFrame.html#setRelativeScaleHeight-float-">setRelativeScaleHeight</a>, <a href="../../../com/aspose/slides/PictureFrame.html#setRelativeScaleWidth-float-">setRelativeScaleWidth</a>, <a href="../../../com/aspose/slides/PictureFrame.html#setShapeType-int-">setShapeType</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.aspose.slides.GeometryShape">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;com.aspose.slides.<a href="../../../com/aspose/slides/GeometryShape.html" title="class in com.aspose.slides">GeometryShape</a></h3>
<code><a href="../../../com/aspose/slides/GeometryShape.html#createShapeElements--">createShapeElements</a>, <a href="../../../com/aspose/slides/GeometryShape.html#getAdjustments--">getAdjustments</a>, <a href="../../../com/aspose/slides/GeometryShape.html#getGeometryPaths--">getGeometryPaths</a>, <a href="../../../com/aspose/slides/GeometryShape.html#getShapeStyle--">getShapeStyle</a>, <a href="../../../com/aspose/slides/GeometryShape.html#setGeometryPath-com.aspose.slides.IGeometryPath-">setGeometryPath</a>, <a href="../../../com/aspose/slides/GeometryShape.html#setGeometryPaths-com.aspose.slides.IGeometryPath:A-">setGeometryPaths</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.aspose.slides.Shape">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;com.aspose.slides.<a href="../../../com/aspose/slides/Shape.html" title="class in com.aspose.slides">Shape</a></h3>
<code><a href="../../../com/aspose/slides/Shape.html#addPlaceholder-com.aspose.slides.IPlaceholder-">addPlaceholder</a>, <a href="../../../com/aspose/slides/Shape.html#getAlternativeText--">getAlternativeText</a>, <a href="../../../com/aspose/slides/Shape.html#getAlternativeTextTitle--">getAlternativeTextTitle</a>, <a href="../../../com/aspose/slides/Shape.html#getBasePlaceholder--">getBasePlaceholder</a>, <a href="../../../com/aspose/slides/Shape.html#getBlackWhiteMode--">getBlackWhiteMode</a>, <a href="../../../com/aspose/slides/Shape.html#getConnectionSiteCount--">getConnectionSiteCount</a>, <a href="../../../com/aspose/slides/Shape.html#getCustomData--">getCustomData</a>, <a href="../../../com/aspose/slides/Shape.html#getEffectFormat--">getEffectFormat</a>, <a href="../../../com/aspose/slides/Shape.html#getFillFormat--">getFillFormat</a>, <a href="../../../com/aspose/slides/Shape.html#getFrame--">getFrame</a>, <a href="../../../com/aspose/slides/Shape.html#getHeight--">getHeight</a>, <a href="../../../com/aspose/slides/Shape.html#getHidden--">getHidden</a>, <a href="../../../com/aspose/slides/Shape.html#getHyperlinkClick--">getHyperlinkClick</a>, <a href="../../../com/aspose/slides/Shape.html#getHyperlinkManager--">getHyperlinkManager</a>, <a href="../../../com/aspose/slides/Shape.html#getHyperlinkMouseOver--">getHyperlinkMouseOver</a>, <a href="../../../com/aspose/slides/Shape.html#getImage--">getImage</a>, <a href="../../../com/aspose/slides/Shape.html#getImage-int-float-float-">getImage</a>, <a href="../../../com/aspose/slides/Shape.html#getLineFormat--">getLineFormat</a>, <a href="../../../com/aspose/slides/Shape.html#getName--">getName</a>, <a href="../../../com/aspose/slides/Shape.html#getOfficeInteropShapeId--">getOfficeInteropShapeId</a>, <a href="../../../com/aspose/slides/Shape.html#getParent_Immediate--">getParent_Immediate</a>, <a href="../../../com/aspose/slides/Shape.html#getParentGroup--">getParentGroup</a>, <a href="../../../com/aspose/slides/Shape.html#getPlaceholder--">getPlaceholder</a>, <a href="../../../com/aspose/slides/Shape.html#getPresentation--">getPresentation</a>, <a href="../../../com/aspose/slides/Shape.html#getRawFrame--">getRawFrame</a>, <a href="../../../com/aspose/slides/Shape.html#getRotation--">getRotation</a>, <a href="../../../com/aspose/slides/Shape.html#getShapeLock--">getShapeLock</a>, <a href="../../../com/aspose/slides/Shape.html#getSlide--">getSlide</a>, <a href="../../../com/aspose/slides/Shape.html#getThreeDFormat--">getThreeDFormat</a>, <a href="../../../com/aspose/slides/Shape.html#getThumbnail--">getThumbnail</a>, <a href="../../../com/aspose/slides/Shape.html#getThumbnail-int-float-float-">getThumbnail</a>, <a href="../../../com/aspose/slides/Shape.html#getUniqueId--">getUniqueId</a>, <a href="../../../com/aspose/slides/Shape.html#getWidth--">getWidth</a>, <a href="../../../com/aspose/slides/Shape.html#getX--">getX</a>, <a href="../../../com/aspose/slides/Shape.html#getY--">getY</a>, <a href="../../../com/aspose/slides/Shape.html#getZOrderPosition--">getZOrderPosition</a>, <a href="../../../com/aspose/slides/Shape.html#isDecorative--">isDecorative</a>, <a href="../../../com/aspose/slides/Shape.html#isGrouped--">isGrouped</a>, <a href="../../../com/aspose/slides/Shape.html#isTextHolder--">isTextHolder</a>, <a href="../../../com/aspose/slides/Shape.html#removePlaceholder--">removePlaceholder</a>, <a href="../../../com/aspose/slides/Shape.html#setAlternativeText-java.lang.String-">setAlternativeText</a>, <a href="../../../com/aspose/slides/Shape.html#setAlternativeTextTitle-java.lang.String-">setAlternativeTextTitle</a>, <a href="../../../com/aspose/slides/Shape.html#setBlackWhiteMode-byte-">setBlackWhiteMode</a>, <a href="../../../com/aspose/slides/Shape.html#setDecorative-boolean-">setDecorative</a>, <a href="../../../com/aspose/slides/Shape.html#setFrame-com.aspose.slides.IShapeFrame-">setFrame</a>, <a href="../../../com/aspose/slides/Shape.html#setHeight-float-">setHeight</a>, <a href="../../../com/aspose/slides/Shape.html#setHidden-boolean-">setHidden</a>, <a href="../../../com/aspose/slides/Shape.html#setHyperlinkClick-com.aspose.slides.IHyperlink-">setHyperlinkClick</a>, <a href="../../../com/aspose/slides/Shape.html#setHyperlinkMouseOver-com.aspose.slides.IHyperlink-">setHyperlinkMouseOver</a>, <a href="../../../com/aspose/slides/Shape.html#setName-java.lang.String-">setName</a>, <a href="../../../com/aspose/slides/Shape.html#setRawFrame-com.aspose.slides.IShapeFrame-">setRawFrame</a>, <a href="../../../com/aspose/slides/Shape.html#setRotation-float-">setRotation</a>, <a href="../../../com/aspose/slides/Shape.html#setWidth-float-">setWidth</a>, <a href="../../../com/aspose/slides/Shape.html#setX-float-">setX</a>, <a href="../../../com/aspose/slides/Shape.html#setY-float-">setY</a>, <a href="../../../com/aspose/slides/Shape.html#writeAsSvg-java.io.OutputStream-">writeAsSvg</a>, <a href="../../../com/aspose/slides/Shape.html#writeAsSvg-java.io.OutputStream-com.aspose.slides.ISVGOptions-">writeAsSvg</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.aspose.slides.IPictureFrame">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;com.aspose.slides.<a href="../../../com/aspose/slides/IPictureFrame.html" title="interface in com.aspose.slides">IPictureFrame</a></h3>
<code><a href="../../../com/aspose/slides/IPictureFrame.html#getPictureFormat--">getPictureFormat</a>, <a href="../../../com/aspose/slides/IPictureFrame.html#getPictureFrameLock--">getPictureFrameLock</a>, <a href="../../../com/aspose/slides/IPictureFrame.html#getRelativeScaleHeight--">getRelativeScaleHeight</a>, <a href="../../../com/aspose/slides/IPictureFrame.html#getRelativeScaleWidth--">getRelativeScaleWidth</a>, <a href="../../../com/aspose/slides/IPictureFrame.html#setRelativeScaleHeight-float-">setRelativeScaleHeight</a>, <a href="../../../com/aspose/slides/IPictureFrame.html#setRelativeScaleWidth-float-">setRelativeScaleWidth</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.aspose.slides.IGeometryShape">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;com.aspose.slides.<a href="../../../com/aspose/slides/IGeometryShape.html" title="interface in com.aspose.slides">IGeometryShape</a></h3>
<code><a href="../../../com/aspose/slides/IGeometryShape.html#createShapeElements--">createShapeElements</a>, <a href="../../../com/aspose/slides/IGeometryShape.html#getAdjustments--">getAdjustments</a>, <a href="../../../com/aspose/slides/IGeometryShape.html#getGeometryPaths--">getGeometryPaths</a>, <a href="../../../com/aspose/slides/IGeometryShape.html#getShapeStyle--">getShapeStyle</a>, <a href="../../../com/aspose/slides/IGeometryShape.html#getShapeType--">getShapeType</a>, <a href="../../../com/aspose/slides/IGeometryShape.html#setGeometryPath-com.aspose.slides.IGeometryPath-">setGeometryPath</a>, <a href="../../../com/aspose/slides/IGeometryShape.html#setGeometryPaths-com.aspose.slides.IGeometryPath:A-">setGeometryPaths</a>, <a href="../../../com/aspose/slides/IGeometryShape.html#setShapeType-int-">setShapeType</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.aspose.slides.IShape">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;com.aspose.slides.<a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a></h3>
<code><a href="../../../com/aspose/slides/IShape.html#addPlaceholder-com.aspose.slides.IPlaceholder-">addPlaceholder</a>, <a href="../../../com/aspose/slides/IShape.html#getAlternativeText--">getAlternativeText</a>, <a href="../../../com/aspose/slides/IShape.html#getAlternativeTextTitle--">getAlternativeTextTitle</a>, <a href="../../../com/aspose/slides/IShape.html#getBasePlaceholder--">getBasePlaceholder</a>, <a href="../../../com/aspose/slides/IShape.html#getBlackWhiteMode--">getBlackWhiteMode</a>, <a href="../../../com/aspose/slides/IShape.html#getConnectionSiteCount--">getConnectionSiteCount</a>, <a href="../../../com/aspose/slides/IShape.html#getCustomData--">getCustomData</a>, <a href="../../../com/aspose/slides/IShape.html#getEffectFormat--">getEffectFormat</a>, <a href="../../../com/aspose/slides/IShape.html#getFillFormat--">getFillFormat</a>, <a href="../../../com/aspose/slides/IShape.html#getFrame--">getFrame</a>, <a href="../../../com/aspose/slides/IShape.html#getHeight--">getHeight</a>, <a href="../../../com/aspose/slides/IShape.html#getHidden--">getHidden</a>, <a href="../../../com/aspose/slides/IShape.html#getImage--">getImage</a>, <a href="../../../com/aspose/slides/IShape.html#getImage-int-float-float-">getImage</a>, <a href="../../../com/aspose/slides/IShape.html#getLineFormat--">getLineFormat</a>, <a href="../../../com/aspose/slides/IShape.html#getName--">getName</a>, <a href="../../../com/aspose/slides/IShape.html#getOfficeInteropShapeId--">getOfficeInteropShapeId</a>, <a href="../../../com/aspose/slides/IShape.html#getParentGroup--">getParentGroup</a>, <a href="../../../com/aspose/slides/IShape.html#getPlaceholder--">getPlaceholder</a>, <a href="../../../com/aspose/slides/IShape.html#getRawFrame--">getRawFrame</a>, <a href="../../../com/aspose/slides/IShape.html#getRotation--">getRotation</a>, <a href="../../../com/aspose/slides/IShape.html#getShapeLock--">getShapeLock</a>, <a href="../../../com/aspose/slides/IShape.html#getThreeDFormat--">getThreeDFormat</a>, <a href="../../../com/aspose/slides/IShape.html#getThumbnail--">getThumbnail</a>, <a href="../../../com/aspose/slides/IShape.html#getThumbnail-int-float-float-">getThumbnail</a>, <a href="../../../com/aspose/slides/IShape.html#getUniqueId--">getUniqueId</a>, <a href="../../../com/aspose/slides/IShape.html#getWidth--">getWidth</a>, <a href="../../../com/aspose/slides/IShape.html#getX--">getX</a>, <a href="../../../com/aspose/slides/IShape.html#getY--">getY</a>, <a href="../../../com/aspose/slides/IShape.html#getZOrderPosition--">getZOrderPosition</a>, <a href="../../../com/aspose/slides/IShape.html#isDecorative--">isDecorative</a>, <a href="../../../com/aspose/slides/IShape.html#isGrouped--">isGrouped</a>, <a href="../../../com/aspose/slides/IShape.html#isTextHolder--">isTextHolder</a>, <a href="../../../com/aspose/slides/IShape.html#removePlaceholder--">removePlaceholder</a>, <a href="../../../com/aspose/slides/IShape.html#setAlternativeText-java.lang.String-">setAlternativeText</a>, <a href="../../../com/aspose/slides/IShape.html#setAlternativeTextTitle-java.lang.String-">setAlternativeTextTitle</a>, <a href="../../../com/aspose/slides/IShape.html#setBlackWhiteMode-byte-">setBlackWhiteMode</a>, <a href="../../../com/aspose/slides/IShape.html#setDecorative-boolean-">setDecorative</a>, <a href="../../../com/aspose/slides/IShape.html#setFrame-com.aspose.slides.IShapeFrame-">setFrame</a>, <a href="../../../com/aspose/slides/IShape.html#setHeight-float-">setHeight</a>, <a href="../../../com/aspose/slides/IShape.html#setHidden-boolean-">setHidden</a>, <a href="../../../com/aspose/slides/IShape.html#setName-java.lang.String-">setName</a>, <a href="../../../com/aspose/slides/IShape.html#setRawFrame-com.aspose.slides.IShapeFrame-">setRawFrame</a>, <a href="../../../com/aspose/slides/IShape.html#setRotation-float-">setRotation</a>, <a href="../../../com/aspose/slides/IShape.html#setWidth-float-">setWidth</a>, <a href="../../../com/aspose/slides/IShape.html#setX-float-">setX</a>, <a href="../../../com/aspose/slides/IShape.html#setY-float-">setY</a>, <a href="../../../com/aspose/slides/IShape.html#writeAsSvg-java.io.OutputStream-">writeAsSvg</a>, <a href="../../../com/aspose/slides/IShape.html#writeAsSvg-java.io.OutputStream-com.aspose.slides.ISVGOptions-">writeAsSvg</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.aspose.slides.ISlideComponent">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;com.aspose.slides.<a href="../../../com/aspose/slides/ISlideComponent.html" title="interface in com.aspose.slides">ISlideComponent</a></h3>
<code><a href="../../../com/aspose/slides/ISlideComponent.html#getSlide--">getSlide</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.aspose.slides.IPresentationComponent">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;com.aspose.slides.<a href="../../../com/aspose/slides/IPresentationComponent.html" title="interface in com.aspose.slides">IPresentationComponent</a></h3>
<code><a href="../../../com/aspose/slides/IPresentationComponent.html#getPresentation--">getPresentation</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.aspose.slides.IHyperlinkContainer">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;com.aspose.slides.<a href="../../../com/aspose/slides/IHyperlinkContainer.html" title="interface in com.aspose.slides">IHyperlinkContainer</a></h3>
<code><a href="../../../com/aspose/slides/IHyperlinkContainer.html#getHyperlinkClick--">getHyperlinkClick</a>, <a href="../../../com/aspose/slides/IHyperlinkContainer.html#getHyperlinkManager--">getHyperlinkManager</a>, <a href="../../../com/aspose/slides/IHyperlinkContainer.html#getHyperlinkMouseOver--">getHyperlinkMouseOver</a>, <a href="../../../com/aspose/slides/IHyperlinkContainer.html#setHyperlinkClick-com.aspose.slides.IHyperlink-">setHyperlinkClick</a>, <a href="../../../com/aspose/slides/IHyperlinkContainer.html#setHyperlinkMouseOver-com.aspose.slides.IHyperlink-">setHyperlinkMouseOver</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getRewindVideo--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRewindVideo</h4>
<pre>public final&nbsp;boolean&nbsp;getRewindVideo()</pre>
<div class="block"><p>
 Determines whether a video is automatically rewinded to start
 as soon as the movie has finished playing.
 Read/write <code>boolean</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IVideoFrame.html#getRewindVideo--">getRewindVideo</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IVideoFrame.html" title="interface in com.aspose.slides">IVideoFrame</a></code></dd>
</dl>
</li>
</ul>
<a name="setRewindVideo-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRewindVideo</h4>
<pre>public final&nbsp;void&nbsp;setRewindVideo(boolean&nbsp;value)</pre>
<div class="block"><p>
 Determines whether a video is automatically rewinded to start
 as soon as the movie has finished playing.
 Read/write <code>boolean</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IVideoFrame.html#setRewindVideo-boolean-">setRewindVideo</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IVideoFrame.html" title="interface in com.aspose.slides">IVideoFrame</a></code></dd>
</dl>
</li>
</ul>
<a name="getPlayLoopMode--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPlayLoopMode</h4>
<pre>public final&nbsp;boolean&nbsp;getPlayLoopMode()</pre>
<div class="block"><p>
 Determines whether a video is looped.
 Read/write <code>boolean</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IVideoFrame.html#getPlayLoopMode--">getPlayLoopMode</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IVideoFrame.html" title="interface in com.aspose.slides">IVideoFrame</a></code></dd>
</dl>
</li>
</ul>
<a name="setPlayLoopMode-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPlayLoopMode</h4>
<pre>public final&nbsp;void&nbsp;setPlayLoopMode(boolean&nbsp;value)</pre>
<div class="block"><p>
 Determines whether a video is looped.
 Read/write <code>boolean</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IVideoFrame.html#setPlayLoopMode-boolean-">setPlayLoopMode</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IVideoFrame.html" title="interface in com.aspose.slides">IVideoFrame</a></code></dd>
</dl>
</li>
</ul>
<a name="getHideAtShowing--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHideAtShowing</h4>
<pre>public final&nbsp;boolean&nbsp;getHideAtShowing()</pre>
<div class="block"><p>
 Determines whether a VideoFrame is hidden.
 Read/write <code>boolean</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IVideoFrame.html#getHideAtShowing--">getHideAtShowing</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IVideoFrame.html" title="interface in com.aspose.slides">IVideoFrame</a></code></dd>
</dl>
</li>
</ul>
<a name="setHideAtShowing-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setHideAtShowing</h4>
<pre>public final&nbsp;void&nbsp;setHideAtShowing(boolean&nbsp;value)</pre>
<div class="block"><p>
 Determines whether a VideoFrame is hidden.
 Read/write <code>boolean</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IVideoFrame.html#setHideAtShowing-boolean-">setHideAtShowing</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IVideoFrame.html" title="interface in com.aspose.slides">IVideoFrame</a></code></dd>
</dl>
</li>
</ul>
<a name="getVolume--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVolume</h4>
<pre>public final&nbsp;int&nbsp;getVolume()</pre>
<div class="block"><p>
 Returns or sets the audio volume.
 Read/write <a href="../../../com/aspose/slides/AudioVolumeMode.html" title="class in com.aspose.slides"><code>AudioVolumeMode</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IVideoFrame.html#getVolume--">getVolume</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IVideoFrame.html" title="interface in com.aspose.slides">IVideoFrame</a></code></dd>
</dl>
</li>
</ul>
<a name="setVolume-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setVolume</h4>
<pre>public final&nbsp;void&nbsp;setVolume(int&nbsp;value)</pre>
<div class="block"><p>
 Returns or sets the audio volume.
 Read/write <a href="../../../com/aspose/slides/AudioVolumeMode.html" title="class in com.aspose.slides"><code>AudioVolumeMode</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IVideoFrame.html#setVolume-int-">setVolume</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IVideoFrame.html" title="interface in com.aspose.slides">IVideoFrame</a></code></dd>
</dl>
</li>
</ul>
<a name="getPlayMode--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPlayMode</h4>
<pre>public final&nbsp;int&nbsp;getPlayMode()</pre>
<div class="block"><p>
 Returns or sets the video play mode.
 Read/write <a href="../../../com/aspose/slides/VideoPlayModePreset.html" title="class in com.aspose.slides"><code>VideoPlayModePreset</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IVideoFrame.html#getPlayMode--">getPlayMode</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IVideoFrame.html" title="interface in com.aspose.slides">IVideoFrame</a></code></dd>
</dl>
</li>
</ul>
<a name="setPlayMode-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPlayMode</h4>
<pre>public final&nbsp;void&nbsp;setPlayMode(int&nbsp;value)</pre>
<div class="block"><p>
 Returns or sets the video play mode.
 Read/write <a href="../../../com/aspose/slides/VideoPlayModePreset.html" title="class in com.aspose.slides"><code>VideoPlayModePreset</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IVideoFrame.html#setPlayMode-int-">setPlayMode</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IVideoFrame.html" title="interface in com.aspose.slides">IVideoFrame</a></code></dd>
</dl>
</li>
</ul>
<a name="getFullScreenMode--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFullScreenMode</h4>
<pre>public final&nbsp;boolean&nbsp;getFullScreenMode()</pre>
<div class="block"><p>
 Determines whether a video is shown in full screen mode.
 Read/write <code>boolean</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IVideoFrame.html#getFullScreenMode--">getFullScreenMode</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IVideoFrame.html" title="interface in com.aspose.slides">IVideoFrame</a></code></dd>
</dl>
</li>
</ul>
<a name="setFullScreenMode-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFullScreenMode</h4>
<pre>public final&nbsp;void&nbsp;setFullScreenMode(boolean&nbsp;value)</pre>
<div class="block"><p>
 Determines whether a video is shown in full screen mode.
 Read/write <code>boolean</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IVideoFrame.html#setFullScreenMode-boolean-">setFullScreenMode</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IVideoFrame.html" title="interface in com.aspose.slides">IVideoFrame</a></code></dd>
</dl>
</li>
</ul>
<a name="getLinkPathLong--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLinkPathLong</h4>
<pre>public final&nbsp;java.lang.String&nbsp;getLinkPathLong()</pre>
<div class="block"><p>
 Returns or sets the name of an video file which is linked to a VideoFrame.
 Read/write <code>String</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IVideoFrame.html#getLinkPathLong--">getLinkPathLong</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IVideoFrame.html" title="interface in com.aspose.slides">IVideoFrame</a></code></dd>
</dl>
</li>
</ul>
<a name="setLinkPathLong-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLinkPathLong</h4>
<pre>public final&nbsp;void&nbsp;setLinkPathLong(java.lang.String&nbsp;value)</pre>
<div class="block"><p>
 Returns or sets the name of an video file which is linked to a VideoFrame.
 Read/write <code>String</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IVideoFrame.html#setLinkPathLong-java.lang.String-">setLinkPathLong</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IVideoFrame.html" title="interface in com.aspose.slides">IVideoFrame</a></code></dd>
</dl>
</li>
</ul>
<a name="getEmbeddedVideo--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEmbeddedVideo</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IVideo.html" title="interface in com.aspose.slides">IVideo</a>&nbsp;getEmbeddedVideo()</pre>
<div class="block"><p>
 Returns or sets embedded video object.
 Read/write <a href="../../../com/aspose/slides/IVideo.html" title="interface in com.aspose.slides"><code>IVideo</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IVideoFrame.html#getEmbeddedVideo--">getEmbeddedVideo</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IVideoFrame.html" title="interface in com.aspose.slides">IVideoFrame</a></code></dd>
</dl>
</li>
</ul>
<a name="setEmbeddedVideo-com.aspose.slides.IVideo-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEmbeddedVideo</h4>
<pre>public final&nbsp;void&nbsp;setEmbeddedVideo(<a href="../../../com/aspose/slides/IVideo.html" title="interface in com.aspose.slides">IVideo</a>&nbsp;value)</pre>
<div class="block"><p>
 Returns or sets embedded video object.
 Read/write <a href="../../../com/aspose/slides/IVideo.html" title="interface in com.aspose.slides"><code>IVideo</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IVideoFrame.html#setEmbeddedVideo-com.aspose.slides.IVideo-">setEmbeddedVideo</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IVideoFrame.html" title="interface in com.aspose.slides">IVideoFrame</a></code></dd>
</dl>
</li>
</ul>
<a name="getTrimFromStart--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTrimFromStart</h4>
<pre>public final&nbsp;float&nbsp;getTrimFromStart()</pre>
<div class="block"><p>
 Trim start [ms]
 </p><p><hr><blockquote><pre>Example:
 <pre>
 Presentation pres = new Presentation();
 try {
     ISlide slide = pres.getSlides().get_Item(0);
     IVideo video = pres.getVideos().addVideo(Files.readAllBytes(Paths.get("video.mp4")));
     IVideoFrame videoFrame = slide.getShapes().addVideoFrame(0, 0, 100, 100, video);
     //set triming start time 1sec
     videoFrame.setTrimFromStart(1000f);
     //set triming end time 2sec
     videoFrame.setTrimFromEnd(2000f);
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IVideoFrame.html#getTrimFromStart--">getTrimFromStart</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IVideoFrame.html" title="interface in com.aspose.slides">IVideoFrame</a></code></dd>
</dl>
</li>
</ul>
<a name="setTrimFromStart-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTrimFromStart</h4>
<pre>public final&nbsp;void&nbsp;setTrimFromStart(float&nbsp;value)</pre>
<div class="block"><p>
 Trim start [ms]
 </p><p><hr><blockquote><pre>Example:
 <pre>
 Presentation pres = new Presentation();
 try {
     ISlide slide = pres.getSlides().get_Item(0);
     IVideo video = pres.getVideos().addVideo(Files.readAllBytes(Paths.get("video.mp4")));
     IVideoFrame videoFrame = slide.getShapes().addVideoFrame(0, 0, 100, 100, video);
     //set triming start time 1sec
     videoFrame.setTrimFromStart(1000f);
     //set triming end time 2sec
     videoFrame.setTrimFromEnd(2000f);
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IVideoFrame.html#setTrimFromStart-float-">setTrimFromStart</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IVideoFrame.html" title="interface in com.aspose.slides">IVideoFrame</a></code></dd>
</dl>
</li>
</ul>
<a name="getTrimFromEnd--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTrimFromEnd</h4>
<pre>public final&nbsp;float&nbsp;getTrimFromEnd()</pre>
<div class="block"><p>
 Trim end [ms]
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IVideoFrame.html#getTrimFromEnd--">getTrimFromEnd</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IVideoFrame.html" title="interface in com.aspose.slides">IVideoFrame</a></code></dd>
</dl>
</li>
</ul>
<a name="setTrimFromEnd-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTrimFromEnd</h4>
<pre>public final&nbsp;void&nbsp;setTrimFromEnd(float&nbsp;value)</pre>
<div class="block"><p>
 Trim end [ms]
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IVideoFrame.html#setTrimFromEnd-float-">setTrimFromEnd</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IVideoFrame.html" title="interface in com.aspose.slides">IVideoFrame</a></code></dd>
</dl>
</li>
</ul>
<a name="getCaptionTracks--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getCaptionTracks</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/ICaptionsCollection.html" title="interface in com.aspose.slides">ICaptionsCollection</a>&nbsp;getCaptionTracks()</pre>
<div class="block"><p>
  Returns the closed captions collection of the video.
  Read-only <a href="../../../com/aspose/slides/ICaptionsCollection.html" title="interface in com.aspose.slides"><code>ICaptionsCollection</code></a>.
  </p><p><hr><blockquote><pre>Example:
  <pre>
 Presentation pres = new Presentation("video with captions.pptx");
 try {
     for (IShape shape : pres.getSlides().get_Item(0).getShapes())
     {
         if (!(shape instanceof IVideoFrame))
             continue;
         IVideoFrame videoFrame = (IVideoFrame) shape;
         for (ICaptions captionTrack : videoFrame.getCaptionTracks())
         {
             // Extracts the captions binary data and saves them to the file
             FileOutputStream fos = new FileOutputStream(captionTrack.getCaptionId() + ".vtt");
             fos.write(captionTrack.getBinaryData());
             fos.close();
         }
     }
 } finally {
     if (pres != null) pres.dispose();
 }
  </pre>
  </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IVideoFrame.html#getCaptionTracks--">getCaptionTracks</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IVideoFrame.html" title="interface in com.aspose.slides">IVideoFrame</a></code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/VideoCollection.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/VideoPlayerHtmlController.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/VideoFrame.html" target="_top">Frames</a></li>
<li><a href="VideoFrame.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><i>Copyright &copy; 2004-2025 Aspose Pty Ltd. All Rights Reserved.</i></small></p>
</body>
</html>
