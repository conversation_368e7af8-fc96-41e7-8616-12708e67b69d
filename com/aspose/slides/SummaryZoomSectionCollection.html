<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_421) on Fri Jul 11 05:32:44 CDT 2025 -->
<title>SummaryZoomSectionCollection (Aspose.Slides for Java (25.7) API Documentation)</title>
<meta name="date" content="2025-07-11">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="SummaryZoomSectionCollection (Aspose.Slides for Java (25.7) API Documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/SummaryZoomSection.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/SvgCoordinateUnit.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/SummaryZoomSectionCollection.html" target="_top">Frames</a></li>
<li><a href="SummaryZoomSectionCollection.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.aspose.slides</div>
<h2 title="Class SummaryZoomSectionCollection" class="title">Class SummaryZoomSectionCollection</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../com/aspose/slides/DomObject.html" title="class in com.aspose.slides">com.aspose.slides.DomObject</a>&lt;<a href="../../../com/aspose/slides/SummaryZoomFrame.html" title="class in com.aspose.slides">SummaryZoomFrame</a>&gt;</li>
<li>
<ul class="inheritance">
<li>com.aspose.slides.SummaryZoomSectionCollection</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd>com.aspose.ms.System.Collections.Generic.IGenericEnumerable&lt;<a href="../../../com/aspose/slides/ISummaryZoomSection.html" title="interface in com.aspose.slides">ISummaryZoomSection</a>&gt;, com.aspose.ms.System.Collections.ICollection&lt;<a href="../../../com/aspose/slides/ISummaryZoomSection.html" title="interface in com.aspose.slides">ISummaryZoomSection</a>&gt;, com.aspose.ms.System.Collections.IEnumerable&lt;<a href="../../../com/aspose/slides/ISummaryZoomSection.html" title="interface in com.aspose.slides">ISummaryZoomSection</a>&gt;, <a href="../../../com/aspose/slides/IGenericCollection.html" title="interface in com.aspose.slides">IGenericCollection</a>&lt;<a href="../../../com/aspose/slides/ISummaryZoomSection.html" title="interface in com.aspose.slides">ISummaryZoomSection</a>&gt;, <a href="../../../com/aspose/slides/ISummaryZoomSectionCollection.html" title="interface in com.aspose.slides">ISummaryZoomSectionCollection</a>, java.lang.Iterable&lt;<a href="../../../com/aspose/slides/ISummaryZoomSection.html" title="interface in com.aspose.slides">ISummaryZoomSection</a>&gt;</dd>
</dl>
<hr>
<br>
<pre>public final class <span class="typeNameLabel">SummaryZoomSectionCollection</span>
extends <a href="../../../com/aspose/slides/DomObject.html" title="class in com.aspose.slides">DomObject</a>&lt;<a href="../../../com/aspose/slides/SummaryZoomFrame.html" title="class in com.aspose.slides">SummaryZoomFrame</a>&gt;
implements <a href="../../../com/aspose/slides/ISummaryZoomSectionCollection.html" title="interface in com.aspose.slides">ISummaryZoomSectionCollection</a></pre>
<div class="block"><p>
 Represents a collection of Summary Zoom Section objects.
 </p></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/ISummaryZoomSection.html" title="interface in com.aspose.slides">ISummaryZoomSection</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SummaryZoomSectionCollection.html#addSummaryZoomSection-com.aspose.slides.ISection-">addSummaryZoomSection</a></span>(<a href="../../../com/aspose/slides/ISection.html" title="interface in com.aspose.slides">ISection</a>&nbsp;section)</code>
<div class="block">
 Creates new Summary Zoom Section object and add it to the collection</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SummaryZoomSectionCollection.html#clear--">clear</a></span>()</code>
<div class="block">
 Removes all SummaryZoomSection objects from the collection.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SummaryZoomSectionCollection.html#copyTo-com.aspose.ms.System.Array-int-">copyTo</a></span>(com.aspose.ms.System.Array&nbsp;array,
      int&nbsp;index)</code>
<div class="block">
 Copies the entire collection to the specified array.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/ISummaryZoomSection.html" title="interface in com.aspose.slides">ISummaryZoomSection</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SummaryZoomSectionCollection.html#get_Item-int-">get_Item</a></span>(int&nbsp;index)</code>
<div class="block">
 Gets the element at the specified index.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/ISummaryZoomSection.html" title="interface in com.aspose.slides">ISummaryZoomSection</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SummaryZoomSectionCollection.html#getSummarySection-com.aspose.slides.ISection-">getSummarySection</a></span>(<a href="../../../com/aspose/slides/ISection.html" title="interface in com.aspose.slides">ISection</a>&nbsp;section)</code>
<div class="block">
 Returns Summary Zoom Section element for the given section.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>java.lang.Object</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SummaryZoomSectionCollection.html#getSyncRoot--">getSyncRoot</a></span>()</code>
<div class="block">
 Returns a synchronization root.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SummaryZoomSectionCollection.html#indexOf-com.aspose.slides.ISummaryZoomSection-">indexOf</a></span>(<a href="../../../com/aspose/slides/ISummaryZoomSection.html" title="interface in com.aspose.slides">ISummaryZoomSection</a>&nbsp;summaryZoomSection)</code>
<div class="block">
 Returns an index of the specified SummaryZoomSection object.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SummaryZoomSectionCollection.html#isSynchronized--">isSynchronized</a></span>()</code>
<div class="block">
 Returns a value indicating whether access to the collection is synchronized (thread-safe).</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>com.aspose.ms.System.Collections.Generic.IGenericEnumerator&lt;<a href="../../../com/aspose/slides/ISummaryZoomSection.html" title="interface in com.aspose.slides">ISummaryZoomSection</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SummaryZoomSectionCollection.html#iterator--">iterator</a></span>()</code>
<div class="block">
 Returns an enumerator that iterates through the collection.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>com.aspose.ms.System.Collections.Generic.IGenericEnumerator&lt;<a href="../../../com/aspose/slides/ISummaryZoomSection.html" title="interface in com.aspose.slides">ISummaryZoomSection</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SummaryZoomSectionCollection.html#iteratorJava--">iteratorJava</a></span>()</code>
<div class="block">
 Returns a java iterator for the entire collection.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SummaryZoomSectionCollection.html#removeSummaryZoomSection-com.aspose.slides.ISection-">removeSummaryZoomSection</a></span>(<a href="../../../com/aspose/slides/ISection.html" title="interface in com.aspose.slides">ISection</a>&nbsp;section)</code>
<div class="block">
 Remove Summary Zoom Section object from the collection.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SummaryZoomSectionCollection.html#size--">size</a></span>()</code>
<div class="block">
 Gets the number of elements actually contained in the collection.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.aspose.slides.DomObject">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;com.aspose.slides.<a href="../../../com/aspose/slides/DomObject.html" title="class in com.aspose.slides">DomObject</a></h3>
<code><a href="../../../com/aspose/slides/DomObject.html#getParent_Immediate--">getParent_Immediate</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Iterable">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;java.lang.Iterable</h3>
<code>forEach, spliterator</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="get_Item-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_Item</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/ISummaryZoomSection.html" title="interface in com.aspose.slides">ISummaryZoomSection</a>&nbsp;get_Item(int&nbsp;index)</pre>
<div class="block"><p>
 Gets the element at the specified index.
 Read-only <a href="../../../com/aspose/slides/ISummaryZoomSection.html" title="interface in com.aspose.slides"><code>ISummaryZoomSection</code></a>.
 </p><p><hr><blockquote><pre>The example demonstrates getting Summary Zoom Section element by index:
 <pre>
 Presentation pres = new Presentation("pres.pptx");
 try {
      ISummaryZoomFrame zoomFrame = (ISummaryZoomFrame)pres.getSlides().get_Item(1).getShapes().get_Item(0);
      ISummaryZoomSectionCollection collection = zoomFrame.getSummaryZoomCollection();
      ISummaryZoomSection zoomSection = collection.get_Item(1);
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISummaryZoomSectionCollection.html#get_Item-int-">get_Item</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISummaryZoomSectionCollection.html" title="interface in com.aspose.slides">ISummaryZoomSectionCollection</a></code></dd>
</dl>
</li>
</ul>
<a name="addSummaryZoomSection-com.aspose.slides.ISection-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addSummaryZoomSection</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/ISummaryZoomSection.html" title="interface in com.aspose.slides">ISummaryZoomSection</a>&nbsp;addSummaryZoomSection(<a href="../../../com/aspose/slides/ISection.html" title="interface in com.aspose.slides">ISection</a>&nbsp;section)</pre>
<div class="block"><p>
 Creates new Summary Zoom Section object and add it to the collection
 </p><p><hr><blockquote><pre>The example demonstrates getting Summary Zoom Section element by index:
 <pre>
 Presentation pres = new Presentation("pres.pptx");
 try {
      ISummaryZoomFrame zoomFrame = (ISummaryZoomFrame)pres.getSlides().get_Item(1).getShapes().get_Item(0);
      ISummaryZoomSectionCollection collection = zoomFrame.getSummaryZoomCollection();
      ISummaryZoomSection newZoomSection = collection.addSummaryZoomSection(pres.getSections().get_Item(3));
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISummaryZoomSectionCollection.html#addSummaryZoomSection-com.aspose.slides.ISection-">addSummaryZoomSection</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISummaryZoomSectionCollection.html" title="interface in com.aspose.slides">ISummaryZoomSectionCollection</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>section</code> - Section for a new Summary Zoom Section element <a href="../../../com/aspose/slides/ISection.html" title="interface in com.aspose.slides"><code>ISection</code></a>
 <p><hr>
 If an element for this section already exists in the collection, the existing element is returned.
 </hr></p></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Added <a href="../../../com/aspose/slides/ISummaryZoomFrame.html" title="interface in com.aspose.slides"><code>ISummaryZoomFrame</code></a> element</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>com.aspose.ms.System.ArgumentException</code> - Referenced section does not belong to the current presentation or does not contains any slides.</dd>
</dl>
</li>
</ul>
<a name="size--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>size</h4>
<pre>public final&nbsp;int&nbsp;size()</pre>
<div class="block"><p>
 Gets the number of elements actually contained in the collection.
 Read-only <code>int</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>size</code>&nbsp;in interface&nbsp;<code>com.aspose.ms.System.Collections.ICollection&lt;<a href="../../../com/aspose/slides/ISummaryZoomSection.html" title="interface in com.aspose.slides">ISummaryZoomSection</a>&gt;</code></dd>
</dl>
</li>
</ul>
<a name="indexOf-com.aspose.slides.ISummaryZoomSection-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>indexOf</h4>
<pre>public final&nbsp;int&nbsp;indexOf(<a href="../../../com/aspose/slides/ISummaryZoomSection.html" title="interface in com.aspose.slides">ISummaryZoomSection</a>&nbsp;summaryZoomSection)</pre>
<div class="block"><p>
 Returns an index of the specified SummaryZoomSection object.
 </p><p><hr><blockquote><pre>The example demonstrates getting Summary Zoom Section element by index:
 <pre>
 Presentation pres = new Presentation("pres.pptx");
 try {
      ISummaryZoomFrame zoomFrame = (ISummaryZoomFrame)pres.getSlides().get_Item(1).getShapes().get_Item(0);
      ISummaryZoomSectionCollection collection = zoomFrame.getSummaryZoomCollection();
      ISummaryZoomSection selectedObject = collection.getSummarySection(pres.getSections().get_Item(2));
      int idx = collection.indexOf(selectedObject);
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISummaryZoomSectionCollection.html#indexOf-com.aspose.slides.ISummaryZoomSection-">indexOf</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISummaryZoomSectionCollection.html" title="interface in com.aspose.slides">ISummaryZoomSectionCollection</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>summaryZoomSection</code> - SummaryZoomSection object to find <a href="../../../com/aspose/slides/ISummaryZoomSection.html" title="interface in com.aspose.slides"><code>ISummaryZoomSection</code></a>.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Index of a SummaryZoomSection object or -1 if SummaryZoomSection object not from this collection.</dd>
</dl>
</li>
</ul>
<a name="removeSummaryZoomSection-com.aspose.slides.ISection-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>removeSummaryZoomSection</h4>
<pre>public final&nbsp;void&nbsp;removeSummaryZoomSection(<a href="../../../com/aspose/slides/ISection.html" title="interface in com.aspose.slides">ISection</a>&nbsp;section)</pre>
<div class="block"><p>
 Remove Summary Zoom Section object from the collection.
 </p><p><hr><blockquote><pre>The example demonstrates getting Summary Zoom Section element by index:
 <pre>
 Presentation pres = new Presentation("pres.pptx");
 try {
      ISummaryZoomFrame zoomFrame = (ISummaryZoomFrame)pres.getSlides().get_Item(1).getShapes().get_Item(0);
      ISummaryZoomSectionCollection collection = zoomFrame.getSummaryZoomCollection();
      collection.removeSummaryZoomSection(pres.getSections().get_Item(1));
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISummaryZoomSectionCollection.html#removeSummaryZoomSection-com.aspose.slides.ISection-">removeSummaryZoomSection</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISummaryZoomSectionCollection.html" title="interface in com.aspose.slides">ISummaryZoomSectionCollection</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>section</code> - Section for which the Summary Zoom Section element is to be removed <a href="../../../com/aspose/slides/ISection.html" title="interface in com.aspose.slides"><code>ISection</code></a>.</dd>
</dl>
</li>
</ul>
<a name="getSummarySection-com.aspose.slides.ISection-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSummarySection</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/ISummaryZoomSection.html" title="interface in com.aspose.slides">ISummaryZoomSection</a>&nbsp;getSummarySection(<a href="../../../com/aspose/slides/ISection.html" title="interface in com.aspose.slides">ISection</a>&nbsp;section)</pre>
<div class="block"><p>
 Returns Summary Zoom Section element for the given section.
 </p><p><hr><blockquote><pre>The example demonstrates getting Summary Zoom Section element by index:
 <pre>
 Presentation pres = new Presentation("pres.pptx");
 try {
      ISummaryZoomFrame zoomFrame = (ISummaryZoomFrame)pres.getSlides().get_Item(1).getShapes().get_Item(0);
      ISummaryZoomSectionCollection collection = zoomFrame.getSummaryZoomCollection();
      ISummaryZoomSection selectedObject = collection.getSummarySection(pres.getSections().get_Item(2));
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISummaryZoomSectionCollection.html#getSummarySection-com.aspose.slides.ISection-">getSummarySection</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISummaryZoomSectionCollection.html" title="interface in com.aspose.slides">ISummaryZoomSectionCollection</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>section</code> - Section to find <a href="../../../com/aspose/slides/ISection.html" title="interface in com.aspose.slides"><code>ISection</code></a></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd><a href="../../../com/aspose/slides/ISummaryZoomSection.html" title="interface in com.aspose.slides"><code>ISummaryZoomSection</code></a> or null if collection does not contains element for the section.</dd>
</dl>
</li>
</ul>
<a name="clear--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>clear</h4>
<pre>public final&nbsp;void&nbsp;clear()</pre>
<div class="block"><p>
 Removes all SummaryZoomSection objects from the collection.
 </p><p><hr><blockquote><pre>The example demonstrates getting Summary Zoom Section element by index:
 <pre>
 Presentation pres = new Presentation("pres.pptx");
 try {
      ISummaryZoomFrame zoomFrame = (ISummaryZoomFrame)pres.getSlides().get_Item(1).getShapes().get_Item(0);
      ISummaryZoomSectionCollection collection = zoomFrame.getSummaryZoomCollection();
      collection.clear();
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISummaryZoomSectionCollection.html#clear--">clear</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISummaryZoomSectionCollection.html" title="interface in com.aspose.slides">ISummaryZoomSectionCollection</a></code></dd>
</dl>
</li>
</ul>
<a name="copyTo-com.aspose.ms.System.Array-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>copyTo</h4>
<pre>public final&nbsp;void&nbsp;copyTo(com.aspose.ms.System.Array&nbsp;array,
                         int&nbsp;index)</pre>
<div class="block"><p>
 Copies the entire collection to the specified array.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>copyTo</code>&nbsp;in interface&nbsp;<code>com.aspose.ms.System.Collections.ICollection&lt;<a href="../../../com/aspose/slides/ISummaryZoomSection.html" title="interface in com.aspose.slides">ISummaryZoomSection</a>&gt;</code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>array</code> - Target array</dd>
<dd><code>index</code> - Index in the target array.</dd>
</dl>
</li>
</ul>
<a name="isSynchronized--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isSynchronized</h4>
<pre>public final&nbsp;boolean&nbsp;isSynchronized()</pre>
<div class="block"><p>
 Returns a value indicating whether access to the collection is synchronized (thread-safe).
 Read-only <code>boolean</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>isSynchronized</code>&nbsp;in interface&nbsp;<code>com.aspose.ms.System.Collections.ICollection&lt;<a href="../../../com/aspose/slides/ISummaryZoomSection.html" title="interface in com.aspose.slides">ISummaryZoomSection</a>&gt;</code></dd>
</dl>
</li>
</ul>
<a name="getSyncRoot--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSyncRoot</h4>
<pre>public final&nbsp;java.lang.Object&nbsp;getSyncRoot()</pre>
<div class="block"><p>
 Returns a synchronization root.
 Read-only <code>Object</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>getSyncRoot</code>&nbsp;in interface&nbsp;<code>com.aspose.ms.System.Collections.ICollection&lt;<a href="../../../com/aspose/slides/ISummaryZoomSection.html" title="interface in com.aspose.slides">ISummaryZoomSection</a>&gt;</code></dd>
</dl>
</li>
</ul>
<a name="iterator--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>iterator</h4>
<pre>public final&nbsp;com.aspose.ms.System.Collections.Generic.IGenericEnumerator&lt;<a href="../../../com/aspose/slides/ISummaryZoomSection.html" title="interface in com.aspose.slides">ISummaryZoomSection</a>&gt;&nbsp;iterator()</pre>
<div class="block"><p>
 Returns an enumerator that iterates through the collection.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>iterator</code>&nbsp;in interface&nbsp;<code>com.aspose.ms.System.Collections.Generic.IGenericEnumerable&lt;<a href="../../../com/aspose/slides/ISummaryZoomSection.html" title="interface in com.aspose.slides">ISummaryZoomSection</a>&gt;</code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>iterator</code>&nbsp;in interface&nbsp;<code>com.aspose.ms.System.Collections.IEnumerable&lt;<a href="../../../com/aspose/slides/ISummaryZoomSection.html" title="interface in com.aspose.slides">ISummaryZoomSection</a>&gt;</code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>iterator</code>&nbsp;in interface&nbsp;<code>java.lang.Iterable&lt;<a href="../../../com/aspose/slides/ISummaryZoomSection.html" title="interface in com.aspose.slides">ISummaryZoomSection</a>&gt;</code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>A <code>IGenericEnumerator</code> that can be used to iterate through the collection.</dd>
</dl>
</li>
</ul>
<a name="iteratorJava--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>iteratorJava</h4>
<pre>public final&nbsp;com.aspose.ms.System.Collections.Generic.IGenericEnumerator&lt;<a href="../../../com/aspose/slides/ISummaryZoomSection.html" title="interface in com.aspose.slides">ISummaryZoomSection</a>&gt;&nbsp;iteratorJava()</pre>
<div class="block"><p>
 Returns a java iterator for the entire collection.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IGenericCollection.html#iteratorJava--">iteratorJava</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IGenericCollection.html" title="interface in com.aspose.slides">IGenericCollection</a>&lt;<a href="../../../com/aspose/slides/ISummaryZoomSection.html" title="interface in com.aspose.slides">ISummaryZoomSection</a>&gt;</code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>An <code>Iterator</code> for the entire collection.</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/SummaryZoomSection.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/SvgCoordinateUnit.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/SummaryZoomSectionCollection.html" target="_top">Frames</a></li>
<li><a href="SummaryZoomSectionCollection.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><i>Copyright &copy; 2004-2025 Aspose Pty Ltd. All Rights Reserved.</i></small></p>
</body>
</html>
