<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_421) on Fri Jul 11 05:32:44 CDT 2025 -->
<title>Shape<PERSON>rame (Aspose.Slides for Java (25.7) API Documentation)</title>
<meta name="date" content="2025-07-11">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ShapeFrame (Aspose.Slides for Java (25.7) API Documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/ShapeElementStrokeSource.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/ShapesAlignmentType.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/ShapeFrame.html" target="_top">Frames</a></li>
<li><a href="ShapeFrame.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.aspose.slides</div>
<h2 title="Class ShapeFrame" class="title">Class ShapeFrame</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.aspose.slides.ShapeFrame</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../com/aspose/slides/IGenericCloneable.html" title="interface in com.aspose.slides">IGenericCloneable</a>&lt;<a href="../../../com/aspose/slides/IShapeFrame.html" title="interface in com.aspose.slides">IShapeFrame</a>&gt;, <a href="../../../com/aspose/slides/IShapeFrame.html" title="interface in com.aspose.slides">IShapeFrame</a></dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">ShapeFrame</span>
extends java.lang.Object
implements <a href="../../../com/aspose/slides/IShapeFrame.html" title="interface in com.aspose.slides">IShapeFrame</a></pre>
<div class="block"><p>
 Represents shape frame's properties.
 </p></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeFrame.html#ShapeFrame-float-float-float-float-byte-byte-float-">ShapeFrame</a></span>(float&nbsp;x,
          float&nbsp;y,
          float&nbsp;width,
          float&nbsp;height,
          byte&nbsp;flipH,
          byte&nbsp;flipV,
          float&nbsp;rotationAngle)</code>
<div class="block">
 Creates new shape frame's properties.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IShapeFrame.html" title="interface in com.aspose.slides">IShapeFrame</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeFrame.html#cloneT--">cloneT</a></span>()</code>
<div class="block">
 Clones.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>java.lang.Object</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeFrame.html#deepClone--">deepClone</a></span>()</code>
<div class="block">
 Clones</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeFrame.html#equals-java.lang.Object-">equals</a></span>(java.lang.Object&nbsp;obj)</code>
<div class="block">
 Returns a value indicating whether this instance is equal to a specified object.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeFrame.html#equals-com.aspose.slides.ShapeFrame-">equals</a></span>(<a href="../../../com/aspose/slides/ShapeFrame.html" title="class in com.aspose.slides">ShapeFrame</a>&nbsp;value)</code>
<div class="block">
 Returns a value indicating whether this instance is equal to a specified object.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeFrame.html#getCenterX--">getCenterX</a></span>()</code>
<div class="block">
 Returns the X coordinate of a frame's center.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeFrame.html#getCenterY--">getCenterY</a></span>()</code>
<div class="block">
 Returns the Y coordinate of a frame's center.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>byte</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeFrame.html#getFlipH--">getFlipH</a></span>()</code>
<div class="block">
 Determines whether a frame is flipped horizontally.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>byte</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeFrame.html#getFlipV--">getFlipV</a></span>()</code>
<div class="block">
 Determines whether a frame is flipped vertically.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeFrame.html#getHeight--">getHeight</a></span>()</code>
<div class="block">
 Returns the height of a frame.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>java.awt.geom.Rectangle2D.Float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeFrame.html#getRectangle--">getRectangle</a></span>()</code>
<div class="block">
 Returns the coordinates of a frame.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeFrame.html#getRotation--">getRotation</a></span>()</code>
<div class="block">
 Returns the number of degrees a frame is rotated around the z-axis.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeFrame.html#getWidth--">getWidth</a></span>()</code>
<div class="block">
 Returns the width of a frame.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeFrame.html#getX--">getX</a></span>()</code>
<div class="block">
 Returns the X coordinate of the upper-left corner of a frame.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeFrame.html#getY--">getY</a></span>()</code>
<div class="block">
 Returns the Y coordinate of the upper-left corner of a frame.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeFrame.html#hashCode--">hashCode</a></span>()</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>getClass, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="ShapeFrame-float-float-float-float-byte-byte-float-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>ShapeFrame</h4>
<pre>public&nbsp;ShapeFrame(float&nbsp;x,
                  float&nbsp;y,
                  float&nbsp;width,
                  float&nbsp;height,
                  byte&nbsp;flipH,
                  byte&nbsp;flipV,
                  float&nbsp;rotationAngle)</pre>
<div class="block"><p>
 Creates new shape frame's properties.
 </p></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>x</code> - X coordinate of a frame.</dd>
<dd><code>y</code> - Y coordinate of a frame.</dd>
<dd><code>width</code> - Width of a frame.</dd>
<dd><code>height</code> - Height of a frame.</dd>
<dd><code>flipH</code> - True if a frame flipped horizontally.</dd>
<dd><code>flipV</code> - True if a frame flipped vertivally.</dd>
<dd><code>rotationAngle</code> - Number of degrees a frame is rotated.</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getX--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getX</h4>
<pre>public final&nbsp;float&nbsp;getX()</pre>
<div class="block"><p>
 Returns the X coordinate of the upper-left corner of a frame.
 Read-only <code>float</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShapeFrame.html#getX--">getX</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShapeFrame.html" title="interface in com.aspose.slides">IShapeFrame</a></code></dd>
</dl>
</li>
</ul>
<a name="getY--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getY</h4>
<pre>public final&nbsp;float&nbsp;getY()</pre>
<div class="block"><p>
 Returns the Y coordinate of the upper-left corner of a frame.
 Read-only <code>float</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShapeFrame.html#getY--">getY</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShapeFrame.html" title="interface in com.aspose.slides">IShapeFrame</a></code></dd>
</dl>
</li>
</ul>
<a name="getWidth--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWidth</h4>
<pre>public final&nbsp;float&nbsp;getWidth()</pre>
<div class="block"><p>
 Returns the width of a frame.
 Read-only <code>float</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShapeFrame.html#getWidth--">getWidth</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShapeFrame.html" title="interface in com.aspose.slides">IShapeFrame</a></code></dd>
</dl>
</li>
</ul>
<a name="getHeight--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHeight</h4>
<pre>public final&nbsp;float&nbsp;getHeight()</pre>
<div class="block"><p>
 Returns the height of a frame.
 Read-only <code>float</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShapeFrame.html#getHeight--">getHeight</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShapeFrame.html" title="interface in com.aspose.slides">IShapeFrame</a></code></dd>
</dl>
</li>
</ul>
<a name="getRotation--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRotation</h4>
<pre>public final&nbsp;float&nbsp;getRotation()</pre>
<div class="block"><p>
 Returns the number of degrees a frame is rotated around the z-axis.
 A positive value indicates clockwise rotation; a negative value
 indicates counterclockwise rotation.
 Read-only <code>float</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShapeFrame.html#getRotation--">getRotation</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShapeFrame.html" title="interface in com.aspose.slides">IShapeFrame</a></code></dd>
</dl>
</li>
</ul>
<a name="getCenterX--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCenterX</h4>
<pre>public final&nbsp;float&nbsp;getCenterX()</pre>
<div class="block"><p>
 Returns the X coordinate of a frame's center.
 Read-only <code>float</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShapeFrame.html#getCenterX--">getCenterX</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShapeFrame.html" title="interface in com.aspose.slides">IShapeFrame</a></code></dd>
</dl>
</li>
</ul>
<a name="getCenterY--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCenterY</h4>
<pre>public final&nbsp;float&nbsp;getCenterY()</pre>
<div class="block"><p>
 Returns the Y coordinate of a frame's center.
 Read-only <code>float</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShapeFrame.html#getCenterY--">getCenterY</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShapeFrame.html" title="interface in com.aspose.slides">IShapeFrame</a></code></dd>
</dl>
</li>
</ul>
<a name="getFlipH--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFlipH</h4>
<pre>public final&nbsp;byte&nbsp;getFlipH()</pre>
<div class="block"><p>
 Determines whether a frame is flipped horizontally.
 Read-only <a href="../../../com/aspose/slides/NullableBool.html" title="class in com.aspose.slides"><code>NullableBool</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShapeFrame.html#getFlipH--">getFlipH</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShapeFrame.html" title="interface in com.aspose.slides">IShapeFrame</a></code></dd>
</dl>
</li>
</ul>
<a name="getFlipV--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFlipV</h4>
<pre>public final&nbsp;byte&nbsp;getFlipV()</pre>
<div class="block"><p>
 Determines whether a frame is flipped vertically.
 Read-only <a href="../../../com/aspose/slides/NullableBool.html" title="class in com.aspose.slides"><code>NullableBool</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShapeFrame.html#getFlipV--">getFlipV</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShapeFrame.html" title="interface in com.aspose.slides">IShapeFrame</a></code></dd>
</dl>
</li>
</ul>
<a name="getRectangle--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRectangle</h4>
<pre>public final&nbsp;java.awt.geom.Rectangle2D.Float&nbsp;getRectangle()</pre>
<div class="block"><p>
 Returns the coordinates of a frame.
 Read-only <code>Rectangle2D.Float</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShapeFrame.html#getRectangle--">getRectangle</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShapeFrame.html" title="interface in com.aspose.slides">IShapeFrame</a></code></dd>
</dl>
</li>
</ul>
<a name="deepClone--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>deepClone</h4>
<pre>public final&nbsp;java.lang.Object&nbsp;deepClone()</pre>
<div class="block"><p>
 Clones
 </p></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Cloned shape frame.</dd>
</dl>
</li>
</ul>
<a name="cloneT--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cloneT</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IShapeFrame.html" title="interface in com.aspose.slides">IShapeFrame</a>&nbsp;cloneT()</pre>
<div class="block"><p>
 Clones.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IGenericCloneable.html#cloneT--">cloneT</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IGenericCloneable.html" title="interface in com.aspose.slides">IGenericCloneable</a>&lt;<a href="../../../com/aspose/slides/IShapeFrame.html" title="interface in com.aspose.slides">IShapeFrame</a>&gt;</code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Cloned shape frame.</dd>
</dl>
</li>
</ul>
<a name="hashCode--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>hashCode</h4>
<pre>public&nbsp;int&nbsp;hashCode()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code>hashCode</code>&nbsp;in class&nbsp;<code>java.lang.Object</code></dd>
</dl>
</li>
</ul>
<a name="equals-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>equals</h4>
<pre>public&nbsp;boolean&nbsp;equals(java.lang.Object&nbsp;obj)</pre>
<div class="block"><p>
 Returns a value indicating whether this instance is equal to a specified object.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code>equals</code>&nbsp;in class&nbsp;<code>java.lang.Object</code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>obj</code> - The object to compare with this instance.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd><b>true</b> if obj is a ShapeFrame that has the same value as this instance; otherwise, <b>false</b>.</dd>
</dl>
</li>
</ul>
<a name="equals-com.aspose.slides.ShapeFrame-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>equals</h4>
<pre>public final&nbsp;boolean&nbsp;equals(<a href="../../../com/aspose/slides/ShapeFrame.html" title="class in com.aspose.slides">ShapeFrame</a>&nbsp;value)</pre>
<div class="block"><p>
 Returns a value indicating whether this instance is equal to a specified object.
 </p></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - The ShapeFRameEx to compare with this instance.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd><b>true</b> if value is a ShapeFrame that has the same value as this instance; otherwise, <b>false</b>.</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/ShapeElementStrokeSource.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/ShapesAlignmentType.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/ShapeFrame.html" target="_top">Frames</a></li>
<li><a href="ShapeFrame.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><i>Copyright &copy; 2004-2025 Aspose Pty Ltd. All Rights Reserved.</i></small></p>
</body>
</html>
