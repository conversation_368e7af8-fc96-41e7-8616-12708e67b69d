<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_421) on Fri Jul 11 05:32:44 CDT 2025 -->
<title>TextToHtmlConversionOptions (Aspose.Slides for Java (25.7) API Documentation)</title>
<meta name="date" content="2025-07-11">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="TextToHtmlConversionOptions (Aspose.Slides for Java (25.7) API Documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/TextStyle.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/TextUnderlineType.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/TextToHtmlConversionOptions.html" target="_top">Frames</a></li>
<li><a href="TextToHtmlConversionOptions.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.aspose.slides</div>
<h2 title="Class TextToHtmlConversionOptions" class="title">Class TextToHtmlConversionOptions</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.aspose.slides.TextToHtmlConversionOptions</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../com/aspose/slides/ITextToHtmlConversionOptions.html" title="interface in com.aspose.slides">ITextToHtmlConversionOptions</a></dd>
</dl>
<hr>
<br>
<pre>public final class <span class="typeNameLabel">TextToHtmlConversionOptions</span>
extends java.lang.Object
implements <a href="../../../com/aspose/slides/ITextToHtmlConversionOptions.html" title="interface in com.aspose.slides">ITextToHtmlConversionOptions</a></pre>
<div class="block"><p>
 Options for extracting HTML from the Pptx text.
 </p></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TextToHtmlConversionOptions.html#TextToHtmlConversionOptions--">TextToHtmlConversionOptions</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TextToHtmlConversionOptions.html#getAddClipboardFragmentHeader--">getAddClipboardFragmentHeader</a></span>()</code>
<div class="block">
 Returns or sets value, indicating if Clipboard headers should be added.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TextToHtmlConversionOptions.html#getEncodingName--">getEncodingName</a></span>()</code>
<div class="block">
 Returns or sets html encoding name.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/ILinkEmbedController.html" title="interface in com.aspose.slides">ILinkEmbedController</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TextToHtmlConversionOptions.html#getLinkEmbedController--">getLinkEmbedController</a></span>()</code>
<div class="block">
 Returns or sets a callback object which controlls how external object will be stored.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TextToHtmlConversionOptions.html#getTextInheritanceLimit--">getTextInheritanceLimit</a></span>()</code>
<div class="block">
 Returns or sets inhering depth for text properties.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TextToHtmlConversionOptions.html#setAddClipboardFragmentHeader-boolean-">setAddClipboardFragmentHeader</a></span>(boolean&nbsp;value)</code>
<div class="block">
 Returns or sets value, indicating if Clipboard headers should be added.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TextToHtmlConversionOptions.html#setEncodingName-java.lang.String-">setEncodingName</a></span>(java.lang.String&nbsp;value)</code>
<div class="block">
 Returns or sets html encoding name.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TextToHtmlConversionOptions.html#setLinkEmbedController-com.aspose.slides.ILinkEmbedController-">setLinkEmbedController</a></span>(<a href="../../../com/aspose/slides/ILinkEmbedController.html" title="interface in com.aspose.slides">ILinkEmbedController</a>&nbsp;value)</code>
<div class="block">
 Returns or sets a callback object which controlls how external object will be stored.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TextToHtmlConversionOptions.html#setTextInheritanceLimit-int-">setTextInheritanceLimit</a></span>(int&nbsp;value)</code>
<div class="block">
 Returns or sets inhering depth for text properties.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="TextToHtmlConversionOptions--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>TextToHtmlConversionOptions</h4>
<pre>public&nbsp;TextToHtmlConversionOptions()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getAddClipboardFragmentHeader--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAddClipboardFragmentHeader</h4>
<pre>public final&nbsp;boolean&nbsp;getAddClipboardFragmentHeader()</pre>
<div class="block"><p>
 Returns or sets value, indicating if Clipboard headers should be added.
 Read/write <code>boolean</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITextToHtmlConversionOptions.html#getAddClipboardFragmentHeader--">getAddClipboardFragmentHeader</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITextToHtmlConversionOptions.html" title="interface in com.aspose.slides">ITextToHtmlConversionOptions</a></code></dd>
</dl>
</li>
</ul>
<a name="setAddClipboardFragmentHeader-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAddClipboardFragmentHeader</h4>
<pre>public final&nbsp;void&nbsp;setAddClipboardFragmentHeader(boolean&nbsp;value)</pre>
<div class="block"><p>
 Returns or sets value, indicating if Clipboard headers should be added.
 Read/write <code>boolean</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITextToHtmlConversionOptions.html#setAddClipboardFragmentHeader-boolean-">setAddClipboardFragmentHeader</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITextToHtmlConversionOptions.html" title="interface in com.aspose.slides">ITextToHtmlConversionOptions</a></code></dd>
</dl>
</li>
</ul>
<a name="getTextInheritanceLimit--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTextInheritanceLimit</h4>
<pre>public final&nbsp;int&nbsp;getTextInheritanceLimit()</pre>
<div class="block"><p>
 Returns or sets inhering depth for text properties.
 Read/write <a href="../../../com/aspose/slides/TextInheritanceLimit.html" title="class in com.aspose.slides"><code>TextInheritanceLimit</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITextToHtmlConversionOptions.html#getTextInheritanceLimit--">getTextInheritanceLimit</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITextToHtmlConversionOptions.html" title="interface in com.aspose.slides">ITextToHtmlConversionOptions</a></code></dd>
</dl>
</li>
</ul>
<a name="setTextInheritanceLimit-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTextInheritanceLimit</h4>
<pre>public final&nbsp;void&nbsp;setTextInheritanceLimit(int&nbsp;value)</pre>
<div class="block"><p>
 Returns or sets inhering depth for text properties.
 Read/write <a href="../../../com/aspose/slides/TextInheritanceLimit.html" title="class in com.aspose.slides"><code>TextInheritanceLimit</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITextToHtmlConversionOptions.html#setTextInheritanceLimit-int-">setTextInheritanceLimit</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITextToHtmlConversionOptions.html" title="interface in com.aspose.slides">ITextToHtmlConversionOptions</a></code></dd>
</dl>
</li>
</ul>
<a name="getLinkEmbedController--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLinkEmbedController</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/ILinkEmbedController.html" title="interface in com.aspose.slides">ILinkEmbedController</a>&nbsp;getLinkEmbedController()</pre>
<div class="block"><p>
 Returns or sets a callback object which controlls how external object will be stored.
 Read/write <a href="../../../com/aspose/slides/ILinkEmbedController.html" title="interface in com.aspose.slides"><code>ILinkEmbedController</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITextToHtmlConversionOptions.html#getLinkEmbedController--">getLinkEmbedController</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITextToHtmlConversionOptions.html" title="interface in com.aspose.slides">ITextToHtmlConversionOptions</a></code></dd>
</dl>
</li>
</ul>
<a name="setLinkEmbedController-com.aspose.slides.ILinkEmbedController-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLinkEmbedController</h4>
<pre>public final&nbsp;void&nbsp;setLinkEmbedController(<a href="../../../com/aspose/slides/ILinkEmbedController.html" title="interface in com.aspose.slides">ILinkEmbedController</a>&nbsp;value)</pre>
<div class="block"><p>
 Returns or sets a callback object which controlls how external object will be stored.
 Read/write <a href="../../../com/aspose/slides/ILinkEmbedController.html" title="interface in com.aspose.slides"><code>ILinkEmbedController</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITextToHtmlConversionOptions.html#setLinkEmbedController-com.aspose.slides.ILinkEmbedController-">setLinkEmbedController</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITextToHtmlConversionOptions.html" title="interface in com.aspose.slides">ITextToHtmlConversionOptions</a></code></dd>
</dl>
</li>
</ul>
<a name="getEncodingName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEncodingName</h4>
<pre>public final&nbsp;java.lang.String&nbsp;getEncodingName()</pre>
<div class="block"><p>
 Returns or sets html encoding name.
 This value will be saved to the generated HTML file, but its up to caller to ensure that file will be saved in this encoding.
 Read/write <code>String</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITextToHtmlConversionOptions.html#getEncodingName--">getEncodingName</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITextToHtmlConversionOptions.html" title="interface in com.aspose.slides">ITextToHtmlConversionOptions</a></code></dd>
</dl>
</li>
</ul>
<a name="setEncodingName-java.lang.String-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setEncodingName</h4>
<pre>public final&nbsp;void&nbsp;setEncodingName(java.lang.String&nbsp;value)</pre>
<div class="block"><p>
 Returns or sets html encoding name.
 This value will be saved to the generated HTML file, but its up to caller to ensure that file will be saved in this encoding.
 Read/write <code>String</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITextToHtmlConversionOptions.html#setEncodingName-java.lang.String-">setEncodingName</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITextToHtmlConversionOptions.html" title="interface in com.aspose.slides">ITextToHtmlConversionOptions</a></code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/TextStyle.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/TextUnderlineType.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/TextToHtmlConversionOptions.html" target="_top">Frames</a></li>
<li><a href="TextToHtmlConversionOptions.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><i>Copyright &copy; 2004-2025 Aspose Pty Ltd. All Rights Reserved.</i></small></p>
</body>
</html>
