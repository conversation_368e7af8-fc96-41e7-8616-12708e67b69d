<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_421) on Fri Jul 11 05:32:45 CDT 2025 -->
<title>com.aspose.slides Class Hierarchy (Aspose.Slides for Java (25.7) API Documentation)</title>
<meta name="date" content="2025-07-11">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="com.aspose.slides Class Hierarchy (Aspose.Slides for Java (25.7) API Documentation)";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li>Class</li>
<li class="navBarCell1Rev">Tree</li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/package-tree.html" target="_top">Frames</a></li>
<li><a href="package-tree.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 class="title">Hierarchy For Package com.aspose.slides</h1>
</div>
<div class="contentContainer">
<h2 title="Class Hierarchy">Class Hierarchy</h2>
<ul>
<li type="circle">java.lang.Object
<ul>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/AdjustValue.html" title="class in com.aspose.slides"><span class="typeNameLink">AdjustValue</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IAdjustValue.html" title="interface in com.aspose.slides">IAdjustValue</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/BaseChartValue.html" title="class in com.aspose.slides"><span class="typeNameLink">BaseChartValue</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IBaseChartValue.html" title="interface in com.aspose.slides">IBaseChartValue</a>)
<ul>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/DoubleChartValue.html" title="class in com.aspose.slides"><span class="typeNameLink">DoubleChartValue</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IDoubleChartValue.html" title="interface in com.aspose.slides">IDoubleChartValue</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/StringChartValue.html" title="class in com.aspose.slides"><span class="typeNameLink">StringChartValue</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IStringChartValue.html" title="interface in com.aspose.slides">IStringChartValue</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/StringOrDoubleChartValue.html" title="class in com.aspose.slides"><span class="typeNameLink">StringOrDoubleChartValue</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IStringOrDoubleChartValue.html" title="interface in com.aspose.slides">IStringOrDoubleChartValue</a>)</li>
</ul>
</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/BaseHeaderFooterManager.html" title="class in com.aspose.slides"><span class="typeNameLink">BaseHeaderFooterManager</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IBaseHeaderFooterManager.html" title="interface in com.aspose.slides">IBaseHeaderFooterManager</a>)
<ul>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/BaseSlideHeaderFooterManager.html" title="class in com.aspose.slides"><span class="typeNameLink">BaseSlideHeaderFooterManager</span></a>
<ul>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/BaseHandoutNotesSlideHeaderFooterManager.html" title="class in com.aspose.slides"><span class="typeNameLink">BaseHandoutNotesSlideHeaderFooterManager</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IBaseHandoutNotesSlideHeaderFooterManag.html" title="interface in com.aspose.slides">IBaseHandoutNotesSlideHeaderFooterManag</a>)
<ul>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/MasterHandoutSlideHeaderFooterManager.html" title="class in com.aspose.slides"><span class="typeNameLink">MasterHandoutSlideHeaderFooterManager</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IMasterHandoutSlideHeaderFooterManager.html" title="interface in com.aspose.slides">IMasterHandoutSlideHeaderFooterManager</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/MasterNotesSlideHeaderFooterManager.html" title="class in com.aspose.slides"><span class="typeNameLink">MasterNotesSlideHeaderFooterManager</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IMasterNotesSlideHeaderFooterManager.html" title="interface in com.aspose.slides">IMasterNotesSlideHeaderFooterManager</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/NotesSlideHeaderFooterManager.html" title="class in com.aspose.slides"><span class="typeNameLink">NotesSlideHeaderFooterManager</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/INotesSlideHeaderFooterManager.html" title="interface in com.aspose.slides">INotesSlideHeaderFooterManager</a>)</li>
</ul>
</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/LayoutSlideHeaderFooterManager.html" title="class in com.aspose.slides"><span class="typeNameLink">LayoutSlideHeaderFooterManager</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/ILayoutSlideHeaderFooterManager.html" title="interface in com.aspose.slides">ILayoutSlideHeaderFooterManager</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/MasterSlideHeaderFooterManager.html" title="class in com.aspose.slides"><span class="typeNameLink">MasterSlideHeaderFooterManager</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IMasterSlideHeaderFooterManager.html" title="interface in com.aspose.slides">IMasterSlideHeaderFooterManager</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/SlideHeaderFooterManager.html" title="class in com.aspose.slides"><span class="typeNameLink">SlideHeaderFooterManager</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/ISlideHeaderFooterManager.html" title="interface in com.aspose.slides">ISlideHeaderFooterManager</a>)</li>
</ul>
</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/PresentationHeaderFooterManager.html" title="class in com.aspose.slides"><span class="typeNameLink">PresentationHeaderFooterManager</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IPresentationHeaderFooterManager.html" title="interface in com.aspose.slides">IPresentationHeaderFooterManager</a>)</li>
</ul>
</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/BaseShapeLock.html" title="class in com.aspose.slides"><span class="typeNameLink">BaseShapeLock</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IBaseShapeLock.html" title="interface in com.aspose.slides">IBaseShapeLock</a>)
<ul>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/AutoShapeLock.html" title="class in com.aspose.slides"><span class="typeNameLink">AutoShapeLock</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IAutoShapeLock.html" title="interface in com.aspose.slides">IAutoShapeLock</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ConnectorLock.html" title="class in com.aspose.slides"><span class="typeNameLink">ConnectorLock</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IConnectorLock.html" title="interface in com.aspose.slides">IConnectorLock</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/GraphicalObjectLock.html" title="class in com.aspose.slides"><span class="typeNameLink">GraphicalObjectLock</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IGraphicalObjectLock.html" title="interface in com.aspose.slides">IGraphicalObjectLock</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/GroupShapeLock.html" title="class in com.aspose.slides"><span class="typeNameLink">GroupShapeLock</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IGroupShapeLock.html" title="interface in com.aspose.slides">IGroupShapeLock</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/PictureFrameLock.html" title="class in com.aspose.slides"><span class="typeNameLink">PictureFrameLock</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IPictureFrameLock.html" title="interface in com.aspose.slides">IPictureFrameLock</a>)</li>
</ul>
</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/BaseSlide.html" title="class in com.aspose.slides"><span class="typeNameLink">BaseSlide</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IBaseSlide.html" title="interface in com.aspose.slides">IBaseSlide</a>)
<ul>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/LayoutSlide.html" title="class in com.aspose.slides"><span class="typeNameLink">LayoutSlide</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/ILayoutSlide.html" title="interface in com.aspose.slides">ILayoutSlide</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/MasterHandoutSlide.html" title="class in com.aspose.slides"><span class="typeNameLink">MasterHandoutSlide</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IMasterHandoutSlide.html" title="interface in com.aspose.slides">IMasterHandoutSlide</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/MasterNotesSlide.html" title="class in com.aspose.slides"><span class="typeNameLink">MasterNotesSlide</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IMasterNotesSlide.html" title="interface in com.aspose.slides">IMasterNotesSlide</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/MasterSlide.html" title="class in com.aspose.slides"><span class="typeNameLink">MasterSlide</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IMasterSlide.html" title="interface in com.aspose.slides">IMasterSlide</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/NotesSlide.html" title="class in com.aspose.slides"><span class="typeNameLink">NotesSlide</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/INotesSlide.html" title="interface in com.aspose.slides">INotesSlide</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/Slide.html" title="class in com.aspose.slides"><span class="typeNameLink">Slide</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a>)</li>
</ul>
</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/BaseThemeManager.html" title="class in com.aspose.slides"><span class="typeNameLink">BaseThemeManager</span></a>
<ul>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/BaseOverrideThemeManager.html" title="class in com.aspose.slides"><span class="typeNameLink">BaseOverrideThemeManager</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IOverrideThemeManager.html" title="interface in com.aspose.slides">IOverrideThemeManager</a>)
<ul>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ChartThemeManager.html" title="class in com.aspose.slides"><span class="typeNameLink">ChartThemeManager</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/LayoutSlideThemeManager.html" title="class in com.aspose.slides"><span class="typeNameLink">LayoutSlideThemeManager</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/NotesSlideThemeManager.html" title="class in com.aspose.slides"><span class="typeNameLink">NotesSlideThemeManager</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/SlideThemeManager.html" title="class in com.aspose.slides"><span class="typeNameLink">SlideThemeManager</span></a></li>
</ul>
</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/MasterThemeManager.html" title="class in com.aspose.slides"><span class="typeNameLink">MasterThemeManager</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IMasterThemeManager.html" title="interface in com.aspose.slides">IMasterThemeManager</a>)</li>
</ul>
</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/Behavior.html" title="class in com.aspose.slides"><span class="typeNameLink">Behavior</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IBehavior.html" title="interface in com.aspose.slides">IBehavior</a>)
<ul>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ColorEffect.html" title="class in com.aspose.slides"><span class="typeNameLink">ColorEffect</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IColorEffect.html" title="interface in com.aspose.slides">IColorEffect</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/CommandEffect.html" title="class in com.aspose.slides"><span class="typeNameLink">CommandEffect</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/ICommandEffect.html" title="interface in com.aspose.slides">ICommandEffect</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/FilterEffect.html" title="class in com.aspose.slides"><span class="typeNameLink">FilterEffect</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IFilterEffect.html" title="interface in com.aspose.slides">IFilterEffect</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/MotionEffect.html" title="class in com.aspose.slides"><span class="typeNameLink">MotionEffect</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IMotionEffect.html" title="interface in com.aspose.slides">IMotionEffect</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/PropertyEffect.html" title="class in com.aspose.slides"><span class="typeNameLink">PropertyEffect</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IPropertyEffect.html" title="interface in com.aspose.slides">IPropertyEffect</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/RotationEffect.html" title="class in com.aspose.slides"><span class="typeNameLink">RotationEffect</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IRotationEffect.html" title="interface in com.aspose.slides">IRotationEffect</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ScaleEffect.html" title="class in com.aspose.slides"><span class="typeNameLink">ScaleEffect</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IScaleEffect.html" title="interface in com.aspose.slides">IScaleEffect</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/SetEffect.html" title="class in com.aspose.slides"><span class="typeNameLink">SetEffect</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/ISetEffect.html" title="interface in com.aspose.slides">ISetEffect</a>)</li>
</ul>
</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/BehaviorCollection.html" title="class in com.aspose.slides"><span class="typeNameLink">BehaviorCollection</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IBehaviorCollection.html" title="interface in com.aspose.slides">IBehaviorCollection</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/BehaviorFactory.html" title="class in com.aspose.slides"><span class="typeNameLink">BehaviorFactory</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IBehaviorFactory.html" title="interface in com.aspose.slides">IBehaviorFactory</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/BehaviorProperty.html" title="class in com.aspose.slides"><span class="typeNameLink">BehaviorProperty</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IBehaviorProperty.html" title="interface in com.aspose.slides">IBehaviorProperty</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/BehaviorPropertyCollection.html" title="class in com.aspose.slides"><span class="typeNameLink">BehaviorPropertyCollection</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IBehaviorPropertyCollection.html" title="interface in com.aspose.slides">IBehaviorPropertyCollection</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/BlobManagementOptions.html" title="class in com.aspose.slides"><span class="typeNameLink">BlobManagementOptions</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IBlobManagementOptions.html" title="interface in com.aspose.slides">IBlobManagementOptions</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/BuildVersionInfo.html" title="class in com.aspose.slides"><span class="typeNameLink">BuildVersionInfo</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/Captions.html" title="class in com.aspose.slides"><span class="typeNameLink">Captions</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/ICaptions.html" title="interface in com.aspose.slides">ICaptions</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/CaptionsCollection.html" title="class in com.aspose.slides"><span class="typeNameLink">CaptionsCollection</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/ICaptionsCollection.html" title="interface in com.aspose.slides">ICaptionsCollection</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/Cell.html" title="class in com.aspose.slides"><span class="typeNameLink">Cell</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/ICell.html" title="interface in com.aspose.slides">ICell</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/CellCollection.html" title="class in com.aspose.slides"><span class="typeNameLink">CellCollection</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/ICellCollection.html" title="interface in com.aspose.slides">ICellCollection</a>)
<ul>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/Column.html" title="class in com.aspose.slides"><span class="typeNameLink">Column</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IColumn.html" title="interface in com.aspose.slides">IColumn</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/Row.html" title="class in com.aspose.slides"><span class="typeNameLink">Row</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IRow.html" title="interface in com.aspose.slides">IRow</a>)</li>
</ul>
</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ChartCategory.html" title="class in com.aspose.slides"><span class="typeNameLink">ChartCategory</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IChartCategory.html" title="interface in com.aspose.slides">IChartCategory</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ChartCategoryLevelsManager.html" title="class in com.aspose.slides"><span class="typeNameLink">ChartCategoryLevelsManager</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IChartCategoryLevelsManager.html" title="interface in com.aspose.slides">IChartCategoryLevelsManager</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ChartCellCollection.html" title="class in com.aspose.slides"><span class="typeNameLink">ChartCellCollection</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IChartCellCollection.html" title="interface in com.aspose.slides">IChartCellCollection</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ChartDataCell.html" title="class in com.aspose.slides"><span class="typeNameLink">ChartDataCell</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IChartDataCell.html" title="interface in com.aspose.slides">IChartDataCell</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ChartDataPoint.html" title="class in com.aspose.slides"><span class="typeNameLink">ChartDataPoint</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IChartDataPoint.html" title="interface in com.aspose.slides">IChartDataPoint</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ChartDataWorksheetCollection.html" title="class in com.aspose.slides"><span class="typeNameLink">ChartDataWorksheetCollection</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IChartDataWorksheetCollection.html" title="interface in com.aspose.slides">IChartDataWorksheetCollection</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ChartLinesFormat.html" title="class in com.aspose.slides"><span class="typeNameLink">ChartLinesFormat</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IChartLinesFormat.html" title="interface in com.aspose.slides">IChartLinesFormat</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ChartSeries.html" title="class in com.aspose.slides"><span class="typeNameLink">ChartSeries</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IChartSeries.html" title="interface in com.aspose.slides">IChartSeries</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ChartSeriesGroup.html" title="class in com.aspose.slides"><span class="typeNameLink">ChartSeriesGroup</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IChartSeriesGroup.html" title="interface in com.aspose.slides">IChartSeriesGroup</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ChartTextFormat.html" title="class in com.aspose.slides"><span class="typeNameLink">ChartTextFormat</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IChartTextFormat.html" title="interface in com.aspose.slides">IChartTextFormat</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ChartTitle.html" title="class in com.aspose.slides"><span class="typeNameLink">ChartTitle</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IChartTitle.html" title="interface in com.aspose.slides">IChartTitle</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ChartTypeCharacterizer.html" title="class in com.aspose.slides"><span class="typeNameLink">ChartTypeCharacterizer</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/Collect.html" title="class in com.aspose.slides"><span class="typeNameLink">Collect</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ColorOffset.html" title="class in com.aspose.slides"><span class="typeNameLink">ColorOffset</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IColorOffset.html" title="interface in com.aspose.slides">IColorOffset</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ColorOperation.html" title="class in com.aspose.slides"><span class="typeNameLink">ColorOperation</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IColorOperation.html" title="interface in com.aspose.slides">IColorOperation</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ColorOperationCollection.html" title="class in com.aspose.slides"><span class="typeNameLink">ColorOperationCollection</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IColorOperationCollection.html" title="interface in com.aspose.slides">IColorOperationCollection</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ColorScheme.html" title="class in com.aspose.slides"><span class="typeNameLink">ColorScheme</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IColorScheme.html" title="interface in com.aspose.slides">IColorScheme</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/Comment.html" title="class in com.aspose.slides"><span class="typeNameLink">Comment</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IComment.html" title="interface in com.aspose.slides">IComment</a>)
<ul>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ModernComment.html" title="class in com.aspose.slides"><span class="typeNameLink">ModernComment</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IModernComment.html" title="interface in com.aspose.slides">IModernComment</a>)</li>
</ul>
</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/CommentAuthor.html" title="class in com.aspose.slides"><span class="typeNameLink">CommentAuthor</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/ICommentAuthor.html" title="interface in com.aspose.slides">ICommentAuthor</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/CommonSlideViewProperties.html" title="class in com.aspose.slides"><span class="typeNameLink">CommonSlideViewProperties</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/ICommonSlideViewProperties.html" title="interface in com.aspose.slides">ICommonSlideViewProperties</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/Compress.html" title="class in com.aspose.slides"><span class="typeNameLink">Compress</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ControlCollection.html" title="class in com.aspose.slides"><span class="typeNameLink">ControlCollection</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IControlCollection.html" title="interface in com.aspose.slides">IControlCollection</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ControlPropertiesCollection.html" title="class in com.aspose.slides"><span class="typeNameLink">ControlPropertiesCollection</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IControlPropertiesCollection.html" title="interface in com.aspose.slides">IControlPropertiesCollection</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/Convert.html" title="class in com.aspose.slides"><span class="typeNameLink">Convert</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/CurrentThreadSettings.html" title="class in com.aspose.slides"><span class="typeNameLink">CurrentThreadSettings</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/CustomData.html" title="class in com.aspose.slides"><span class="typeNameLink">CustomData</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/ICustomData.html" title="interface in com.aspose.slides">ICustomData</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/CustomXmlPart.html" title="class in com.aspose.slides"><span class="typeNameLink">CustomXmlPart</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/ICustomXmlPart.html" title="interface in com.aspose.slides">ICustomXmlPart</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/CustomXmlPartCollection.html" title="class in com.aspose.slides"><span class="typeNameLink">CustomXmlPartCollection</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/ICustomXmlPartCollection.html" title="interface in com.aspose.slides">ICustomXmlPartCollection</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/DataLabel.html" title="class in com.aspose.slides"><span class="typeNameLink">DataLabel</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IDataLabel.html" title="interface in com.aspose.slides">IDataLabel</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/DataSourceTypeForErrorBarsCustomValues.html" title="class in com.aspose.slides"><span class="typeNameLink">DataSourceTypeForErrorBarsCustomValues</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IDataSourceTypeForErrorBarsCustomValues.html" title="interface in com.aspose.slides">IDataSourceTypeForErrorBarsCustomValues</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/DigitalSignature.html" title="class in com.aspose.slides"><span class="typeNameLink">DigitalSignature</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IDigitalSignature.html" title="interface in com.aspose.slides">IDigitalSignature</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/DocumentProperties.html" title="class in com.aspose.slides"><span class="typeNameLink">DocumentProperties</span></a> (implements java.lang.Cloneable, com.aspose.slides.<a href="../../../com/aspose/slides/IDocumentProperties.html" title="interface in com.aspose.slides">IDocumentProperties</a>, com.aspose.slides.<a href="../../../com/aspose/slides/IGenericCloneable.html" title="interface in com.aspose.slides">IGenericCloneable</a>&lt;T&gt;)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/DomObject.html" title="class in com.aspose.slides"><span class="typeNameLink">DomObject</span></a>&lt;TParent&gt;
<ul>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/AdjustValueCollection.html" title="class in com.aspose.slides"><span class="typeNameLink">AdjustValueCollection</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IAdjustValueCollection.html" title="interface in com.aspose.slides">IAdjustValueCollection</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/AnimationTimeLine.html" title="class in com.aspose.slides"><span class="typeNameLink">AnimationTimeLine</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IAnimationTimeLine.html" title="interface in com.aspose.slides">IAnimationTimeLine</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/Audio.html" title="class in com.aspose.slides"><span class="typeNameLink">Audio</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IAudio.html" title="interface in com.aspose.slides">IAudio</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/AudioCollection.html" title="class in com.aspose.slides"><span class="typeNameLink">AudioCollection</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IAudioCollection.html" title="interface in com.aspose.slides">IAudioCollection</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/AxesManager.html" title="class in com.aspose.slides"><span class="typeNameLink">AxesManager</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IAxesManager.html" title="interface in com.aspose.slides">IAxesManager</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/Axis.html" title="class in com.aspose.slides"><span class="typeNameLink">Axis</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IAxis.html" title="interface in com.aspose.slides">IAxis</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/AxisFormat.html" title="class in com.aspose.slides"><span class="typeNameLink">AxisFormat</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IAxisFormat.html" title="interface in com.aspose.slides">IAxisFormat</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ChartCategoryCollection.html" title="class in com.aspose.slides"><span class="typeNameLink">ChartCategoryCollection</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IChartCategoryCollection.html" title="interface in com.aspose.slides">IChartCategoryCollection</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ChartData.html" title="class in com.aspose.slides"><span class="typeNameLink">ChartData</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IChartData.html" title="interface in com.aspose.slides">IChartData</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ChartDataPointCollection.html" title="class in com.aspose.slides"><span class="typeNameLink">ChartDataPointCollection</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IChartDataPointCollection.html" title="interface in com.aspose.slides">IChartDataPointCollection</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ChartDataPointLevel.html" title="class in com.aspose.slides"><span class="typeNameLink">ChartDataPointLevel</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IChartDataPointLevel.html" title="interface in com.aspose.slides">IChartDataPointLevel</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ChartDataPointLevelsManager.html" title="class in com.aspose.slides"><span class="typeNameLink">ChartDataPointLevelsManager</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IChartDataPointLevelsManager.html" title="interface in com.aspose.slides">IChartDataPointLevelsManager</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ChartDataWorkbook.html" title="class in com.aspose.slides"><span class="typeNameLink">ChartDataWorkbook</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IChartDataWorkbook.html" title="interface in com.aspose.slides">IChartDataWorkbook</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ChartDataWorksheet.html" title="class in com.aspose.slides"><span class="typeNameLink">ChartDataWorksheet</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IChartDataWorksheet.html" title="interface in com.aspose.slides">IChartDataWorksheet</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ChartPlotArea.html" title="class in com.aspose.slides"><span class="typeNameLink">ChartPlotArea</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IChartPlotArea.html" title="interface in com.aspose.slides">IChartPlotArea</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ChartSeriesCollection.html" title="class in com.aspose.slides"><span class="typeNameLink">ChartSeriesCollection</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IChartSeriesCollection.html" title="interface in com.aspose.slides">IChartSeriesCollection</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ChartWall.html" title="class in com.aspose.slides"><span class="typeNameLink">ChartWall</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IChartWall.html" title="interface in com.aspose.slides">IChartWall</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ColumnCollection.html" title="class in com.aspose.slides"><span class="typeNameLink">ColumnCollection</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IColumnCollection.html" title="interface in com.aspose.slides">IColumnCollection</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ColumnFormat.html" title="class in com.aspose.slides"><span class="typeNameLink">ColumnFormat</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IColumnFormat.html" title="interface in com.aspose.slides">IColumnFormat</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/CommentAuthorCollection.html" title="class in com.aspose.slides"><span class="typeNameLink">CommentAuthorCollection</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/ICommentAuthorCollection.html" title="interface in com.aspose.slides">ICommentAuthorCollection</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/CommentCollection.html" title="class in com.aspose.slides"><span class="typeNameLink">CommentCollection</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/ICommentCollection.html" title="interface in com.aspose.slides">ICommentCollection</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/Control.html" title="class in com.aspose.slides"><span class="typeNameLink">Control</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IControl.html" title="interface in com.aspose.slides">IControl</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/DataLabelCollection.html" title="class in com.aspose.slides"><span class="typeNameLink">DataLabelCollection</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IDataLabelCollection.html" title="interface in com.aspose.slides">IDataLabelCollection</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/DataTable.html" title="class in com.aspose.slides"><span class="typeNameLink">DataTable</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IDataTable.html" title="interface in com.aspose.slides">IDataTable</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/DigitalSignatureCollection.html" title="class in com.aspose.slides"><span class="typeNameLink">DigitalSignatureCollection</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IDigitalSignatureCollection.html" title="interface in com.aspose.slides">IDigitalSignatureCollection</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/EffectStyle.html" title="class in com.aspose.slides"><span class="typeNameLink">EffectStyle</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IEffectStyle.html" title="interface in com.aspose.slides">IEffectStyle</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/EffectStyleCollection.html" title="class in com.aspose.slides"><span class="typeNameLink">EffectStyleCollection</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IEffectStyleCollection.html" title="interface in com.aspose.slides">IEffectStyleCollection</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ErrorBarsCustomValues.html" title="class in com.aspose.slides"><span class="typeNameLink">ErrorBarsCustomValues</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IErrorBarsCustomValues.html" title="interface in com.aspose.slides">IErrorBarsCustomValues</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ErrorBarsFormat.html" title="class in com.aspose.slides"><span class="typeNameLink">ErrorBarsFormat</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IErrorBarsFormat.html" title="interface in com.aspose.slides">IErrorBarsFormat</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/Field.html" title="class in com.aspose.slides"><span class="typeNameLink">Field</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IField.html" title="interface in com.aspose.slides">IField</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/FillFormatCollection.html" title="class in com.aspose.slides"><span class="typeNameLink">FillFormatCollection</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IFillFormatCollection.html" title="interface in com.aspose.slides">IFillFormatCollection</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ImageCollection.html" title="class in com.aspose.slides"><span class="typeNameLink">ImageCollection</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IImageCollection.html" title="interface in com.aspose.slides">IImageCollection</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/Legend.html" title="class in com.aspose.slides"><span class="typeNameLink">Legend</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/ILegend.html" title="interface in com.aspose.slides">ILegend</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/LineFormatCollection.html" title="class in com.aspose.slides"><span class="typeNameLink">LineFormatCollection</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/ILineFormatCollection.html" title="interface in com.aspose.slides">ILineFormatCollection</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/MasterSlideCollection.html" title="class in com.aspose.slides"><span class="typeNameLink">MasterSlideCollection</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IMasterSlideCollection.html" title="interface in com.aspose.slides">IMasterSlideCollection</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/NotesSlideManager.html" title="class in com.aspose.slides"><span class="typeNameLink">NotesSlideManager</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/INotesSlideManager.html" title="interface in com.aspose.slides">INotesSlideManager</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ParagraphCollection.html" title="class in com.aspose.slides"><span class="typeNameLink">ParagraphCollection</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IParagraphCollection.html" title="interface in com.aspose.slides">IParagraphCollection</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/PortionCollection.html" title="class in com.aspose.slides"><span class="typeNameLink">PortionCollection</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IPortionCollection.html" title="interface in com.aspose.slides">IPortionCollection</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/RowCollection.html" title="class in com.aspose.slides"><span class="typeNameLink">RowCollection</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IRowCollection.html" title="interface in com.aspose.slides">IRowCollection</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/RowFormat.html" title="class in com.aspose.slides"><span class="typeNameLink">RowFormat</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IRowFormat.html" title="interface in com.aspose.slides">IRowFormat</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/Section.html" title="class in com.aspose.slides"><span class="typeNameLink">Section</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/ISection.html" title="interface in com.aspose.slides">ISection</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/SectionCollection.html" title="class in com.aspose.slides"><span class="typeNameLink">SectionCollection</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/ISectionCollection.html" title="interface in com.aspose.slides">ISectionCollection</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/SectionSlideCollection.html" title="class in com.aspose.slides"><span class="typeNameLink">SectionSlideCollection</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/ISectionSlideCollection.html" title="interface in com.aspose.slides">ISectionSlideCollection</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ShapeCollection.html" title="class in com.aspose.slides"><span class="typeNameLink">ShapeCollection</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IShapeCollection.html" title="interface in com.aspose.slides">IShapeCollection</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ShapeStyle.html" title="class in com.aspose.slides"><span class="typeNameLink">ShapeStyle</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IShapeStyle.html" title="interface in com.aspose.slides">IShapeStyle</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/SlideCollection.html" title="class in com.aspose.slides"><span class="typeNameLink">SlideCollection</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/ISlideCollection.html" title="interface in com.aspose.slides">ISlideCollection</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/SlideShowTransition.html" title="class in com.aspose.slides"><span class="typeNameLink">SlideShowTransition</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/ISlideShowTransition.html" title="interface in com.aspose.slides">ISlideShowTransition</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/SlideSize.html" title="class in com.aspose.slides"><span class="typeNameLink">SlideSize</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/ISlideSize.html" title="interface in com.aspose.slides">ISlideSize</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/SummaryZoomSectionCollection.html" title="class in com.aspose.slides"><span class="typeNameLink">SummaryZoomSectionCollection</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/ISummaryZoomSectionCollection.html" title="interface in com.aspose.slides">ISummaryZoomSectionCollection</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/TableFormat.html" title="class in com.aspose.slides"><span class="typeNameLink">TableFormat</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/ITableFormat.html" title="interface in com.aspose.slides">ITableFormat</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/Trendline.html" title="class in com.aspose.slides"><span class="typeNameLink">Trendline</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/ITrendline.html" title="interface in com.aspose.slides">ITrendline</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/TrendlineCollection.html" title="class in com.aspose.slides"><span class="typeNameLink">TrendlineCollection</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/ITrendlineCollection.html" title="interface in com.aspose.slides">ITrendlineCollection</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/UpDownBarsManager.html" title="class in com.aspose.slides"><span class="typeNameLink">UpDownBarsManager</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IUpDownBarsManager.html" title="interface in com.aspose.slides">IUpDownBarsManager</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/VideoCollection.html" title="class in com.aspose.slides"><span class="typeNameLink">VideoCollection</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IVideoCollection.html" title="interface in com.aspose.slides">IVideoCollection</a>)</li>
</ul>
</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/DrawingGuide.html" title="class in com.aspose.slides"><span class="typeNameLink">DrawingGuide</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IDrawingGuide.html" title="interface in com.aspose.slides">IDrawingGuide</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/DrawingGuidesCollection.html" title="class in com.aspose.slides"><span class="typeNameLink">DrawingGuidesCollection</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IDrawingGuidesCollection.html" title="interface in com.aspose.slides">IDrawingGuidesCollection</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/Effect.html" title="class in com.aspose.slides"><span class="typeNameLink">Effect</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IEffect.html" title="interface in com.aspose.slides">IEffect</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/EffectFactory.html" title="class in com.aspose.slides"><span class="typeNameLink">EffectFactory</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IEffectFactory.html" title="interface in com.aspose.slides">IEffectFactory</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/EmbedAllFontsHtmlController.html" title="class in com.aspose.slides"><span class="typeNameLink">EmbedAllFontsHtmlController</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IHtmlFormattingController.html" title="interface in com.aspose.slides">IHtmlFormattingController</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/EmbeddedEotFontsHtmlController.html" title="class in com.aspose.slides"><span class="typeNameLink">EmbeddedEotFontsHtmlController</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IEmbeddedEotFontsHtmlController.html" title="interface in com.aspose.slides">IEmbeddedEotFontsHtmlController</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/EmbeddedWoffFontsHtmlController.html" title="class in com.aspose.slides"><span class="typeNameLink">EmbeddedWoffFontsHtmlController</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IEmbeddedWoffFontsHtmlController.html" title="interface in com.aspose.slides">IEmbeddedWoffFontsHtmlController</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ExternalResourceResolver.html" title="class in com.aspose.slides"><span class="typeNameLink">ExternalResourceResolver</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IExternalResourceResolver.html" title="interface in com.aspose.slides">IExternalResourceResolver</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ExtraColorScheme.html" title="class in com.aspose.slides"><span class="typeNameLink">ExtraColorScheme</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IExtraColorScheme.html" title="interface in com.aspose.slides">IExtraColorScheme</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ExtraColorSchemeCollection.html" title="class in com.aspose.slides"><span class="typeNameLink">ExtraColorSchemeCollection</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IExtraColorSchemeCollection.html" title="interface in com.aspose.slides">IExtraColorSchemeCollection</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/FieldType.html" title="class in com.aspose.slides"><span class="typeNameLink">FieldType</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IFieldType.html" title="interface in com.aspose.slides">IFieldType</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/FontData.html" title="class in com.aspose.slides"><span class="typeNameLink">FontData</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IFontData.html" title="interface in com.aspose.slides">IFontData</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/FontDataFactory.html" title="class in com.aspose.slides"><span class="typeNameLink">FontDataFactory</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IFontDataFactory.html" title="interface in com.aspose.slides">IFontDataFactory</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/FontFallBackRule.html" title="class in com.aspose.slides"><span class="typeNameLink">FontFallBackRule</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IFontFallBackRule.html" title="interface in com.aspose.slides">IFontFallBackRule</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/FontFallBackRulesCollection.html" title="class in com.aspose.slides"><span class="typeNameLink">FontFallBackRulesCollection</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IFontFallBackRulesCollection.html" title="interface in com.aspose.slides">IFontFallBackRulesCollection</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/Fonts.html" title="class in com.aspose.slides"><span class="typeNameLink">Fonts</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IFonts.html" title="interface in com.aspose.slides">IFonts</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/FontScheme.html" title="class in com.aspose.slides"><span class="typeNameLink">FontScheme</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IFontScheme.html" title="interface in com.aspose.slides">IFontScheme</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/FontsLoader.html" title="class in com.aspose.slides"><span class="typeNameLink">FontsLoader</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IFontsLoader.html" title="interface in com.aspose.slides">IFontsLoader</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/FontsManager.html" title="class in com.aspose.slides"><span class="typeNameLink">FontsManager</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IFontsManager.html" title="interface in com.aspose.slides">IFontsManager</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/FontSources.html" title="class in com.aspose.slides"><span class="typeNameLink">FontSources</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IFontSources.html" title="interface in com.aspose.slides">IFontSources</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/FontSubstitutionInfo.html" title="class in com.aspose.slides"><span class="typeNameLink">FontSubstitutionInfo</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/FontSubstRule.html" title="class in com.aspose.slides"><span class="typeNameLink">FontSubstRule</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IFontSubstRule.html" title="interface in com.aspose.slides">IFontSubstRule</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/FontSubstRuleCollection.html" title="class in com.aspose.slides"><span class="typeNameLink">FontSubstRuleCollection</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IFontSubstRuleCollection.html" title="interface in com.aspose.slides">IFontSubstRuleCollection</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ForEach.html" title="class in com.aspose.slides"><span class="typeNameLink">ForEach</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/FormatFactory.html" title="class in com.aspose.slides"><span class="typeNameLink">FormatFactory</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IFormatFactory.html" title="interface in com.aspose.slides">IFormatFactory</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/FormatScheme.html" title="class in com.aspose.slides"><span class="typeNameLink">FormatScheme</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IFormatScheme.html" title="interface in com.aspose.slides">IFormatScheme</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/FrameTickEventArgs.html" title="class in com.aspose.slides"><span class="typeNameLink">FrameTickEventArgs</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/GeometryPath.html" title="class in com.aspose.slides"><span class="typeNameLink">GeometryPath</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IGeometryPath.html" title="interface in com.aspose.slides">IGeometryPath</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/Glow.html" title="class in com.aspose.slides"><span class="typeNameLink">Glow</span></a> (implements java.lang.Cloneable, com.aspose.slides.<a href="../../../com/aspose/slides/IGlow.html" title="interface in com.aspose.slides">IGlow</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/GradientStopCollectionEffectiveData.html" title="class in com.aspose.slides"><span class="typeNameLink">GradientStopCollectionEffectiveData</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IGradientStopCollectionEffectiveData.html" title="interface in com.aspose.slides">IGradientStopCollectionEffectiveData</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/GradientStopEffectiveData.html" title="class in com.aspose.slides"><span class="typeNameLink">GradientStopEffectiveData</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IGradientStopEffectiveData.html" title="interface in com.aspose.slides">IGradientStopEffectiveData</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/HandoutLayoutingOptions.html" title="class in com.aspose.slides"><span class="typeNameLink">HandoutLayoutingOptions</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/ISlidesLayoutOptions.html" title="interface in com.aspose.slides">ISlidesLayoutOptions</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/HeadingPair.html" title="class in com.aspose.slides"><span class="typeNameLink">HeadingPair</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IHeadingPair.html" title="interface in com.aspose.slides">IHeadingPair</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/HtmlExternalResolver.html" title="class in com.aspose.slides"><span class="typeNameLink">HtmlExternalResolver</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IHtmlExternalResolver.html" title="interface in com.aspose.slides">IHtmlExternalResolver</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/HtmlFormatter.html" title="class in com.aspose.slides"><span class="typeNameLink">HtmlFormatter</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IHtmlFormatter.html" title="interface in com.aspose.slides">IHtmlFormatter</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/HtmlGenerator.html" title="class in com.aspose.slides"><span class="typeNameLink">HtmlGenerator</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IHtmlGenerator.html" title="interface in com.aspose.slides">IHtmlGenerator</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/HyperlinkManager.html" title="class in com.aspose.slides"><span class="typeNameLink">HyperlinkManager</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IHyperlinkManager.html" title="interface in com.aspose.slides">IHyperlinkManager</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/HyperlinkQueries.html" title="class in com.aspose.slides"><span class="typeNameLink">HyperlinkQueries</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IHyperlinkQueries.html" title="interface in com.aspose.slides">IHyperlinkQueries</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/Images.html" title="class in com.aspose.slides"><span class="typeNameLink">Images</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ImageTransformOCollectionEffectiveData.html" title="class in com.aspose.slides"><span class="typeNameLink">ImageTransformOCollectionEffectiveData</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IImageTransformOCollectionEffectiveData.html" title="interface in com.aspose.slides">IImageTransformOCollectionEffectiveData</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ImageTransformOperationFactory.html" title="class in com.aspose.slides"><span class="typeNameLink">ImageTransformOperationFactory</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IImageTransformOperationFactory.html" title="interface in com.aspose.slides">IImageTransformOperationFactory</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/InkBrush.html" title="class in com.aspose.slides"><span class="typeNameLink">InkBrush</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IInkBrush.html" title="interface in com.aspose.slides">IInkBrush</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/InkOptions.html" title="class in com.aspose.slides"><span class="typeNameLink">InkOptions</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IInkOptions.html" title="interface in com.aspose.slides">IInkOptions</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/InkTrace.html" title="class in com.aspose.slides"><span class="typeNameLink">InkTrace</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IInkTrace.html" title="interface in com.aspose.slides">IInkTrace</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/InnerShadow.html" title="class in com.aspose.slides"><span class="typeNameLink">InnerShadow</span></a> (implements java.lang.Cloneable, com.aspose.slides.<a href="../../../com/aspose/slides/IInnerShadow.html" title="interface in com.aspose.slides">IInnerShadow</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/Input.html" title="class in com.aspose.slides"><span class="typeNameLink">Input</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/InterruptionToken.html" title="class in com.aspose.slides"><span class="typeNameLink">InterruptionToken</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IInterruptionToken.html" title="interface in com.aspose.slides">IInterruptionToken</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/InterruptionTokenSource.html" title="class in com.aspose.slides"><span class="typeNameLink">InterruptionTokenSource</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/LayoutPlaceholderManager.html" title="class in com.aspose.slides"><span class="typeNameLink">LayoutPlaceholderManager</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/ILayoutPlaceholderManager.html" title="interface in com.aspose.slides">ILayoutPlaceholderManager</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/LayoutSlideCollection.html" title="class in com.aspose.slides"><span class="typeNameLink">LayoutSlideCollection</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/ILayoutSlideCollection.html" title="interface in com.aspose.slides">ILayoutSlideCollection</a>)
<ul>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/GlobalLayoutSlideCollection.html" title="class in com.aspose.slides"><span class="typeNameLink">GlobalLayoutSlideCollection</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IGlobalLayoutSlideCollection.html" title="interface in com.aspose.slides">IGlobalLayoutSlideCollection</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/MasterLayoutSlideCollection.html" title="class in com.aspose.slides"><span class="typeNameLink">MasterLayoutSlideCollection</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IMasterLayoutSlideCollection.html" title="interface in com.aspose.slides">IMasterLayoutSlideCollection</a>)</li>
</ul>
</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/LegendEntryCollection.html" title="class in com.aspose.slides"><span class="typeNameLink">LegendEntryCollection</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/ILegendEntryCollection.html" title="interface in com.aspose.slides">ILegendEntryCollection</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/LegendEntryProperties.html" title="class in com.aspose.slides"><span class="typeNameLink">LegendEntryProperties</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/ILegendEntryProperties.html" title="interface in com.aspose.slides">ILegendEntryProperties</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/License.html" title="class in com.aspose.slides"><span class="typeNameLink">License</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/ILicense.html" title="interface in com.aspose.slides">ILicense</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/LoadOptions.html" title="class in com.aspose.slides"><span class="typeNameLink">LoadOptions</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/ILoadOptions.html" title="interface in com.aspose.slides">ILoadOptions</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/Marker.html" title="class in com.aspose.slides"><span class="typeNameLink">Marker</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IMarker.html" title="interface in com.aspose.slides">IMarker</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/MathAccentFactory.html" title="class in com.aspose.slides"><span class="typeNameLink">MathAccentFactory</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IMathAccentFactory.html" title="interface in com.aspose.slides">IMathAccentFactory</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/MathArrayFactory.html" title="class in com.aspose.slides"><span class="typeNameLink">MathArrayFactory</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IMathArrayFactory.html" title="interface in com.aspose.slides">IMathArrayFactory</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/MathBarFactory.html" title="class in com.aspose.slides"><span class="typeNameLink">MathBarFactory</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IMathBarFactory.html" title="interface in com.aspose.slides">IMathBarFactory</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/MathBlockFactory.html" title="class in com.aspose.slides"><span class="typeNameLink">MathBlockFactory</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IMathBlockFactory.html" title="interface in com.aspose.slides">IMathBlockFactory</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/MathBorderBoxFactory.html" title="class in com.aspose.slides"><span class="typeNameLink">MathBorderBoxFactory</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IMathBorderBoxFactory.html" title="interface in com.aspose.slides">IMathBorderBoxFactory</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/MathBoxFactory.html" title="class in com.aspose.slides"><span class="typeNameLink">MathBoxFactory</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IMathBoxFactory.html" title="interface in com.aspose.slides">IMathBoxFactory</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/MathDelimiterFactory.html" title="class in com.aspose.slides"><span class="typeNameLink">MathDelimiterFactory</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IMathDelimiterFactory.html" title="interface in com.aspose.slides">IMathDelimiterFactory</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/MathElementBase.html" title="class in com.aspose.slides"><span class="typeNameLink">MathElementBase</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IMathElement.html" title="interface in com.aspose.slides">IMathElement</a>)
<ul>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/BaseScript.html" title="class in com.aspose.slides"><span class="typeNameLink">BaseScript</span></a>
<ul>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/MathLeftSubSuperscriptElement.html" title="class in com.aspose.slides"><span class="typeNameLink">MathLeftSubSuperscriptElement</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IMathLeftSubSuperscriptElement.html" title="interface in com.aspose.slides">IMathLeftSubSuperscriptElement</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/MathRightSubSuperscriptElement.html" title="class in com.aspose.slides"><span class="typeNameLink">MathRightSubSuperscriptElement</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IMathRightSubSuperscriptElement.html" title="interface in com.aspose.slides">IMathRightSubSuperscriptElement</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/MathSubscriptElement.html" title="class in com.aspose.slides"><span class="typeNameLink">MathSubscriptElement</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IMathSubscriptElement.html" title="interface in com.aspose.slides">IMathSubscriptElement</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/MathSuperscriptElement.html" title="class in com.aspose.slides"><span class="typeNameLink">MathSuperscriptElement</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IMathSuperscriptElement.html" title="interface in com.aspose.slides">IMathSuperscriptElement</a>)</li>
</ul>
</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/MathAccent.html" title="class in com.aspose.slides"><span class="typeNameLink">MathAccent</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IMathAccent.html" title="interface in com.aspose.slides">IMathAccent</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/MathArray.html" title="class in com.aspose.slides"><span class="typeNameLink">MathArray</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IMathArray.html" title="interface in com.aspose.slides">IMathArray</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/MathBar.html" title="class in com.aspose.slides"><span class="typeNameLink">MathBar</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IMathBar.html" title="interface in com.aspose.slides">IMathBar</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/MathBlock.html" title="class in com.aspose.slides"><span class="typeNameLink">MathBlock</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IMathBlock.html" title="interface in com.aspose.slides">IMathBlock</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/MathBorderBox.html" title="class in com.aspose.slides"><span class="typeNameLink">MathBorderBox</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IMathBorderBox.html" title="interface in com.aspose.slides">IMathBorderBox</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/MathBox.html" title="class in com.aspose.slides"><span class="typeNameLink">MathBox</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IMathBox.html" title="interface in com.aspose.slides">IMathBox</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/MathDelimiter.html" title="class in com.aspose.slides"><span class="typeNameLink">MathDelimiter</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IMathDelimiter.html" title="interface in com.aspose.slides">IMathDelimiter</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/MathematicalText.html" title="class in com.aspose.slides"><span class="typeNameLink">MathematicalText</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IMathematicalText.html" title="interface in com.aspose.slides">IMathematicalText</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/MathFraction.html" title="class in com.aspose.slides"><span class="typeNameLink">MathFraction</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IMathFraction.html" title="interface in com.aspose.slides">IMathFraction</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/MathFunction.html" title="class in com.aspose.slides"><span class="typeNameLink">MathFunction</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IMathFunction.html" title="interface in com.aspose.slides">IMathFunction</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/MathGroupingCharacter.html" title="class in com.aspose.slides"><span class="typeNameLink">MathGroupingCharacter</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IMathGroupingCharacter.html" title="interface in com.aspose.slides">IMathGroupingCharacter</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/MathLimit.html" title="class in com.aspose.slides"><span class="typeNameLink">MathLimit</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IMathLimit.html" title="interface in com.aspose.slides">IMathLimit</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/MathMatrix.html" title="class in com.aspose.slides"><span class="typeNameLink">MathMatrix</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IMathMatrix.html" title="interface in com.aspose.slides">IMathMatrix</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/MathNaryOperator.html" title="class in com.aspose.slides"><span class="typeNameLink">MathNaryOperator</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IMathNaryOperator.html" title="interface in com.aspose.slides">IMathNaryOperator</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/MathRadical.html" title="class in com.aspose.slides"><span class="typeNameLink">MathRadical</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IMathRadical.html" title="interface in com.aspose.slides">IMathRadical</a>)</li>
</ul>
</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/MathematicalTextFactory.html" title="class in com.aspose.slides"><span class="typeNameLink">MathematicalTextFactory</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IMathematicalTextFactory.html" title="interface in com.aspose.slides">IMathematicalTextFactory</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/MathFractionFactory.html" title="class in com.aspose.slides"><span class="typeNameLink">MathFractionFactory</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IMathFractionFactory.html" title="interface in com.aspose.slides">IMathFractionFactory</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/MathFunctionFactory.html" title="class in com.aspose.slides"><span class="typeNameLink">MathFunctionFactory</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IMathFunctionFactory.html" title="interface in com.aspose.slides">IMathFunctionFactory</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/MathGroupingCharacterFactory.html" title="class in com.aspose.slides"><span class="typeNameLink">MathGroupingCharacterFactory</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IMathGroupingCharacterFactory.html" title="interface in com.aspose.slides">IMathGroupingCharacterFactory</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/MathLimitFactory.html" title="class in com.aspose.slides"><span class="typeNameLink">MathLimitFactory</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IMathLimitFactory.html" title="interface in com.aspose.slides">IMathLimitFactory</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/MathMatrixFactory.html" title="class in com.aspose.slides"><span class="typeNameLink">MathMatrixFactory</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IMathMatrixFactory.html" title="interface in com.aspose.slides">IMathMatrixFactory</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/MathNaryOperatorFactory.html" title="class in com.aspose.slides"><span class="typeNameLink">MathNaryOperatorFactory</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IMathNaryOperatorFactory.html" title="interface in com.aspose.slides">IMathNaryOperatorFactory</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/MathParagraph.html" title="class in com.aspose.slides"><span class="typeNameLink">MathParagraph</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IMathParagraph.html" title="interface in com.aspose.slides">IMathParagraph</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/MathParagraphFactory.html" title="class in com.aspose.slides"><span class="typeNameLink">MathParagraphFactory</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IMathParagraphFactory.html" title="interface in com.aspose.slides">IMathParagraphFactory</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/MathRadicalFactory.html" title="class in com.aspose.slides"><span class="typeNameLink">MathRadicalFactory</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IMathRadicalFactory.html" title="interface in com.aspose.slides">IMathRadicalFactory</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/MathRightSubSuperscriptElementFactory.html" title="class in com.aspose.slides"><span class="typeNameLink">MathRightSubSuperscriptElementFactory</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IMathRightSubSuperscriptElementFactory.html" title="interface in com.aspose.slides">IMathRightSubSuperscriptElementFactory</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/MathSubscriptElementFactory.html" title="class in com.aspose.slides"><span class="typeNameLink">MathSubscriptElementFactory</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IMathSubscriptElementFactory.html" title="interface in com.aspose.slides">IMathSubscriptElementFactory</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/MathSuperscriptElementFactory.html" title="class in com.aspose.slides"><span class="typeNameLink">MathSuperscriptElementFactory</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IMathSuperscriptElementFactory.html" title="interface in com.aspose.slides">IMathSuperscriptElementFactory</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/Metered.html" title="class in com.aspose.slides"><span class="typeNameLink">Metered</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/MotionCmdPath.html" title="class in com.aspose.slides"><span class="typeNameLink">MotionCmdPath</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IMotionCmdPath.html" title="interface in com.aspose.slides">IMotionCmdPath</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/MotionPath.html" title="class in com.aspose.slides"><span class="typeNameLink">MotionPath</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IMotionPath.html" title="interface in com.aspose.slides">IMotionPath</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/NormalViewProperties.html" title="class in com.aspose.slides"><span class="typeNameLink">NormalViewProperties</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/INormalViewProperties.html" title="interface in com.aspose.slides">INormalViewProperties</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/NormalViewRestoredProperties.html" title="class in com.aspose.slides"><span class="typeNameLink">NormalViewRestoredProperties</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/INormalViewRestoredProperties.html" title="interface in com.aspose.slides">INormalViewRestoredProperties</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/NotesCommentsLayoutingOptions.html" title="class in com.aspose.slides"><span class="typeNameLink">NotesCommentsLayoutingOptions</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/INotesCommentsLayoutingOptions.html" title="interface in com.aspose.slides">INotesCommentsLayoutingOptions</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/NotesSize.html" title="class in com.aspose.slides"><span class="typeNameLink">NotesSize</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/INotesSize.html" title="interface in com.aspose.slides">INotesSize</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/OleEmbeddedDataInfo.html" title="class in com.aspose.slides"><span class="typeNameLink">OleEmbeddedDataInfo</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IOleEmbeddedDataInfo.html" title="interface in com.aspose.slides">IOleEmbeddedDataInfo</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/OpenAIWebClient.html" title="class in com.aspose.slides"><span class="typeNameLink">OpenAIWebClient</span></a> (implements java.io.Closeable, com.aspose.slides.<a href="../../../com/aspose/slides/IAIWebClient.html" title="interface in com.aspose.slides">IAIWebClient</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/OuterShadow.html" title="class in com.aspose.slides"><span class="typeNameLink">OuterShadow</span></a> (implements java.lang.Cloneable, com.aspose.slides.<a href="../../../com/aspose/slides/IOuterShadow.html" title="interface in com.aspose.slides">IOuterShadow</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/Output.html" title="class in com.aspose.slides"><span class="typeNameLink">Output</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/OutputFile.html" title="class in com.aspose.slides"><span class="typeNameLink">OutputFile</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IOutputFile.html" title="interface in com.aspose.slides">IOutputFile</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/Paragraph.html" title="class in com.aspose.slides"><span class="typeNameLink">Paragraph</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IParagraph.html" title="interface in com.aspose.slides">IParagraph</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ParagraphFactory.html" title="class in com.aspose.slides"><span class="typeNameLink">ParagraphFactory</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IParagraphFactory.html" title="interface in com.aspose.slides">IParagraphFactory</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/PathSegment.html" title="class in com.aspose.slides"><span class="typeNameLink">PathSegment</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IPathSegment.html" title="interface in com.aspose.slides">IPathSegment</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/PdfImportOptions.html" title="class in com.aspose.slides"><span class="typeNameLink">PdfImportOptions</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/Picture.html" title="class in com.aspose.slides"><span class="typeNameLink">Picture</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/ISlidesPicture.html" title="interface in com.aspose.slides">ISlidesPicture</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/PieSplitCustomPointCollection.html" title="class in com.aspose.slides"><span class="typeNameLink">PieSplitCustomPointCollection</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IPieSplitCustomPointCollection.html" title="interface in com.aspose.slides">IPieSplitCustomPointCollection</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/Placeholder.html" title="class in com.aspose.slides"><span class="typeNameLink">Placeholder</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IPlaceholder.html" title="interface in com.aspose.slides">IPlaceholder</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/Point.html" title="class in com.aspose.slides"><span class="typeNameLink">Point</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IPoint.html" title="interface in com.aspose.slides">IPoint</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/PointCollection.html" title="class in com.aspose.slides"><span class="typeNameLink">PointCollection</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IPointCollection.html" title="interface in com.aspose.slides">IPointCollection</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/Portion.html" title="class in com.aspose.slides"><span class="typeNameLink">Portion</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IPortion.html" title="interface in com.aspose.slides">IPortion</a>)
<ul>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/MathPortion.html" title="class in com.aspose.slides"><span class="typeNameLink">MathPortion</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IMathPortion.html" title="interface in com.aspose.slides">IMathPortion</a>)</li>
</ul>
</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/PortionFactory.html" title="class in com.aspose.slides"><span class="typeNameLink">PortionFactory</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IPortionFactory.html" title="interface in com.aspose.slides">IPortionFactory</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/PPImage.html" title="class in com.aspose.slides"><span class="typeNameLink">PPImage</span></a> (implements com.aspose.ms.System.IDisposable, com.aspose.slides.<a href="../../../com/aspose/slides/IPPImage.html" title="interface in com.aspose.slides">IPPImage</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/Presentation.html" title="class in com.aspose.slides"><span class="typeNameLink">Presentation</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IPresentation.html" title="interface in com.aspose.slides">IPresentation</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/PresentationAnimationsGenerator.html" title="class in com.aspose.slides"><span class="typeNameLink">PresentationAnimationsGenerator</span></a> (implements com.aspose.ms.System.IDisposable)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/PresentationFactory.html" title="class in com.aspose.slides"><span class="typeNameLink">PresentationFactory</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IPresentationFactory.html" title="interface in com.aspose.slides">IPresentationFactory</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/PresentationInfo.html" title="class in com.aspose.slides"><span class="typeNameLink">PresentationInfo</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IPresentationInfo.html" title="interface in com.aspose.slides">IPresentationInfo</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/PresentationPlayer.html" title="class in com.aspose.slides"><span class="typeNameLink">PresentationPlayer</span></a> (implements com.aspose.ms.System.IDisposable)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/PresentationText.html" title="class in com.aspose.slides"><span class="typeNameLink">PresentationText</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IPresentationText.html" title="interface in com.aspose.slides">IPresentationText</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/PresetShadow.html" title="class in com.aspose.slides"><span class="typeNameLink">PresetShadow</span></a> (implements java.lang.Cloneable, com.aspose.slides.<a href="../../../com/aspose/slides/IPresetShadow.html" title="interface in com.aspose.slides">IPresetShadow</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ProtectionManager.html" title="class in com.aspose.slides"><span class="typeNameLink">ProtectionManager</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IProtectionManager.html" title="interface in com.aspose.slides">IProtectionManager</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/PVIObject.html" title="class in com.aspose.slides"><span class="typeNameLink">PVIObject</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/ISlideComponent.html" title="interface in com.aspose.slides">ISlideComponent</a>)
<ul>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/Backdrop3DScene.html" title="class in com.aspose.slides"><span class="typeNameLink">Backdrop3DScene</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IBackdrop3DScene.html" title="interface in com.aspose.slides">IBackdrop3DScene</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/Background.html" title="class in com.aspose.slides"><span class="typeNameLink">Background</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IBackground.html" title="interface in com.aspose.slides">IBackground</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/BasePortionFormat.html" title="class in com.aspose.slides"><span class="typeNameLink">BasePortionFormat</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IBasePortionFormat.html" title="interface in com.aspose.slides">IBasePortionFormat</a>)
<ul>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ChartPortionFormat.html" title="class in com.aspose.slides"><span class="typeNameLink">ChartPortionFormat</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IChartPortionFormat.html" title="interface in com.aspose.slides">IChartPortionFormat</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/PortionFormat.html" title="class in com.aspose.slides"><span class="typeNameLink">PortionFormat</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IPortionFormat.html" title="interface in com.aspose.slides">IPortionFormat</a>)</li>
</ul>
</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/BulletFormat.html" title="class in com.aspose.slides"><span class="typeNameLink">BulletFormat</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IBulletFormat.html" title="interface in com.aspose.slides">IBulletFormat</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/Camera.html" title="class in com.aspose.slides"><span class="typeNameLink">Camera</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/ICamera.html" title="interface in com.aspose.slides">ICamera</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/CellFormat.html" title="class in com.aspose.slides"><span class="typeNameLink">CellFormat</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/ICellFormat.html" title="interface in com.aspose.slides">ICellFormat</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ColorFormat.html" title="class in com.aspose.slides"><span class="typeNameLink">ColorFormat</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IColorFormat.html" title="interface in com.aspose.slides">IColorFormat</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/DataLabelFormat.html" title="class in com.aspose.slides"><span class="typeNameLink">DataLabelFormat</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IDataLabelFormat.html" title="interface in com.aspose.slides">IDataLabelFormat</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/EffectFormat.html" title="class in com.aspose.slides"><span class="typeNameLink">EffectFormat</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IEffectFormat.html" title="interface in com.aspose.slides">IEffectFormat</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/FillFormat.html" title="class in com.aspose.slides"><span class="typeNameLink">FillFormat</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IFillFormat.html" title="interface in com.aspose.slides">IFillFormat</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/Format.html" title="class in com.aspose.slides"><span class="typeNameLink">Format</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IFormat.html" title="interface in com.aspose.slides">IFormat</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/GradientFormat.html" title="class in com.aspose.slides"><span class="typeNameLink">GradientFormat</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IGradientFormat.html" title="interface in com.aspose.slides">IGradientFormat</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/GradientStop.html" title="class in com.aspose.slides"><span class="typeNameLink">GradientStop</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IGradientStop.html" title="interface in com.aspose.slides">IGradientStop</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/GradientStopCollection.html" title="class in com.aspose.slides"><span class="typeNameLink">GradientStopCollection</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IGradientStopCollection.html" title="interface in com.aspose.slides">IGradientStopCollection</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/Hyperlink.html" title="class in com.aspose.slides"><span class="typeNameLink">Hyperlink</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IHyperlink.html" title="interface in com.aspose.slides">IHyperlink</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ImageTransformOperation.html" title="class in com.aspose.slides"><span class="typeNameLink">ImageTransformOperation</span></a> (implements java.lang.Cloneable, com.aspose.slides.<a href="../../../com/aspose/slides/IImageTransformOperation.html" title="interface in com.aspose.slides">IImageTransformOperation</a>)
<ul>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/AlphaBiLevel.html" title="class in com.aspose.slides"><span class="typeNameLink">AlphaBiLevel</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IAlphaBiLevel.html" title="interface in com.aspose.slides">IAlphaBiLevel</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/AlphaCeiling.html" title="class in com.aspose.slides"><span class="typeNameLink">AlphaCeiling</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IAlphaCeiling.html" title="interface in com.aspose.slides">IAlphaCeiling</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/AlphaFloor.html" title="class in com.aspose.slides"><span class="typeNameLink">AlphaFloor</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IAlphaFloor.html" title="interface in com.aspose.slides">IAlphaFloor</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/AlphaInverse.html" title="class in com.aspose.slides"><span class="typeNameLink">AlphaInverse</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IAlphaInverse.html" title="interface in com.aspose.slides">IAlphaInverse</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/AlphaModulate.html" title="class in com.aspose.slides"><span class="typeNameLink">AlphaModulate</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IAlphaModulate.html" title="interface in com.aspose.slides">IAlphaModulate</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/AlphaModulateFixed.html" title="class in com.aspose.slides"><span class="typeNameLink">AlphaModulateFixed</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IAlphaModulateFixed.html" title="interface in com.aspose.slides">IAlphaModulateFixed</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/AlphaReplace.html" title="class in com.aspose.slides"><span class="typeNameLink">AlphaReplace</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IAlphaReplace.html" title="interface in com.aspose.slides">IAlphaReplace</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/BiLevel.html" title="class in com.aspose.slides"><span class="typeNameLink">BiLevel</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IBiLevel.html" title="interface in com.aspose.slides">IBiLevel</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/Blur.html" title="class in com.aspose.slides"><span class="typeNameLink">Blur</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IBlur.html" title="interface in com.aspose.slides">IBlur</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ColorChange.html" title="class in com.aspose.slides"><span class="typeNameLink">ColorChange</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IColorChange.html" title="interface in com.aspose.slides">IColorChange</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ColorReplace.html" title="class in com.aspose.slides"><span class="typeNameLink">ColorReplace</span></a> (implements java.lang.Cloneable, com.aspose.slides.<a href="../../../com/aspose/slides/IColorReplace.html" title="interface in com.aspose.slides">IColorReplace</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/Duotone.html" title="class in com.aspose.slides"><span class="typeNameLink">Duotone</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IDuotone.html" title="interface in com.aspose.slides">IDuotone</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/FillOverlay.html" title="class in com.aspose.slides"><span class="typeNameLink">FillOverlay</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IFillOverlay.html" title="interface in com.aspose.slides">IFillOverlay</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/GrayScale.html" title="class in com.aspose.slides"><span class="typeNameLink">GrayScale</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IGrayScale.html" title="interface in com.aspose.slides">IGrayScale</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/HSL.html" title="class in com.aspose.slides"><span class="typeNameLink">HSL</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IHSL.html" title="interface in com.aspose.slides">IHSL</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/Luminance.html" title="class in com.aspose.slides"><span class="typeNameLink">Luminance</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/ILuminance.html" title="interface in com.aspose.slides">ILuminance</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/Tint.html" title="class in com.aspose.slides"><span class="typeNameLink">Tint</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/ITint.html" title="interface in com.aspose.slides">ITint</a>)</li>
</ul>
</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ImageTransformOperationCollection.html" title="class in com.aspose.slides"><span class="typeNameLink">ImageTransformOperationCollection</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IImageTransformOperationCollection.html" title="interface in com.aspose.slides">IImageTransformOperationCollection</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/LightRig.html" title="class in com.aspose.slides"><span class="typeNameLink">LightRig</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/ILightRig.html" title="interface in com.aspose.slides">ILightRig</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/LineFillFormat.html" title="class in com.aspose.slides"><span class="typeNameLink">LineFillFormat</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/ILineFillFormat.html" title="interface in com.aspose.slides">ILineFillFormat</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/LineFormat.html" title="class in com.aspose.slides"><span class="typeNameLink">LineFormat</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/ILineFormat.html" title="interface in com.aspose.slides">ILineFormat</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ParagraphFormat.html" title="class in com.aspose.slides"><span class="typeNameLink">ParagraphFormat</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IChartParagraphFormat.html" title="interface in com.aspose.slides">IChartParagraphFormat</a>, com.aspose.slides.<a href="../../../com/aspose/slides/IParagraphFormat.html" title="interface in com.aspose.slides">IParagraphFormat</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/PatternFormat.html" title="class in com.aspose.slides"><span class="typeNameLink">PatternFormat</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IPatternFormat.html" title="interface in com.aspose.slides">IPatternFormat</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/PictureFillFormat.html" title="class in com.aspose.slides"><span class="typeNameLink">PictureFillFormat</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IPictureFillFormat.html" title="interface in com.aspose.slides">IPictureFillFormat</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ShapeBevel.html" title="class in com.aspose.slides"><span class="typeNameLink">ShapeBevel</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IShapeBevel.html" title="interface in com.aspose.slides">IShapeBevel</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/SketchFormat.html" title="class in com.aspose.slides"><span class="typeNameLink">SketchFormat</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/ISketchFormat.html" title="interface in com.aspose.slides">ISketchFormat</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/Tab.html" title="class in com.aspose.slides"><span class="typeNameLink">Tab</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/ITab.html" title="interface in com.aspose.slides">ITab</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/TextFrameFormat.html" title="class in com.aspose.slides"><span class="typeNameLink">TextFrameFormat</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IChartTextBlockFormat.html" title="interface in com.aspose.slides">IChartTextBlockFormat</a>, com.aspose.slides.<a href="../../../com/aspose/slides/ITextFrameFormat.html" title="interface in com.aspose.slides">ITextFrameFormat</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/TextStyle.html" title="class in com.aspose.slides"><span class="typeNameLink">TextStyle</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/ITextStyle.html" title="interface in com.aspose.slides">ITextStyle</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ThreeDFormat.html" title="class in com.aspose.slides"><span class="typeNameLink">ThreeDFormat</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IThreeDFormat.html" title="interface in com.aspose.slides">IThreeDFormat</a>, com.aspose.slides.<a href="../../../com/aspose/slides/IThreeDParamSource.html" title="interface in com.aspose.slides">IThreeDParamSource</a>)</li>
</ul>
</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/Reflection.html" title="class in com.aspose.slides"><span class="typeNameLink">Reflection</span></a> (implements java.lang.Cloneable, com.aspose.slides.<a href="../../../com/aspose/slides/IReflection.html" title="interface in com.aspose.slides">IReflection</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ResponsiveHtmlController.html" title="class in com.aspose.slides"><span class="typeNameLink">ResponsiveHtmlController</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IResponsiveHtmlController.html" title="interface in com.aspose.slides">IResponsiveHtmlController</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/Rotation3D.html" title="class in com.aspose.slides"><span class="typeNameLink">Rotation3D</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IRotation3D.html" title="interface in com.aspose.slides">IRotation3D</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/SaveOptions.html" title="class in com.aspose.slides"><span class="typeNameLink">SaveOptions</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/ISaveOptions.html" title="interface in com.aspose.slides">ISaveOptions</a>)
<ul>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/GifOptions.html" title="class in com.aspose.slides"><span class="typeNameLink">GifOptions</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IGifOptions.html" title="interface in com.aspose.slides">IGifOptions</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/Html5Options.html" title="class in com.aspose.slides"><span class="typeNameLink">Html5Options</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IHtml5Options.html" title="interface in com.aspose.slides">IHtml5Options</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/HtmlOptions.html" title="class in com.aspose.slides"><span class="typeNameLink">HtmlOptions</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IHtmlOptions.html" title="interface in com.aspose.slides">IHtmlOptions</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/MarkdownSaveOptions.html" title="class in com.aspose.slides"><span class="typeNameLink">MarkdownSaveOptions</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/PdfOptions.html" title="class in com.aspose.slides"><span class="typeNameLink">PdfOptions</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IPdfOptions.html" title="interface in com.aspose.slides">IPdfOptions</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/PptOptions.html" title="class in com.aspose.slides"><span class="typeNameLink">PptOptions</span></a> (implements java.lang.Cloneable, com.aspose.slides.<a href="../../../com/aspose/slides/IPptOptions.html" title="interface in com.aspose.slides">IPptOptions</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/PptxOptions.html" title="class in com.aspose.slides"><span class="typeNameLink">PptxOptions</span></a> (implements java.lang.Cloneable, com.aspose.slides.<a href="../../../com/aspose/slides/IPptxOptions.html" title="interface in com.aspose.slides">IPptxOptions</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/RenderingOptions.html" title="class in com.aspose.slides"><span class="typeNameLink">RenderingOptions</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IRenderingOptions.html" title="interface in com.aspose.slides">IRenderingOptions</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/SVGOptions.html" title="class in com.aspose.slides"><span class="typeNameLink">SVGOptions</span></a> (implements java.lang.Cloneable, com.aspose.slides.<a href="../../../com/aspose/slides/ISVGOptions.html" title="interface in com.aspose.slides">ISVGOptions</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/SwfOptions.html" title="class in com.aspose.slides"><span class="typeNameLink">SwfOptions</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/ISwfOptions.html" title="interface in com.aspose.slides">ISwfOptions</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/TiffOptions.html" title="class in com.aspose.slides"><span class="typeNameLink">TiffOptions</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/ITiffOptions.html" title="interface in com.aspose.slides">ITiffOptions</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/XamlOptions.html" title="class in com.aspose.slides"><span class="typeNameLink">XamlOptions</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IXamlOptions.html" title="interface in com.aspose.slides">IXamlOptions</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/XpsOptions.html" title="class in com.aspose.slides"><span class="typeNameLink">XpsOptions</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IXpsOptions.html" title="interface in com.aspose.slides">IXpsOptions</a>)</li>
</ul>
</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/SaveOptionsFactory.html" title="class in com.aspose.slides"><span class="typeNameLink">SaveOptionsFactory</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/ISaveOptionsFactory.html" title="interface in com.aspose.slides">ISaveOptionsFactory</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/Sequence.html" title="class in com.aspose.slides"><span class="typeNameLink">Sequence</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/ISequence.html" title="interface in com.aspose.slides">ISequence</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/SequenceCollection.html" title="class in com.aspose.slides"><span class="typeNameLink">SequenceCollection</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/ISequenceCollection.html" title="interface in com.aspose.slides">ISequenceCollection</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/Shape.html" title="class in com.aspose.slides"><span class="typeNameLink">Shape</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a>)
<ul>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/GeometryShape.html" title="class in com.aspose.slides"><span class="typeNameLink">GeometryShape</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IGeometryShape.html" title="interface in com.aspose.slides">IGeometryShape</a>)
<ul>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/AutoShape.html" title="class in com.aspose.slides"><span class="typeNameLink">AutoShape</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IAutoShape.html" title="interface in com.aspose.slides">IAutoShape</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/Connector.html" title="class in com.aspose.slides"><span class="typeNameLink">Connector</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IConnector.html" title="interface in com.aspose.slides">IConnector</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/PictureFrame.html" title="class in com.aspose.slides"><span class="typeNameLink">PictureFrame</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IPictureFrame.html" title="interface in com.aspose.slides">IPictureFrame</a>)
<ul>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/AudioFrame.html" title="class in com.aspose.slides"><span class="typeNameLink">AudioFrame</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IAudioFrame.html" title="interface in com.aspose.slides">IAudioFrame</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/VideoFrame.html" title="class in com.aspose.slides"><span class="typeNameLink">VideoFrame</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IVideoFrame.html" title="interface in com.aspose.slides">IVideoFrame</a>)</li>
</ul>
</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/SmartArtShape.html" title="class in com.aspose.slides"><span class="typeNameLink">SmartArtShape</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/ISmartArtShape.html" title="interface in com.aspose.slides">ISmartArtShape</a>)</li>
</ul>
</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/GraphicalObject.html" title="class in com.aspose.slides"><span class="typeNameLink">GraphicalObject</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IGraphicalObject.html" title="interface in com.aspose.slides">IGraphicalObject</a>)
<ul>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/Chart.html" title="class in com.aspose.slides"><span class="typeNameLink">Chart</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IChart.html" title="interface in com.aspose.slides">IChart</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/Ink.html" title="class in com.aspose.slides"><span class="typeNameLink">Ink</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IInk.html" title="interface in com.aspose.slides">IInk</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/InkActions.html" title="class in com.aspose.slides"><span class="typeNameLink">InkActions</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IInkActions.html" title="interface in com.aspose.slides">IInkActions</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/LegacyDiagram.html" title="class in com.aspose.slides"><span class="typeNameLink">LegacyDiagram</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/ILegacyDiagram.html" title="interface in com.aspose.slides">ILegacyDiagram</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/OleObjectFrame.html" title="class in com.aspose.slides"><span class="typeNameLink">OleObjectFrame</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IOleObjectFrame.html" title="interface in com.aspose.slides">IOleObjectFrame</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/SmartArt.html" title="class in com.aspose.slides"><span class="typeNameLink">SmartArt</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/ISmartArt.html" title="interface in com.aspose.slides">ISmartArt</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/SummaryZoomFrame.html" title="class in com.aspose.slides"><span class="typeNameLink">SummaryZoomFrame</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/ISummaryZoomFrame.html" title="interface in com.aspose.slides">ISummaryZoomFrame</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/Table.html" title="class in com.aspose.slides"><span class="typeNameLink">Table</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/ITable.html" title="interface in com.aspose.slides">ITable</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ZoomObject.html" title="class in com.aspose.slides"><span class="typeNameLink">ZoomObject</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IZoomObject.html" title="interface in com.aspose.slides">IZoomObject</a>)
<ul>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/SectionZoomFrame.html" title="class in com.aspose.slides"><span class="typeNameLink">SectionZoomFrame</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/ISectionZoomFrame.html" title="interface in com.aspose.slides">ISectionZoomFrame</a>)
<ul>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/SummaryZoomSection.html" title="class in com.aspose.slides"><span class="typeNameLink">SummaryZoomSection</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/ISummaryZoomSection.html" title="interface in com.aspose.slides">ISummaryZoomSection</a>)</li>
</ul>
</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ZoomFrame.html" title="class in com.aspose.slides"><span class="typeNameLink">ZoomFrame</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IZoomFrame.html" title="interface in com.aspose.slides">IZoomFrame</a>)</li>
</ul>
</li>
</ul>
</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/GroupShape.html" title="class in com.aspose.slides"><span class="typeNameLink">GroupShape</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IGroupShape.html" title="interface in com.aspose.slides">IGroupShape</a>)</li>
</ul>
</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ShapeElement.html" title="class in com.aspose.slides"><span class="typeNameLink">ShapeElement</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IShapeElement.html" title="interface in com.aspose.slides">IShapeElement</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ShapeFrame.html" title="class in com.aspose.slides"><span class="typeNameLink">ShapeFrame</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IShapeFrame.html" title="interface in com.aspose.slides">IShapeFrame</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ShapeUtil.html" title="class in com.aspose.slides"><span class="typeNameLink">ShapeUtil</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/SlideImageFormat.html" title="class in com.aspose.slides"><span class="typeNameLink">SlideImageFormat</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/ISlideImageFormat.html" title="interface in com.aspose.slides">ISlideImageFormat</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/SlidesAIAgent.html" title="class in com.aspose.slides"><span class="typeNameLink">SlidesAIAgent</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/SlideShowSettings.html" title="class in com.aspose.slides"><span class="typeNameLink">SlideShowSettings</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/SlideShowType.html" title="class in com.aspose.slides"><span class="typeNameLink">SlideShowType</span></a>
<ul>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/BrowsedAtKiosk.html" title="class in com.aspose.slides"><span class="typeNameLink">BrowsedAtKiosk</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/BrowsedByIndividual.html" title="class in com.aspose.slides"><span class="typeNameLink">BrowsedByIndividual</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/PresentedBySpeaker.html" title="class in com.aspose.slides"><span class="typeNameLink">PresentedBySpeaker</span></a></li>
</ul>
</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/SlidesRange.html" title="class in com.aspose.slides"><span class="typeNameLink">SlidesRange</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/SlideUtil.html" title="class in com.aspose.slides"><span class="typeNameLink">SlideUtil</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/SmartArtNode.html" title="class in com.aspose.slides"><span class="typeNameLink">SmartArtNode</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/ISmartArtNode.html" title="interface in com.aspose.slides">ISmartArtNode</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/SmartArtNodeCollection.html" title="class in com.aspose.slides"><span class="typeNameLink">SmartArtNodeCollection</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/ISmartArtNodeCollection.html" title="interface in com.aspose.slides">ISmartArtNodeCollection</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/SmartArtShapeCollection.html" title="class in com.aspose.slides"><span class="typeNameLink">SmartArtShapeCollection</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/ISmartArtShapeCollection.html" title="interface in com.aspose.slides">ISmartArtShapeCollection</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/SoftEdge.html" title="class in com.aspose.slides"><span class="typeNameLink">SoftEdge</span></a> (implements java.lang.Cloneable, com.aspose.slides.<a href="../../../com/aspose/slides/ISoftEdge.html" title="interface in com.aspose.slides">ISoftEdge</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/SpreadsheetOptions.html" title="class in com.aspose.slides"><span class="typeNameLink">SpreadsheetOptions</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/ISpreadsheetOptions.html" title="interface in com.aspose.slides">ISpreadsheetOptions</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/Storage.html" title="class in com.aspose.slides"><span class="typeNameLink">Storage</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/SvgImage.html" title="class in com.aspose.slides"><span class="typeNameLink">SvgImage</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/ISvgImage.html" title="interface in com.aspose.slides">ISvgImage</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/SvgShape.html" title="class in com.aspose.slides"><span class="typeNameLink">SvgShape</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/ISvgShape.html" title="interface in com.aspose.slides">ISvgShape</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/SvgTSpan.html" title="class in com.aspose.slides"><span class="typeNameLink">SvgTSpan</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/ISvgTSpan.html" title="interface in com.aspose.slides">ISvgTSpan</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/TabCollection.html" title="class in com.aspose.slides"><span class="typeNameLink">TabCollection</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/ITabCollection.html" title="interface in com.aspose.slides">ITabCollection</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/TabFactory.html" title="class in com.aspose.slides"><span class="typeNameLink">TabFactory</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/ITabFactory.html" title="interface in com.aspose.slides">ITabFactory</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/TagCollection.html" title="class in com.aspose.slides"><span class="typeNameLink">TagCollection</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/ITagCollection.html" title="interface in com.aspose.slides">ITagCollection</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/TemplateContext.html" title="class in com.aspose.slides"><span class="typeNameLink">TemplateContext</span></a>&lt;TObject&gt;</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/TextAnimation.html" title="class in com.aspose.slides"><span class="typeNameLink">TextAnimation</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/ITextAnimation.html" title="interface in com.aspose.slides">ITextAnimation</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/TextAnimationCollection.html" title="class in com.aspose.slides"><span class="typeNameLink">TextAnimationCollection</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/ITextAnimationCollection.html" title="interface in com.aspose.slides">ITextAnimationCollection</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/TextFrame.html" title="class in com.aspose.slides"><span class="typeNameLink">TextFrame</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/ITextFrame.html" title="interface in com.aspose.slides">ITextFrame</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/TextHighlightingOptions.html" title="class in com.aspose.slides"><span class="typeNameLink">TextHighlightingOptions</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/ITextHighlightingOptions.html" title="interface in com.aspose.slides">ITextHighlightingOptions</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/TextSearchOptions.html" title="class in com.aspose.slides"><span class="typeNameLink">TextSearchOptions</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/ITextSearchOptions.html" title="interface in com.aspose.slides">ITextSearchOptions</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/TextToHtmlConversionOptions.html" title="class in com.aspose.slides"><span class="typeNameLink">TextToHtmlConversionOptions</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/ITextToHtmlConversionOptions.html" title="interface in com.aspose.slides">ITextToHtmlConversionOptions</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/Theme.html" title="class in com.aspose.slides"><span class="typeNameLink">Theme</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/ITheme.html" title="interface in com.aspose.slides">ITheme</a>)
<ul>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/MasterTheme.html" title="class in com.aspose.slides"><span class="typeNameLink">MasterTheme</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IMasterTheme.html" title="interface in com.aspose.slides">IMasterTheme</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/OverrideTheme.html" title="class in com.aspose.slides"><span class="typeNameLink">OverrideTheme</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IOverrideTheme.html" title="interface in com.aspose.slides">IOverrideTheme</a>)</li>
</ul>
</li>
<li type="circle">java.lang.Throwable (implements java.io.Serializable)
<ul>
<li type="circle">java.lang.Exception
<ul>
<li type="circle">java.lang.RuntimeException
<ul>
<li type="circle">com.aspose.ms.System.Exception
<ul>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/AsposeLicenseException.html" title="class in com.aspose.slides"><span class="typeNameLink">AsposeLicenseException</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/InvalidPasswordException.html" title="class in com.aspose.slides"><span class="typeNameLink">InvalidPasswordException</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/OdpException.html" title="class in com.aspose.slides"><span class="typeNameLink">OdpException</span></a>
<ul>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/OdpReadException.html" title="class in com.aspose.slides"><span class="typeNameLink">OdpReadException</span></a></li>
</ul>
</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/OOXMLException.html" title="class in com.aspose.slides"><span class="typeNameLink">OOXMLException</span></a>
<ul>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/OOXMLCorruptFileException.html" title="class in com.aspose.slides"><span class="typeNameLink">OOXMLCorruptFileException</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/PptxException.html" title="class in com.aspose.slides"><span class="typeNameLink">PptxException</span></a>
<ul>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/PptxEditException.html" title="class in com.aspose.slides"><span class="typeNameLink">PptxEditException</span></a>
<ul>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/CellCircularReferenceException.html" title="class in com.aspose.slides"><span class="typeNameLink">CellCircularReferenceException</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/CellInvalidFormulaException.html" title="class in com.aspose.slides"><span class="typeNameLink">CellInvalidFormulaException</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/CellInvalidReferenceException.html" title="class in com.aspose.slides"><span class="typeNameLink">CellInvalidReferenceException</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/CellUnsupportedDataException.html" title="class in com.aspose.slides"><span class="typeNameLink">CellUnsupportedDataException</span></a></li>
</ul>
</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/PptxReadException.html" title="class in com.aspose.slides"><span class="typeNameLink">PptxReadException</span></a>
<ul>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/PptxCorruptFileException.html" title="class in com.aspose.slides"><span class="typeNameLink">PptxCorruptFileException</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/PptxUnsupportedFormatException.html" title="class in com.aspose.slides"><span class="typeNameLink">PptxUnsupportedFormatException</span></a></li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/PptException.html" title="class in com.aspose.slides"><span class="typeNameLink">PptException</span></a>
<ul>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/PptEditException.html" title="class in com.aspose.slides"><span class="typeNameLink">PptEditException</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/PptReadException.html" title="class in com.aspose.slides"><span class="typeNameLink">PptReadException</span></a>
<ul>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/PptCorruptFileException.html" title="class in com.aspose.slides"><span class="typeNameLink">PptCorruptFileException</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/PptUnsupportedFormatException.html" title="class in com.aspose.slides"><span class="typeNameLink">PptUnsupportedFormatException</span></a></li>
</ul>
</li>
</ul>
</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/SlidesAIAgentException.html" title="class in com.aspose.slides"><span class="typeNameLink">SlidesAIAgentException</span></a></li>
<li type="circle">com.aspose.ms.System.SystemException
<ul>
<li type="circle">com.aspose.ms.System.InvalidOperationException
<ul>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/AxesCompositionNotCombinableException.html" title="class in com.aspose.slides"><span class="typeNameLink">AxesCompositionNotCombinableException</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/CannotCombine2DAnd3DChartsException.html" title="class in com.aspose.slides"><span class="typeNameLink">CannotCombine2DAnd3DChartsException</span></a></li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/Timing.html" title="class in com.aspose.slides"><span class="typeNameLink">Timing</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/ITiming.html" title="interface in com.aspose.slides">ITiming</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/TransitionValueBase.html" title="class in com.aspose.slides"><span class="typeNameLink">TransitionValueBase</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/ITransitionValueBase.html" title="interface in com.aspose.slides">ITransitionValueBase</a>)
<ul>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/CornerDirectionTransition.html" title="class in com.aspose.slides"><span class="typeNameLink">CornerDirectionTransition</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/ICornerDirectionTransition.html" title="interface in com.aspose.slides">ICornerDirectionTransition</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/EightDirectionTransition.html" title="class in com.aspose.slides"><span class="typeNameLink">EightDirectionTransition</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IEightDirectionTransition.html" title="interface in com.aspose.slides">IEightDirectionTransition</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/EmptyTransition.html" title="class in com.aspose.slides"><span class="typeNameLink">EmptyTransition</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IEmptyTransition.html" title="interface in com.aspose.slides">IEmptyTransition</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/FlyThroughTransition.html" title="class in com.aspose.slides"><span class="typeNameLink">FlyThroughTransition</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IFlyThroughTransition.html" title="interface in com.aspose.slides">IFlyThroughTransition</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/GlitterTransition.html" title="class in com.aspose.slides"><span class="typeNameLink">GlitterTransition</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IGlitterTransition.html" title="interface in com.aspose.slides">IGlitterTransition</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/InOutTransition.html" title="class in com.aspose.slides"><span class="typeNameLink">InOutTransition</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IInOutTransition.html" title="interface in com.aspose.slides">IInOutTransition</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/LeftRightDirectionTransition.html" title="class in com.aspose.slides"><span class="typeNameLink">LeftRightDirectionTransition</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/ILeftRightDirectionTransition.html" title="interface in com.aspose.slides">ILeftRightDirectionTransition</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/MorphTransition.html" title="class in com.aspose.slides"><span class="typeNameLink">MorphTransition</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IMorphTransition.html" title="interface in com.aspose.slides">IMorphTransition</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/OptionalBlackTransition.html" title="class in com.aspose.slides"><span class="typeNameLink">OptionalBlackTransition</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IOptionalBlackTransition.html" title="interface in com.aspose.slides">IOptionalBlackTransition</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/OrientationTransition.html" title="class in com.aspose.slides"><span class="typeNameLink">OrientationTransition</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IOrientationTransition.html" title="interface in com.aspose.slides">IOrientationTransition</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/RevealTransition.html" title="class in com.aspose.slides"><span class="typeNameLink">RevealTransition</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IRevealTransition.html" title="interface in com.aspose.slides">IRevealTransition</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/RippleTransition.html" title="class in com.aspose.slides"><span class="typeNameLink">RippleTransition</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IRippleTransition.html" title="interface in com.aspose.slides">IRippleTransition</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ShredTransition.html" title="class in com.aspose.slides"><span class="typeNameLink">ShredTransition</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IShredTransition.html" title="interface in com.aspose.slides">IShredTransition</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/SideDirectionTransition.html" title="class in com.aspose.slides"><span class="typeNameLink">SideDirectionTransition</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/ISideDirectionTransition.html" title="interface in com.aspose.slides">ISideDirectionTransition</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/SplitTransition.html" title="class in com.aspose.slides"><span class="typeNameLink">SplitTransition</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/ISplitTransition.html" title="interface in com.aspose.slides">ISplitTransition</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/WheelTransition.html" title="class in com.aspose.slides"><span class="typeNameLink">WheelTransition</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IWheelTransition.html" title="interface in com.aspose.slides">IWheelTransition</a>)</li>
</ul>
</li>
<li type="circle">com.aspose.ms.System.ValueType&lt;T&gt;
<ul>
<li type="circle">com.aspose.ms.System.Enum
<ul>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/AfterAnimationType.html" title="class in com.aspose.slides"><span class="typeNameLink">AfterAnimationType</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/AnimateTextType.html" title="class in com.aspose.slides"><span class="typeNameLink">AnimateTextType</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/AudioPlayModePreset.html" title="class in com.aspose.slides"><span class="typeNameLink">AudioPlayModePreset</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/AudioVolumeMode.html" title="class in com.aspose.slides"><span class="typeNameLink">AudioVolumeMode</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/AxisAggregationType.html" title="class in com.aspose.slides"><span class="typeNameLink">AxisAggregationType</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/AxisPositionType.html" title="class in com.aspose.slides"><span class="typeNameLink">AxisPositionType</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/BackgroundType.html" title="class in com.aspose.slides"><span class="typeNameLink">BackgroundType</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/BehaviorAccumulateType.html" title="class in com.aspose.slides"><span class="typeNameLink">BehaviorAccumulateType</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/BehaviorAdditiveType.html" title="class in com.aspose.slides"><span class="typeNameLink">BehaviorAdditiveType</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/BevelPresetType.html" title="class in com.aspose.slides"><span class="typeNameLink">BevelPresetType</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/BlackWhiteConversionMode.html" title="class in com.aspose.slides"><span class="typeNameLink">BlackWhiteConversionMode</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/BlackWhiteMode.html" title="class in com.aspose.slides"><span class="typeNameLink">BlackWhiteMode</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/BubbleSizeRepresentationType.html" title="class in com.aspose.slides"><span class="typeNameLink">BubbleSizeRepresentationType</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/BuildType.html" title="class in com.aspose.slides"><span class="typeNameLink">BuildType</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/BulletType.html" title="class in com.aspose.slides"><span class="typeNameLink">BulletType</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/CameraPresetType.html" title="class in com.aspose.slides"><span class="typeNameLink">CameraPresetType</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/CategoryAxisType.html" title="class in com.aspose.slides"><span class="typeNameLink">CategoryAxisType</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ChartDataSourceType.html" title="class in com.aspose.slides"><span class="typeNameLink">ChartDataSourceType</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ChartShapeType.html" title="class in com.aspose.slides"><span class="typeNameLink">ChartShapeType</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ChartType.html" title="class in com.aspose.slides"><span class="typeNameLink">ChartType</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ColorDirection.html" title="class in com.aspose.slides"><span class="typeNameLink">ColorDirection</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ColorSchemeIndex.html" title="class in com.aspose.slides"><span class="typeNameLink">ColorSchemeIndex</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ColorSpace.html" title="class in com.aspose.slides"><span class="typeNameLink">ColorSpace</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ColorStringFormat.html" title="class in com.aspose.slides"><span class="typeNameLink">ColorStringFormat</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ColorTransformOperation.html" title="class in com.aspose.slides"><span class="typeNameLink">ColorTransformOperation</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ColorType.html" title="class in com.aspose.slides"><span class="typeNameLink">ColorType</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/CombinableSeriesTypesGroup.html" title="class in com.aspose.slides"><span class="typeNameLink">CombinableSeriesTypesGroup</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/CommandEffectType.html" title="class in com.aspose.slides"><span class="typeNameLink">CommandEffectType</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/CommentsPositions.html" title="class in com.aspose.slides"><span class="typeNameLink">CommentsPositions</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/Conformance.html" title="class in com.aspose.slides"><span class="typeNameLink">Conformance</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ControlType.html" title="class in com.aspose.slides"><span class="typeNameLink">ControlType</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/CrossesType.html" title="class in com.aspose.slides"><span class="typeNameLink">CrossesType</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/DataSourceType.html" title="class in com.aspose.slides"><span class="typeNameLink">DataSourceType</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/DisplayBlanksAsType.html" title="class in com.aspose.slides"><span class="typeNameLink">DisplayBlanksAsType</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/DisplayUnitType.html" title="class in com.aspose.slides"><span class="typeNameLink">DisplayUnitType</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/EffectChartMajorGroupingType.html" title="class in com.aspose.slides"><span class="typeNameLink">EffectChartMajorGroupingType</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/EffectChartMinorGroupingType.html" title="class in com.aspose.slides"><span class="typeNameLink">EffectChartMinorGroupingType</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/EffectFillType.html" title="class in com.aspose.slides"><span class="typeNameLink">EffectFillType</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/EffectPresetClassType.html" title="class in com.aspose.slides"><span class="typeNameLink">EffectPresetClassType</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/EffectRestartType.html" title="class in com.aspose.slides"><span class="typeNameLink">EffectRestartType</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/EffectSubtype.html" title="class in com.aspose.slides"><span class="typeNameLink">EffectSubtype</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/EffectTriggerType.html" title="class in com.aspose.slides"><span class="typeNameLink">EffectTriggerType</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/EffectType.html" title="class in com.aspose.slides"><span class="typeNameLink">EffectType</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/EmbeddingLevel.html" title="class in com.aspose.slides"><span class="typeNameLink">EmbeddingLevel</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/EmbedFontCharacters.html" title="class in com.aspose.slides"><span class="typeNameLink">EmbedFontCharacters</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ErrorBarType.html" title="class in com.aspose.slides"><span class="typeNameLink">ErrorBarType</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ErrorBarValueType.html" title="class in com.aspose.slides"><span class="typeNameLink">ErrorBarValueType</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/FillBlendMode.html" title="class in com.aspose.slides"><span class="typeNameLink">FillBlendMode</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/FillType.html" title="class in com.aspose.slides"><span class="typeNameLink">FillType</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/FilterEffectRevealType.html" title="class in com.aspose.slides"><span class="typeNameLink">FilterEffectRevealType</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/FilterEffectSubtype.html" title="class in com.aspose.slides"><span class="typeNameLink">FilterEffectSubtype</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/FilterEffectType.html" title="class in com.aspose.slides"><span class="typeNameLink">FilterEffectType</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/Flavor.html" title="class in com.aspose.slides"><span class="typeNameLink">Flavor</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/FontAlignment.html" title="class in com.aspose.slides"><span class="typeNameLink">FontAlignment</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/FontCollectionIndex.html" title="class in com.aspose.slides"><span class="typeNameLink">FontCollectionIndex</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/FontStyle.html" title="class in com.aspose.slides"><span class="typeNameLink">FontStyle</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/FontSubstCondition.html" title="class in com.aspose.slides"><span class="typeNameLink">FontSubstCondition</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/GradientDirection.html" title="class in com.aspose.slides"><span class="typeNameLink">GradientDirection</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/GradientShape.html" title="class in com.aspose.slides"><span class="typeNameLink">GradientShape</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/GradientStyle.html" title="class in com.aspose.slides"><span class="typeNameLink">GradientStyle</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/HandleRepeatedSpaces.html" title="class in com.aspose.slides"><span class="typeNameLink">HandleRepeatedSpaces</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/HandoutType.html" title="class in com.aspose.slides"><span class="typeNameLink">HandoutType</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/HyperlinkActionType.html" title="class in com.aspose.slides"><span class="typeNameLink">HyperlinkActionType</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/HyperlinkColorSource.html" title="class in com.aspose.slides"><span class="typeNameLink">HyperlinkColorSource</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ImageFormat.html" title="class in com.aspose.slides"><span class="typeNameLink">ImageFormat</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ImagePixelFormat.html" title="class in com.aspose.slides"><span class="typeNameLink">ImagePixelFormat</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/InkEffectType.html" title="class in com.aspose.slides"><span class="typeNameLink">InkEffectType</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/LayoutTargetType.html" title="class in com.aspose.slides"><span class="typeNameLink">LayoutTargetType</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/LegendDataLabelPosition.html" title="class in com.aspose.slides"><span class="typeNameLink">LegendDataLabelPosition</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/LegendPositionType.html" title="class in com.aspose.slides"><span class="typeNameLink">LegendPositionType</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/LightingDirection.html" title="class in com.aspose.slides"><span class="typeNameLink">LightingDirection</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/LightRigPresetType.html" title="class in com.aspose.slides"><span class="typeNameLink">LightRigPresetType</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/LineAlignment.html" title="class in com.aspose.slides"><span class="typeNameLink">LineAlignment</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/LineArrowheadLength.html" title="class in com.aspose.slides"><span class="typeNameLink">LineArrowheadLength</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/LineArrowheadStyle.html" title="class in com.aspose.slides"><span class="typeNameLink">LineArrowheadStyle</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/LineArrowheadWidth.html" title="class in com.aspose.slides"><span class="typeNameLink">LineArrowheadWidth</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/LineCapStyle.html" title="class in com.aspose.slides"><span class="typeNameLink">LineCapStyle</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/LineDashStyle.html" title="class in com.aspose.slides"><span class="typeNameLink">LineDashStyle</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/LineJoinStyle.html" title="class in com.aspose.slides"><span class="typeNameLink">LineJoinStyle</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/LineSketchType.html" title="class in com.aspose.slides"><span class="typeNameLink">LineSketchType</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/LineStyle.html" title="class in com.aspose.slides"><span class="typeNameLink">LineStyle</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/LinkEmbedDecision.html" title="class in com.aspose.slides"><span class="typeNameLink">LinkEmbedDecision</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/LoadFormat.html" title="class in com.aspose.slides"><span class="typeNameLink">LoadFormat</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/LoadingStreamBehavior.html" title="class in com.aspose.slides"><span class="typeNameLink">LoadingStreamBehavior</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/MarkdownExportType.html" title="class in com.aspose.slides"><span class="typeNameLink">MarkdownExportType</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/MarkerStyleType.html" title="class in com.aspose.slides"><span class="typeNameLink">MarkerStyleType</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/MaterialPresetType.html" title="class in com.aspose.slides"><span class="typeNameLink">MaterialPresetType</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/MathDelimiterShape.html" title="class in com.aspose.slides"><span class="typeNameLink">MathDelimiterShape</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/MathFractionTypes.html" title="class in com.aspose.slides"><span class="typeNameLink">MathFractionTypes</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/MathFunctionsOfOneArgument.html" title="class in com.aspose.slides"><span class="typeNameLink">MathFunctionsOfOneArgument</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/MathFunctionsOfTwoArguments.html" title="class in com.aspose.slides"><span class="typeNameLink">MathFunctionsOfTwoArguments</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/MathHorizontalAlignment.html" title="class in com.aspose.slides"><span class="typeNameLink">MathHorizontalAlignment</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/MathIntegralTypes.html" title="class in com.aspose.slides"><span class="typeNameLink">MathIntegralTypes</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/MathJustification.html" title="class in com.aspose.slides"><span class="typeNameLink">MathJustification</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/MathLimitLocations.html" title="class in com.aspose.slides"><span class="typeNameLink">MathLimitLocations</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/MathNaryOperatorTypes.html" title="class in com.aspose.slides"><span class="typeNameLink">MathNaryOperatorTypes</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/MathRowSpacingRule.html" title="class in com.aspose.slides"><span class="typeNameLink">MathRowSpacingRule</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/MathSpacingRules.html" title="class in com.aspose.slides"><span class="typeNameLink">MathSpacingRules</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/MathTopBotPositions.html" title="class in com.aspose.slides"><span class="typeNameLink">MathTopBotPositions</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/MathVerticalAlignment.html" title="class in com.aspose.slides"><span class="typeNameLink">MathVerticalAlignment</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ModernCommentStatus.html" title="class in com.aspose.slides"><span class="typeNameLink">ModernCommentStatus</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/MotionCommandPathType.html" title="class in com.aspose.slides"><span class="typeNameLink">MotionCommandPathType</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/MotionOriginType.html" title="class in com.aspose.slides"><span class="typeNameLink">MotionOriginType</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/MotionPathEditMode.html" title="class in com.aspose.slides"><span class="typeNameLink">MotionPathEditMode</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/MotionPathPointsType.html" title="class in com.aspose.slides"><span class="typeNameLink">MotionPathPointsType</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/NewLineType.html" title="class in com.aspose.slides"><span class="typeNameLink">NewLineType</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/NotesPositions.html" title="class in com.aspose.slides"><span class="typeNameLink">NotesPositions</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/NullableBool.html" title="class in com.aspose.slides"><span class="typeNameLink">NullableBool</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/NumberedBulletStyle.html" title="class in com.aspose.slides"><span class="typeNameLink">NumberedBulletStyle</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/OrganizationChartLayoutType.html" title="class in com.aspose.slides"><span class="typeNameLink">OrganizationChartLayoutType</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/Orientation.html" title="class in com.aspose.slides"><span class="typeNameLink">Orientation</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ParentLabelLayoutType.html" title="class in com.aspose.slides"><span class="typeNameLink">ParentLabelLayoutType</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/PathCommandType.html" title="class in com.aspose.slides"><span class="typeNameLink">PathCommandType</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/PathFillModeType.html" title="class in com.aspose.slides"><span class="typeNameLink">PathFillModeType</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/PatternStyle.html" title="class in com.aspose.slides"><span class="typeNameLink">PatternStyle</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/PdfAccessPermissions.html" title="class in com.aspose.slides"><span class="typeNameLink">PdfAccessPermissions</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/PdfCompliance.html" title="class in com.aspose.slides"><span class="typeNameLink">PdfCompliance</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/PdfTextCompression.html" title="class in com.aspose.slides"><span class="typeNameLink">PdfTextCompression</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/PersistenceType.html" title="class in com.aspose.slides"><span class="typeNameLink">PersistenceType</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/PictureFillMode.html" title="class in com.aspose.slides"><span class="typeNameLink">PictureFillMode</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/PicturesCompression.html" title="class in com.aspose.slides"><span class="typeNameLink">PicturesCompression</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/PictureType.html" title="class in com.aspose.slides"><span class="typeNameLink">PictureType</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/PieSplitType.html" title="class in com.aspose.slides"><span class="typeNameLink">PieSplitType</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/PlaceholderSize.html" title="class in com.aspose.slides"><span class="typeNameLink">PlaceholderSize</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/PlaceholderType.html" title="class in com.aspose.slides"><span class="typeNameLink">PlaceholderType</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/PresentationContentAmountType.html" title="class in com.aspose.slides"><span class="typeNameLink">PresentationContentAmountType</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/PresentationLockingBehavior.html" title="class in com.aspose.slides"><span class="typeNameLink">PresentationLockingBehavior</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/PresetColor.html" title="class in com.aspose.slides"><span class="typeNameLink">PresetColor</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/PresetShadowType.html" title="class in com.aspose.slides"><span class="typeNameLink">PresetShadowType</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/PropertyCalcModeType.html" title="class in com.aspose.slides"><span class="typeNameLink">PropertyCalcModeType</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/PropertyValueType.html" title="class in com.aspose.slides"><span class="typeNameLink">PropertyValueType</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/QuartileMethodType.html" title="class in com.aspose.slides"><span class="typeNameLink">QuartileMethodType</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/RectangleAlignment.html" title="class in com.aspose.slides"><span class="typeNameLink">RectangleAlignment</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ResourceLoadingAction.html" title="class in com.aspose.slides"><span class="typeNameLink">ResourceLoadingAction</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ReturnAction.html" title="class in com.aspose.slides"><span class="typeNameLink">ReturnAction</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/SaveFormat.html" title="class in com.aspose.slides"><span class="typeNameLink">SaveFormat</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/SchemeColor.html" title="class in com.aspose.slides"><span class="typeNameLink">SchemeColor</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ShapeAdjustmentType.html" title="class in com.aspose.slides"><span class="typeNameLink">ShapeAdjustmentType</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ShapeElementFillSource.html" title="class in com.aspose.slides"><span class="typeNameLink">ShapeElementFillSource</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ShapeElementStrokeSource.html" title="class in com.aspose.slides"><span class="typeNameLink">ShapeElementStrokeSource</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ShapesAlignmentType.html" title="class in com.aspose.slides"><span class="typeNameLink">ShapesAlignmentType</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ShapeThumbnailBounds.html" title="class in com.aspose.slides"><span class="typeNameLink">ShapeThumbnailBounds</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ShapeType.html" title="class in com.aspose.slides"><span class="typeNameLink">ShapeType</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/SlideLayoutType.html" title="class in com.aspose.slides"><span class="typeNameLink">SlideLayoutType</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/SlideOrientation.html" title="class in com.aspose.slides"><span class="typeNameLink">SlideOrientation</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/SlideSizeScaleType.html" title="class in com.aspose.slides"><span class="typeNameLink">SlideSizeScaleType</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/SlideSizeType.html" title="class in com.aspose.slides"><span class="typeNameLink">SlideSizeType</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/SmartArtColorType.html" title="class in com.aspose.slides"><span class="typeNameLink">SmartArtColorType</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/SmartArtLayoutType.html" title="class in com.aspose.slides"><span class="typeNameLink">SmartArtLayoutType</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/SmartArtQuickStyleType.html" title="class in com.aspose.slides"><span class="typeNameLink">SmartArtQuickStyleType</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/SourceFormat.html" title="class in com.aspose.slides"><span class="typeNameLink">SourceFormat</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/SplitterBarStateType.html" title="class in com.aspose.slides"><span class="typeNameLink">SplitterBarStateType</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/StyleType.html" title="class in com.aspose.slides"><span class="typeNameLink">StyleType</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/SvgCoordinateUnit.html" title="class in com.aspose.slides"><span class="typeNameLink">SvgCoordinateUnit</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/SvgEvent.html" title="class in com.aspose.slides"><span class="typeNameLink">SvgEvent</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/SvgExternalFontsHandling.html" title="class in com.aspose.slides"><span class="typeNameLink">SvgExternalFontsHandling</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/SystemColor.html" title="class in com.aspose.slides"><span class="typeNameLink">SystemColor</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/TabAlignment.html" title="class in com.aspose.slides"><span class="typeNameLink">TabAlignment</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/TableStylePreset.html" title="class in com.aspose.slides"><span class="typeNameLink">TableStylePreset</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/TextAlignment.html" title="class in com.aspose.slides"><span class="typeNameLink">TextAlignment</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/TextAnchorType.html" title="class in com.aspose.slides"><span class="typeNameLink">TextAnchorType</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/TextAutofitType.html" title="class in com.aspose.slides"><span class="typeNameLink">TextAutofitType</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/TextCapType.html" title="class in com.aspose.slides"><span class="typeNameLink">TextCapType</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/TextExtractionArrangingMode.html" title="class in com.aspose.slides"><span class="typeNameLink">TextExtractionArrangingMode</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/TextInheritanceLimit.html" title="class in com.aspose.slides"><span class="typeNameLink">TextInheritanceLimit</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/TextShapeType.html" title="class in com.aspose.slides"><span class="typeNameLink">TextShapeType</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/TextStrikethroughType.html" title="class in com.aspose.slides"><span class="typeNameLink">TextStrikethroughType</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/TextUnderlineType.html" title="class in com.aspose.slides"><span class="typeNameLink">TextUnderlineType</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/TextVerticalOverflowType.html" title="class in com.aspose.slides"><span class="typeNameLink">TextVerticalOverflowType</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/TextVerticalType.html" title="class in com.aspose.slides"><span class="typeNameLink">TextVerticalType</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/TickLabelPositionType.html" title="class in com.aspose.slides"><span class="typeNameLink">TickLabelPositionType</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/TickMarkType.html" title="class in com.aspose.slides"><span class="typeNameLink">TickMarkType</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/TiffCompressionTypes.html" title="class in com.aspose.slides"><span class="typeNameLink">TiffCompressionTypes</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/TileFlip.html" title="class in com.aspose.slides"><span class="typeNameLink">TileFlip</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/TimeUnitType.html" title="class in com.aspose.slides"><span class="typeNameLink">TimeUnitType</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/TransitionCornerAndCenterDirectionType.html" title="class in com.aspose.slides"><span class="typeNameLink">TransitionCornerAndCenterDirectionType</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/TransitionCornerDirectionType.html" title="class in com.aspose.slides"><span class="typeNameLink">TransitionCornerDirectionType</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/TransitionEightDirectionType.html" title="class in com.aspose.slides"><span class="typeNameLink">TransitionEightDirectionType</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/TransitionInOutDirectionType.html" title="class in com.aspose.slides"><span class="typeNameLink">TransitionInOutDirectionType</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/TransitionLeftRightDirectionType.html" title="class in com.aspose.slides"><span class="typeNameLink">TransitionLeftRightDirectionType</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/TransitionMorphType.html" title="class in com.aspose.slides"><span class="typeNameLink">TransitionMorphType</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/TransitionPattern.html" title="class in com.aspose.slides"><span class="typeNameLink">TransitionPattern</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/TransitionShredPattern.html" title="class in com.aspose.slides"><span class="typeNameLink">TransitionShredPattern</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/TransitionSideDirectionType.html" title="class in com.aspose.slides"><span class="typeNameLink">TransitionSideDirectionType</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/TransitionSoundMode.html" title="class in com.aspose.slides"><span class="typeNameLink">TransitionSoundMode</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/TransitionSpeed.html" title="class in com.aspose.slides"><span class="typeNameLink">TransitionSpeed</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/TransitionType.html" title="class in com.aspose.slides"><span class="typeNameLink">TransitionType</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/TrendlineType.html" title="class in com.aspose.slides"><span class="typeNameLink">TrendlineType</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/VideoPlayModePreset.html" title="class in com.aspose.slides"><span class="typeNameLink">VideoPlayModePreset</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ViewType.html" title="class in com.aspose.slides"><span class="typeNameLink">ViewType</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/WarningType.html" title="class in com.aspose.slides"><span class="typeNameLink">WarningType</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/Zip64Mode.html" title="class in com.aspose.slides"><span class="typeNameLink">Zip64Mode</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ZoomImageType.html" title="class in com.aspose.slides"><span class="typeNameLink">ZoomImageType</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ZoomLayout.html" title="class in com.aspose.slides"><span class="typeNameLink">ZoomLayout</span></a></li>
</ul>
</li>
</ul>
</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/VbaModule.html" title="class in com.aspose.slides"><span class="typeNameLink">VbaModule</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IVbaModule.html" title="interface in com.aspose.slides">IVbaModule</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/VbaModuleCollection.html" title="class in com.aspose.slides"><span class="typeNameLink">VbaModuleCollection</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IVbaModuleCollection.html" title="interface in com.aspose.slides">IVbaModuleCollection</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/VbaProject.html" title="class in com.aspose.slides"><span class="typeNameLink">VbaProject</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IVbaProject.html" title="interface in com.aspose.slides">IVbaProject</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/VbaProjectFactory.html" title="class in com.aspose.slides"><span class="typeNameLink">VbaProjectFactory</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IVbaProjectFactory.html" title="interface in com.aspose.slides">IVbaProjectFactory</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/VbaReferenceCollection.html" title="class in com.aspose.slides"><span class="typeNameLink">VbaReferenceCollection</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IVbaReferenceCollection.html" title="interface in com.aspose.slides">IVbaReferenceCollection</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/VbaReferenceFactory.html" title="class in com.aspose.slides"><span class="typeNameLink">VbaReferenceFactory</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IVbaReferenceFactory.html" title="interface in com.aspose.slides">IVbaReferenceFactory</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/VbaReferenceOleTypeLib.html" title="class in com.aspose.slides"><span class="typeNameLink">VbaReferenceOleTypeLib</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IVbaReferenceOleTypeLib.html" title="interface in com.aspose.slides">IVbaReferenceOleTypeLib</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/Video.html" title="class in com.aspose.slides"><span class="typeNameLink">Video</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IVideo.html" title="interface in com.aspose.slides">IVideo</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/VideoPlayerHtmlController.html" title="class in com.aspose.slides"><span class="typeNameLink">VideoPlayerHtmlController</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IVideoPlayerHtmlController.html" title="interface in com.aspose.slides">IVideoPlayerHtmlController</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/VideoPlayerHtmlControllerFactory.html" title="class in com.aspose.slides"><span class="typeNameLink">VideoPlayerHtmlControllerFactory</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IVideoPlayerHtmlControllerFactory.html" title="interface in com.aspose.slides">IVideoPlayerHtmlControllerFactory</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ViewProperties.html" title="class in com.aspose.slides"><span class="typeNameLink">ViewProperties</span></a> (implements com.aspose.slides.<a href="../../../com/aspose/slides/IViewProperties.html" title="interface in com.aspose.slides">IViewProperties</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/WebDocument.html" title="class in com.aspose.slides"><span class="typeNameLink">WebDocument</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/WebDocumentOptions.html" title="class in com.aspose.slides"><span class="typeNameLink">WebDocumentOptions</span></a></li>
</ul>
</li>
</ul>
<h2 title="Interface Hierarchy">Interface Hierarchy</h2>
<ul>
<li type="circle">java.lang.Comparable&lt;T&gt;
<ul>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ITab.html" title="interface in com.aspose.slides"><span class="typeNameLink">ITab</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ITabEffectiveData.html" title="interface in com.aspose.slides"><span class="typeNameLink">ITabEffectiveData</span></a></li>
</ul>
</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/Convert.GetOutPathCallback.html" title="interface in com.aspose.slides"><span class="typeNameLink">Convert.GetOutPathCallback</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ForEach.ForEachLayoutSlideCallback.html" title="interface in com.aspose.slides"><span class="typeNameLink">ForEach.ForEachLayoutSlideCallback</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ForEach.ForEachMasterSlideCallback.html" title="interface in com.aspose.slides"><span class="typeNameLink">ForEach.ForEachMasterSlideCallback</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ForEach.ForEachParagraphCallback.html" title="interface in com.aspose.slides"><span class="typeNameLink">ForEach.ForEachParagraphCallback</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ForEach.ForEachPortionCallback.html" title="interface in com.aspose.slides"><span class="typeNameLink">ForEach.ForEachPortionCallback</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ForEach.ForEachShapeCallback.html" title="interface in com.aspose.slides"><span class="typeNameLink">ForEach.ForEachShapeCallback</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ForEach.ForEachSlideCallback.html" title="interface in com.aspose.slides"><span class="typeNameLink">ForEach.ForEachSlideCallback</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IAccessiblePVIObject.html" title="interface in com.aspose.slides"><span class="typeNameLink">IAccessiblePVIObject</span></a>&lt;T&gt;
<ul>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IAlphaBiLevel.html" title="interface in com.aspose.slides"><span class="typeNameLink">IAlphaBiLevel</span></a> (also extends com.aspose.slides.<a href="../../../com/aspose/slides/IImageTransformOperation.html" title="interface in com.aspose.slides">IImageTransformOperation</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IAlphaCeiling.html" title="interface in com.aspose.slides"><span class="typeNameLink">IAlphaCeiling</span></a> (also extends com.aspose.slides.<a href="../../../com/aspose/slides/IImageTransformOperation.html" title="interface in com.aspose.slides">IImageTransformOperation</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IAlphaFloor.html" title="interface in com.aspose.slides"><span class="typeNameLink">IAlphaFloor</span></a> (also extends com.aspose.slides.<a href="../../../com/aspose/slides/IImageTransformOperation.html" title="interface in com.aspose.slides">IImageTransformOperation</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IAlphaInverse.html" title="interface in com.aspose.slides"><span class="typeNameLink">IAlphaInverse</span></a> (also extends com.aspose.slides.<a href="../../../com/aspose/slides/IImageTransformOperation.html" title="interface in com.aspose.slides">IImageTransformOperation</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IAlphaModulate.html" title="interface in com.aspose.slides"><span class="typeNameLink">IAlphaModulate</span></a> (also extends com.aspose.slides.<a href="../../../com/aspose/slides/IImageTransformOperation.html" title="interface in com.aspose.slides">IImageTransformOperation</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IAlphaModulateFixed.html" title="interface in com.aspose.slides"><span class="typeNameLink">IAlphaModulateFixed</span></a> (also extends com.aspose.slides.<a href="../../../com/aspose/slides/IImageTransformOperation.html" title="interface in com.aspose.slides">IImageTransformOperation</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IAlphaReplace.html" title="interface in com.aspose.slides"><span class="typeNameLink">IAlphaReplace</span></a> (also extends com.aspose.slides.<a href="../../../com/aspose/slides/IImageTransformOperation.html" title="interface in com.aspose.slides">IImageTransformOperation</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IBiLevel.html" title="interface in com.aspose.slides"><span class="typeNameLink">IBiLevel</span></a> (also extends com.aspose.slides.<a href="../../../com/aspose/slides/IImageTransformOperation.html" title="interface in com.aspose.slides">IImageTransformOperation</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IBlur.html" title="interface in com.aspose.slides"><span class="typeNameLink">IBlur</span></a> (also extends com.aspose.slides.<a href="../../../com/aspose/slides/IImageTransformOperation.html" title="interface in com.aspose.slides">IImageTransformOperation</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IColorChange.html" title="interface in com.aspose.slides"><span class="typeNameLink">IColorChange</span></a> (also extends com.aspose.slides.<a href="../../../com/aspose/slides/IImageTransformOperation.html" title="interface in com.aspose.slides">IImageTransformOperation</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IColorReplace.html" title="interface in com.aspose.slides"><span class="typeNameLink">IColorReplace</span></a> (also extends com.aspose.slides.<a href="../../../com/aspose/slides/IImageTransformOperation.html" title="interface in com.aspose.slides">IImageTransformOperation</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IDuotone.html" title="interface in com.aspose.slides"><span class="typeNameLink">IDuotone</span></a> (also extends com.aspose.slides.<a href="../../../com/aspose/slides/IImageTransformOperation.html" title="interface in com.aspose.slides">IImageTransformOperation</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IFillOverlay.html" title="interface in com.aspose.slides"><span class="typeNameLink">IFillOverlay</span></a> (also extends com.aspose.slides.<a href="../../../com/aspose/slides/IImageTransformOperation.html" title="interface in com.aspose.slides">IImageTransformOperation</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IGlow.html" title="interface in com.aspose.slides"><span class="typeNameLink">IGlow</span></a> (also extends com.aspose.slides.<a href="../../../com/aspose/slides/IImageTransformOperation.html" title="interface in com.aspose.slides">IImageTransformOperation</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IGrayScale.html" title="interface in com.aspose.slides"><span class="typeNameLink">IGrayScale</span></a> (also extends com.aspose.slides.<a href="../../../com/aspose/slides/IImageTransformOperation.html" title="interface in com.aspose.slides">IImageTransformOperation</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IHSL.html" title="interface in com.aspose.slides"><span class="typeNameLink">IHSL</span></a> (also extends com.aspose.slides.<a href="../../../com/aspose/slides/IImageTransformOperation.html" title="interface in com.aspose.slides">IImageTransformOperation</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IInnerShadow.html" title="interface in com.aspose.slides"><span class="typeNameLink">IInnerShadow</span></a> (also extends com.aspose.slides.<a href="../../../com/aspose/slides/IImageTransformOperation.html" title="interface in com.aspose.slides">IImageTransformOperation</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ILuminance.html" title="interface in com.aspose.slides"><span class="typeNameLink">ILuminance</span></a> (also extends com.aspose.slides.<a href="../../../com/aspose/slides/IImageTransformOperation.html" title="interface in com.aspose.slides">IImageTransformOperation</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IOuterShadow.html" title="interface in com.aspose.slides"><span class="typeNameLink">IOuterShadow</span></a> (also extends com.aspose.slides.<a href="../../../com/aspose/slides/IImageTransformOperation.html" title="interface in com.aspose.slides">IImageTransformOperation</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IPresetShadow.html" title="interface in com.aspose.slides"><span class="typeNameLink">IPresetShadow</span></a> (also extends com.aspose.slides.<a href="../../../com/aspose/slides/IImageTransformOperation.html" title="interface in com.aspose.slides">IImageTransformOperation</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IReflection.html" title="interface in com.aspose.slides"><span class="typeNameLink">IReflection</span></a> (also extends com.aspose.slides.<a href="../../../com/aspose/slides/IImageTransformOperation.html" title="interface in com.aspose.slides">IImageTransformOperation</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ISoftEdge.html" title="interface in com.aspose.slides"><span class="typeNameLink">ISoftEdge</span></a> (also extends com.aspose.slides.<a href="../../../com/aspose/slides/IImageTransformOperation.html" title="interface in com.aspose.slides">IImageTransformOperation</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ITint.html" title="interface in com.aspose.slides"><span class="typeNameLink">ITint</span></a> (also extends com.aspose.slides.<a href="../../../com/aspose/slides/IImageTransformOperation.html" title="interface in com.aspose.slides">IImageTransformOperation</a>)</li>
</ul>
</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IActualLayout.html" title="interface in com.aspose.slides"><span class="typeNameLink">IActualLayout</span></a>
<ul>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IChartDataPoint.html" title="interface in com.aspose.slides"><span class="typeNameLink">IChartDataPoint</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IChartPlotArea.html" title="interface in com.aspose.slides"><span class="typeNameLink">IChartPlotArea</span></a> (also extends com.aspose.slides.<a href="../../../com/aspose/slides/ILayoutable.html" title="interface in com.aspose.slides">ILayoutable</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IDataLabel.html" title="interface in com.aspose.slides"><span class="typeNameLink">IDataLabel</span></a> (also extends com.aspose.slides.<a href="../../../com/aspose/slides/ILayoutable.html" title="interface in com.aspose.slides">ILayoutable</a>, com.aspose.slides.<a href="../../../com/aspose/slides/IOverridableText.html" title="interface in com.aspose.slides">IOverridableText</a>)</li>
</ul>
</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IAdjustValue.html" title="interface in com.aspose.slides"><span class="typeNameLink">IAdjustValue</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IAIConversation.html" title="interface in com.aspose.slides"><span class="typeNameLink">IAIConversation</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IAIWebClient.html" title="interface in com.aspose.slides"><span class="typeNameLink">IAIWebClient</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IAnimationTimeLine.html" title="interface in com.aspose.slides"><span class="typeNameLink">IAnimationTimeLine</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IAudio.html" title="interface in com.aspose.slides"><span class="typeNameLink">IAudio</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IAxesManager.html" title="interface in com.aspose.slides"><span class="typeNameLink">IAxesManager</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IAxisFormat.html" title="interface in com.aspose.slides"><span class="typeNameLink">IAxisFormat</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IBackdrop3DScene.html" title="interface in com.aspose.slides"><span class="typeNameLink">IBackdrop3DScene</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IBaseChartValue.html" title="interface in com.aspose.slides"><span class="typeNameLink">IBaseChartValue</span></a>
<ul>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IMultipleCellChartValue.html" title="interface in com.aspose.slides"><span class="typeNameLink">IMultipleCellChartValue</span></a>
<ul>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IStringChartValue.html" title="interface in com.aspose.slides"><span class="typeNameLink">IStringChartValue</span></a></li>
</ul>
</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ISingleCellChartValue.html" title="interface in com.aspose.slides"><span class="typeNameLink">ISingleCellChartValue</span></a>
<ul>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IDoubleChartValue.html" title="interface in com.aspose.slides"><span class="typeNameLink">IDoubleChartValue</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IStringOrDoubleChartValue.html" title="interface in com.aspose.slides"><span class="typeNameLink">IStringOrDoubleChartValue</span></a></li>
</ul>
</li>
</ul>
</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IBaseHeaderFooterManager.html" title="interface in com.aspose.slides"><span class="typeNameLink">IBaseHeaderFooterManager</span></a>
<ul>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IBaseSlideHeaderFooterManager.html" title="interface in com.aspose.slides"><span class="typeNameLink">IBaseSlideHeaderFooterManager</span></a>
<ul>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IBaseHandoutNotesSlideHeaderFooterManag.html" title="interface in com.aspose.slides"><span class="typeNameLink">IBaseHandoutNotesSlideHeaderFooterManag</span></a>
<ul>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IMasterHandoutSlideHeaderFooterManager.html" title="interface in com.aspose.slides"><span class="typeNameLink">IMasterHandoutSlideHeaderFooterManager</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IMasterNotesSlideHeaderFooterManager.html" title="interface in com.aspose.slides"><span class="typeNameLink">IMasterNotesSlideHeaderFooterManager</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/INotesSlideHeaderFooterManager.html" title="interface in com.aspose.slides"><span class="typeNameLink">INotesSlideHeaderFooterManager</span></a></li>
</ul>
</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ILayoutSlideHeaderFooterManager.html" title="interface in com.aspose.slides"><span class="typeNameLink">ILayoutSlideHeaderFooterManager</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IMasterSlideHeaderFooterManager.html" title="interface in com.aspose.slides"><span class="typeNameLink">IMasterSlideHeaderFooterManager</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ISlideHeaderFooterManager.html" title="interface in com.aspose.slides"><span class="typeNameLink">ISlideHeaderFooterManager</span></a></li>
</ul>
</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IPresentationHeaderFooterManager.html" title="interface in com.aspose.slides"><span class="typeNameLink">IPresentationHeaderFooterManager</span></a></li>
</ul>
</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IBasePortionFormat.html" title="interface in com.aspose.slides"><span class="typeNameLink">IBasePortionFormat</span></a>
<ul>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IChartPortionFormat.html" title="interface in com.aspose.slides"><span class="typeNameLink">IChartPortionFormat</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IPortionFormat.html" title="interface in com.aspose.slides"><span class="typeNameLink">IPortionFormat</span></a> (also extends com.aspose.slides.<a href="../../../com/aspose/slides/IHyperlinkContainer.html" title="interface in com.aspose.slides">IHyperlinkContainer</a>)</li>
</ul>
</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IBasePortionFormatEffectiveData.html" title="interface in com.aspose.slides"><span class="typeNameLink">IBasePortionFormatEffectiveData</span></a>
<ul>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IPortionFormatEffectiveData.html" title="interface in com.aspose.slides"><span class="typeNameLink">IPortionFormatEffectiveData</span></a></li>
</ul>
</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IBaseShapeLock.html" title="interface in com.aspose.slides"><span class="typeNameLink">IBaseShapeLock</span></a>
<ul>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IAutoShapeLock.html" title="interface in com.aspose.slides"><span class="typeNameLink">IAutoShapeLock</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IConnectorLock.html" title="interface in com.aspose.slides"><span class="typeNameLink">IConnectorLock</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IGraphicalObjectLock.html" title="interface in com.aspose.slides"><span class="typeNameLink">IGraphicalObjectLock</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IGroupShapeLock.html" title="interface in com.aspose.slides"><span class="typeNameLink">IGroupShapeLock</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IPictureFrameLock.html" title="interface in com.aspose.slides"><span class="typeNameLink">IPictureFrameLock</span></a></li>
</ul>
</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IBaseTableFormatEffectiveData.html" title="interface in com.aspose.slides"><span class="typeNameLink">IBaseTableFormatEffectiveData</span></a>
<ul>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ICellFormatEffectiveData.html" title="interface in com.aspose.slides"><span class="typeNameLink">ICellFormatEffectiveData</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IColumnFormatEffectiveData.html" title="interface in com.aspose.slides"><span class="typeNameLink">IColumnFormatEffectiveData</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IRowFormatEffectiveData.html" title="interface in com.aspose.slides"><span class="typeNameLink">IRowFormatEffectiveData</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ITableFormatEffectiveData.html" title="interface in com.aspose.slides"><span class="typeNameLink">ITableFormatEffectiveData</span></a></li>
</ul>
</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IBehavior.html" title="interface in com.aspose.slides"><span class="typeNameLink">IBehavior</span></a>
<ul>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IColorEffect.html" title="interface in com.aspose.slides"><span class="typeNameLink">IColorEffect</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ICommandEffect.html" title="interface in com.aspose.slides"><span class="typeNameLink">ICommandEffect</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IFilterEffect.html" title="interface in com.aspose.slides"><span class="typeNameLink">IFilterEffect</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IMotionEffect.html" title="interface in com.aspose.slides"><span class="typeNameLink">IMotionEffect</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IPropertyEffect.html" title="interface in com.aspose.slides"><span class="typeNameLink">IPropertyEffect</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IRotationEffect.html" title="interface in com.aspose.slides"><span class="typeNameLink">IRotationEffect</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IScaleEffect.html" title="interface in com.aspose.slides"><span class="typeNameLink">IScaleEffect</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ISetEffect.html" title="interface in com.aspose.slides"><span class="typeNameLink">ISetEffect</span></a></li>
</ul>
</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IBehaviorFactory.html" title="interface in com.aspose.slides"><span class="typeNameLink">IBehaviorFactory</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IBehaviorProperty.html" title="interface in com.aspose.slides"><span class="typeNameLink">IBehaviorProperty</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IBlobManagementOptions.html" title="interface in com.aspose.slides"><span class="typeNameLink">IBlobManagementOptions</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IBulkTextFormattable.html" title="interface in com.aspose.slides"><span class="typeNameLink">IBulkTextFormattable</span></a>
<ul>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IColumn.html" title="interface in com.aspose.slides"><span class="typeNameLink">IColumn</span></a> (also extends com.aspose.slides.<a href="../../../com/aspose/slides/ICellCollection.html" title="interface in com.aspose.slides">ICellCollection</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IRow.html" title="interface in com.aspose.slides"><span class="typeNameLink">IRow</span></a> (also extends com.aspose.slides.<a href="../../../com/aspose/slides/ICellCollection.html" title="interface in com.aspose.slides">ICellCollection</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ITable.html" title="interface in com.aspose.slides"><span class="typeNameLink">ITable</span></a> (also extends com.aspose.slides.<a href="../../../com/aspose/slides/IGraphicalObject.html" title="interface in com.aspose.slides">IGraphicalObject</a>)</li>
</ul>
</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IBulletFormat.html" title="interface in com.aspose.slides"><span class="typeNameLink">IBulletFormat</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IBulletFormatEffectiveData.html" title="interface in com.aspose.slides"><span class="typeNameLink">IBulletFormatEffectiveData</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ICamera.html" title="interface in com.aspose.slides"><span class="typeNameLink">ICamera</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ICameraEffectiveData.html" title="interface in com.aspose.slides"><span class="typeNameLink">ICameraEffectiveData</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ICaptions.html" title="interface in com.aspose.slides"><span class="typeNameLink">ICaptions</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ICellFormat.html" title="interface in com.aspose.slides"><span class="typeNameLink">ICellFormat</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IChartCategory.html" title="interface in com.aspose.slides"><span class="typeNameLink">IChartCategory</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IChartCategoryLevelsManager.html" title="interface in com.aspose.slides"><span class="typeNameLink">IChartCategoryLevelsManager</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IChartData.html" title="interface in com.aspose.slides"><span class="typeNameLink">IChartData</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IChartDataCell.html" title="interface in com.aspose.slides"><span class="typeNameLink">IChartDataCell</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IChartDataPointLevel.html" title="interface in com.aspose.slides"><span class="typeNameLink">IChartDataPointLevel</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IChartDataPointLevelsManager.html" title="interface in com.aspose.slides"><span class="typeNameLink">IChartDataPointLevelsManager</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IChartDataWorkbook.html" title="interface in com.aspose.slides"><span class="typeNameLink">IChartDataWorkbook</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IChartDataWorksheet.html" title="interface in com.aspose.slides"><span class="typeNameLink">IChartDataWorksheet</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IChartLinesFormat.html" title="interface in com.aspose.slides"><span class="typeNameLink">IChartLinesFormat</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IChartParagraphFormat.html" title="interface in com.aspose.slides"><span class="typeNameLink">IChartParagraphFormat</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IChartTextBlockFormat.html" title="interface in com.aspose.slides"><span class="typeNameLink">IChartTextBlockFormat</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IChartTextFormat.html" title="interface in com.aspose.slides"><span class="typeNameLink">IChartTextFormat</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IChartWall.html" title="interface in com.aspose.slides"><span class="typeNameLink">IChartWall</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IColorOffset.html" title="interface in com.aspose.slides"><span class="typeNameLink">IColorOffset</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IColorOperation.html" title="interface in com.aspose.slides"><span class="typeNameLink">IColorOperation</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IColorSchemeEffectiveData.html" title="interface in com.aspose.slides"><span class="typeNameLink">IColorSchemeEffectiveData</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IColumnFormat.html" title="interface in com.aspose.slides"><span class="typeNameLink">IColumnFormat</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IComment.html" title="interface in com.aspose.slides"><span class="typeNameLink">IComment</span></a>
<ul>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IModernComment.html" title="interface in com.aspose.slides"><span class="typeNameLink">IModernComment</span></a></li>
</ul>
</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ICommentAuthor.html" title="interface in com.aspose.slides"><span class="typeNameLink">ICommentAuthor</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ICommonSlideViewProperties.html" title="interface in com.aspose.slides"><span class="typeNameLink">ICommonSlideViewProperties</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ICustomData.html" title="interface in com.aspose.slides"><span class="typeNameLink">ICustomData</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ICustomXmlPart.html" title="interface in com.aspose.slides"><span class="typeNameLink">ICustomXmlPart</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IDataSourceTypeForErrorBarsCustomValues.html" title="interface in com.aspose.slides"><span class="typeNameLink">IDataSourceTypeForErrorBarsCustomValues</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IDigitalSignature.html" title="interface in com.aspose.slides"><span class="typeNameLink">IDigitalSignature</span></a></li>
<li type="circle">com.aspose.ms.System.IDisposable
<ul>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IImage.html" title="interface in com.aspose.slides"><span class="typeNameLink">IImage</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IPresentation.html" title="interface in com.aspose.slides"><span class="typeNameLink">IPresentation</span></a> (also extends com.aspose.slides.<a href="../../../com/aspose/slides/IPresentationComponent.html" title="interface in com.aspose.slides">IPresentationComponent</a>)</li>
</ul>
</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IDocumentProperties.html" title="interface in com.aspose.slides"><span class="typeNameLink">IDocumentProperties</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IDrawingGuide.html" title="interface in com.aspose.slides"><span class="typeNameLink">IDrawingGuide</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IEffect.html" title="interface in com.aspose.slides"><span class="typeNameLink">IEffect</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IEffectEffectiveData.html" title="interface in com.aspose.slides"><span class="typeNameLink">IEffectEffectiveData</span></a>
<ul>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IAlphaBiLevelEffectiveData.html" title="interface in com.aspose.slides"><span class="typeNameLink">IAlphaBiLevelEffectiveData</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IAlphaCeilingEffectiveData.html" title="interface in com.aspose.slides"><span class="typeNameLink">IAlphaCeilingEffectiveData</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IAlphaFloorEffectiveData.html" title="interface in com.aspose.slides"><span class="typeNameLink">IAlphaFloorEffectiveData</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IAlphaInverseEffectiveData.html" title="interface in com.aspose.slides"><span class="typeNameLink">IAlphaInverseEffectiveData</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IAlphaModulateEffectiveData.html" title="interface in com.aspose.slides"><span class="typeNameLink">IAlphaModulateEffectiveData</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IAlphaModulateFixedEffectiveData.html" title="interface in com.aspose.slides"><span class="typeNameLink">IAlphaModulateFixedEffectiveData</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IAlphaReplaceEffectiveData.html" title="interface in com.aspose.slides"><span class="typeNameLink">IAlphaReplaceEffectiveData</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IBiLevelEffectiveData.html" title="interface in com.aspose.slides"><span class="typeNameLink">IBiLevelEffectiveData</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IBlurEffectiveData.html" title="interface in com.aspose.slides"><span class="typeNameLink">IBlurEffectiveData</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IColorChangeEffectiveData.html" title="interface in com.aspose.slides"><span class="typeNameLink">IColorChangeEffectiveData</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IColorReplaceEffectiveData.html" title="interface in com.aspose.slides"><span class="typeNameLink">IColorReplaceEffectiveData</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IDuotoneEffectiveData.html" title="interface in com.aspose.slides"><span class="typeNameLink">IDuotoneEffectiveData</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IFillOverlayEffectiveData.html" title="interface in com.aspose.slides"><span class="typeNameLink">IFillOverlayEffectiveData</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IGlowEffectiveData.html" title="interface in com.aspose.slides"><span class="typeNameLink">IGlowEffectiveData</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IGrayScaleEffectiveData.html" title="interface in com.aspose.slides"><span class="typeNameLink">IGrayScaleEffectiveData</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IHSLEffectiveData.html" title="interface in com.aspose.slides"><span class="typeNameLink">IHSLEffectiveData</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IInnerShadowEffectiveData.html" title="interface in com.aspose.slides"><span class="typeNameLink">IInnerShadowEffectiveData</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ILuminanceEffectiveData.html" title="interface in com.aspose.slides"><span class="typeNameLink">ILuminanceEffectiveData</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IOuterShadowEffectiveData.html" title="interface in com.aspose.slides"><span class="typeNameLink">IOuterShadowEffectiveData</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IPresetShadowEffectiveData.html" title="interface in com.aspose.slides"><span class="typeNameLink">IPresetShadowEffectiveData</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IReflectionEffectiveData.html" title="interface in com.aspose.slides"><span class="typeNameLink">IReflectionEffectiveData</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ISoftEdgeEffectiveData.html" title="interface in com.aspose.slides"><span class="typeNameLink">ISoftEdgeEffectiveData</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ITintEffectiveData.html" title="interface in com.aspose.slides"><span class="typeNameLink">ITintEffectiveData</span></a></li>
</ul>
</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IEffectFactory.html" title="interface in com.aspose.slides"><span class="typeNameLink">IEffectFactory</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IEffectParamSource.html" title="interface in com.aspose.slides"><span class="typeNameLink">IEffectParamSource</span></a>
<ul>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IEffectFormat.html" title="interface in com.aspose.slides"><span class="typeNameLink">IEffectFormat</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IEffectFormatEffectiveData.html" title="interface in com.aspose.slides"><span class="typeNameLink">IEffectFormatEffectiveData</span></a></li>
</ul>
</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IEffectStyle.html" title="interface in com.aspose.slides"><span class="typeNameLink">IEffectStyle</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IEffectStyleEffectiveData.html" title="interface in com.aspose.slides"><span class="typeNameLink">IEffectStyleEffectiveData</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IErrorBarsCustomValues.html" title="interface in com.aspose.slides"><span class="typeNameLink">IErrorBarsCustomValues</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IExternalResourceResolver.html" title="interface in com.aspose.slides"><span class="typeNameLink">IExternalResourceResolver</span></a>
<ul>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IHtmlExternalResolver.html" title="interface in com.aspose.slides"><span class="typeNameLink">IHtmlExternalResolver</span></a></li>
</ul>
</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IExtraColorScheme.html" title="interface in com.aspose.slides"><span class="typeNameLink">IExtraColorScheme</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IFieldType.html" title="interface in com.aspose.slides"><span class="typeNameLink">IFieldType</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IFillParamSource.html" title="interface in com.aspose.slides"><span class="typeNameLink">IFillParamSource</span></a>
<ul>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IBackground.html" title="interface in com.aspose.slides"><span class="typeNameLink">IBackground</span></a> (also extends com.aspose.slides.<a href="../../../com/aspose/slides/ISlideComponent.html" title="interface in com.aspose.slides">ISlideComponent</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IBackgroundEffectiveData.html" title="interface in com.aspose.slides"><span class="typeNameLink">IBackgroundEffectiveData</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IColorFormat.html" title="interface in com.aspose.slides"><span class="typeNameLink">IColorFormat</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IFillFormat.html" title="interface in com.aspose.slides"><span class="typeNameLink">IFillFormat</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IFillFormatEffectiveData.html" title="interface in com.aspose.slides"><span class="typeNameLink">IFillFormatEffectiveData</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IGradientFormat.html" title="interface in com.aspose.slides"><span class="typeNameLink">IGradientFormat</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IGradientFormatEffectiveData.html" title="interface in com.aspose.slides"><span class="typeNameLink">IGradientFormatEffectiveData</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ILineFillFormat.html" title="interface in com.aspose.slides"><span class="typeNameLink">ILineFillFormat</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ILineFillFormatEffectiveData.html" title="interface in com.aspose.slides"><span class="typeNameLink">ILineFillFormatEffectiveData</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IPictureFillFormat.html" title="interface in com.aspose.slides"><span class="typeNameLink">IPictureFillFormat</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IPictureFillFormatEffectiveData.html" title="interface in com.aspose.slides"><span class="typeNameLink">IPictureFillFormatEffectiveData</span></a></li>
</ul>
</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IFindResultCallback.html" title="interface in com.aspose.slides"><span class="typeNameLink">IFindResultCallback</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IFontData.html" title="interface in com.aspose.slides"><span class="typeNameLink">IFontData</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IFontDataFactory.html" title="interface in com.aspose.slides"><span class="typeNameLink">IFontDataFactory</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IFontFallBackRule.html" title="interface in com.aspose.slides"><span class="typeNameLink">IFontFallBackRule</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IFonts.html" title="interface in com.aspose.slides"><span class="typeNameLink">IFonts</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IFontScheme.html" title="interface in com.aspose.slides"><span class="typeNameLink">IFontScheme</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IFontSchemeEffectiveData.html" title="interface in com.aspose.slides"><span class="typeNameLink">IFontSchemeEffectiveData</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IFontsEffectiveData.html" title="interface in com.aspose.slides"><span class="typeNameLink">IFontsEffectiveData</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IFontsLoader.html" title="interface in com.aspose.slides"><span class="typeNameLink">IFontsLoader</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IFontsManager.html" title="interface in com.aspose.slides"><span class="typeNameLink">IFontsManager</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IFontSources.html" title="interface in com.aspose.slides"><span class="typeNameLink">IFontSources</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IFontSubstRule.html" title="interface in com.aspose.slides"><span class="typeNameLink">IFontSubstRule</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IFormat.html" title="interface in com.aspose.slides"><span class="typeNameLink">IFormat</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IFormatFactory.html" title="interface in com.aspose.slides"><span class="typeNameLink">IFormatFactory</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IFormatSchemeEffectiveData.html" title="interface in com.aspose.slides"><span class="typeNameLink">IFormatSchemeEffectiveData</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IGenericCloneable.html" title="interface in com.aspose.slides"><span class="typeNameLink">IGenericCloneable</span></a>&lt;T&gt;
<ul>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IColorOperationCollection.html" title="interface in com.aspose.slides"><span class="typeNameLink">IColorOperationCollection</span></a> (also extends com.aspose.slides.<a href="../../../com/aspose/slides/IGenericCollection.html" title="interface in com.aspose.slides">IGenericCollection</a>&lt;T&gt;)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IShapeFrame.html" title="interface in com.aspose.slides"><span class="typeNameLink">IShapeFrame</span></a></li>
</ul>
</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IGeometryPath.html" title="interface in com.aspose.slides"><span class="typeNameLink">IGeometryPath</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IGradientStop.html" title="interface in com.aspose.slides"><span class="typeNameLink">IGradientStop</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IGradientStopEffectiveData.html" title="interface in com.aspose.slides"><span class="typeNameLink">IGradientStopEffectiveData</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IHeadingPair.html" title="interface in com.aspose.slides"><span class="typeNameLink">IHeadingPair</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IHtmlFormatter.html" title="interface in com.aspose.slides"><span class="typeNameLink">IHtmlFormatter</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IHtmlFormattingController.html" title="interface in com.aspose.slides"><span class="typeNameLink">IHtmlFormattingController</span></a>
<ul>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IEmbeddedEotFontsHtmlController.html" title="interface in com.aspose.slides"><span class="typeNameLink">IEmbeddedEotFontsHtmlController</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IEmbeddedWoffFontsHtmlController.html" title="interface in com.aspose.slides"><span class="typeNameLink">IEmbeddedWoffFontsHtmlController</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IResponsiveHtmlController.html" title="interface in com.aspose.slides"><span class="typeNameLink">IResponsiveHtmlController</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IVideoPlayerHtmlController.html" title="interface in com.aspose.slides"><span class="typeNameLink">IVideoPlayerHtmlController</span></a> (also extends com.aspose.slides.<a href="../../../com/aspose/slides/ILinkEmbedController.html" title="interface in com.aspose.slides">ILinkEmbedController</a>, com.aspose.slides.<a href="../../../com/aspose/slides/ISvgShapeFormattingController.html" title="interface in com.aspose.slides">ISvgShapeFormattingController</a>)</li>
</ul>
</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IHtmlGenerator.html" title="interface in com.aspose.slides"><span class="typeNameLink">IHtmlGenerator</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IHyperlink.html" title="interface in com.aspose.slides"><span class="typeNameLink">IHyperlink</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IHyperlinkContainer.html" title="interface in com.aspose.slides"><span class="typeNameLink">IHyperlinkContainer</span></a>
<ul>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IPortionFormat.html" title="interface in com.aspose.slides"><span class="typeNameLink">IPortionFormat</span></a> (also extends com.aspose.slides.<a href="../../../com/aspose/slides/IBasePortionFormat.html" title="interface in com.aspose.slides">IBasePortionFormat</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides"><span class="typeNameLink">IShape</span></a> (also extends com.aspose.slides.<a href="../../../com/aspose/slides/ISlideComponent.html" title="interface in com.aspose.slides">ISlideComponent</a>)
<ul>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IGeometryShape.html" title="interface in com.aspose.slides"><span class="typeNameLink">IGeometryShape</span></a>
<ul>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IAutoShape.html" title="interface in com.aspose.slides"><span class="typeNameLink">IAutoShape</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IConnector.html" title="interface in com.aspose.slides"><span class="typeNameLink">IConnector</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IPictureFrame.html" title="interface in com.aspose.slides"><span class="typeNameLink">IPictureFrame</span></a>
<ul>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IAudioFrame.html" title="interface in com.aspose.slides"><span class="typeNameLink">IAudioFrame</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IVideoFrame.html" title="interface in com.aspose.slides"><span class="typeNameLink">IVideoFrame</span></a></li>
</ul>
</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ISmartArtShape.html" title="interface in com.aspose.slides"><span class="typeNameLink">ISmartArtShape</span></a></li>
</ul>
</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IGraphicalObject.html" title="interface in com.aspose.slides"><span class="typeNameLink">IGraphicalObject</span></a>
<ul>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IChart.html" title="interface in com.aspose.slides"><span class="typeNameLink">IChart</span></a> (also extends com.aspose.slides.<a href="../../../com/aspose/slides/IFormattedTextContainer.html" title="interface in com.aspose.slides">IFormattedTextContainer</a>, com.aspose.slides.<a href="../../../com/aspose/slides/IOverrideThemeable.html" title="interface in com.aspose.slides">IOverrideThemeable</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IInk.html" title="interface in com.aspose.slides"><span class="typeNameLink">IInk</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IInkActions.html" title="interface in com.aspose.slides"><span class="typeNameLink">IInkActions</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ILegacyDiagram.html" title="interface in com.aspose.slides"><span class="typeNameLink">ILegacyDiagram</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IOleObjectFrame.html" title="interface in com.aspose.slides"><span class="typeNameLink">IOleObjectFrame</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ISmartArt.html" title="interface in com.aspose.slides"><span class="typeNameLink">ISmartArt</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ISummaryZoomFrame.html" title="interface in com.aspose.slides"><span class="typeNameLink">ISummaryZoomFrame</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ITable.html" title="interface in com.aspose.slides"><span class="typeNameLink">ITable</span></a> (also extends com.aspose.slides.<a href="../../../com/aspose/slides/IBulkTextFormattable.html" title="interface in com.aspose.slides">IBulkTextFormattable</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IZoomObject.html" title="interface in com.aspose.slides"><span class="typeNameLink">IZoomObject</span></a>
<ul>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ISectionZoomFrame.html" title="interface in com.aspose.slides"><span class="typeNameLink">ISectionZoomFrame</span></a>
<ul>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ISummaryZoomSection.html" title="interface in com.aspose.slides"><span class="typeNameLink">ISummaryZoomSection</span></a></li>
</ul>
</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IZoomFrame.html" title="interface in com.aspose.slides"><span class="typeNameLink">IZoomFrame</span></a></li>
</ul>
</li>
</ul>
</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IGroupShape.html" title="interface in com.aspose.slides"><span class="typeNameLink">IGroupShape</span></a></li>
</ul>
</li>
</ul>
</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IHyperlinkManager.html" title="interface in com.aspose.slides"><span class="typeNameLink">IHyperlinkManager</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IHyperlinkQueries.html" title="interface in com.aspose.slides"><span class="typeNameLink">IHyperlinkQueries</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IImageTransformOperation.html" title="interface in com.aspose.slides"><span class="typeNameLink">IImageTransformOperation</span></a>
<ul>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IAlphaBiLevel.html" title="interface in com.aspose.slides"><span class="typeNameLink">IAlphaBiLevel</span></a> (also extends com.aspose.slides.<a href="../../../com/aspose/slides/IAccessiblePVIObject.html" title="interface in com.aspose.slides">IAccessiblePVIObject</a>&lt;T&gt;)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IAlphaCeiling.html" title="interface in com.aspose.slides"><span class="typeNameLink">IAlphaCeiling</span></a> (also extends com.aspose.slides.<a href="../../../com/aspose/slides/IAccessiblePVIObject.html" title="interface in com.aspose.slides">IAccessiblePVIObject</a>&lt;T&gt;)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IAlphaFloor.html" title="interface in com.aspose.slides"><span class="typeNameLink">IAlphaFloor</span></a> (also extends com.aspose.slides.<a href="../../../com/aspose/slides/IAccessiblePVIObject.html" title="interface in com.aspose.slides">IAccessiblePVIObject</a>&lt;T&gt;)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IAlphaInverse.html" title="interface in com.aspose.slides"><span class="typeNameLink">IAlphaInverse</span></a> (also extends com.aspose.slides.<a href="../../../com/aspose/slides/IAccessiblePVIObject.html" title="interface in com.aspose.slides">IAccessiblePVIObject</a>&lt;T&gt;)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IAlphaModulate.html" title="interface in com.aspose.slides"><span class="typeNameLink">IAlphaModulate</span></a> (also extends com.aspose.slides.<a href="../../../com/aspose/slides/IAccessiblePVIObject.html" title="interface in com.aspose.slides">IAccessiblePVIObject</a>&lt;T&gt;)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IAlphaModulateFixed.html" title="interface in com.aspose.slides"><span class="typeNameLink">IAlphaModulateFixed</span></a> (also extends com.aspose.slides.<a href="../../../com/aspose/slides/IAccessiblePVIObject.html" title="interface in com.aspose.slides">IAccessiblePVIObject</a>&lt;T&gt;)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IAlphaReplace.html" title="interface in com.aspose.slides"><span class="typeNameLink">IAlphaReplace</span></a> (also extends com.aspose.slides.<a href="../../../com/aspose/slides/IAccessiblePVIObject.html" title="interface in com.aspose.slides">IAccessiblePVIObject</a>&lt;T&gt;)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IBiLevel.html" title="interface in com.aspose.slides"><span class="typeNameLink">IBiLevel</span></a> (also extends com.aspose.slides.<a href="../../../com/aspose/slides/IAccessiblePVIObject.html" title="interface in com.aspose.slides">IAccessiblePVIObject</a>&lt;T&gt;)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IBlur.html" title="interface in com.aspose.slides"><span class="typeNameLink">IBlur</span></a> (also extends com.aspose.slides.<a href="../../../com/aspose/slides/IAccessiblePVIObject.html" title="interface in com.aspose.slides">IAccessiblePVIObject</a>&lt;T&gt;)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IColorChange.html" title="interface in com.aspose.slides"><span class="typeNameLink">IColorChange</span></a> (also extends com.aspose.slides.<a href="../../../com/aspose/slides/IAccessiblePVIObject.html" title="interface in com.aspose.slides">IAccessiblePVIObject</a>&lt;T&gt;)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IColorReplace.html" title="interface in com.aspose.slides"><span class="typeNameLink">IColorReplace</span></a> (also extends com.aspose.slides.<a href="../../../com/aspose/slides/IAccessiblePVIObject.html" title="interface in com.aspose.slides">IAccessiblePVIObject</a>&lt;T&gt;)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IDuotone.html" title="interface in com.aspose.slides"><span class="typeNameLink">IDuotone</span></a> (also extends com.aspose.slides.<a href="../../../com/aspose/slides/IAccessiblePVIObject.html" title="interface in com.aspose.slides">IAccessiblePVIObject</a>&lt;T&gt;)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IFillOverlay.html" title="interface in com.aspose.slides"><span class="typeNameLink">IFillOverlay</span></a> (also extends com.aspose.slides.<a href="../../../com/aspose/slides/IAccessiblePVIObject.html" title="interface in com.aspose.slides">IAccessiblePVIObject</a>&lt;T&gt;)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IGlow.html" title="interface in com.aspose.slides"><span class="typeNameLink">IGlow</span></a> (also extends com.aspose.slides.<a href="../../../com/aspose/slides/IAccessiblePVIObject.html" title="interface in com.aspose.slides">IAccessiblePVIObject</a>&lt;T&gt;)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IGrayScale.html" title="interface in com.aspose.slides"><span class="typeNameLink">IGrayScale</span></a> (also extends com.aspose.slides.<a href="../../../com/aspose/slides/IAccessiblePVIObject.html" title="interface in com.aspose.slides">IAccessiblePVIObject</a>&lt;T&gt;)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IHSL.html" title="interface in com.aspose.slides"><span class="typeNameLink">IHSL</span></a> (also extends com.aspose.slides.<a href="../../../com/aspose/slides/IAccessiblePVIObject.html" title="interface in com.aspose.slides">IAccessiblePVIObject</a>&lt;T&gt;)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IInnerShadow.html" title="interface in com.aspose.slides"><span class="typeNameLink">IInnerShadow</span></a> (also extends com.aspose.slides.<a href="../../../com/aspose/slides/IAccessiblePVIObject.html" title="interface in com.aspose.slides">IAccessiblePVIObject</a>&lt;T&gt;)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ILuminance.html" title="interface in com.aspose.slides"><span class="typeNameLink">ILuminance</span></a> (also extends com.aspose.slides.<a href="../../../com/aspose/slides/IAccessiblePVIObject.html" title="interface in com.aspose.slides">IAccessiblePVIObject</a>&lt;T&gt;)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IOuterShadow.html" title="interface in com.aspose.slides"><span class="typeNameLink">IOuterShadow</span></a> (also extends com.aspose.slides.<a href="../../../com/aspose/slides/IAccessiblePVIObject.html" title="interface in com.aspose.slides">IAccessiblePVIObject</a>&lt;T&gt;)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IPresetShadow.html" title="interface in com.aspose.slides"><span class="typeNameLink">IPresetShadow</span></a> (also extends com.aspose.slides.<a href="../../../com/aspose/slides/IAccessiblePVIObject.html" title="interface in com.aspose.slides">IAccessiblePVIObject</a>&lt;T&gt;)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IReflection.html" title="interface in com.aspose.slides"><span class="typeNameLink">IReflection</span></a> (also extends com.aspose.slides.<a href="../../../com/aspose/slides/IAccessiblePVIObject.html" title="interface in com.aspose.slides">IAccessiblePVIObject</a>&lt;T&gt;)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ISoftEdge.html" title="interface in com.aspose.slides"><span class="typeNameLink">ISoftEdge</span></a> (also extends com.aspose.slides.<a href="../../../com/aspose/slides/IAccessiblePVIObject.html" title="interface in com.aspose.slides">IAccessiblePVIObject</a>&lt;T&gt;)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ITint.html" title="interface in com.aspose.slides"><span class="typeNameLink">ITint</span></a> (also extends com.aspose.slides.<a href="../../../com/aspose/slides/IAccessiblePVIObject.html" title="interface in com.aspose.slides">IAccessiblePVIObject</a>&lt;T&gt;)</li>
</ul>
</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IImageTransformOperationFactory.html" title="interface in com.aspose.slides"><span class="typeNameLink">IImageTransformOperationFactory</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IInkBrush.html" title="interface in com.aspose.slides"><span class="typeNameLink">IInkBrush</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IInkOptions.html" title="interface in com.aspose.slides"><span class="typeNameLink">IInkOptions</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IInkTrace.html" title="interface in com.aspose.slides"><span class="typeNameLink">IInkTrace</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IInterruptionToken.html" title="interface in com.aspose.slides"><span class="typeNameLink">IInterruptionToken</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IInterruptionTokenSource.html" title="interface in com.aspose.slides"><span class="typeNameLink">IInterruptionTokenSource</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ILayoutPlaceholderManager.html" title="interface in com.aspose.slides"><span class="typeNameLink">ILayoutPlaceholderManager</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ILegendEntryCollection.html" title="interface in com.aspose.slides"><span class="typeNameLink">ILegendEntryCollection</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ILicense.html" title="interface in com.aspose.slides"><span class="typeNameLink">ILicense</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ILightRig.html" title="interface in com.aspose.slides"><span class="typeNameLink">ILightRig</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ILightRigEffectiveData.html" title="interface in com.aspose.slides"><span class="typeNameLink">ILightRigEffectiveData</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ILineParamSource.html" title="interface in com.aspose.slides"><span class="typeNameLink">ILineParamSource</span></a>
<ul>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ILineFormat.html" title="interface in com.aspose.slides"><span class="typeNameLink">ILineFormat</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ILineFormatEffectiveData.html" title="interface in com.aspose.slides"><span class="typeNameLink">ILineFormatEffectiveData</span></a></li>
</ul>
</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ILinkEmbedController.html" title="interface in com.aspose.slides"><span class="typeNameLink">ILinkEmbedController</span></a>
<ul>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IVideoPlayerHtmlController.html" title="interface in com.aspose.slides"><span class="typeNameLink">IVideoPlayerHtmlController</span></a> (also extends com.aspose.slides.<a href="../../../com/aspose/slides/IHtmlFormattingController.html" title="interface in com.aspose.slides">IHtmlFormattingController</a>, com.aspose.slides.<a href="../../../com/aspose/slides/ISvgShapeFormattingController.html" title="interface in com.aspose.slides">ISvgShapeFormattingController</a>)</li>
</ul>
</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ILoadOptions.html" title="interface in com.aspose.slides"><span class="typeNameLink">ILoadOptions</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IMarker.html" title="interface in com.aspose.slides"><span class="typeNameLink">IMarker</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IMasterHandoutSlideManager.html" title="interface in com.aspose.slides"><span class="typeNameLink">IMasterHandoutSlideManager</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IMasterNotesSlideManager.html" title="interface in com.aspose.slides"><span class="typeNameLink">IMasterNotesSlideManager</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IMathAccentFactory.html" title="interface in com.aspose.slides"><span class="typeNameLink">IMathAccentFactory</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IMathArrayFactory.html" title="interface in com.aspose.slides"><span class="typeNameLink">IMathArrayFactory</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IMathBarFactory.html" title="interface in com.aspose.slides"><span class="typeNameLink">IMathBarFactory</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IMathBlockFactory.html" title="interface in com.aspose.slides"><span class="typeNameLink">IMathBlockFactory</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IMathBorderBoxFactory.html" title="interface in com.aspose.slides"><span class="typeNameLink">IMathBorderBoxFactory</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IMathBoxFactory.html" title="interface in com.aspose.slides"><span class="typeNameLink">IMathBoxFactory</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IMathDelimiterFactory.html" title="interface in com.aspose.slides"><span class="typeNameLink">IMathDelimiterFactory</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IMathElement.html" title="interface in com.aspose.slides"><span class="typeNameLink">IMathElement</span></a>
<ul>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IMathAccent.html" title="interface in com.aspose.slides"><span class="typeNameLink">IMathAccent</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IMathArray.html" title="interface in com.aspose.slides"><span class="typeNameLink">IMathArray</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IMathBar.html" title="interface in com.aspose.slides"><span class="typeNameLink">IMathBar</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IMathBlock.html" title="interface in com.aspose.slides"><span class="typeNameLink">IMathBlock</span></a> (also extends com.aspose.slides.<a href="../../../com/aspose/slides/IMathElementCollection.html" title="interface in com.aspose.slides">IMathElementCollection</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IMathBorderBox.html" title="interface in com.aspose.slides"><span class="typeNameLink">IMathBorderBox</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IMathBox.html" title="interface in com.aspose.slides"><span class="typeNameLink">IMathBox</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IMathDelimiter.html" title="interface in com.aspose.slides"><span class="typeNameLink">IMathDelimiter</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IMathematicalText.html" title="interface in com.aspose.slides"><span class="typeNameLink">IMathematicalText</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IMathFraction.html" title="interface in com.aspose.slides"><span class="typeNameLink">IMathFraction</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IMathFunction.html" title="interface in com.aspose.slides"><span class="typeNameLink">IMathFunction</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IMathGroupingCharacter.html" title="interface in com.aspose.slides"><span class="typeNameLink">IMathGroupingCharacter</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IMathLeftSubSuperscriptElement.html" title="interface in com.aspose.slides"><span class="typeNameLink">IMathLeftSubSuperscriptElement</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IMathLimit.html" title="interface in com.aspose.slides"><span class="typeNameLink">IMathLimit</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IMathMatrix.html" title="interface in com.aspose.slides"><span class="typeNameLink">IMathMatrix</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IMathNaryOperator.html" title="interface in com.aspose.slides"><span class="typeNameLink">IMathNaryOperator</span></a> (also extends com.aspose.slides.<a href="../../../com/aspose/slides/IMathNaryOperatorProperties.html" title="interface in com.aspose.slides">IMathNaryOperatorProperties</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IMathRadical.html" title="interface in com.aspose.slides"><span class="typeNameLink">IMathRadical</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IMathRightSubSuperscriptElement.html" title="interface in com.aspose.slides"><span class="typeNameLink">IMathRightSubSuperscriptElement</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IMathSubscriptElement.html" title="interface in com.aspose.slides"><span class="typeNameLink">IMathSubscriptElement</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IMathSuperscriptElement.html" title="interface in com.aspose.slides"><span class="typeNameLink">IMathSuperscriptElement</span></a></li>
</ul>
</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IMathematicalTextFactory.html" title="interface in com.aspose.slides"><span class="typeNameLink">IMathematicalTextFactory</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IMathFractionFactory.html" title="interface in com.aspose.slides"><span class="typeNameLink">IMathFractionFactory</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IMathFunctionFactory.html" title="interface in com.aspose.slides"><span class="typeNameLink">IMathFunctionFactory</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IMathGroupingCharacterFactory.html" title="interface in com.aspose.slides"><span class="typeNameLink">IMathGroupingCharacterFactory</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IMathLimitFactory.html" title="interface in com.aspose.slides"><span class="typeNameLink">IMathLimitFactory</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IMathMatrixFactory.html" title="interface in com.aspose.slides"><span class="typeNameLink">IMathMatrixFactory</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IMathNaryOperatorFactory.html" title="interface in com.aspose.slides"><span class="typeNameLink">IMathNaryOperatorFactory</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IMathNaryOperatorProperties.html" title="interface in com.aspose.slides"><span class="typeNameLink">IMathNaryOperatorProperties</span></a>
<ul>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IMathNaryOperator.html" title="interface in com.aspose.slides"><span class="typeNameLink">IMathNaryOperator</span></a> (also extends com.aspose.slides.<a href="../../../com/aspose/slides/IMathElement.html" title="interface in com.aspose.slides">IMathElement</a>)</li>
</ul>
</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IMathParagraphFactory.html" title="interface in com.aspose.slides"><span class="typeNameLink">IMathParagraphFactory</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IMathPortion.html" title="interface in com.aspose.slides"><span class="typeNameLink">IMathPortion</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IMathRadicalFactory.html" title="interface in com.aspose.slides"><span class="typeNameLink">IMathRadicalFactory</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IMathRightSubSuperscriptElementFactory.html" title="interface in com.aspose.slides"><span class="typeNameLink">IMathRightSubSuperscriptElementFactory</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IMathSubscriptElementFactory.html" title="interface in com.aspose.slides"><span class="typeNameLink">IMathSubscriptElementFactory</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IMathSuperscriptElementFactory.html" title="interface in com.aspose.slides"><span class="typeNameLink">IMathSuperscriptElementFactory</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IMetered.html" title="interface in com.aspose.slides"><span class="typeNameLink">IMetered</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IMotionCmdPath.html" title="interface in com.aspose.slides"><span class="typeNameLink">IMotionCmdPath</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/INormalViewProperties.html" title="interface in com.aspose.slides"><span class="typeNameLink">INormalViewProperties</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/INormalViewRestoredProperties.html" title="interface in com.aspose.slides"><span class="typeNameLink">INormalViewRestoredProperties</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/INotesSize.html" title="interface in com.aspose.slides"><span class="typeNameLink">INotesSize</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/INotesSlideManager.html" title="interface in com.aspose.slides"><span class="typeNameLink">INotesSlideManager</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IOleEmbeddedDataInfo.html" title="interface in com.aspose.slides"><span class="typeNameLink">IOleEmbeddedDataInfo</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IOutputFile.html" title="interface in com.aspose.slides"><span class="typeNameLink">IOutputFile</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IOutputSaver.html" title="interface in com.aspose.slides"><span class="typeNameLink">IOutputSaver</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IParagraphFactory.html" title="interface in com.aspose.slides"><span class="typeNameLink">IParagraphFactory</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IParagraphFormat.html" title="interface in com.aspose.slides"><span class="typeNameLink">IParagraphFormat</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IParagraphFormatEffectiveData.html" title="interface in com.aspose.slides"><span class="typeNameLink">IParagraphFormatEffectiveData</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IPathSegment.html" title="interface in com.aspose.slides"><span class="typeNameLink">IPathSegment</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IPatternFormat.html" title="interface in com.aspose.slides"><span class="typeNameLink">IPatternFormat</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IPatternFormatEffectiveData.html" title="interface in com.aspose.slides"><span class="typeNameLink">IPatternFormatEffectiveData</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IPictureEffectiveData.html" title="interface in com.aspose.slides"><span class="typeNameLink">IPictureEffectiveData</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IPlaceholder.html" title="interface in com.aspose.slides"><span class="typeNameLink">IPlaceholder</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IPoint.html" title="interface in com.aspose.slides"><span class="typeNameLink">IPoint</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IPortionFactory.html" title="interface in com.aspose.slides"><span class="typeNameLink">IPortionFactory</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IPPImage.html" title="interface in com.aspose.slides"><span class="typeNameLink">IPPImage</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IPresentationAnimationPlayer.html" title="interface in com.aspose.slides"><span class="typeNameLink">IPresentationAnimationPlayer</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IPresentationComponent.html" title="interface in com.aspose.slides"><span class="typeNameLink">IPresentationComponent</span></a>
<ul>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IPresentation.html" title="interface in com.aspose.slides"><span class="typeNameLink">IPresentation</span></a> (also extends com.aspose.ms.System.IDisposable)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ISlideComponent.html" title="interface in com.aspose.slides"><span class="typeNameLink">ISlideComponent</span></a>
<ul>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IBackground.html" title="interface in com.aspose.slides"><span class="typeNameLink">IBackground</span></a> (also extends com.aspose.slides.<a href="../../../com/aspose/slides/IFillParamSource.html" title="interface in com.aspose.slides">IFillParamSource</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ICell.html" title="interface in com.aspose.slides"><span class="typeNameLink">ICell</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ICellCollection.html" title="interface in com.aspose.slides"><span class="typeNameLink">ICellCollection</span></a> (also extends com.aspose.slides.<a href="../../../com/aspose/slides/IGenericCollection.html" title="interface in com.aspose.slides">IGenericCollection</a>&lt;T&gt;)
<ul>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IColumn.html" title="interface in com.aspose.slides"><span class="typeNameLink">IColumn</span></a> (also extends com.aspose.slides.<a href="../../../com/aspose/slides/IBulkTextFormattable.html" title="interface in com.aspose.slides">IBulkTextFormattable</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IRow.html" title="interface in com.aspose.slides"><span class="typeNameLink">IRow</span></a> (also extends com.aspose.slides.<a href="../../../com/aspose/slides/IBulkTextFormattable.html" title="interface in com.aspose.slides">IBulkTextFormattable</a>)</li>
</ul>
</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IChartComponent.html" title="interface in com.aspose.slides"><span class="typeNameLink">IChartComponent</span></a>
<ul>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IChartSeries.html" title="interface in com.aspose.slides"><span class="typeNameLink">IChartSeries</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IChartSeriesGroup.html" title="interface in com.aspose.slides"><span class="typeNameLink">IChartSeriesGroup</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IDataLabelCollection.html" title="interface in com.aspose.slides"><span class="typeNameLink">IDataLabelCollection</span></a> (also extends com.aspose.ms.System.Collections.Generic.IGenericEnumerable&lt;T&gt;)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IErrorBarsFormat.html" title="interface in com.aspose.slides"><span class="typeNameLink">IErrorBarsFormat</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IFormattedTextContainer.html" title="interface in com.aspose.slides"><span class="typeNameLink">IFormattedTextContainer</span></a>
<ul>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IAxis.html" title="interface in com.aspose.slides"><span class="typeNameLink">IAxis</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IChart.html" title="interface in com.aspose.slides"><span class="typeNameLink">IChart</span></a> (also extends com.aspose.slides.<a href="../../../com/aspose/slides/IGraphicalObject.html" title="interface in com.aspose.slides">IGraphicalObject</a>, com.aspose.slides.<a href="../../../com/aspose/slides/IOverrideThemeable.html" title="interface in com.aspose.slides">IOverrideThemeable</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IDataLabelFormat.html" title="interface in com.aspose.slides"><span class="typeNameLink">IDataLabelFormat</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IDataTable.html" title="interface in com.aspose.slides"><span class="typeNameLink">IDataTable</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ILegend.html" title="interface in com.aspose.slides"><span class="typeNameLink">ILegend</span></a> (also extends com.aspose.slides.<a href="../../../com/aspose/slides/ILayoutable.html" title="interface in com.aspose.slides">ILayoutable</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ILegendEntryProperties.html" title="interface in com.aspose.slides"><span class="typeNameLink">ILegendEntryProperties</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IOverridableText.html" title="interface in com.aspose.slides"><span class="typeNameLink">IOverridableText</span></a>
<ul>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IChartTitle.html" title="interface in com.aspose.slides"><span class="typeNameLink">IChartTitle</span></a> (also extends com.aspose.slides.<a href="../../../com/aspose/slides/ILayoutable.html" title="interface in com.aspose.slides">ILayoutable</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IDataLabel.html" title="interface in com.aspose.slides"><span class="typeNameLink">IDataLabel</span></a> (also extends com.aspose.slides.<a href="../../../com/aspose/slides/IActualLayout.html" title="interface in com.aspose.slides">IActualLayout</a>, com.aspose.slides.<a href="../../../com/aspose/slides/ILayoutable.html" title="interface in com.aspose.slides">ILayoutable</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ITrendline.html" title="interface in com.aspose.slides"><span class="typeNameLink">ITrendline</span></a></li>
</ul>
</li>
</ul>
</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ILayoutable.html" title="interface in com.aspose.slides"><span class="typeNameLink">ILayoutable</span></a>
<ul>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IChartPlotArea.html" title="interface in com.aspose.slides"><span class="typeNameLink">IChartPlotArea</span></a> (also extends com.aspose.slides.<a href="../../../com/aspose/slides/IActualLayout.html" title="interface in com.aspose.slides">IActualLayout</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IChartTitle.html" title="interface in com.aspose.slides"><span class="typeNameLink">IChartTitle</span></a> (also extends com.aspose.slides.<a href="../../../com/aspose/slides/IOverridableText.html" title="interface in com.aspose.slides">IOverridableText</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IDataLabel.html" title="interface in com.aspose.slides"><span class="typeNameLink">IDataLabel</span></a> (also extends com.aspose.slides.<a href="../../../com/aspose/slides/IActualLayout.html" title="interface in com.aspose.slides">IActualLayout</a>, com.aspose.slides.<a href="../../../com/aspose/slides/IOverridableText.html" title="interface in com.aspose.slides">IOverridableText</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ILegend.html" title="interface in com.aspose.slides"><span class="typeNameLink">ILegend</span></a> (also extends com.aspose.slides.<a href="../../../com/aspose/slides/IFormattedTextContainer.html" title="interface in com.aspose.slides">IFormattedTextContainer</a>)</li>
</ul>
</li>
</ul>
</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IColorScheme.html" title="interface in com.aspose.slides"><span class="typeNameLink">IColorScheme</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IControl.html" title="interface in com.aspose.slides"><span class="typeNameLink">IControl</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IField.html" title="interface in com.aspose.slides"><span class="typeNameLink">IField</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IFormatScheme.html" title="interface in com.aspose.slides"><span class="typeNameLink">IFormatScheme</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IParagraph.html" title="interface in com.aspose.slides"><span class="typeNameLink">IParagraph</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IParagraphCollection.html" title="interface in com.aspose.slides"><span class="typeNameLink">IParagraphCollection</span></a> (also extends com.aspose.ms.System.Collections.Generic.IGenericEnumerable&lt;T&gt;)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IPortion.html" title="interface in com.aspose.slides"><span class="typeNameLink">IPortion</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides"><span class="typeNameLink">IShape</span></a> (also extends com.aspose.slides.<a href="../../../com/aspose/slides/IHyperlinkContainer.html" title="interface in com.aspose.slides">IHyperlinkContainer</a>)
<ul>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IGeometryShape.html" title="interface in com.aspose.slides"><span class="typeNameLink">IGeometryShape</span></a>
<ul>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IAutoShape.html" title="interface in com.aspose.slides"><span class="typeNameLink">IAutoShape</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IConnector.html" title="interface in com.aspose.slides"><span class="typeNameLink">IConnector</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IPictureFrame.html" title="interface in com.aspose.slides"><span class="typeNameLink">IPictureFrame</span></a>
<ul>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IAudioFrame.html" title="interface in com.aspose.slides"><span class="typeNameLink">IAudioFrame</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IVideoFrame.html" title="interface in com.aspose.slides"><span class="typeNameLink">IVideoFrame</span></a></li>
</ul>
</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ISmartArtShape.html" title="interface in com.aspose.slides"><span class="typeNameLink">ISmartArtShape</span></a></li>
</ul>
</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IGraphicalObject.html" title="interface in com.aspose.slides"><span class="typeNameLink">IGraphicalObject</span></a>
<ul>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IChart.html" title="interface in com.aspose.slides"><span class="typeNameLink">IChart</span></a> (also extends com.aspose.slides.<a href="../../../com/aspose/slides/IFormattedTextContainer.html" title="interface in com.aspose.slides">IFormattedTextContainer</a>, com.aspose.slides.<a href="../../../com/aspose/slides/IOverrideThemeable.html" title="interface in com.aspose.slides">IOverrideThemeable</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IInk.html" title="interface in com.aspose.slides"><span class="typeNameLink">IInk</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IInkActions.html" title="interface in com.aspose.slides"><span class="typeNameLink">IInkActions</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ILegacyDiagram.html" title="interface in com.aspose.slides"><span class="typeNameLink">ILegacyDiagram</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IOleObjectFrame.html" title="interface in com.aspose.slides"><span class="typeNameLink">IOleObjectFrame</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ISmartArt.html" title="interface in com.aspose.slides"><span class="typeNameLink">ISmartArt</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ISummaryZoomFrame.html" title="interface in com.aspose.slides"><span class="typeNameLink">ISummaryZoomFrame</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ITable.html" title="interface in com.aspose.slides"><span class="typeNameLink">ITable</span></a> (also extends com.aspose.slides.<a href="../../../com/aspose/slides/IBulkTextFormattable.html" title="interface in com.aspose.slides">IBulkTextFormattable</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IZoomObject.html" title="interface in com.aspose.slides"><span class="typeNameLink">IZoomObject</span></a>
<ul>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ISectionZoomFrame.html" title="interface in com.aspose.slides"><span class="typeNameLink">ISectionZoomFrame</span></a>
<ul>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ISummaryZoomSection.html" title="interface in com.aspose.slides"><span class="typeNameLink">ISummaryZoomSection</span></a></li>
</ul>
</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IZoomFrame.html" title="interface in com.aspose.slides"><span class="typeNameLink">IZoomFrame</span></a></li>
</ul>
</li>
</ul>
</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IGroupShape.html" title="interface in com.aspose.slides"><span class="typeNameLink">IGroupShape</span></a></li>
</ul>
</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ISlidesPicture.html" title="interface in com.aspose.slides"><span class="typeNameLink">ISlidesPicture</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ITextFrame.html" title="interface in com.aspose.slides"><span class="typeNameLink">ITextFrame</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IThemeable.html" title="interface in com.aspose.slides"><span class="typeNameLink">IThemeable</span></a>
<ul>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IBaseSlide.html" title="interface in com.aspose.slides"><span class="typeNameLink">IBaseSlide</span></a>
<ul>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ILayoutSlide.html" title="interface in com.aspose.slides"><span class="typeNameLink">ILayoutSlide</span></a> (also extends com.aspose.slides.<a href="../../../com/aspose/slides/IOverrideThemeable.html" title="interface in com.aspose.slides">IOverrideThemeable</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IMasterHandoutSlide.html" title="interface in com.aspose.slides"><span class="typeNameLink">IMasterHandoutSlide</span></a> (also extends com.aspose.slides.<a href="../../../com/aspose/slides/IMasterThemeable.html" title="interface in com.aspose.slides">IMasterThemeable</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IMasterNotesSlide.html" title="interface in com.aspose.slides"><span class="typeNameLink">IMasterNotesSlide</span></a> (also extends com.aspose.slides.<a href="../../../com/aspose/slides/IMasterThemeable.html" title="interface in com.aspose.slides">IMasterThemeable</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IMasterSlide.html" title="interface in com.aspose.slides"><span class="typeNameLink">IMasterSlide</span></a> (also extends com.aspose.slides.<a href="../../../com/aspose/slides/IMasterThemeable.html" title="interface in com.aspose.slides">IMasterThemeable</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/INotesSlide.html" title="interface in com.aspose.slides"><span class="typeNameLink">INotesSlide</span></a> (also extends com.aspose.slides.<a href="../../../com/aspose/slides/IOverrideThemeable.html" title="interface in com.aspose.slides">IOverrideThemeable</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides"><span class="typeNameLink">ISlide</span></a> (also extends com.aspose.slides.<a href="../../../com/aspose/slides/IOverrideThemeable.html" title="interface in com.aspose.slides">IOverrideThemeable</a>)</li>
</ul>
</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IMasterThemeable.html" title="interface in com.aspose.slides"><span class="typeNameLink">IMasterThemeable</span></a>
<ul>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IMasterHandoutSlide.html" title="interface in com.aspose.slides"><span class="typeNameLink">IMasterHandoutSlide</span></a> (also extends com.aspose.slides.<a href="../../../com/aspose/slides/IBaseSlide.html" title="interface in com.aspose.slides">IBaseSlide</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IMasterNotesSlide.html" title="interface in com.aspose.slides"><span class="typeNameLink">IMasterNotesSlide</span></a> (also extends com.aspose.slides.<a href="../../../com/aspose/slides/IBaseSlide.html" title="interface in com.aspose.slides">IBaseSlide</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IMasterSlide.html" title="interface in com.aspose.slides"><span class="typeNameLink">IMasterSlide</span></a> (also extends com.aspose.slides.<a href="../../../com/aspose/slides/IBaseSlide.html" title="interface in com.aspose.slides">IBaseSlide</a>)</li>
</ul>
</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IOverrideThemeable.html" title="interface in com.aspose.slides"><span class="typeNameLink">IOverrideThemeable</span></a>
<ul>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IChart.html" title="interface in com.aspose.slides"><span class="typeNameLink">IChart</span></a> (also extends com.aspose.slides.<a href="../../../com/aspose/slides/IFormattedTextContainer.html" title="interface in com.aspose.slides">IFormattedTextContainer</a>, com.aspose.slides.<a href="../../../com/aspose/slides/IGraphicalObject.html" title="interface in com.aspose.slides">IGraphicalObject</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ILayoutSlide.html" title="interface in com.aspose.slides"><span class="typeNameLink">ILayoutSlide</span></a> (also extends com.aspose.slides.<a href="../../../com/aspose/slides/IBaseSlide.html" title="interface in com.aspose.slides">IBaseSlide</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/INotesSlide.html" title="interface in com.aspose.slides"><span class="typeNameLink">INotesSlide</span></a> (also extends com.aspose.slides.<a href="../../../com/aspose/slides/IBaseSlide.html" title="interface in com.aspose.slides">IBaseSlide</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides"><span class="typeNameLink">ISlide</span></a> (also extends com.aspose.slides.<a href="../../../com/aspose/slides/IBaseSlide.html" title="interface in com.aspose.slides">IBaseSlide</a>)</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ITheme.html" title="interface in com.aspose.slides"><span class="typeNameLink">ITheme</span></a>
<ul>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IMasterTheme.html" title="interface in com.aspose.slides"><span class="typeNameLink">IMasterTheme</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IOverrideTheme.html" title="interface in com.aspose.slides"><span class="typeNameLink">IOverrideTheme</span></a></li>
</ul>
</li>
</ul>
</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IPresentationFactory.html" title="interface in com.aspose.slides"><span class="typeNameLink">IPresentationFactory</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IPresentationInfo.html" title="interface in com.aspose.slides"><span class="typeNameLink">IPresentationInfo</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IPresentationText.html" title="interface in com.aspose.slides"><span class="typeNameLink">IPresentationText</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IProgressCallback.html" title="interface in com.aspose.slides"><span class="typeNameLink">IProgressCallback</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IProtectionManager.html" title="interface in com.aspose.slides"><span class="typeNameLink">IProtectionManager</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IResourceLoadingArgs.html" title="interface in com.aspose.slides"><span class="typeNameLink">IResourceLoadingArgs</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IResourceLoadingCallback.html" title="interface in com.aspose.slides"><span class="typeNameLink">IResourceLoadingCallback</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IRotation3D.html" title="interface in com.aspose.slides"><span class="typeNameLink">IRotation3D</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IRowFormat.html" title="interface in com.aspose.slides"><span class="typeNameLink">IRowFormat</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ISaveOptions.html" title="interface in com.aspose.slides"><span class="typeNameLink">ISaveOptions</span></a>
<ul>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IGifOptions.html" title="interface in com.aspose.slides"><span class="typeNameLink">IGifOptions</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IHtml5Options.html" title="interface in com.aspose.slides"><span class="typeNameLink">IHtml5Options</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IHtmlOptions.html" title="interface in com.aspose.slides"><span class="typeNameLink">IHtmlOptions</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IPdfOptions.html" title="interface in com.aspose.slides"><span class="typeNameLink">IPdfOptions</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IPptOptions.html" title="interface in com.aspose.slides"><span class="typeNameLink">IPptOptions</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IPptxOptions.html" title="interface in com.aspose.slides"><span class="typeNameLink">IPptxOptions</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IRenderingOptions.html" title="interface in com.aspose.slides"><span class="typeNameLink">IRenderingOptions</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ISVGOptions.html" title="interface in com.aspose.slides"><span class="typeNameLink">ISVGOptions</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ISwfOptions.html" title="interface in com.aspose.slides"><span class="typeNameLink">ISwfOptions</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ITiffOptions.html" title="interface in com.aspose.slides"><span class="typeNameLink">ITiffOptions</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IXamlOptions.html" title="interface in com.aspose.slides"><span class="typeNameLink">IXamlOptions</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IXpsOptions.html" title="interface in com.aspose.slides"><span class="typeNameLink">IXpsOptions</span></a></li>
</ul>
</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ISaveOptionsFactory.html" title="interface in com.aspose.slides"><span class="typeNameLink">ISaveOptionsFactory</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ISection.html" title="interface in com.aspose.slides"><span class="typeNameLink">ISection</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IShapeBevel.html" title="interface in com.aspose.slides"><span class="typeNameLink">IShapeBevel</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IShapeBevelEffectiveData.html" title="interface in com.aspose.slides"><span class="typeNameLink">IShapeBevelEffectiveData</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IShapeElement.html" title="interface in com.aspose.slides"><span class="typeNameLink">IShapeElement</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IShapeStyle.html" title="interface in com.aspose.slides"><span class="typeNameLink">IShapeStyle</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ISketchFormat.html" title="interface in com.aspose.slides"><span class="typeNameLink">ISketchFormat</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ISketchFormatEffectiveData.html" title="interface in com.aspose.slides"><span class="typeNameLink">ISketchFormatEffectiveData</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ISlideImageFormat.html" title="interface in com.aspose.slides"><span class="typeNameLink">ISlideImageFormat</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ISlideShowTransition.html" title="interface in com.aspose.slides"><span class="typeNameLink">ISlideShowTransition</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ISlideSize.html" title="interface in com.aspose.slides"><span class="typeNameLink">ISlideSize</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ISlidesLayoutOptions.html" title="interface in com.aspose.slides"><span class="typeNameLink">ISlidesLayoutOptions</span></a>
<ul>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/INotesCommentsLayoutingOptions.html" title="interface in com.aspose.slides"><span class="typeNameLink">INotesCommentsLayoutingOptions</span></a></li>
</ul>
</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ISlideText.html" title="interface in com.aspose.slides"><span class="typeNameLink">ISlideText</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ISmartArtNode.html" title="interface in com.aspose.slides"><span class="typeNameLink">ISmartArtNode</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ISpreadsheetOptions.html" title="interface in com.aspose.slides"><span class="typeNameLink">ISpreadsheetOptions</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ISvgImage.html" title="interface in com.aspose.slides"><span class="typeNameLink">ISvgImage</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ISvgShape.html" title="interface in com.aspose.slides"><span class="typeNameLink">ISvgShape</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ISvgShapeFormattingController.html" title="interface in com.aspose.slides"><span class="typeNameLink">ISvgShapeFormattingController</span></a>
<ul>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ISvgShapeAndTextFormattingController.html" title="interface in com.aspose.slides"><span class="typeNameLink">ISvgShapeAndTextFormattingController</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IVideoPlayerHtmlController.html" title="interface in com.aspose.slides"><span class="typeNameLink">IVideoPlayerHtmlController</span></a> (also extends com.aspose.slides.<a href="../../../com/aspose/slides/IHtmlFormattingController.html" title="interface in com.aspose.slides">IHtmlFormattingController</a>, com.aspose.slides.<a href="../../../com/aspose/slides/ILinkEmbedController.html" title="interface in com.aspose.slides">ILinkEmbedController</a>)</li>
</ul>
</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ISvgTSpan.html" title="interface in com.aspose.slides"><span class="typeNameLink">ISvgTSpan</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ITabFactory.html" title="interface in com.aspose.slides"><span class="typeNameLink">ITabFactory</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ITableFormat.html" title="interface in com.aspose.slides"><span class="typeNameLink">ITableFormat</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ITemplateEngine.html" title="interface in com.aspose.slides"><span class="typeNameLink">ITemplateEngine</span></a></li>
<li type="circle">java.lang.Iterable&lt;T&gt;
<ul>
<li type="circle">com.aspose.ms.System.Collections.IEnumerable&lt;T&gt;
<ul>
<li type="circle">com.aspose.ms.System.Collections.ICollection&lt;T&gt;
<ul>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IAdjustValueCollection.html" title="interface in com.aspose.slides"><span class="typeNameLink">IAdjustValueCollection</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IGenericCollection.html" title="interface in com.aspose.slides"><span class="typeNameLink">IGenericCollection</span></a>&lt;T&gt; (also extends com.aspose.ms.System.Collections.Generic.IGenericEnumerable&lt;T&gt;)
<ul>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IAudioCollection.html" title="interface in com.aspose.slides"><span class="typeNameLink">IAudioCollection</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ICellCollection.html" title="interface in com.aspose.slides"><span class="typeNameLink">ICellCollection</span></a> (also extends com.aspose.slides.<a href="../../../com/aspose/slides/ISlideComponent.html" title="interface in com.aspose.slides">ISlideComponent</a>)
<ul>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IColumn.html" title="interface in com.aspose.slides"><span class="typeNameLink">IColumn</span></a> (also extends com.aspose.slides.<a href="../../../com/aspose/slides/IBulkTextFormattable.html" title="interface in com.aspose.slides">IBulkTextFormattable</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IRow.html" title="interface in com.aspose.slides"><span class="typeNameLink">IRow</span></a> (also extends com.aspose.slides.<a href="../../../com/aspose/slides/IBulkTextFormattable.html" title="interface in com.aspose.slides">IBulkTextFormattable</a>)</li>
</ul>
</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IChartCategoryCollection.html" title="interface in com.aspose.slides"><span class="typeNameLink">IChartCategoryCollection</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IChartDataPointCollection.html" title="interface in com.aspose.slides"><span class="typeNameLink">IChartDataPointCollection</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IChartDataWorksheetCollection.html" title="interface in com.aspose.slides"><span class="typeNameLink">IChartDataWorksheetCollection</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IChartSeriesCollection.html" title="interface in com.aspose.slides"><span class="typeNameLink">IChartSeriesCollection</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IChartSeriesGroupCollection.html" title="interface in com.aspose.slides"><span class="typeNameLink">IChartSeriesGroupCollection</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IChartSeriesReadonlyCollection.html" title="interface in com.aspose.slides"><span class="typeNameLink">IChartSeriesReadonlyCollection</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IColorOperationCollection.html" title="interface in com.aspose.slides"><span class="typeNameLink">IColorOperationCollection</span></a> (also extends com.aspose.slides.<a href="../../../com/aspose/slides/IGenericCloneable.html" title="interface in com.aspose.slides">IGenericCloneable</a>&lt;T&gt;)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IColumnCollection.html" title="interface in com.aspose.slides"><span class="typeNameLink">IColumnCollection</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ICommentAuthorCollection.html" title="interface in com.aspose.slides"><span class="typeNameLink">ICommentAuthorCollection</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ICommentCollection.html" title="interface in com.aspose.slides"><span class="typeNameLink">ICommentCollection</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IControlCollection.html" title="interface in com.aspose.slides"><span class="typeNameLink">IControlCollection</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ICustomXmlPartCollection.html" title="interface in com.aspose.slides"><span class="typeNameLink">ICustomXmlPartCollection</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IDigitalSignatureCollection.html" title="interface in com.aspose.slides"><span class="typeNameLink">IDigitalSignatureCollection</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IEffectStyleCollection.html" title="interface in com.aspose.slides"><span class="typeNameLink">IEffectStyleCollection</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IEffectStyleCollectionEffectiveData.html" title="interface in com.aspose.slides"><span class="typeNameLink">IEffectStyleCollectionEffectiveData</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IExtraColorSchemeCollection.html" title="interface in com.aspose.slides"><span class="typeNameLink">IExtraColorSchemeCollection</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IFillFormatCollection.html" title="interface in com.aspose.slides"><span class="typeNameLink">IFillFormatCollection</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IFillFormatCollectionEffectiveData.html" title="interface in com.aspose.slides"><span class="typeNameLink">IFillFormatCollectionEffectiveData</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IFontFallBackRulesCollection.html" title="interface in com.aspose.slides"><span class="typeNameLink">IFontFallBackRulesCollection</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IFontSubstRuleCollection.html" title="interface in com.aspose.slides"><span class="typeNameLink">IFontSubstRuleCollection</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IGradientStopCollection.html" title="interface in com.aspose.slides"><span class="typeNameLink">IGradientStopCollection</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IGradientStopCollectionEffectiveData.html" title="interface in com.aspose.slides"><span class="typeNameLink">IGradientStopCollectionEffectiveData</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IImageCollection.html" title="interface in com.aspose.slides"><span class="typeNameLink">IImageCollection</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IImageTransformOCollectionEffectiveData.html" title="interface in com.aspose.slides"><span class="typeNameLink">IImageTransformOCollectionEffectiveData</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ILayoutSlideCollection.html" title="interface in com.aspose.slides"><span class="typeNameLink">ILayoutSlideCollection</span></a>
<ul>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IGlobalLayoutSlideCollection.html" title="interface in com.aspose.slides"><span class="typeNameLink">IGlobalLayoutSlideCollection</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IMasterLayoutSlideCollection.html" title="interface in com.aspose.slides"><span class="typeNameLink">IMasterLayoutSlideCollection</span></a></li>
</ul>
</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ILineFormatCollection.html" title="interface in com.aspose.slides"><span class="typeNameLink">ILineFormatCollection</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ILineFormatCollectionEffectiveData.html" title="interface in com.aspose.slides"><span class="typeNameLink">ILineFormatCollectionEffectiveData</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IMasterSlideCollection.html" title="interface in com.aspose.slides"><span class="typeNameLink">IMasterSlideCollection</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IRowCollection.html" title="interface in com.aspose.slides"><span class="typeNameLink">IRowCollection</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ISectionCollection.html" title="interface in com.aspose.slides"><span class="typeNameLink">ISectionCollection</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ISectionSlideCollection.html" title="interface in com.aspose.slides"><span class="typeNameLink">ISectionSlideCollection</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IShapeCollection.html" title="interface in com.aspose.slides"><span class="typeNameLink">IShapeCollection</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ISlideCollection.html" title="interface in com.aspose.slides"><span class="typeNameLink">ISlideCollection</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ISmartArtNodeCollection.html" title="interface in com.aspose.slides"><span class="typeNameLink">ISmartArtNodeCollection</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ISmartArtShapeCollection.html" title="interface in com.aspose.slides"><span class="typeNameLink">ISmartArtShapeCollection</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ISummaryZoomSectionCollection.html" title="interface in com.aspose.slides"><span class="typeNameLink">ISummaryZoomSectionCollection</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ITabCollection.html" title="interface in com.aspose.slides"><span class="typeNameLink">ITabCollection</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ITagCollection.html" title="interface in com.aspose.slides"><span class="typeNameLink">ITagCollection</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ITextAnimationCollection.html" title="interface in com.aspose.slides"><span class="typeNameLink">ITextAnimationCollection</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IVbaModuleCollection.html" title="interface in com.aspose.slides"><span class="typeNameLink">IVbaModuleCollection</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IVbaReferenceCollection.html" title="interface in com.aspose.slides"><span class="typeNameLink">IVbaReferenceCollection</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IVideoCollection.html" title="interface in com.aspose.slides"><span class="typeNameLink">IVideoCollection</span></a></li>
</ul>
</li>
</ul>
</li>
<li type="circle">com.aspose.ms.System.Collections.Generic.IGenericEnumerable&lt;T&gt;
<ul>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IBehaviorCollection.html" title="interface in com.aspose.slides"><span class="typeNameLink">IBehaviorCollection</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ICaptionsCollection.html" title="interface in com.aspose.slides"><span class="typeNameLink">ICaptionsCollection</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IChartCellCollection.html" title="interface in com.aspose.slides"><span class="typeNameLink">IChartCellCollection</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IControlPropertiesCollection.html" title="interface in com.aspose.slides"><span class="typeNameLink">IControlPropertiesCollection</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IDataLabelCollection.html" title="interface in com.aspose.slides"><span class="typeNameLink">IDataLabelCollection</span></a> (also extends com.aspose.slides.<a href="../../../com/aspose/slides/IChartComponent.html" title="interface in com.aspose.slides">IChartComponent</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IDrawingGuidesCollection.html" title="interface in com.aspose.slides"><span class="typeNameLink">IDrawingGuidesCollection</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IGenericCollection.html" title="interface in com.aspose.slides"><span class="typeNameLink">IGenericCollection</span></a>&lt;T&gt; (also extends com.aspose.ms.System.Collections.ICollection&lt;T&gt;)
<ul>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IAudioCollection.html" title="interface in com.aspose.slides"><span class="typeNameLink">IAudioCollection</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ICellCollection.html" title="interface in com.aspose.slides"><span class="typeNameLink">ICellCollection</span></a> (also extends com.aspose.slides.<a href="../../../com/aspose/slides/ISlideComponent.html" title="interface in com.aspose.slides">ISlideComponent</a>)
<ul>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IColumn.html" title="interface in com.aspose.slides"><span class="typeNameLink">IColumn</span></a> (also extends com.aspose.slides.<a href="../../../com/aspose/slides/IBulkTextFormattable.html" title="interface in com.aspose.slides">IBulkTextFormattable</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IRow.html" title="interface in com.aspose.slides"><span class="typeNameLink">IRow</span></a> (also extends com.aspose.slides.<a href="../../../com/aspose/slides/IBulkTextFormattable.html" title="interface in com.aspose.slides">IBulkTextFormattable</a>)</li>
</ul>
</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IChartCategoryCollection.html" title="interface in com.aspose.slides"><span class="typeNameLink">IChartCategoryCollection</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IChartDataPointCollection.html" title="interface in com.aspose.slides"><span class="typeNameLink">IChartDataPointCollection</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IChartDataWorksheetCollection.html" title="interface in com.aspose.slides"><span class="typeNameLink">IChartDataWorksheetCollection</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IChartSeriesCollection.html" title="interface in com.aspose.slides"><span class="typeNameLink">IChartSeriesCollection</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IChartSeriesGroupCollection.html" title="interface in com.aspose.slides"><span class="typeNameLink">IChartSeriesGroupCollection</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IChartSeriesReadonlyCollection.html" title="interface in com.aspose.slides"><span class="typeNameLink">IChartSeriesReadonlyCollection</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IColorOperationCollection.html" title="interface in com.aspose.slides"><span class="typeNameLink">IColorOperationCollection</span></a> (also extends com.aspose.slides.<a href="../../../com/aspose/slides/IGenericCloneable.html" title="interface in com.aspose.slides">IGenericCloneable</a>&lt;T&gt;)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IColumnCollection.html" title="interface in com.aspose.slides"><span class="typeNameLink">IColumnCollection</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ICommentAuthorCollection.html" title="interface in com.aspose.slides"><span class="typeNameLink">ICommentAuthorCollection</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ICommentCollection.html" title="interface in com.aspose.slides"><span class="typeNameLink">ICommentCollection</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IControlCollection.html" title="interface in com.aspose.slides"><span class="typeNameLink">IControlCollection</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ICustomXmlPartCollection.html" title="interface in com.aspose.slides"><span class="typeNameLink">ICustomXmlPartCollection</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IDigitalSignatureCollection.html" title="interface in com.aspose.slides"><span class="typeNameLink">IDigitalSignatureCollection</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IEffectStyleCollection.html" title="interface in com.aspose.slides"><span class="typeNameLink">IEffectStyleCollection</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IEffectStyleCollectionEffectiveData.html" title="interface in com.aspose.slides"><span class="typeNameLink">IEffectStyleCollectionEffectiveData</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IExtraColorSchemeCollection.html" title="interface in com.aspose.slides"><span class="typeNameLink">IExtraColorSchemeCollection</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IFillFormatCollection.html" title="interface in com.aspose.slides"><span class="typeNameLink">IFillFormatCollection</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IFillFormatCollectionEffectiveData.html" title="interface in com.aspose.slides"><span class="typeNameLink">IFillFormatCollectionEffectiveData</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IFontFallBackRulesCollection.html" title="interface in com.aspose.slides"><span class="typeNameLink">IFontFallBackRulesCollection</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IFontSubstRuleCollection.html" title="interface in com.aspose.slides"><span class="typeNameLink">IFontSubstRuleCollection</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IGradientStopCollection.html" title="interface in com.aspose.slides"><span class="typeNameLink">IGradientStopCollection</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IGradientStopCollectionEffectiveData.html" title="interface in com.aspose.slides"><span class="typeNameLink">IGradientStopCollectionEffectiveData</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IImageCollection.html" title="interface in com.aspose.slides"><span class="typeNameLink">IImageCollection</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IImageTransformOCollectionEffectiveData.html" title="interface in com.aspose.slides"><span class="typeNameLink">IImageTransformOCollectionEffectiveData</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ILayoutSlideCollection.html" title="interface in com.aspose.slides"><span class="typeNameLink">ILayoutSlideCollection</span></a>
<ul>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IGlobalLayoutSlideCollection.html" title="interface in com.aspose.slides"><span class="typeNameLink">IGlobalLayoutSlideCollection</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IMasterLayoutSlideCollection.html" title="interface in com.aspose.slides"><span class="typeNameLink">IMasterLayoutSlideCollection</span></a></li>
</ul>
</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ILineFormatCollection.html" title="interface in com.aspose.slides"><span class="typeNameLink">ILineFormatCollection</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ILineFormatCollectionEffectiveData.html" title="interface in com.aspose.slides"><span class="typeNameLink">ILineFormatCollectionEffectiveData</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IMasterSlideCollection.html" title="interface in com.aspose.slides"><span class="typeNameLink">IMasterSlideCollection</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IRowCollection.html" title="interface in com.aspose.slides"><span class="typeNameLink">IRowCollection</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ISectionCollection.html" title="interface in com.aspose.slides"><span class="typeNameLink">ISectionCollection</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ISectionSlideCollection.html" title="interface in com.aspose.slides"><span class="typeNameLink">ISectionSlideCollection</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IShapeCollection.html" title="interface in com.aspose.slides"><span class="typeNameLink">IShapeCollection</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ISlideCollection.html" title="interface in com.aspose.slides"><span class="typeNameLink">ISlideCollection</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ISmartArtNodeCollection.html" title="interface in com.aspose.slides"><span class="typeNameLink">ISmartArtNodeCollection</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ISmartArtShapeCollection.html" title="interface in com.aspose.slides"><span class="typeNameLink">ISmartArtShapeCollection</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ISummaryZoomSectionCollection.html" title="interface in com.aspose.slides"><span class="typeNameLink">ISummaryZoomSectionCollection</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ITabCollection.html" title="interface in com.aspose.slides"><span class="typeNameLink">ITabCollection</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ITagCollection.html" title="interface in com.aspose.slides"><span class="typeNameLink">ITagCollection</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ITextAnimationCollection.html" title="interface in com.aspose.slides"><span class="typeNameLink">ITextAnimationCollection</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IVbaModuleCollection.html" title="interface in com.aspose.slides"><span class="typeNameLink">IVbaModuleCollection</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IVbaReferenceCollection.html" title="interface in com.aspose.slides"><span class="typeNameLink">IVbaReferenceCollection</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IVideoCollection.html" title="interface in com.aspose.slides"><span class="typeNameLink">IVideoCollection</span></a></li>
</ul>
</li>
<li type="circle">com.aspose.ms.System.Collections.Generic.IGenericCollection&lt;T&gt;
<ul>
<li type="circle">com.aspose.ms.System.Collections.Generic.IGenericList&lt;T&gt;
<ul>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IBehaviorPropertyCollection.html" title="interface in com.aspose.slides"><span class="typeNameLink">IBehaviorPropertyCollection</span></a></li>
</ul>
</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IImageTransformOperationCollection.html" title="interface in com.aspose.slides"><span class="typeNameLink">IImageTransformOperationCollection</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IPieSplitCustomPointCollection.html" title="interface in com.aspose.slides"><span class="typeNameLink">IPieSplitCustomPointCollection</span></a></li>
</ul>
</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IMathBlockCollection.html" title="interface in com.aspose.slides"><span class="typeNameLink">IMathBlockCollection</span></a>
<ul>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IMathParagraph.html" title="interface in com.aspose.slides"><span class="typeNameLink">IMathParagraph</span></a></li>
</ul>
</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IMathElementCollection.html" title="interface in com.aspose.slides"><span class="typeNameLink">IMathElementCollection</span></a>
<ul>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IMathBlock.html" title="interface in com.aspose.slides"><span class="typeNameLink">IMathBlock</span></a> (also extends com.aspose.slides.<a href="../../../com/aspose/slides/IMathElement.html" title="interface in com.aspose.slides">IMathElement</a>)</li>
</ul>
</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IMotionPath.html" title="interface in com.aspose.slides"><span class="typeNameLink">IMotionPath</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IParagraphCollection.html" title="interface in com.aspose.slides"><span class="typeNameLink">IParagraphCollection</span></a> (also extends com.aspose.slides.<a href="../../../com/aspose/slides/ISlideComponent.html" title="interface in com.aspose.slides">ISlideComponent</a>)</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IPointCollection.html" title="interface in com.aspose.slides"><span class="typeNameLink">IPointCollection</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IPortionCollection.html" title="interface in com.aspose.slides"><span class="typeNameLink">IPortionCollection</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ISequence.html" title="interface in com.aspose.slides"><span class="typeNameLink">ISequence</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ISequenceCollection.html" title="interface in com.aspose.slides"><span class="typeNameLink">ISequenceCollection</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ITrendlineCollection.html" title="interface in com.aspose.slides"><span class="typeNameLink">ITrendlineCollection</span></a></li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ITextAnimation.html" title="interface in com.aspose.slides"><span class="typeNameLink">ITextAnimation</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ITextFrameFormat.html" title="interface in com.aspose.slides"><span class="typeNameLink">ITextFrameFormat</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ITextFrameFormatEffectiveData.html" title="interface in com.aspose.slides"><span class="typeNameLink">ITextFrameFormatEffectiveData</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ITextHighlightingOptions.html" title="interface in com.aspose.slides"><span class="typeNameLink">ITextHighlightingOptions</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ITextSearchOptions.html" title="interface in com.aspose.slides"><span class="typeNameLink">ITextSearchOptions</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ITextStyle.html" title="interface in com.aspose.slides"><span class="typeNameLink">ITextStyle</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ITextStyleEffectiveData.html" title="interface in com.aspose.slides"><span class="typeNameLink">ITextStyleEffectiveData</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ITextToHtmlConversionOptions.html" title="interface in com.aspose.slides"><span class="typeNameLink">ITextToHtmlConversionOptions</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IThemeEffectiveData.html" title="interface in com.aspose.slides"><span class="typeNameLink">IThemeEffectiveData</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IThemeManager.html" title="interface in com.aspose.slides"><span class="typeNameLink">IThemeManager</span></a>
<ul>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IMasterThemeManager.html" title="interface in com.aspose.slides"><span class="typeNameLink">IMasterThemeManager</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IOverrideThemeManager.html" title="interface in com.aspose.slides"><span class="typeNameLink">IOverrideThemeManager</span></a></li>
</ul>
</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IThreeDParamSource.html" title="interface in com.aspose.slides"><span class="typeNameLink">IThreeDParamSource</span></a>
<ul>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IThreeDFormat.html" title="interface in com.aspose.slides"><span class="typeNameLink">IThreeDFormat</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IThreeDFormatEffectiveData.html" title="interface in com.aspose.slides"><span class="typeNameLink">IThreeDFormatEffectiveData</span></a></li>
</ul>
</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ITiming.html" title="interface in com.aspose.slides"><span class="typeNameLink">ITiming</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ITransitionValueBase.html" title="interface in com.aspose.slides"><span class="typeNameLink">ITransitionValueBase</span></a>
<ul>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ICornerDirectionTransition.html" title="interface in com.aspose.slides"><span class="typeNameLink">ICornerDirectionTransition</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IEightDirectionTransition.html" title="interface in com.aspose.slides"><span class="typeNameLink">IEightDirectionTransition</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IEmptyTransition.html" title="interface in com.aspose.slides"><span class="typeNameLink">IEmptyTransition</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IFlyThroughTransition.html" title="interface in com.aspose.slides"><span class="typeNameLink">IFlyThroughTransition</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IGlitterTransition.html" title="interface in com.aspose.slides"><span class="typeNameLink">IGlitterTransition</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IInOutTransition.html" title="interface in com.aspose.slides"><span class="typeNameLink">IInOutTransition</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ILeftRightDirectionTransition.html" title="interface in com.aspose.slides"><span class="typeNameLink">ILeftRightDirectionTransition</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IMorphTransition.html" title="interface in com.aspose.slides"><span class="typeNameLink">IMorphTransition</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IOptionalBlackTransition.html" title="interface in com.aspose.slides"><span class="typeNameLink">IOptionalBlackTransition</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IOrientationTransition.html" title="interface in com.aspose.slides"><span class="typeNameLink">IOrientationTransition</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IRevealTransition.html" title="interface in com.aspose.slides"><span class="typeNameLink">IRevealTransition</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IRippleTransition.html" title="interface in com.aspose.slides"><span class="typeNameLink">IRippleTransition</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IShredTransition.html" title="interface in com.aspose.slides"><span class="typeNameLink">IShredTransition</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ISideDirectionTransition.html" title="interface in com.aspose.slides"><span class="typeNameLink">ISideDirectionTransition</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/ISplitTransition.html" title="interface in com.aspose.slides"><span class="typeNameLink">ISplitTransition</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IWheelTransition.html" title="interface in com.aspose.slides"><span class="typeNameLink">IWheelTransition</span></a></li>
</ul>
</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IUpDownBarsManager.html" title="interface in com.aspose.slides"><span class="typeNameLink">IUpDownBarsManager</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IVbaModule.html" title="interface in com.aspose.slides"><span class="typeNameLink">IVbaModule</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IVbaProject.html" title="interface in com.aspose.slides"><span class="typeNameLink">IVbaProject</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IVbaProjectFactory.html" title="interface in com.aspose.slides"><span class="typeNameLink">IVbaProjectFactory</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IVbaReference.html" title="interface in com.aspose.slides"><span class="typeNameLink">IVbaReference</span></a>
<ul>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IVbaReferenceOleTwiddledTypeLib.html" title="interface in com.aspose.slides"><span class="typeNameLink">IVbaReferenceOleTwiddledTypeLib</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IVbaReferenceOleTypeLib.html" title="interface in com.aspose.slides"><span class="typeNameLink">IVbaReferenceOleTypeLib</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IVbaReferenceProject.html" title="interface in com.aspose.slides"><span class="typeNameLink">IVbaReferenceProject</span></a></li>
</ul>
</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IVbaReferenceFactory.html" title="interface in com.aspose.slides"><span class="typeNameLink">IVbaReferenceFactory</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IVideo.html" title="interface in com.aspose.slides"><span class="typeNameLink">IVideo</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IVideoPlayerHtmlControllerFactory.html" title="interface in com.aspose.slides"><span class="typeNameLink">IVideoPlayerHtmlControllerFactory</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IViewProperties.html" title="interface in com.aspose.slides"><span class="typeNameLink">IViewProperties</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IWarningCallback.html" title="interface in com.aspose.slides"><span class="typeNameLink">IWarningCallback</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IWarningInfo.html" title="interface in com.aspose.slides"><span class="typeNameLink">IWarningInfo</span></a>
<ul>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IKnownIssueWarningInfo.html" title="interface in com.aspose.slides"><span class="typeNameLink">IKnownIssueWarningInfo</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/INotImplementedWarningInfo.html" title="interface in com.aspose.slides"><span class="typeNameLink">INotImplementedWarningInfo</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IObsoletePresLockingBehaviorWarningInfo.html" title="interface in com.aspose.slides"><span class="typeNameLink">IObsoletePresLockingBehaviorWarningInfo</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IPresentationSignedWarningInfo.html" title="interface in com.aspose.slides"><span class="typeNameLink">IPresentationSignedWarningInfo</span></a></li>
</ul>
</li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/IXamlOutputSaver.html" title="interface in com.aspose.slides"><span class="typeNameLink">IXamlOutputSaver</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/PresentationAnimationsGenerator.NewAnimation.html" title="interface in com.aspose.slides"><span class="typeNameLink">PresentationAnimationsGenerator.NewAnimation</span></a></li>
<li type="circle">com.aspose.slides.<a href="../../../com/aspose/slides/PresentationPlayer.FrameTick.html" title="interface in com.aspose.slides"><span class="typeNameLink">PresentationPlayer.FrameTick</span></a></li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li>Class</li>
<li class="navBarCell1Rev">Tree</li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/package-tree.html" target="_top">Frames</a></li>
<li><a href="package-tree.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><i>Copyright &copy; 2004-2025 Aspose Pty Ltd. All Rights Reserved.</i></small></p>
</body>
</html>
