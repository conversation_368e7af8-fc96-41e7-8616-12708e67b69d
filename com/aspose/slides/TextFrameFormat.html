<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_421) on Fri Jul 11 05:32:44 CDT 2025 -->
<title>TextFrameFormat (Aspose.Slides for Java (25.7) API Documentation)</title>
<meta name="date" content="2025-07-11">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="TextFrameFormat (Aspose.Slides for Java (25.7) API Documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10,"i26":10,"i27":10,"i28":10,"i29":10,"i30":10,"i31":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/TextFrame.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/TextHighlightingOptions.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/TextFrameFormat.html" target="_top">Frames</a></li>
<li><a href="TextFrameFormat.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.aspose.slides</div>
<h2 title="Class TextFrameFormat" class="title">Class TextFrameFormat</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../com/aspose/slides/PVIObject.html" title="class in com.aspose.slides">com.aspose.slides.PVIObject</a></li>
<li>
<ul class="inheritance">
<li>com.aspose.slides.TextFrameFormat</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../com/aspose/slides/IChartTextBlockFormat.html" title="interface in com.aspose.slides">IChartTextBlockFormat</a>, <a href="../../../com/aspose/slides/IPresentationComponent.html" title="interface in com.aspose.slides">IPresentationComponent</a>, <a href="../../../com/aspose/slides/ISlideComponent.html" title="interface in com.aspose.slides">ISlideComponent</a>, <a href="../../../com/aspose/slides/ITextFrameFormat.html" title="interface in com.aspose.slides">ITextFrameFormat</a></dd>
</dl>
<hr>
<br>
<pre>public final class <span class="typeNameLabel">TextFrameFormat</span>
extends <a href="../../../com/aspose/slides/PVIObject.html" title="class in com.aspose.slides">PVIObject</a>
implements <a href="../../../com/aspose/slides/ITextFrameFormat.html" title="interface in com.aspose.slides">ITextFrameFormat</a>, <a href="../../../com/aspose/slides/IChartTextBlockFormat.html" title="interface in com.aspose.slides">IChartTextBlockFormat</a></pre>
<div class="block"><p>
  Contains the TextFrame's formatTextFrameFormatting properties.
 </p></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TextFrameFormat.html#TextFrameFormat--">TextFrameFormat</a></span>()</code>
<div class="block">
 Initializes a new instance of <a href="../../../com/aspose/slides/TextFrameFormat.html" title="class in com.aspose.slides"><code>TextFrameFormat</code></a> class.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>byte</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TextFrameFormat.html#getAnchoringType--">getAnchoringType</a></span>()</code>
<div class="block">
 Returns or sets vertical anchor text in a TextFrame.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>byte</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TextFrameFormat.html#getAutofitType--">getAutofitType</a></span>()</code>
<div class="block">
 Returns or sets text's autofit mode.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>byte</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TextFrameFormat.html#getCenterText--">getCenterText</a></span>()</code>
<div class="block">
 If NullableBool.True then text should be centered in box horizontally.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TextFrameFormat.html#getColumnCount--">getColumnCount</a></span>()</code>
<div class="block">
 Returns or sets number of columns in the text area.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TextFrameFormat.html#getColumnSpacing--">getColumnSpacing</a></span>()</code>
<div class="block">
 Returns or sets the space between text columns in the text area (in points).</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/ITextFrameFormatEffectiveData.html" title="interface in com.aspose.slides">ITextFrameFormatEffectiveData</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TextFrameFormat.html#getEffective--">getEffective</a></span>()</code>
<div class="block">
 Gets effective text frame formatting data with the inheritance applied.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TextFrameFormat.html#getKeepTextFlat--">getKeepTextFlat</a></span>()</code>
<div class="block">
 Gets or sets keeping text flat even if a 3-D Rotation effect was applied.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TextFrameFormat.html#getMarginBottom--">getMarginBottom</a></span>()</code>
<div class="block">
 Returns or sets the bottom margin (points) in a TextFrame.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TextFrameFormat.html#getMarginLeft--">getMarginLeft</a></span>()</code>
<div class="block">
 Returns or sets the left margin (points) in a TextFrame.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TextFrameFormat.html#getMarginRight--">getMarginRight</a></span>()</code>
<div class="block">
 Returns or sets the right margin (points) in a TextFrame.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TextFrameFormat.html#getMarginTop--">getMarginTop</a></span>()</code>
<div class="block">
 Returns or sets the top margin (points) in a TextFrame.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TextFrameFormat.html#getRotationAngle--">getRotationAngle</a></span>()</code>
<div class="block">
 Specifies custom the rotation that is being applied to the text within the bounding box.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/ITextStyle.html" title="interface in com.aspose.slides">ITextStyle</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TextFrameFormat.html#getTextStyle--">getTextStyle</a></span>()</code>
<div class="block">
 Returns text's style.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>byte</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TextFrameFormat.html#getTextVerticalType--">getTextVerticalType</a></span>()</code>
<div class="block">
 Determines text orientation.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IThreeDFormat.html" title="interface in com.aspose.slides">IThreeDFormat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TextFrameFormat.html#getThreeDFormat--">getThreeDFormat</a></span>()</code>
<div class="block">
 Returns the ThreeDFormat object that represents 3d effect properties for a text.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>byte</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TextFrameFormat.html#getTransform--">getTransform</a></span>()</code>
<div class="block">
 Gets or sets text wrapping shape.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TextFrameFormat.html#getVersion--">getVersion</a></span>()</code>
<div class="block">
 Version.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>byte</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TextFrameFormat.html#getWrapText--">getWrapText</a></span>()</code>
<div class="block">
 True if text is wrapped at TextFrame's margins.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TextFrameFormat.html#setAnchoringType-byte-">setAnchoringType</a></span>(byte&nbsp;value)</code>
<div class="block">
 Returns or sets vertical anchor text in a TextFrame.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TextFrameFormat.html#setAutofitType-byte-">setAutofitType</a></span>(byte&nbsp;value)</code>
<div class="block">
 Returns or sets text's autofit mode.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TextFrameFormat.html#setCenterText-byte-">setCenterText</a></span>(byte&nbsp;value)</code>
<div class="block">
 If NullableBool.True then text should be centered in box horizontally.</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TextFrameFormat.html#setColumnCount-int-">setColumnCount</a></span>(int&nbsp;value)</code>
<div class="block">
 Returns or sets number of columns in the text area.</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TextFrameFormat.html#setColumnSpacing-double-">setColumnSpacing</a></span>(double&nbsp;value)</code>
<div class="block">
 Returns or sets the space between text columns in the text area (in points).</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TextFrameFormat.html#setKeepTextFlat-boolean-">setKeepTextFlat</a></span>(boolean&nbsp;value)</code>
<div class="block">
 Gets or sets keeping text flat even if a 3-D Rotation effect was applied.</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TextFrameFormat.html#setMarginBottom-double-">setMarginBottom</a></span>(double&nbsp;value)</code>
<div class="block">
 Returns or sets the bottom margin (points) in a TextFrame.</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TextFrameFormat.html#setMarginLeft-double-">setMarginLeft</a></span>(double&nbsp;value)</code>
<div class="block">
 Returns or sets the left margin (points) in a TextFrame.</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TextFrameFormat.html#setMarginRight-double-">setMarginRight</a></span>(double&nbsp;value)</code>
<div class="block">
 Returns or sets the right margin (points) in a TextFrame.</div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TextFrameFormat.html#setMarginTop-double-">setMarginTop</a></span>(double&nbsp;value)</code>
<div class="block">
 Returns or sets the top margin (points) in a TextFrame.</div>
</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TextFrameFormat.html#setRotationAngle-float-">setRotationAngle</a></span>(float&nbsp;value)</code>
<div class="block">
 Specifies custom the rotation that is being applied to the text within the bounding box.</div>
</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TextFrameFormat.html#setTextVerticalType-byte-">setTextVerticalType</a></span>(byte&nbsp;value)</code>
<div class="block">
 Determines text orientation.</div>
</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TextFrameFormat.html#setTransform-byte-">setTransform</a></span>(byte&nbsp;value)</code>
<div class="block">
 Gets or sets text wrapping shape.</div>
</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TextFrameFormat.html#setWrapText-byte-">setWrapText</a></span>(byte&nbsp;value)</code>
<div class="block">
 True if text is wrapped at TextFrame's margins.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.aspose.slides.PVIObject">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;com.aspose.slides.<a href="../../../com/aspose/slides/PVIObject.html" title="class in com.aspose.slides">PVIObject</a></h3>
<code><a href="../../../com/aspose/slides/PVIObject.html#equals-java.lang.Object-">equals</a>, <a href="../../../com/aspose/slides/PVIObject.html#getParent_Immediate--">getParent_Immediate</a>, <a href="../../../com/aspose/slides/PVIObject.html#getParent_IPresentationComponent--">getParent_IPresentationComponent</a>, <a href="../../../com/aspose/slides/PVIObject.html#getParent_ISlideComponent--">getParent_ISlideComponent</a>, <a href="../../../com/aspose/slides/PVIObject.html#getPresentation--">getPresentation</a>, <a href="../../../com/aspose/slides/PVIObject.html#getSlide--">getSlide</a>, <a href="../../../com/aspose/slides/PVIObject.html#hashCode--">hashCode</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>getClass, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="TextFrameFormat--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>TextFrameFormat</h4>
<pre>public&nbsp;TextFrameFormat()</pre>
<div class="block"><p>
 Initializes a new instance of <a href="../../../com/aspose/slides/TextFrameFormat.html" title="class in com.aspose.slides"><code>TextFrameFormat</code></a> class.
 </p></div>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getVersion--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVersion</h4>
<pre>public&nbsp;long&nbsp;getVersion()</pre>
<div class="block"><p>
 Version.
 Read-only <code>long</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../com/aspose/slides/PVIObject.html#getVersion--">getVersion</a></code>&nbsp;in class&nbsp;<code><a href="../../../com/aspose/slides/PVIObject.html" title="class in com.aspose.slides">PVIObject</a></code></dd>
</dl>
</li>
</ul>
<a name="getTextStyle--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTextStyle</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/ITextStyle.html" title="interface in com.aspose.slides">ITextStyle</a>&nbsp;getTextStyle()</pre>
<div class="block"><p>
 Returns text's style.
 Read-only <a href="../../../com/aspose/slides/ITextStyle.html" title="interface in com.aspose.slides"><code>ITextStyle</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITextFrameFormat.html#getTextStyle--">getTextStyle</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITextFrameFormat.html" title="interface in com.aspose.slides">ITextFrameFormat</a></code></dd>
</dl>
</li>
</ul>
<a name="getThreeDFormat--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getThreeDFormat</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IThreeDFormat.html" title="interface in com.aspose.slides">IThreeDFormat</a>&nbsp;getThreeDFormat()</pre>
<div class="block"><p>
 Returns the ThreeDFormat object that represents 3d effect properties for a text.
 Read-only <a href="../../../com/aspose/slides/IThreeDFormat.html" title="interface in com.aspose.slides"><code>IThreeDFormat</code></a>.
 </p><p><hr><blockquote><pre>
 <pre>
 Presentation pres = new Presentation();
 try {
     IAutoShape autoShape = pres.getSlides().get_Item(0).getShapes().addAutoShape(ShapeType.Rectangle, 10, 20, 400, 300);
     ITextFrame textFrame = autoShape.getTextFrame();
     textFrame.setText("Aspose.Slide Test Text");
     // Set text transformation
     textFrame.getTextFrameFormat().setTransform(TextShapeType.ArchUpPour);
     // Set Extrusion
     textFrame.getTextFrameFormat().getThreeDFormat().getExtrusionColor().setColor(Color.ORANGE);
     textFrame.getTextFrameFormat().getThreeDFormat().setExtrusionHeight(6);
     // Set Contour
     textFrame.getTextFrameFormat().getThreeDFormat().getContourColor().setColor(Color.DARK_GRAY);
     textFrame.getTextFrameFormat().getThreeDFormat().setContourWidth(1.5);
     // Set Depth
     textFrame.getTextFrameFormat().getThreeDFormat().setDepth(3);
     // Set Material
     textFrame.getTextFrameFormat().getThreeDFormat().setMaterial(MaterialPresetType.Plastic);
     // Set Lighting
     textFrame.getTextFrameFormat().getThreeDFormat().getLightRig().setDirection(LightingDirection.Top);
     textFrame.getTextFrameFormat().getThreeDFormat().getLightRig().setLightType(LightRigPresetType.Balanced);
     textFrame.getTextFrameFormat().getThreeDFormat().getLightRig().setRotation(0, 0, 40);
     // Set camera type
     textFrame.getTextFrameFormat().getThreeDFormat().getCamera().setCameraType(CameraPresetType.PerspectiveContrastingRightFacing);
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITextFrameFormat.html#getThreeDFormat--">getThreeDFormat</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITextFrameFormat.html" title="interface in com.aspose.slides">ITextFrameFormat</a></code></dd>
</dl>
</li>
</ul>
<a name="getMarginLeft--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMarginLeft</h4>
<pre>public final&nbsp;double&nbsp;getMarginLeft()</pre>
<div class="block"><p>
 Returns or sets the left margin (points) in a TextFrame.
 Read/write <code>double</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IChartTextBlockFormat.html#getMarginLeft--">getMarginLeft</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IChartTextBlockFormat.html" title="interface in com.aspose.slides">IChartTextBlockFormat</a></code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITextFrameFormat.html#getMarginLeft--">getMarginLeft</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITextFrameFormat.html" title="interface in com.aspose.slides">ITextFrameFormat</a></code></dd>
</dl>
</li>
</ul>
<a name="setMarginLeft-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMarginLeft</h4>
<pre>public final&nbsp;void&nbsp;setMarginLeft(double&nbsp;value)</pre>
<div class="block"><p>
 Returns or sets the left margin (points) in a TextFrame.
 Read/write <code>double</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IChartTextBlockFormat.html#setMarginLeft-double-">setMarginLeft</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IChartTextBlockFormat.html" title="interface in com.aspose.slides">IChartTextBlockFormat</a></code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITextFrameFormat.html#setMarginLeft-double-">setMarginLeft</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITextFrameFormat.html" title="interface in com.aspose.slides">ITextFrameFormat</a></code></dd>
</dl>
</li>
</ul>
<a name="getMarginRight--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMarginRight</h4>
<pre>public final&nbsp;double&nbsp;getMarginRight()</pre>
<div class="block"><p>
 Returns or sets the right margin (points) in a TextFrame.
 Read/write <code>double</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IChartTextBlockFormat.html#getMarginRight--">getMarginRight</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IChartTextBlockFormat.html" title="interface in com.aspose.slides">IChartTextBlockFormat</a></code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITextFrameFormat.html#getMarginRight--">getMarginRight</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITextFrameFormat.html" title="interface in com.aspose.slides">ITextFrameFormat</a></code></dd>
</dl>
</li>
</ul>
<a name="setMarginRight-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMarginRight</h4>
<pre>public final&nbsp;void&nbsp;setMarginRight(double&nbsp;value)</pre>
<div class="block"><p>
 Returns or sets the right margin (points) in a TextFrame.
 Read/write <code>double</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IChartTextBlockFormat.html#setMarginRight-double-">setMarginRight</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IChartTextBlockFormat.html" title="interface in com.aspose.slides">IChartTextBlockFormat</a></code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITextFrameFormat.html#setMarginRight-double-">setMarginRight</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITextFrameFormat.html" title="interface in com.aspose.slides">ITextFrameFormat</a></code></dd>
</dl>
</li>
</ul>
<a name="getMarginTop--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMarginTop</h4>
<pre>public final&nbsp;double&nbsp;getMarginTop()</pre>
<div class="block"><p>
 Returns or sets the top margin (points) in a TextFrame.
 Read/write <code>double</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IChartTextBlockFormat.html#getMarginTop--">getMarginTop</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IChartTextBlockFormat.html" title="interface in com.aspose.slides">IChartTextBlockFormat</a></code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITextFrameFormat.html#getMarginTop--">getMarginTop</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITextFrameFormat.html" title="interface in com.aspose.slides">ITextFrameFormat</a></code></dd>
</dl>
</li>
</ul>
<a name="setMarginTop-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMarginTop</h4>
<pre>public final&nbsp;void&nbsp;setMarginTop(double&nbsp;value)</pre>
<div class="block"><p>
 Returns or sets the top margin (points) in a TextFrame.
 Read/write <code>double</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IChartTextBlockFormat.html#setMarginTop-double-">setMarginTop</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IChartTextBlockFormat.html" title="interface in com.aspose.slides">IChartTextBlockFormat</a></code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITextFrameFormat.html#setMarginTop-double-">setMarginTop</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITextFrameFormat.html" title="interface in com.aspose.slides">ITextFrameFormat</a></code></dd>
</dl>
</li>
</ul>
<a name="getMarginBottom--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMarginBottom</h4>
<pre>public final&nbsp;double&nbsp;getMarginBottom()</pre>
<div class="block"><p>
 Returns or sets the bottom margin (points) in a TextFrame.
 Read/write <code>double</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IChartTextBlockFormat.html#getMarginBottom--">getMarginBottom</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IChartTextBlockFormat.html" title="interface in com.aspose.slides">IChartTextBlockFormat</a></code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITextFrameFormat.html#getMarginBottom--">getMarginBottom</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITextFrameFormat.html" title="interface in com.aspose.slides">ITextFrameFormat</a></code></dd>
</dl>
</li>
</ul>
<a name="setMarginBottom-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMarginBottom</h4>
<pre>public final&nbsp;void&nbsp;setMarginBottom(double&nbsp;value)</pre>
<div class="block"><p>
 Returns or sets the bottom margin (points) in a TextFrame.
 Read/write <code>double</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IChartTextBlockFormat.html#setMarginBottom-double-">setMarginBottom</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IChartTextBlockFormat.html" title="interface in com.aspose.slides">IChartTextBlockFormat</a></code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITextFrameFormat.html#setMarginBottom-double-">setMarginBottom</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITextFrameFormat.html" title="interface in com.aspose.slides">ITextFrameFormat</a></code></dd>
</dl>
</li>
</ul>
<a name="getWrapText--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWrapText</h4>
<pre>public final&nbsp;byte&nbsp;getWrapText()</pre>
<div class="block"><p>
 True if text is wrapped at TextFrame's margins.
 Read/write <a href="../../../com/aspose/slides/NullableBool.html" title="class in com.aspose.slides"><code>NullableBool</code></a>.
 </p><p><hr><blockquote><pre>
 The following sample code shows how to wrap text in Presentation.
 <pre>
 Presentation pres = new Presentation();
 try {
     ISlide slide = pres.getSlides().get_Item(0);
     IAutoShape autoShape = slide.getShapes().addAutoShape(ShapeType.Rectangle, 30, 30, 350, 100);
     Portion portion = new Portion("lorem ipsum...");
     portion.getPortionFormat().getFillFormat().getSolidFillColor().setColor(Color.BLACK);
     portion.getPortionFormat().getFillFormat().setFillType(FillType.Solid);
     autoShape.getTextFrame().getParagraphs().get_Item(0).getPortions().add(portion);
     ITextFrameFormat textFrameFormat = autoShape.getTextFrame().getTextFrameFormat();
     textFrameFormat.setWrapText(NullableBool.True);
     pres.save("Output-presentation.pptx", SaveFormat.Pptx);
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IChartTextBlockFormat.html#getWrapText--">getWrapText</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IChartTextBlockFormat.html" title="interface in com.aspose.slides">IChartTextBlockFormat</a></code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITextFrameFormat.html#getWrapText--">getWrapText</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITextFrameFormat.html" title="interface in com.aspose.slides">ITextFrameFormat</a></code></dd>
</dl>
</li>
</ul>
<a name="setWrapText-byte-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setWrapText</h4>
<pre>public final&nbsp;void&nbsp;setWrapText(byte&nbsp;value)</pre>
<div class="block"><p>
 True if text is wrapped at TextFrame's margins.
 Read/write <a href="../../../com/aspose/slides/NullableBool.html" title="class in com.aspose.slides"><code>NullableBool</code></a>.
 </p><p><hr><blockquote><pre>
 The following sample code shows how to wrap text in Presentation.
 <pre>
 Presentation pres = new Presentation();
 try {
     ISlide slide = pres.getSlides().get_Item(0);
     IAutoShape autoShape = slide.getShapes().addAutoShape(ShapeType.Rectangle, 30, 30, 350, 100);
     Portion portion = new Portion("lorem ipsum...");
     portion.getPortionFormat().getFillFormat().getSolidFillColor().setColor(Color.BLACK);
     portion.getPortionFormat().getFillFormat().setFillType(FillType.Solid);
     autoShape.getTextFrame().getParagraphs().get_Item(0).getPortions().add(portion);
     ITextFrameFormat textFrameFormat = autoShape.getTextFrame().getTextFrameFormat();
     textFrameFormat.setWrapText(NullableBool.True);
     pres.save("Output-presentation.pptx", SaveFormat.Pptx);
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IChartTextBlockFormat.html#setWrapText-byte-">setWrapText</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IChartTextBlockFormat.html" title="interface in com.aspose.slides">IChartTextBlockFormat</a></code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITextFrameFormat.html#setWrapText-byte-">setWrapText</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITextFrameFormat.html" title="interface in com.aspose.slides">ITextFrameFormat</a></code></dd>
</dl>
</li>
</ul>
<a name="getAnchoringType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAnchoringType</h4>
<pre>public final&nbsp;byte&nbsp;getAnchoringType()</pre>
<div class="block"><p>
 Returns or sets vertical anchor text in a TextFrame.
 Read/write <a href="../../../com/aspose/slides/TextAnchorType.html" title="class in com.aspose.slides"><code>TextAnchorType</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IChartTextBlockFormat.html#getAnchoringType--">getAnchoringType</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IChartTextBlockFormat.html" title="interface in com.aspose.slides">IChartTextBlockFormat</a></code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITextFrameFormat.html#getAnchoringType--">getAnchoringType</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITextFrameFormat.html" title="interface in com.aspose.slides">ITextFrameFormat</a></code></dd>
</dl>
</li>
</ul>
<a name="setAnchoringType-byte-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAnchoringType</h4>
<pre>public final&nbsp;void&nbsp;setAnchoringType(byte&nbsp;value)</pre>
<div class="block"><p>
 Returns or sets vertical anchor text in a TextFrame.
 Read/write <a href="../../../com/aspose/slides/TextAnchorType.html" title="class in com.aspose.slides"><code>TextAnchorType</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IChartTextBlockFormat.html#setAnchoringType-byte-">setAnchoringType</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IChartTextBlockFormat.html" title="interface in com.aspose.slides">IChartTextBlockFormat</a></code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITextFrameFormat.html#setAnchoringType-byte-">setAnchoringType</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITextFrameFormat.html" title="interface in com.aspose.slides">ITextFrameFormat</a></code></dd>
</dl>
</li>
</ul>
<a name="getCenterText--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCenterText</h4>
<pre>public final&nbsp;byte&nbsp;getCenterText()</pre>
<div class="block"><p>
 If NullableBool.True then text should be centered in box horizontally.
 Read/write <a href="../../../com/aspose/slides/NullableBool.html" title="class in com.aspose.slides"><code>NullableBool</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IChartTextBlockFormat.html#getCenterText--">getCenterText</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IChartTextBlockFormat.html" title="interface in com.aspose.slides">IChartTextBlockFormat</a></code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITextFrameFormat.html#getCenterText--">getCenterText</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITextFrameFormat.html" title="interface in com.aspose.slides">ITextFrameFormat</a></code></dd>
</dl>
</li>
</ul>
<a name="setCenterText-byte-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCenterText</h4>
<pre>public final&nbsp;void&nbsp;setCenterText(byte&nbsp;value)</pre>
<div class="block"><p>
 If NullableBool.True then text should be centered in box horizontally.
 Read/write <a href="../../../com/aspose/slides/NullableBool.html" title="class in com.aspose.slides"><code>NullableBool</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IChartTextBlockFormat.html#setCenterText-byte-">setCenterText</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IChartTextBlockFormat.html" title="interface in com.aspose.slides">IChartTextBlockFormat</a></code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITextFrameFormat.html#setCenterText-byte-">setCenterText</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITextFrameFormat.html" title="interface in com.aspose.slides">ITextFrameFormat</a></code></dd>
</dl>
</li>
</ul>
<a name="getTextVerticalType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTextVerticalType</h4>
<pre>public final&nbsp;byte&nbsp;getTextVerticalType()</pre>
<div class="block"><p>
 Determines text orientation.
 The resulted value of visual text rotation summarized from this property and custom angle
 in property RotationAngle.
 Read/write <a href="../../../com/aspose/slides/TextVerticalType.html" title="class in com.aspose.slides"><code>TextVerticalType</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IChartTextBlockFormat.html#getTextVerticalType--">getTextVerticalType</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IChartTextBlockFormat.html" title="interface in com.aspose.slides">IChartTextBlockFormat</a></code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITextFrameFormat.html#getTextVerticalType--">getTextVerticalType</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITextFrameFormat.html" title="interface in com.aspose.slides">ITextFrameFormat</a></code></dd>
</dl>
</li>
</ul>
<a name="setTextVerticalType-byte-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTextVerticalType</h4>
<pre>public final&nbsp;void&nbsp;setTextVerticalType(byte&nbsp;value)</pre>
<div class="block"><p>
 Determines text orientation.
 The resulted value of visual text rotation summarized from this property and custom angle
 in property RotationAngle.
 Read/write <a href="../../../com/aspose/slides/TextVerticalType.html" title="class in com.aspose.slides"><code>TextVerticalType</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IChartTextBlockFormat.html#setTextVerticalType-byte-">setTextVerticalType</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IChartTextBlockFormat.html" title="interface in com.aspose.slides">IChartTextBlockFormat</a></code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITextFrameFormat.html#setTextVerticalType-byte-">setTextVerticalType</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITextFrameFormat.html" title="interface in com.aspose.slides">ITextFrameFormat</a></code></dd>
</dl>
</li>
</ul>
<a name="getAutofitType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAutofitType</h4>
<pre>public final&nbsp;byte&nbsp;getAutofitType()</pre>
<div class="block"><p>
 Returns or sets text's autofit mode.
 Read/write <a href="../../../com/aspose/slides/TextAutofitType.html" title="class in com.aspose.slides"><code>TextAutofitType</code></a>.
 </p><p><hr><blockquote><pre>
 The following sample code shows how to resize shape to Fit Text in a PowerPoint Presentation.
 <pre>
 Presentation pres = new Presentation();
 try {
     ISlide slide = pres.getSlides().get_Item(0);
     IAutoShape autoShape = slide.getShapes().addAutoShape(ShapeType.Rectangle, 30, 30, 350, 100);
     Portion portion = new Portion("lorem ipsum...");
     portion.getPortionFormat().getFillFormat().getSolidFillColor().setColor(Color.BLACK);
     portion.getPortionFormat().getFillFormat().setFillType(FillType.Solid);
     autoShape.getTextFrame().getParagraphs().get_Item(0).getPortions().add(portion);
     ITextFrameFormat textFrameFormat = autoShape.getTextFrame().getTextFrameFormat();
     textFrameFormat.setAutofitType(TextAutofitType.Shape);
     pres.save("Output-presentation.pptx", SaveFormat.Pptx);
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 The following sample code shows how to shrink text on overflow.
 <pre>
 Presentation pres = new Presentation();
 try {
     ISlide slide = pres.getSlides().get_Item(0);
     IAutoShape autoShape = slide.getShapes().addAutoShape(ShapeType.Rectangle, 30, 30, 350, 100);
     Portion portion = new Portion("lorem ipsum...");
     portion.getPortionFormat().getFillFormat().getSolidFillColor().setColor(Color.BLACK);
     portion.getPortionFormat().getFillFormat().setFillType(FillType.Solid);
     autoShape.getTextFrame().getParagraphs().get_Item(0).getPortions().add(portion);
     ITextFrameFormat textFrameFormat = autoShape.getTextFrame().getTextFrameFormat();
     textFrameFormat.setAutofitType(TextAutofitType.Normal);
     pres.save("Output-presentation.pptx", SaveFormat.Pptx);
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IChartTextBlockFormat.html#getAutofitType--">getAutofitType</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IChartTextBlockFormat.html" title="interface in com.aspose.slides">IChartTextBlockFormat</a></code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITextFrameFormat.html#getAutofitType--">getAutofitType</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITextFrameFormat.html" title="interface in com.aspose.slides">ITextFrameFormat</a></code></dd>
</dl>
</li>
</ul>
<a name="setAutofitType-byte-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAutofitType</h4>
<pre>public final&nbsp;void&nbsp;setAutofitType(byte&nbsp;value)</pre>
<div class="block"><p>
 Returns or sets text's autofit mode.
 Read/write <a href="../../../com/aspose/slides/TextAutofitType.html" title="class in com.aspose.slides"><code>TextAutofitType</code></a>.
 </p><p><hr><blockquote><pre>
 The following sample code shows how to resize shape to Fit Text in a PowerPoint Presentation.
 <pre>
 Presentation pres = new Presentation();
 try {
     ISlide slide = pres.getSlides().get_Item(0);
     IAutoShape autoShape = slide.getShapes().addAutoShape(ShapeType.Rectangle, 30, 30, 350, 100);
     Portion portion = new Portion("lorem ipsum...");
     portion.getPortionFormat().getFillFormat().getSolidFillColor().setColor(Color.BLACK);
     portion.getPortionFormat().getFillFormat().setFillType(FillType.Solid);
     autoShape.getTextFrame().getParagraphs().get_Item(0).getPortions().add(portion);
     ITextFrameFormat textFrameFormat = autoShape.getTextFrame().getTextFrameFormat();
     textFrameFormat.setAutofitType(TextAutofitType.Shape);
     pres.save("Output-presentation.pptx", SaveFormat.Pptx);
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 The following sample code shows how to shrink text on overflow.
 <pre>
 Presentation pres = new Presentation();
 try {
     ISlide slide = pres.getSlides().get_Item(0);
     IAutoShape autoShape = slide.getShapes().addAutoShape(ShapeType.Rectangle, 30, 30, 350, 100);
     Portion portion = new Portion("lorem ipsum...");
     portion.getPortionFormat().getFillFormat().getSolidFillColor().setColor(Color.BLACK);
     portion.getPortionFormat().getFillFormat().setFillType(FillType.Solid);
     autoShape.getTextFrame().getParagraphs().get_Item(0).getPortions().add(portion);
     ITextFrameFormat textFrameFormat = autoShape.getTextFrame().getTextFrameFormat();
     textFrameFormat.setAutofitType(TextAutofitType.Normal);
     pres.save("Output-presentation.pptx", SaveFormat.Pptx);
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IChartTextBlockFormat.html#setAutofitType-byte-">setAutofitType</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IChartTextBlockFormat.html" title="interface in com.aspose.slides">IChartTextBlockFormat</a></code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITextFrameFormat.html#setAutofitType-byte-">setAutofitType</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITextFrameFormat.html" title="interface in com.aspose.slides">ITextFrameFormat</a></code></dd>
</dl>
</li>
</ul>
<a name="getColumnCount--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getColumnCount</h4>
<pre>public final&nbsp;int&nbsp;getColumnCount()</pre>
<div class="block"><p>
 Returns or sets number of columns in the text area.
 This value must be a positive number. Otherwise, the value will be set to zero. 
 Value 0 means undefined value.
 Read/write <code>int</code>.
 </p><p><hr><blockquote><pre>
 The following sample code shows how to add column in Text frame inside a PowerPoint Presentation.
 <pre>
 Presentation pres = new Presentation();
 try {
     IAutoShape shape1 = pres.getSlides().get_Item(0).getShapes().addAutoShape(ShapeType.Rectangle, 100, 100, 300, 300);
     TextFrameFormat format = (TextFrameFormat)shape1.getTextFrame().getTextFrameFormat();
     format.setColumnCount(2);
     format.setColumnSpacing(20);
     shape1.getTextFrame().setText("All these columns are forced to stay within a single text container -- " +
     "you can add or delete text - and the new or remaining text automatically adjusts " +
     "itself to stay within the container. You cannot have text spill over from one container " +
     "to other, though -- because PowerPoint's column options for text are limited!");
     pres.save("Columns_output.pptx", SaveFormat.Pptx);
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITextFrameFormat.html#getColumnCount--">getColumnCount</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITextFrameFormat.html" title="interface in com.aspose.slides">ITextFrameFormat</a></code></dd>
</dl>
</li>
</ul>
<a name="setColumnCount-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setColumnCount</h4>
<pre>public final&nbsp;void&nbsp;setColumnCount(int&nbsp;value)</pre>
<div class="block"><p>
 Returns or sets number of columns in the text area.
 This value must be a positive number. Otherwise, the value will be set to zero. 
 Value 0 means undefined value.
 Read/write <code>int</code>.
 </p><p><hr><blockquote><pre>
 The following sample code shows how to add column in Text frame inside a PowerPoint Presentation.
 <pre>
 Presentation pres = new Presentation();
 try {
     IAutoShape shape1 = pres.getSlides().get_Item(0).getShapes().addAutoShape(ShapeType.Rectangle, 100, 100, 300, 300);
     TextFrameFormat format = (TextFrameFormat)shape1.getTextFrame().getTextFrameFormat();
     format.setColumnCount(2);
     format.setColumnSpacing(20);
     shape1.getTextFrame().setText("All these columns are forced to stay within a single text container -- " +
     "you can add or delete text - and the new or remaining text automatically adjusts " +
     "itself to stay within the container. You cannot have text spill over from one container " +
     "to other, though -- because PowerPoint's column options for text are limited!");
     pres.save("Columns_output.pptx", SaveFormat.Pptx);
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITextFrameFormat.html#setColumnCount-int-">setColumnCount</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITextFrameFormat.html" title="interface in com.aspose.slides">ITextFrameFormat</a></code></dd>
</dl>
</li>
</ul>
<a name="getColumnSpacing--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getColumnSpacing</h4>
<pre>public final&nbsp;double&nbsp;getColumnSpacing()</pre>
<div class="block"><p>
 Returns or sets the space between text columns in the text area (in points). This should only apply 
 when there is more than 1 column present.
 This value must be a positive number. Otherwise, the value will be set to zero. 
 Read/write <code>double</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITextFrameFormat.html#getColumnSpacing--">getColumnSpacing</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITextFrameFormat.html" title="interface in com.aspose.slides">ITextFrameFormat</a></code></dd>
</dl>
</li>
</ul>
<a name="setColumnSpacing-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setColumnSpacing</h4>
<pre>public final&nbsp;void&nbsp;setColumnSpacing(double&nbsp;value)</pre>
<div class="block"><p>
 Returns or sets the space between text columns in the text area (in points). This should only apply 
 when there is more than 1 column present.
 This value must be a positive number. Otherwise, the value will be set to zero. 
 Read/write <code>double</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITextFrameFormat.html#setColumnSpacing-double-">setColumnSpacing</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITextFrameFormat.html" title="interface in com.aspose.slides">ITextFrameFormat</a></code></dd>
</dl>
</li>
</ul>
<a name="getRotationAngle--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRotationAngle</h4>
<pre>public final&nbsp;float&nbsp;getRotationAngle()</pre>
<div class="block"><p>
 Specifies custom the rotation that is being applied to the text within the bounding box. If it not
 specified, the rotation of the accompanying shape is used. If it is specified, then this is
 applied independently from the shape. That is the shape can have a rotation applied in
 addition to the text itself having a rotation applied to it.
 The resulted value of visual text rotation summarized from this property and predefined
 vertical type in property TextVerticalType.
 Read/write <code>float</code>.
 </p><p><hr><blockquote><pre>
 Consider the case where a shape has a rotation of 90 degrees clockwise applied to it. 
 In addition to this, the text body itself has a rotation of -90 degrees 
 counter-clockwise applied to it. Then the resulting shape would appear to
 be rotated but the text within it would appear as though it had not been rotated at all.
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IChartTextBlockFormat.html#getRotationAngle--">getRotationAngle</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IChartTextBlockFormat.html" title="interface in com.aspose.slides">IChartTextBlockFormat</a></code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITextFrameFormat.html#getRotationAngle--">getRotationAngle</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITextFrameFormat.html" title="interface in com.aspose.slides">ITextFrameFormat</a></code></dd>
</dl>
</li>
</ul>
<a name="setRotationAngle-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRotationAngle</h4>
<pre>public final&nbsp;void&nbsp;setRotationAngle(float&nbsp;value)</pre>
<div class="block"><p>
 Specifies custom the rotation that is being applied to the text within the bounding box. If it not
 specified, the rotation of the accompanying shape is used. If it is specified, then this is
 applied independently from the shape. That is the shape can have a rotation applied in
 addition to the text itself having a rotation applied to it.
 The resulted value of visual text rotation summarized from this property and predefined
 vertical type in property TextVerticalType.
 Read/write <code>float</code>.
 </p><p><hr><blockquote><pre>
 Consider the case where a shape has a rotation of 90 degrees clockwise applied to it. 
 In addition to this, the text body itself has a rotation of -90 degrees 
 counter-clockwise applied to it. Then the resulting shape would appear to
 be rotated but the text within it would appear as though it had not been rotated at all.
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IChartTextBlockFormat.html#setRotationAngle-float-">setRotationAngle</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IChartTextBlockFormat.html" title="interface in com.aspose.slides">IChartTextBlockFormat</a></code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITextFrameFormat.html#setRotationAngle-float-">setRotationAngle</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITextFrameFormat.html" title="interface in com.aspose.slides">ITextFrameFormat</a></code></dd>
</dl>
</li>
</ul>
<a name="getTransform--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTransform</h4>
<pre>public final&nbsp;byte&nbsp;getTransform()</pre>
<div class="block"><p>
 Gets or sets text wrapping shape.
 Read/write <a href="../../../com/aspose/slides/TextShapeType.html" title="class in com.aspose.slides"><code>TextShapeType</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITextFrameFormat.html#getTransform--">getTransform</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITextFrameFormat.html" title="interface in com.aspose.slides">ITextFrameFormat</a></code></dd>
</dl>
</li>
</ul>
<a name="setTransform-byte-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTransform</h4>
<pre>public final&nbsp;void&nbsp;setTransform(byte&nbsp;value)</pre>
<div class="block"><p>
 Gets or sets text wrapping shape.
 Read/write <a href="../../../com/aspose/slides/TextShapeType.html" title="class in com.aspose.slides"><code>TextShapeType</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITextFrameFormat.html#setTransform-byte-">setTransform</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITextFrameFormat.html" title="interface in com.aspose.slides">ITextFrameFormat</a></code></dd>
</dl>
</li>
</ul>
<a name="getKeepTextFlat--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getKeepTextFlat</h4>
<pre>public final&nbsp;boolean&nbsp;getKeepTextFlat()</pre>
<div class="block"><p>
 Gets or sets keeping text flat even if a 3-D Rotation effect was applied.
 Read/write <code>boolean</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITextFrameFormat.html#getKeepTextFlat--">getKeepTextFlat</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITextFrameFormat.html" title="interface in com.aspose.slides">ITextFrameFormat</a></code></dd>
</dl>
</li>
</ul>
<a name="setKeepTextFlat-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setKeepTextFlat</h4>
<pre>public final&nbsp;void&nbsp;setKeepTextFlat(boolean&nbsp;value)</pre>
<div class="block"><p>
 Gets or sets keeping text flat even if a 3-D Rotation effect was applied.
 Read/write <code>boolean</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITextFrameFormat.html#setKeepTextFlat-boolean-">setKeepTextFlat</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITextFrameFormat.html" title="interface in com.aspose.slides">ITextFrameFormat</a></code></dd>
</dl>
</li>
</ul>
<a name="getEffective--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getEffective</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/ITextFrameFormatEffectiveData.html" title="interface in com.aspose.slides">ITextFrameFormatEffectiveData</a>&nbsp;getEffective()</pre>
<div class="block"><p>
 Gets effective text frame formatting data with the inheritance applied.
 </p><p><hr><blockquote><pre>
 This example demonstrates getting some of effective text frame formatting properties.
 <pre>
 Presentation pres = new Presentation("MyPresentation.pptx");
 try
 {
     IAutoShape shape = (IAutoShape)pres.getSlides().get_Item(0).getShapes().get_Item(0);
     ITextFrameFormatEffectiveData effectiveTextFrameFormat = shape.getTextFrame().getTextFrameFormat().getEffective();
    
     System.out.println("Anchoring type: " + effectiveTextFrameFormat.getAnchoringType());
     System.out.println("Autofit type: " + effectiveTextFrameFormat.getAutofitType());
     System.out.println("Text vertical type: " + effectiveTextFrameFormat.getTextVerticalType());
     System.out.println("Margins");
     System.out.println("   Left: " + effectiveTextFrameFormat.getMarginLeft());
     System.out.println("   Top: " + effectiveTextFrameFormat.getMarginTop());
     System.out.println("   Right: " + effectiveTextFrameFormat.getMarginRight());
     System.out.println("   Bottom: " + effectiveTextFrameFormat.getMarginBottom());
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITextFrameFormat.html#getEffective--">getEffective</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITextFrameFormat.html" title="interface in com.aspose.slides">ITextFrameFormat</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>A <a href="../../../com/aspose/slides/ITextFrameFormatEffectiveData.html" title="interface in com.aspose.slides"><code>ITextFrameFormatEffectiveData</code></a>.</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/TextFrame.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/TextHighlightingOptions.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/TextFrameFormat.html" target="_top">Frames</a></li>
<li><a href="TextFrameFormat.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><i>Copyright &copy; 2004-2025 Aspose Pty Ltd. All Rights Reserved.</i></small></p>
</body>
</html>
