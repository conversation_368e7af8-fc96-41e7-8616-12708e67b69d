<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_421) on Fri Jul 11 05:32:44 CDT 2025 -->
<title>SlideLayoutType (Aspose.Slides for Java (25.7) API Documentation)</title>
<meta name="date" content="2025-07-11">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="SlideLayoutType (Aspose.Slides for Java (25.7) API Documentation)";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/SlideImageFormat.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/SlideOrientation.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/SlideLayoutType.html" target="_top">Frames</a></li>
<li><a href="SlideLayoutType.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.classes.inherited.from.class.com.aspose.ms.System.Enum">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#methods.inherited.from.class.com.aspose.ms.System.Enum">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li>Method</li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.aspose.slides</div>
<h2 title="Class SlideLayoutType" class="title">Class SlideLayoutType</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.aspose.ms.System.ValueType&lt;com.aspose.ms.System.Enum&gt;</li>
<li>
<ul class="inheritance">
<li>com.aspose.ms.System.Enum</li>
<li>
<ul class="inheritance">
<li>com.aspose.slides.SlideLayoutType</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public final class <span class="typeNameLabel">SlideLayoutType</span>
extends com.aspose.ms.System.Enum</pre>
<div class="block"><p>
 Represents the slide layout type.
 </p></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.com.aspose.ms.System.Enum">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from class&nbsp;com.aspose.ms.System.Enum</h3>
<code>com.aspose.ms.System.Enum.AbstractEnum, com.aspose.ms.System.Enum.FlaggedEnum, com.aspose.ms.System.Enum.ObjectEnum, com.aspose.ms.System.Enum.SimpleEnum</code></li>
</ul>
</li>
</ul>
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static byte</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideLayoutType.html#Blank">Blank</a></span></code>
<div class="block">
  Blank</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static byte</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideLayoutType.html#Chart">Chart</a></span></code>
<div class="block">
  Chart</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static byte</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideLayoutType.html#ChartAndText">ChartAndText</a></span></code>
<div class="block">
  Chart and Text</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static byte</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideLayoutType.html#ClipArtAndText">ClipArtAndText</a></span></code>
<div class="block">
  Clip Art and Text</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static byte</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideLayoutType.html#ClipArtAndVerticalText">ClipArtAndVerticalText</a></span></code>
<div class="block">
  Clip Art and Vertical Text</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static byte</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideLayoutType.html#Custom">Custom</a></span></code>
<div class="block">
  Custom</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static byte</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideLayoutType.html#Diagram">Diagram</a></span></code>
<div class="block">
  Diagram</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static byte</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideLayoutType.html#FourObjects">FourObjects</a></span></code>
<div class="block">
  Four Objects</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static byte</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideLayoutType.html#MediaAndText">MediaAndText</a></span></code>
<div class="block">
  Media and Text</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static byte</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideLayoutType.html#Object">Object</a></span></code>
<div class="block">
  Object</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static byte</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideLayoutType.html#ObjectAndText">ObjectAndText</a></span></code>
<div class="block">
  Object and Text</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static byte</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideLayoutType.html#ObjectAndTwoObject">ObjectAndTwoObject</a></span></code>
<div class="block">
  Object and Two Object</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static byte</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideLayoutType.html#ObjectOverText">ObjectOverText</a></span></code>
<div class="block">
  Object over Text</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static byte</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideLayoutType.html#PictureAndCaption">PictureAndCaption</a></span></code>
<div class="block">
  Picture and Caption</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static byte</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideLayoutType.html#SectionHeader">SectionHeader</a></span></code>
<div class="block">
  Section Header</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static byte</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideLayoutType.html#Table">Table</a></span></code>
<div class="block">
  Table</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static byte</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideLayoutType.html#Text">Text</a></span></code>
<div class="block">
  Text</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static byte</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideLayoutType.html#TextAndChart">TextAndChart</a></span></code>
<div class="block">
  Text and Chart</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static byte</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideLayoutType.html#TextAndClipArt">TextAndClipArt</a></span></code>
<div class="block">
  Text and Clip Art</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static byte</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideLayoutType.html#TextAndMedia">TextAndMedia</a></span></code>
<div class="block">
  Text and Media</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static byte</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideLayoutType.html#TextAndObject">TextAndObject</a></span></code>
<div class="block">
  Text and Object</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static byte</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideLayoutType.html#TextAndTwoObjects">TextAndTwoObjects</a></span></code>
<div class="block">
  Text and Two Objects</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static byte</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideLayoutType.html#TextOverObject">TextOverObject</a></span></code>
<div class="block">
  Text over Object</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static byte</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideLayoutType.html#Title">Title</a></span></code>
<div class="block">
  Title</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static byte</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideLayoutType.html#TitleAndObject">TitleAndObject</a></span></code>
<div class="block">
  Title and Object</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static byte</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideLayoutType.html#TitleObjectAndCaption">TitleObjectAndCaption</a></span></code>
<div class="block">
  Title, Object, and Caption</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static byte</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideLayoutType.html#TitleOnly">TitleOnly</a></span></code>
<div class="block">
  Title Only</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static byte</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideLayoutType.html#TwoColumnText">TwoColumnText</a></span></code>
<div class="block">
  Two Column Text</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static byte</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideLayoutType.html#TwoObjects">TwoObjects</a></span></code>
<div class="block">
  Two Objects</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static byte</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideLayoutType.html#TwoObjectsAndObject">TwoObjectsAndObject</a></span></code>
<div class="block">
  Two Objects and Object</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static byte</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideLayoutType.html#TwoObjectsAndText">TwoObjectsAndText</a></span></code>
<div class="block">
  Two Objects and Text</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static byte</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideLayoutType.html#TwoObjectsOverText">TwoObjectsOverText</a></span></code>
<div class="block">
  Two Objects over Text</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static byte</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideLayoutType.html#TwoTextAndTwoObjects">TwoTextAndTwoObjects</a></span></code>
<div class="block">
  Two Text and Two Objects</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static byte</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideLayoutType.html#VerticalText">VerticalText</a></span></code>
<div class="block">
  Vertical Text</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static byte</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideLayoutType.html#VerticalTitleAndText">VerticalTitleAndText</a></span></code>
<div class="block">
  Vertical Title and Text</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static byte</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideLayoutType.html#VerticalTitleAndTextOverChart">VerticalTitleAndTextOverChart</a></span></code>
<div class="block">
  Vertical Title and Text Over Chart</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.com.aspose.ms.System.Enum">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;com.aspose.ms.System.Enum</h3>
<code>EnumSeparatorCharArray</code></li>
</ul>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.aspose.ms.System.Enum">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;com.aspose.ms.System.Enum</h3>
<code>Clone, CloneTo, format, format, get_Caption, get_Value, getName, getName, getNames, getNames, getUnderlyingType, getUnderlyingType, getValue, getValues, isDefined, isDefined, isDefined, isDefined, parse, parse, parse, parse, register, toObject, toString</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="Custom">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Custom</h4>
<pre>public static final&nbsp;byte Custom</pre>
<div class="block"><p>
  Custom 
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SlideLayoutType.Custom">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Title">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Title</h4>
<pre>public static final&nbsp;byte Title</pre>
<div class="block"><p>
  Title 
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SlideLayoutType.Title">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Text">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Text</h4>
<pre>public static final&nbsp;byte Text</pre>
<div class="block"><p>
  Text 
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SlideLayoutType.Text">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="TwoColumnText">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TwoColumnText</h4>
<pre>public static final&nbsp;byte TwoColumnText</pre>
<div class="block"><p>
  Two Column Text 
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SlideLayoutType.TwoColumnText">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Table">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Table</h4>
<pre>public static final&nbsp;byte Table</pre>
<div class="block"><p>
  Table 
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SlideLayoutType.Table">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="TextAndChart">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TextAndChart</h4>
<pre>public static final&nbsp;byte TextAndChart</pre>
<div class="block"><p>
  Text and Chart 
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SlideLayoutType.TextAndChart">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="ChartAndText">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ChartAndText</h4>
<pre>public static final&nbsp;byte ChartAndText</pre>
<div class="block"><p>
  Chart and Text 
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SlideLayoutType.ChartAndText">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Diagram">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Diagram</h4>
<pre>public static final&nbsp;byte Diagram</pre>
<div class="block"><p>
  Diagram 
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SlideLayoutType.Diagram">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Chart">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Chart</h4>
<pre>public static final&nbsp;byte Chart</pre>
<div class="block"><p>
  Chart
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SlideLayoutType.Chart">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="TextAndClipArt">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TextAndClipArt</h4>
<pre>public static final&nbsp;byte TextAndClipArt</pre>
<div class="block"><p>
  Text and Clip Art
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SlideLayoutType.TextAndClipArt">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="ClipArtAndText">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ClipArtAndText</h4>
<pre>public static final&nbsp;byte ClipArtAndText</pre>
<div class="block"><p>
  Clip Art and Text
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SlideLayoutType.ClipArtAndText">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="TitleOnly">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TitleOnly</h4>
<pre>public static final&nbsp;byte TitleOnly</pre>
<div class="block"><p>
  Title Only 
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SlideLayoutType.TitleOnly">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Blank">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Blank</h4>
<pre>public static final&nbsp;byte Blank</pre>
<div class="block"><p>
  Blank 
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SlideLayoutType.Blank">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="TextAndObject">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TextAndObject</h4>
<pre>public static final&nbsp;byte TextAndObject</pre>
<div class="block"><p>
  Text and Object 
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SlideLayoutType.TextAndObject">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="ObjectAndText">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ObjectAndText</h4>
<pre>public static final&nbsp;byte ObjectAndText</pre>
<div class="block"><p>
  Object and Text 
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SlideLayoutType.ObjectAndText">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Object">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Object</h4>
<pre>public static final&nbsp;byte Object</pre>
<div class="block"><p>
  Object
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SlideLayoutType.Object">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="TitleAndObject">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TitleAndObject</h4>
<pre>public static final&nbsp;byte TitleAndObject</pre>
<div class="block"><p>
  Title and Object
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SlideLayoutType.TitleAndObject">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="TextAndMedia">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TextAndMedia</h4>
<pre>public static final&nbsp;byte TextAndMedia</pre>
<div class="block"><p>
  Text and Media 
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SlideLayoutType.TextAndMedia">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MediaAndText">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MediaAndText</h4>
<pre>public static final&nbsp;byte MediaAndText</pre>
<div class="block"><p>
  Media and Text 
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SlideLayoutType.MediaAndText">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="ObjectOverText">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ObjectOverText</h4>
<pre>public static final&nbsp;byte ObjectOverText</pre>
<div class="block"><p>
  Object over Text
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SlideLayoutType.ObjectOverText">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="TextOverObject">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TextOverObject</h4>
<pre>public static final&nbsp;byte TextOverObject</pre>
<div class="block"><p>
  Text over Object
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SlideLayoutType.TextOverObject">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="TextAndTwoObjects">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TextAndTwoObjects</h4>
<pre>public static final&nbsp;byte TextAndTwoObjects</pre>
<div class="block"><p>
  Text and Two Objects
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SlideLayoutType.TextAndTwoObjects">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="TwoObjectsAndText">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TwoObjectsAndText</h4>
<pre>public static final&nbsp;byte TwoObjectsAndText</pre>
<div class="block"><p>
  Two Objects and Text
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SlideLayoutType.TwoObjectsAndText">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="TwoObjectsOverText">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TwoObjectsOverText</h4>
<pre>public static final&nbsp;byte TwoObjectsOverText</pre>
<div class="block"><p>
  Two Objects over Text
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SlideLayoutType.TwoObjectsOverText">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="FourObjects">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FourObjects</h4>
<pre>public static final&nbsp;byte FourObjects</pre>
<div class="block"><p>
  Four Objects
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SlideLayoutType.FourObjects">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="VerticalText">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>VerticalText</h4>
<pre>public static final&nbsp;byte VerticalText</pre>
<div class="block"><p>
  Vertical Text
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SlideLayoutType.VerticalText">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="ClipArtAndVerticalText">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ClipArtAndVerticalText</h4>
<pre>public static final&nbsp;byte ClipArtAndVerticalText</pre>
<div class="block"><p>
  Clip Art and Vertical Text
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SlideLayoutType.ClipArtAndVerticalText">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="VerticalTitleAndText">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>VerticalTitleAndText</h4>
<pre>public static final&nbsp;byte VerticalTitleAndText</pre>
<div class="block"><p>
  Vertical Title and Text
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SlideLayoutType.VerticalTitleAndText">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="VerticalTitleAndTextOverChart">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>VerticalTitleAndTextOverChart</h4>
<pre>public static final&nbsp;byte VerticalTitleAndTextOverChart</pre>
<div class="block"><p>
  Vertical Title and Text Over Chart
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SlideLayoutType.VerticalTitleAndTextOverChart">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="TwoObjects">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TwoObjects</h4>
<pre>public static final&nbsp;byte TwoObjects</pre>
<div class="block"><p>
  Two Objects
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SlideLayoutType.TwoObjects">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="ObjectAndTwoObject">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ObjectAndTwoObject</h4>
<pre>public static final&nbsp;byte ObjectAndTwoObject</pre>
<div class="block"><p>
  Object and Two Object
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SlideLayoutType.ObjectAndTwoObject">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="TwoObjectsAndObject">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TwoObjectsAndObject</h4>
<pre>public static final&nbsp;byte TwoObjectsAndObject</pre>
<div class="block"><p>
  Two Objects and Object
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SlideLayoutType.TwoObjectsAndObject">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="SectionHeader">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SectionHeader</h4>
<pre>public static final&nbsp;byte SectionHeader</pre>
<div class="block"><p>
  Section Header
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SlideLayoutType.SectionHeader">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="TwoTextAndTwoObjects">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TwoTextAndTwoObjects</h4>
<pre>public static final&nbsp;byte TwoTextAndTwoObjects</pre>
<div class="block"><p>
  Two Text and Two Objects
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SlideLayoutType.TwoTextAndTwoObjects">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="TitleObjectAndCaption">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TitleObjectAndCaption</h4>
<pre>public static final&nbsp;byte TitleObjectAndCaption</pre>
<div class="block"><p>
  Title, Object, and Caption
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SlideLayoutType.TitleObjectAndCaption">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="PictureAndCaption">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>PictureAndCaption</h4>
<pre>public static final&nbsp;byte PictureAndCaption</pre>
<div class="block"><p>
  Picture and Caption
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SlideLayoutType.PictureAndCaption">Constant Field Values</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/SlideImageFormat.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/SlideOrientation.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/SlideLayoutType.html" target="_top">Frames</a></li>
<li><a href="SlideLayoutType.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.classes.inherited.from.class.com.aspose.ms.System.Enum">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#methods.inherited.from.class.com.aspose.ms.System.Enum">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li>Method</li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><i>Copyright &copy; 2004-2025 Aspose Pty Ltd. All Rights Reserved.</i></small></p>
</body>
</html>
