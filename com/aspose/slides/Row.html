<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_421) on Fri Jul 11 05:32:44 CDT 2025 -->
<title>Row (Aspose.Slides for Java (25.7) API Documentation)</title>
<meta name="date" content="2025-07-11">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Row (Aspose.Slides for Java (25.7) API Documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/RotationEffect.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/RowCollection.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/Row.html" target="_top">Frames</a></li>
<li><a href="Row.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.aspose.slides</div>
<h2 title="Class Row" class="title">Class Row</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../com/aspose/slides/CellCollection.html" title="class in com.aspose.slides">com.aspose.slides.CellCollection</a></li>
<li>
<ul class="inheritance">
<li>com.aspose.slides.Row</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd>com.aspose.ms.System.Collections.Generic.IGenericEnumerable&lt;<a href="../../../com/aspose/slides/ICell.html" title="interface in com.aspose.slides">ICell</a>&gt;, com.aspose.ms.System.Collections.ICollection&lt;<a href="../../../com/aspose/slides/ICell.html" title="interface in com.aspose.slides">ICell</a>&gt;, com.aspose.ms.System.Collections.IEnumerable&lt;<a href="../../../com/aspose/slides/ICell.html" title="interface in com.aspose.slides">ICell</a>&gt;, <a href="../../../com/aspose/slides/IBulkTextFormattable.html" title="interface in com.aspose.slides">IBulkTextFormattable</a>, <a href="../../../com/aspose/slides/ICellCollection.html" title="interface in com.aspose.slides">ICellCollection</a>, <a href="../../../com/aspose/slides/IGenericCollection.html" title="interface in com.aspose.slides">IGenericCollection</a>&lt;<a href="../../../com/aspose/slides/ICell.html" title="interface in com.aspose.slides">ICell</a>&gt;, <a href="../../../com/aspose/slides/IPresentationComponent.html" title="interface in com.aspose.slides">IPresentationComponent</a>, <a href="../../../com/aspose/slides/IRow.html" title="interface in com.aspose.slides">IRow</a>, <a href="../../../com/aspose/slides/ISlideComponent.html" title="interface in com.aspose.slides">ISlideComponent</a>, java.lang.Iterable&lt;<a href="../../../com/aspose/slides/ICell.html" title="interface in com.aspose.slides">ICell</a>&gt;</dd>
</dl>
<hr>
<br>
<pre>public final class <span class="typeNameLabel">Row</span>
extends <a href="../../../com/aspose/slides/CellCollection.html" title="class in com.aspose.slides">CellCollection</a>
implements <a href="../../../com/aspose/slides/IRow.html" title="interface in com.aspose.slides">IRow</a></pre>
<div class="block"><p>
 Represents a row in a table.
 </p></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Row.html#getHeight--">getHeight</a></span>()</code>
<div class="block">
 Returns the height of a row.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Row.html#getMinimalHeight--">getMinimalHeight</a></span>()</code>
<div class="block">
 Returns or sets the minimal possible height of a row.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IRowFormat.html" title="interface in com.aspose.slides">IRowFormat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Row.html#getRowFormat--">getRowFormat</a></span>()</code>
<div class="block">
 Returns the RowFormat object that contains formatting properties for this row.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Row.html#setMinimalHeight-double-">setMinimalHeight</a></span>(double&nbsp;value)</code>
<div class="block">
 Returns or sets the minimal possible height of a row.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Row.html#setTextFormat-com.aspose.slides.IParagraphFormat-">setTextFormat</a></span>(<a href="../../../com/aspose/slides/IParagraphFormat.html" title="interface in com.aspose.slides">IParagraphFormat</a>&nbsp;source)</code>
<div class="block">
 Sets defined paragraph format properties to all row cells' paragraphs.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Row.html#setTextFormat-com.aspose.slides.IPortionFormat-">setTextFormat</a></span>(<a href="../../../com/aspose/slides/IPortionFormat.html" title="interface in com.aspose.slides">IPortionFormat</a>&nbsp;source)</code>
<div class="block">
 Sets defined portion format properties to all row cells' portions.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Row.html#setTextFormat-com.aspose.slides.ITextFrameFormat-">setTextFormat</a></span>(<a href="../../../com/aspose/slides/ITextFrameFormat.html" title="interface in com.aspose.slides">ITextFrameFormat</a>&nbsp;source)</code>
<div class="block">
 Sets defined text frame format properties to all row cells' text frames.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.aspose.slides.CellCollection">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;com.aspose.slides.<a href="../../../com/aspose/slides/CellCollection.html" title="class in com.aspose.slides">CellCollection</a></h3>
<code><a href="../../../com/aspose/slides/CellCollection.html#copyTo-com.aspose.ms.System.Array-int-">copyTo</a>, <a href="../../../com/aspose/slides/CellCollection.html#get_Item-int-">get_Item</a>, <a href="../../../com/aspose/slides/CellCollection.html#getParent_Immediate--">getParent_Immediate</a>, <a href="../../../com/aspose/slides/CellCollection.html#getPresentation--">getPresentation</a>, <a href="../../../com/aspose/slides/CellCollection.html#getSlide--">getSlide</a>, <a href="../../../com/aspose/slides/CellCollection.html#getSyncRoot--">getSyncRoot</a>, <a href="../../../com/aspose/slides/CellCollection.html#isSynchronized--">isSynchronized</a>, <a href="../../../com/aspose/slides/CellCollection.html#iterator--">iterator</a>, <a href="../../../com/aspose/slides/CellCollection.html#iteratorJava--">iteratorJava</a>, <a href="../../../com/aspose/slides/CellCollection.html#size--">size</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.aspose.slides.ICellCollection">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;com.aspose.slides.<a href="../../../com/aspose/slides/ICellCollection.html" title="interface in com.aspose.slides">ICellCollection</a></h3>
<code><a href="../../../com/aspose/slides/ICellCollection.html#get_Item-int-">get_Item</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.aspose.slides.ISlideComponent">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;com.aspose.slides.<a href="../../../com/aspose/slides/ISlideComponent.html" title="interface in com.aspose.slides">ISlideComponent</a></h3>
<code><a href="../../../com/aspose/slides/ISlideComponent.html#getSlide--">getSlide</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.aspose.slides.IPresentationComponent">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;com.aspose.slides.<a href="../../../com/aspose/slides/IPresentationComponent.html" title="interface in com.aspose.slides">IPresentationComponent</a></h3>
<code><a href="../../../com/aspose/slides/IPresentationComponent.html#getPresentation--">getPresentation</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.aspose.slides.IGenericCollection">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;com.aspose.slides.<a href="../../../com/aspose/slides/IGenericCollection.html" title="interface in com.aspose.slides">IGenericCollection</a></h3>
<code><a href="../../../com/aspose/slides/IGenericCollection.html#iteratorJava--">iteratorJava</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.aspose.ms.System.Collections.Generic.IGenericEnumerable">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;com.aspose.ms.System.Collections.Generic.IGenericEnumerable</h3>
<code>iterator</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.aspose.ms.System.Collections.ICollection">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;com.aspose.ms.System.Collections.ICollection</h3>
<code>copyTo, getSyncRoot, isSynchronized, size</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Iterable">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;java.lang.Iterable</h3>
<code>forEach, spliterator</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getHeight--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHeight</h4>
<pre>public final&nbsp;double&nbsp;getHeight()</pre>
<div class="block"><p>
 Returns the height of a row.
 Read-only <code>double</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IRow.html#getHeight--">getHeight</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IRow.html" title="interface in com.aspose.slides">IRow</a></code></dd>
</dl>
</li>
</ul>
<a name="getMinimalHeight--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMinimalHeight</h4>
<pre>public final&nbsp;double&nbsp;getMinimalHeight()</pre>
<div class="block"><p>
 Returns or sets the minimal possible height of a row.
 Read/write <code>double</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IRow.html#getMinimalHeight--">getMinimalHeight</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IRow.html" title="interface in com.aspose.slides">IRow</a></code></dd>
</dl>
</li>
</ul>
<a name="setMinimalHeight-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMinimalHeight</h4>
<pre>public final&nbsp;void&nbsp;setMinimalHeight(double&nbsp;value)</pre>
<div class="block"><p>
 Returns or sets the minimal possible height of a row.
 Read/write <code>double</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IRow.html#setMinimalHeight-double-">setMinimalHeight</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IRow.html" title="interface in com.aspose.slides">IRow</a></code></dd>
</dl>
</li>
</ul>
<a name="setTextFormat-com.aspose.slides.IPortionFormat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTextFormat</h4>
<pre>public final&nbsp;void&nbsp;setTextFormat(<a href="../../../com/aspose/slides/IPortionFormat.html" title="interface in com.aspose.slides">IPortionFormat</a>&nbsp;source)</pre>
<div class="block"><p>
 Sets defined portion format properties to all row cells' portions.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IBulkTextFormattable.html#setTextFormat-com.aspose.slides.IPortionFormat-">setTextFormat</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IBulkTextFormattable.html" title="interface in com.aspose.slides">IBulkTextFormattable</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>source</code> - IPortionFormat object with necessary properties set.</dd>
</dl>
</li>
</ul>
<a name="setTextFormat-com.aspose.slides.IParagraphFormat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTextFormat</h4>
<pre>public final&nbsp;void&nbsp;setTextFormat(<a href="../../../com/aspose/slides/IParagraphFormat.html" title="interface in com.aspose.slides">IParagraphFormat</a>&nbsp;source)</pre>
<div class="block"><p>
 Sets defined paragraph format properties to all row cells' paragraphs.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IBulkTextFormattable.html#setTextFormat-com.aspose.slides.IParagraphFormat-">setTextFormat</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IBulkTextFormattable.html" title="interface in com.aspose.slides">IBulkTextFormattable</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>source</code> - IParagraphFormat object with necessary properties set.</dd>
</dl>
</li>
</ul>
<a name="setTextFormat-com.aspose.slides.ITextFrameFormat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTextFormat</h4>
<pre>public final&nbsp;void&nbsp;setTextFormat(<a href="../../../com/aspose/slides/ITextFrameFormat.html" title="interface in com.aspose.slides">ITextFrameFormat</a>&nbsp;source)</pre>
<div class="block"><p>
 Sets defined text frame format properties to all row cells' text frames.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IBulkTextFormattable.html#setTextFormat-com.aspose.slides.ITextFrameFormat-">setTextFormat</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IBulkTextFormattable.html" title="interface in com.aspose.slides">IBulkTextFormattable</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>source</code> - ITextFrameFormat object with necessary properties set.</dd>
</dl>
</li>
</ul>
<a name="getRowFormat--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getRowFormat</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IRowFormat.html" title="interface in com.aspose.slides">IRowFormat</a>&nbsp;getRowFormat()</pre>
<div class="block"><p>
 Returns the RowFormat object that contains formatting properties for this row.
 Read-only <a href="../../../com/aspose/slides/IRowFormat.html" title="interface in com.aspose.slides"><code>IRowFormat</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IRow.html#getRowFormat--">getRowFormat</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IRow.html" title="interface in com.aspose.slides">IRow</a></code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/RotationEffect.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/RowCollection.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/Row.html" target="_top">Frames</a></li>
<li><a href="Row.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><i>Copyright &copy; 2004-2025 Aspose Pty Ltd. All Rights Reserved.</i></small></p>
</body>
</html>
