<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_421) on Fri Jul 11 05:32:44 CDT 2025 -->
<title>PropertyEffect (Aspose.Slides for Java (25.7) API Documentation)</title>
<meta name="date" content="2025-07-11">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="PropertyEffect (Aspose.Slides for Java (25.7) API Documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/PropertyCalcModeType.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/PropertyValueType.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/PropertyEffect.html" target="_top">Frames</a></li>
<li><a href="PropertyEffect.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.aspose.slides</div>
<h2 title="Class PropertyEffect" class="title">Class PropertyEffect</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../com/aspose/slides/Behavior.html" title="class in com.aspose.slides">com.aspose.slides.Behavior</a></li>
<li>
<ul class="inheritance">
<li>com.aspose.slides.PropertyEffect</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../com/aspose/slides/IBehavior.html" title="interface in com.aspose.slides">IBehavior</a>, <a href="../../../com/aspose/slides/IPropertyEffect.html" title="interface in com.aspose.slides">IPropertyEffect</a></dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">PropertyEffect</span>
extends <a href="../../../com/aspose/slides/Behavior.html" title="class in com.aspose.slides">Behavior</a>
implements <a href="../../../com/aspose/slides/IPropertyEffect.html" title="interface in com.aspose.slides">IPropertyEffect</a></pre>
<div class="block"><p>
 Represent property effect behavior.
 </p></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PropertyEffect.html#PropertyEffect--">PropertyEffect</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PropertyEffect.html#getBy--">getBy</a></span>()</code>
<div class="block">
 Specifies a relative offset value for the animation with respect to its
 position before the start of the animation.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PropertyEffect.html#getCalcMode--">getCalcMode</a></span>()</code>
<div class="block">
 Specifies the interpolation mode for the animation
 Read/write <a href="../../../com/aspose/slides/PropertyCalcModeType.html" title="class in com.aspose.slides"><code>PropertyCalcModeType</code></a>.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PropertyEffect.html#getFrom--">getFrom</a></span>()</code>
<div class="block">
 Specifies the starting value of the animation.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IPointCollection.html" title="interface in com.aspose.slides">IPointCollection</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PropertyEffect.html#getPoints--">getPoints</a></span>()</code>
<div class="block">
 Specifies the points of the animation.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PropertyEffect.html#getTo--">getTo</a></span>()</code>
<div class="block">
 Specifies the ending value for the animation.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PropertyEffect.html#getValueType--">getValueType</a></span>()</code>
<div class="block">
 Specifies the type of a property value.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PropertyEffect.html#setBy-java.lang.String-">setBy</a></span>(java.lang.String&nbsp;value)</code>
<div class="block">
 Specifies a relative offset value for the animation with respect to its
 position before the start of the animation.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PropertyEffect.html#setCalcMode-int-">setCalcMode</a></span>(int&nbsp;value)</code>
<div class="block">
 Specifies the interpolation mode for the animation
 Read/write <a href="../../../com/aspose/slides/PropertyCalcModeType.html" title="class in com.aspose.slides"><code>PropertyCalcModeType</code></a>.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PropertyEffect.html#setFrom-java.lang.String-">setFrom</a></span>(java.lang.String&nbsp;value)</code>
<div class="block">
 Specifies the starting value of the animation.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PropertyEffect.html#setPoints-com.aspose.slides.IPointCollection-">setPoints</a></span>(<a href="../../../com/aspose/slides/IPointCollection.html" title="interface in com.aspose.slides">IPointCollection</a>&nbsp;value)</code>
<div class="block">
 Specifies the points of the animation.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PropertyEffect.html#setTo-java.lang.String-">setTo</a></span>(java.lang.String&nbsp;value)</code>
<div class="block">
 Specifies the ending value for the animation.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PropertyEffect.html#setValueType-int-">setValueType</a></span>(int&nbsp;value)</code>
<div class="block">
 Specifies the type of a property value.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.aspose.slides.Behavior">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;com.aspose.slides.<a href="../../../com/aspose/slides/Behavior.html" title="class in com.aspose.slides">Behavior</a></h3>
<code><a href="../../../com/aspose/slides/Behavior.html#getAccumulate--">getAccumulate</a>, <a href="../../../com/aspose/slides/Behavior.html#getAdditive--">getAdditive</a>, <a href="../../../com/aspose/slides/Behavior.html#getParent_Immediate--">getParent_Immediate</a>, <a href="../../../com/aspose/slides/Behavior.html#getProperties--">getProperties</a>, <a href="../../../com/aspose/slides/Behavior.html#getTiming--">getTiming</a>, <a href="../../../com/aspose/slides/Behavior.html#setAccumulate-byte-">setAccumulate</a>, <a href="../../../com/aspose/slides/Behavior.html#setAdditive-int-">setAdditive</a>, <a href="../../../com/aspose/slides/Behavior.html#setTiming-com.aspose.slides.ITiming-">setTiming</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.aspose.slides.IBehavior">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;com.aspose.slides.<a href="../../../com/aspose/slides/IBehavior.html" title="interface in com.aspose.slides">IBehavior</a></h3>
<code><a href="../../../com/aspose/slides/IBehavior.html#getAccumulate--">getAccumulate</a>, <a href="../../../com/aspose/slides/IBehavior.html#getAdditive--">getAdditive</a>, <a href="../../../com/aspose/slides/IBehavior.html#getProperties--">getProperties</a>, <a href="../../../com/aspose/slides/IBehavior.html#getTiming--">getTiming</a>, <a href="../../../com/aspose/slides/IBehavior.html#setAccumulate-byte-">setAccumulate</a>, <a href="../../../com/aspose/slides/IBehavior.html#setAdditive-int-">setAdditive</a>, <a href="../../../com/aspose/slides/IBehavior.html#setTiming-com.aspose.slides.ITiming-">setTiming</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="PropertyEffect--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>PropertyEffect</h4>
<pre>public&nbsp;PropertyEffect()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getFrom--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFrom</h4>
<pre>public final&nbsp;java.lang.String&nbsp;getFrom()</pre>
<div class="block"><p>
 Specifies the starting value of the animation.
 Read/write <code>String</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPropertyEffect.html#getFrom--">getFrom</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPropertyEffect.html" title="interface in com.aspose.slides">IPropertyEffect</a></code></dd>
</dl>
</li>
</ul>
<a name="setFrom-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFrom</h4>
<pre>public final&nbsp;void&nbsp;setFrom(java.lang.String&nbsp;value)</pre>
<div class="block"><p>
 Specifies the starting value of the animation.
 Read/write <code>String</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPropertyEffect.html#setFrom-java.lang.String-">setFrom</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPropertyEffect.html" title="interface in com.aspose.slides">IPropertyEffect</a></code></dd>
</dl>
</li>
</ul>
<a name="getTo--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTo</h4>
<pre>public final&nbsp;java.lang.String&nbsp;getTo()</pre>
<div class="block"><p>
 Specifies the ending value for the animation.
 Read/write <code>String</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPropertyEffect.html#getTo--">getTo</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPropertyEffect.html" title="interface in com.aspose.slides">IPropertyEffect</a></code></dd>
</dl>
</li>
</ul>
<a name="setTo-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTo</h4>
<pre>public final&nbsp;void&nbsp;setTo(java.lang.String&nbsp;value)</pre>
<div class="block"><p>
 Specifies the ending value for the animation.
 Read/write <code>String</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPropertyEffect.html#setTo-java.lang.String-">setTo</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPropertyEffect.html" title="interface in com.aspose.slides">IPropertyEffect</a></code></dd>
</dl>
</li>
</ul>
<a name="getBy--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBy</h4>
<pre>public final&nbsp;java.lang.String&nbsp;getBy()</pre>
<div class="block"><p>
 Specifies a relative offset value for the animation with respect to its
 position before the start of the animation.
 Read/write <code>String</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPropertyEffect.html#getBy--">getBy</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPropertyEffect.html" title="interface in com.aspose.slides">IPropertyEffect</a></code></dd>
</dl>
</li>
</ul>
<a name="setBy-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBy</h4>
<pre>public final&nbsp;void&nbsp;setBy(java.lang.String&nbsp;value)</pre>
<div class="block"><p>
 Specifies a relative offset value for the animation with respect to its
 position before the start of the animation.
 Read/write <code>String</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPropertyEffect.html#setBy-java.lang.String-">setBy</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPropertyEffect.html" title="interface in com.aspose.slides">IPropertyEffect</a></code></dd>
</dl>
</li>
</ul>
<a name="getValueType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getValueType</h4>
<pre>public final&nbsp;int&nbsp;getValueType()</pre>
<div class="block"><p>
 Specifies the type of a property value.
 Read/write <a href="../../../com/aspose/slides/PropertyValueType.html" title="class in com.aspose.slides"><code>PropertyValueType</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPropertyEffect.html#getValueType--">getValueType</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPropertyEffect.html" title="interface in com.aspose.slides">IPropertyEffect</a></code></dd>
</dl>
</li>
</ul>
<a name="setValueType-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setValueType</h4>
<pre>public final&nbsp;void&nbsp;setValueType(int&nbsp;value)</pre>
<div class="block"><p>
 Specifies the type of a property value.
 Read/write <a href="../../../com/aspose/slides/PropertyValueType.html" title="class in com.aspose.slides"><code>PropertyValueType</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPropertyEffect.html#setValueType-int-">setValueType</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPropertyEffect.html" title="interface in com.aspose.slides">IPropertyEffect</a></code></dd>
</dl>
</li>
</ul>
<a name="getCalcMode--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCalcMode</h4>
<pre>public final&nbsp;int&nbsp;getCalcMode()</pre>
<div class="block"><p>
 Specifies the interpolation mode for the animation
 Read/write <a href="../../../com/aspose/slides/PropertyCalcModeType.html" title="class in com.aspose.slides"><code>PropertyCalcModeType</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPropertyEffect.html#getCalcMode--">getCalcMode</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPropertyEffect.html" title="interface in com.aspose.slides">IPropertyEffect</a></code></dd>
</dl>
</li>
</ul>
<a name="setCalcMode-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCalcMode</h4>
<pre>public final&nbsp;void&nbsp;setCalcMode(int&nbsp;value)</pre>
<div class="block"><p>
 Specifies the interpolation mode for the animation
 Read/write <a href="../../../com/aspose/slides/PropertyCalcModeType.html" title="class in com.aspose.slides"><code>PropertyCalcModeType</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPropertyEffect.html#setCalcMode-int-">setCalcMode</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPropertyEffect.html" title="interface in com.aspose.slides">IPropertyEffect</a></code></dd>
</dl>
</li>
</ul>
<a name="getPoints--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPoints</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IPointCollection.html" title="interface in com.aspose.slides">IPointCollection</a>&nbsp;getPoints()</pre>
<div class="block"><p>
 Specifies the points of the animation.
 Read/write <a href="../../../com/aspose/slides/IPointCollection.html" title="interface in com.aspose.slides"><code>IPointCollection</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPropertyEffect.html#getPoints--">getPoints</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPropertyEffect.html" title="interface in com.aspose.slides">IPropertyEffect</a></code></dd>
</dl>
</li>
</ul>
<a name="setPoints-com.aspose.slides.IPointCollection-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setPoints</h4>
<pre>public final&nbsp;void&nbsp;setPoints(<a href="../../../com/aspose/slides/IPointCollection.html" title="interface in com.aspose.slides">IPointCollection</a>&nbsp;value)</pre>
<div class="block"><p>
 Specifies the points of the animation.
 Read/write <a href="../../../com/aspose/slides/IPointCollection.html" title="interface in com.aspose.slides"><code>IPointCollection</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPropertyEffect.html#setPoints-com.aspose.slides.IPointCollection-">setPoints</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPropertyEffect.html" title="interface in com.aspose.slides">IPropertyEffect</a></code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/PropertyCalcModeType.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/PropertyValueType.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/PropertyEffect.html" target="_top">Frames</a></li>
<li><a href="PropertyEffect.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><i>Copyright &copy; 2004-2025 Aspose Pty Ltd. All Rights Reserved.</i></small></p>
</body>
</html>
