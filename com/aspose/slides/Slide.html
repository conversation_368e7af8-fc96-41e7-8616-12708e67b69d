<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_421) on Fri Jul 11 05:32:44 CDT 2025 -->
<title>Slide (Aspose.Slides for Java (25.7) API Documentation)</title>
<meta name="date" content="2025-07-11">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Slide (Aspose.Slides for Java (25.7) API Documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":42,"i16":42,"i17":42,"i18":42,"i19":42,"i20":42,"i21":42,"i22":10,"i23":10,"i24":42,"i25":42,"i26":42,"i27":10,"i28":10,"i29":10,"i30":10,"i31":10,"i32":10,"i33":10,"i34":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"],32:["t6","Deprecated Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/SketchFormat.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/SlideCollection.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/Slide.html" target="_top">Frames</a></li>
<li><a href="Slide.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.aspose.slides</div>
<h2 title="Class Slide" class="title">Class Slide</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../com/aspose/slides/BaseSlide.html" title="class in com.aspose.slides">com.aspose.slides.BaseSlide</a></li>
<li>
<ul class="inheritance">
<li>com.aspose.slides.Slide</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../com/aspose/slides/IBaseSlide.html" title="interface in com.aspose.slides">IBaseSlide</a>, <a href="../../../com/aspose/slides/IOverrideThemeable.html" title="interface in com.aspose.slides">IOverrideThemeable</a>, <a href="../../../com/aspose/slides/IPresentationComponent.html" title="interface in com.aspose.slides">IPresentationComponent</a>, <a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a>, <a href="../../../com/aspose/slides/ISlideComponent.html" title="interface in com.aspose.slides">ISlideComponent</a>, <a href="../../../com/aspose/slides/IThemeable.html" title="interface in com.aspose.slides">IThemeable</a></dd>
</dl>
<hr>
<br>
<pre>public final class <span class="typeNameLabel">Slide</span>
extends <a href="../../../com/aspose/slides/BaseSlide.html" title="class in com.aspose.slides">BaseSlide</a>
implements <a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a></pre>
<div class="block"><p>
  Represents a slide in a presentation.
 </p></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t6" class="tableTab"><span><a href="javascript:show(32);">Deprecated Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/ISlideHeaderFooterManager.html" title="interface in com.aspose.slides">ISlideHeaderFooterManager</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Slide.html#getHeaderFooterManager--">getHeaderFooterManager</a></span>()</code>
<div class="block">
 Returns HeaderFooter manager of the slide.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Slide.html#getHidden--">getHidden</a></span>()</code>
<div class="block">
 Determines whether the specified slide is hidden during a slide show.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IImage.html" title="interface in com.aspose.slides">IImage</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Slide.html#getImage--">getImage</a></span>()</code>
<div class="block">
 Returns a Thumbnail Image object (20% of real size).</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IImage.html" title="interface in com.aspose.slides">IImage</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Slide.html#getImage-java.awt.Dimension-">getImage</a></span>(java.awt.Dimension&nbsp;imageSize)</code>
<div class="block">
 Returns a Thumbnail Image object with specified size.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IImage.html" title="interface in com.aspose.slides">IImage</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Slide.html#getImage-float-float-">getImage</a></span>(float&nbsp;scaleX,
        float&nbsp;scaleY)</code>
<div class="block"> 
 Returns a Thumbnail Image object with custom scaling.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IImage.html" title="interface in com.aspose.slides">IImage</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Slide.html#getImage-com.aspose.slides.IRenderingOptions-">getImage</a></span>(<a href="../../../com/aspose/slides/IRenderingOptions.html" title="interface in com.aspose.slides">IRenderingOptions</a>&nbsp;options)</code>
<div class="block">
 Returns a Thumbnail Image object.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IImage.html" title="interface in com.aspose.slides">IImage</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Slide.html#getImage-com.aspose.slides.IRenderingOptions-java.awt.Dimension-">getImage</a></span>(<a href="../../../com/aspose/slides/IRenderingOptions.html" title="interface in com.aspose.slides">IRenderingOptions</a>&nbsp;options,
        java.awt.Dimension&nbsp;imageSize)</code>
<div class="block">
 Returns a Thumbnail Image object with specified size.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IImage.html" title="interface in com.aspose.slides">IImage</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Slide.html#getImage-com.aspose.slides.IRenderingOptions-float-float-">getImage</a></span>(<a href="../../../com/aspose/slides/IRenderingOptions.html" title="interface in com.aspose.slides">IRenderingOptions</a>&nbsp;options,
        float&nbsp;scaleX,
        float&nbsp;scaleY)</code>
<div class="block">
 Returns a Thumbnail Image object with custom scaling.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IImage.html" title="interface in com.aspose.slides">IImage</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Slide.html#getImage-com.aspose.slides.ITiffOptions-">getImage</a></span>(<a href="../../../com/aspose/slides/ITiffOptions.html" title="interface in com.aspose.slides">ITiffOptions</a>&nbsp;options)</code>
<div class="block">
 Returns a Thumbnail tiff image object with specified parameters.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/ILayoutSlide.html" title="interface in com.aspose.slides">ILayoutSlide</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Slide.html#getLayoutSlide--">getLayoutSlide</a></span>()</code>
<div class="block">
 Returns or sets the layout slide for the current slide.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/INotesSlideManager.html" title="interface in com.aspose.slides">INotesSlideManager</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Slide.html#getNotesSlideManager--">getNotesSlideManager</a></span>()</code>
<div class="block">
 Allow to access notes slide, add and remove it.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Slide.html#getShowMasterShapes--">getShowMasterShapes</a></span>()</code>
<div class="block">
 Specifies if shapes on the master slide should be shown on slides or not.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IComment.html" title="interface in com.aspose.slides">IComment</a>[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Slide.html#getSlideComments-com.aspose.slides.ICommentAuthor-">getSlideComments</a></span>(<a href="../../../com/aspose/slides/ICommentAuthor.html" title="interface in com.aspose.slides">ICommentAuthor</a>&nbsp;author)</code>
<div class="block">
 Returns all slide comments added by specific author.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Slide.html#getSlideNumber--">getSlideNumber</a></span>()</code>
<div class="block">
 Returns a number of slide.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IOverrideThemeManager.html" title="interface in com.aspose.slides">IOverrideThemeManager</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Slide.html#getThemeManager--">getThemeManager</a></span>()</code>
<div class="block">
 Returns the overriding theme manager.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>java.awt.image.BufferedImage</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Slide.html#getThumbnail--">getThumbnail</a></span>()</code>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;
<div class="block"><span class="deprecationComment">Use GetImage method instead. The method will be removed after release of version 24.7.</span></div>
</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>java.awt.image.BufferedImage</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Slide.html#getThumbnail-java.awt.Dimension-">getThumbnail</a></span>(java.awt.Dimension&nbsp;imageSize)</code>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;
<div class="block"><span class="deprecationComment">Use GetImage(Size imageSize) method instead. The method will be removed after release of version 24.7.</span></div>
</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>java.awt.image.BufferedImage</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Slide.html#getThumbnail-float-float-">getThumbnail</a></span>(float&nbsp;scaleX,
            float&nbsp;scaleY)</code>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;
<div class="block"><span class="deprecationComment">Use getImage(float scaleX, float scaleY) method instead. The method will be removed after release of version 24.7.</span></div>
</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>java.awt.image.BufferedImage</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Slide.html#getThumbnail-com.aspose.slides.IRenderingOptions-">getThumbnail</a></span>(<a href="../../../com/aspose/slides/IRenderingOptions.html" title="interface in com.aspose.slides">IRenderingOptions</a>&nbsp;options)</code>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;
<div class="block"><span class="deprecationComment">Use GetImage(IRenderingOptions options) method instead. The method will be removed after release of version 24.7.</span></div>
</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>java.awt.image.BufferedImage</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Slide.html#getThumbnail-com.aspose.slides.IRenderingOptions-java.awt.Dimension-">getThumbnail</a></span>(<a href="../../../com/aspose/slides/IRenderingOptions.html" title="interface in com.aspose.slides">IRenderingOptions</a>&nbsp;options,
            java.awt.Dimension&nbsp;imageSize)</code>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;
<div class="block"><span class="deprecationComment">Use GetImage(IRenderingOptions options, Size imageSize) method instead. The method will be removed after release of version 24.7.</span></div>
</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>java.awt.image.BufferedImage</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Slide.html#getThumbnail-com.aspose.slides.IRenderingOptions-float-float-">getThumbnail</a></span>(<a href="../../../com/aspose/slides/IRenderingOptions.html" title="interface in com.aspose.slides">IRenderingOptions</a>&nbsp;options,
            float&nbsp;scaleX,
            float&nbsp;scaleY)</code>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;
<div class="block"><span class="deprecationComment">Use GetImage(IRenderingOptions options, float scaleX, float scaleY) method instead. The method will be removed after release of version 24.7.</span></div>
</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>java.awt.image.BufferedImage</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Slide.html#getThumbnail-com.aspose.slides.ITiffOptions-">getThumbnail</a></span>(<a href="../../../com/aspose/slides/ITiffOptions.html" title="interface in com.aspose.slides">ITiffOptions</a>&nbsp;options)</code>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;
<div class="block"><span class="deprecationComment">Use GetImage(ITiffOptions options) method instead. The method will be removed after release of version 24.7.</span></div>
</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Slide.html#joinPortionsWithSameFormatting--">joinPortionsWithSameFormatting</a></span>()</code>
<div class="block">
 Joins runs with same formatting in all paragraphs in all acceptable shapes.</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Slide.html#remove--">remove</a></span>()</code>
<div class="block">
 Removes slide from presentation.</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Slide.html#renderToGraphics-com.aspose.slides.IRenderingOptions-java.awt.Graphics2D-">renderToGraphics</a></span>(<a href="../../../com/aspose/slides/IRenderingOptions.html" title="interface in com.aspose.slides">IRenderingOptions</a>&nbsp;options,
                java.awt.Graphics2D&nbsp;graphics)</code>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;
<div class="block"><span class="deprecationComment">The method will be removed after release of version 24.7.</span></div>
</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Slide.html#renderToGraphics-com.aspose.slides.IRenderingOptions-java.awt.Graphics2D-java.awt.Dimension-">renderToGraphics</a></span>(<a href="../../../com/aspose/slides/IRenderingOptions.html" title="interface in com.aspose.slides">IRenderingOptions</a>&nbsp;options,
                java.awt.Graphics2D&nbsp;graphics,
                java.awt.Dimension&nbsp;renderingSize)</code>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;
<div class="block"><span class="deprecationComment">The method will be removed after release of version 24.7.</span></div>
</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Slide.html#renderToGraphics-com.aspose.slides.IRenderingOptions-java.awt.Graphics2D-float-float-">renderToGraphics</a></span>(<a href="../../../com/aspose/slides/IRenderingOptions.html" title="interface in com.aspose.slides">IRenderingOptions</a>&nbsp;options,
                java.awt.Graphics2D&nbsp;graphics,
                float&nbsp;scaleX,
                float&nbsp;scaleY)</code>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;
<div class="block"><span class="deprecationComment">The method will be removed after release of version 24.7.</span></div>
</div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Slide.html#reset--">reset</a></span>()</code>
<div class="block">
 Resets position, size and formatting of every shape that has a prototype on LayoutSlide.</div>
</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Slide.html#setHidden-boolean-">setHidden</a></span>(boolean&nbsp;value)</code>
<div class="block">
 Determines whether the specified slide is hidden during a slide show.</div>
</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Slide.html#setLayoutSlide-com.aspose.slides.ILayoutSlide-">setLayoutSlide</a></span>(<a href="../../../com/aspose/slides/ILayoutSlide.html" title="interface in com.aspose.slides">ILayoutSlide</a>&nbsp;value)</code>
<div class="block">
 Returns or sets the layout slide for the current slide.</div>
</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Slide.html#setShowMasterShapes-boolean-">setShowMasterShapes</a></span>(boolean&nbsp;value)</code>
<div class="block">
 Specifies if shapes on the master slide should be shown on slides or not.</div>
</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Slide.html#setSlideNumber-int-">setSlideNumber</a></span>(int&nbsp;value)</code>
<div class="block">
 Returns a number of slide.</div>
</td>
</tr>
<tr id="i32" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Slide.html#writeAsEmf-java.io.OutputStream-">writeAsEmf</a></span>(java.io.OutputStream&nbsp;stream)</code>
<div class="block">
 Saves the slide content as an EMF file.</div>
</td>
</tr>
<tr id="i33" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Slide.html#writeAsSvg-java.io.OutputStream-">writeAsSvg</a></span>(java.io.OutputStream&nbsp;stream)</code>
<div class="block">
 Saves the slide content as an SVG file.</div>
</td>
</tr>
<tr id="i34" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Slide.html#writeAsSvg-java.io.OutputStream-com.aspose.slides.ISVGOptions-">writeAsSvg</a></span>(java.io.OutputStream&nbsp;stream,
          <a href="../../../com/aspose/slides/ISVGOptions.html" title="interface in com.aspose.slides">ISVGOptions</a>&nbsp;svgOptions)</code>
<div class="block">
 Saves the slide content as an SVG file.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.aspose.slides.BaseSlide">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;com.aspose.slides.<a href="../../../com/aspose/slides/BaseSlide.html" title="class in com.aspose.slides">BaseSlide</a></h3>
<code><a href="../../../com/aspose/slides/BaseSlide.html#createThemeEffective--">createThemeEffective</a>, <a href="../../../com/aspose/slides/BaseSlide.html#equals-com.aspose.slides.IBaseSlide-">equals</a>, <a href="../../../com/aspose/slides/BaseSlide.html#findShapeByAltText-java.lang.String-">findShapeByAltText</a>, <a href="../../../com/aspose/slides/BaseSlide.html#getBackground--">getBackground</a>, <a href="../../../com/aspose/slides/BaseSlide.html#getControls--">getControls</a>, <a href="../../../com/aspose/slides/BaseSlide.html#getCustomData--">getCustomData</a>, <a href="../../../com/aspose/slides/BaseSlide.html#getHyperlinkQueries--">getHyperlinkQueries</a>, <a href="../../../com/aspose/slides/BaseSlide.html#getName--">getName</a>, <a href="../../../com/aspose/slides/BaseSlide.html#getParent_Immediate--">getParent_Immediate</a>, <a href="../../../com/aspose/slides/BaseSlide.html#getPresentation--">getPresentation</a>, <a href="../../../com/aspose/slides/BaseSlide.html#getShapes--">getShapes</a>, <a href="../../../com/aspose/slides/BaseSlide.html#getSlide--">getSlide</a>, <a href="../../../com/aspose/slides/BaseSlide.html#getSlideId--">getSlideId</a>, <a href="../../../com/aspose/slides/BaseSlide.html#getSlideShowTransition--">getSlideShowTransition</a>, <a href="../../../com/aspose/slides/BaseSlide.html#getTimeline--">getTimeline</a>, <a href="../../../com/aspose/slides/BaseSlide.html#joinPortionsWithSameFormatting-com.aspose.slides.IShapeCollection-">joinPortionsWithSameFormatting</a>, <a href="../../../com/aspose/slides/BaseSlide.html#setName-java.lang.String-">setName</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.aspose.slides.IBaseSlide">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;com.aspose.slides.<a href="../../../com/aspose/slides/IBaseSlide.html" title="interface in com.aspose.slides">IBaseSlide</a></h3>
<code><a href="../../../com/aspose/slides/IBaseSlide.html#equals-com.aspose.slides.IBaseSlide-">equals</a>, <a href="../../../com/aspose/slides/IBaseSlide.html#findShapeByAltText-java.lang.String-">findShapeByAltText</a>, <a href="../../../com/aspose/slides/IBaseSlide.html#getBackground--">getBackground</a>, <a href="../../../com/aspose/slides/IBaseSlide.html#getControls--">getControls</a>, <a href="../../../com/aspose/slides/IBaseSlide.html#getCustomData--">getCustomData</a>, <a href="../../../com/aspose/slides/IBaseSlide.html#getHyperlinkQueries--">getHyperlinkQueries</a>, <a href="../../../com/aspose/slides/IBaseSlide.html#getName--">getName</a>, <a href="../../../com/aspose/slides/IBaseSlide.html#getShapes--">getShapes</a>, <a href="../../../com/aspose/slides/IBaseSlide.html#getSlideId--">getSlideId</a>, <a href="../../../com/aspose/slides/IBaseSlide.html#getSlideShowTransition--">getSlideShowTransition</a>, <a href="../../../com/aspose/slides/IBaseSlide.html#getTimeline--">getTimeline</a>, <a href="../../../com/aspose/slides/IBaseSlide.html#setName-java.lang.String-">setName</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.aspose.slides.IThemeable">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;com.aspose.slides.<a href="../../../com/aspose/slides/IThemeable.html" title="interface in com.aspose.slides">IThemeable</a></h3>
<code><a href="../../../com/aspose/slides/IThemeable.html#createThemeEffective--">createThemeEffective</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.aspose.slides.ISlideComponent">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;com.aspose.slides.<a href="../../../com/aspose/slides/ISlideComponent.html" title="interface in com.aspose.slides">ISlideComponent</a></h3>
<code><a href="../../../com/aspose/slides/ISlideComponent.html#getSlide--">getSlide</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.aspose.slides.IPresentationComponent">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;com.aspose.slides.<a href="../../../com/aspose/slides/IPresentationComponent.html" title="interface in com.aspose.slides">IPresentationComponent</a></h3>
<code><a href="../../../com/aspose/slides/IPresentationComponent.html#getPresentation--">getPresentation</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getHeaderFooterManager--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHeaderFooterManager</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/ISlideHeaderFooterManager.html" title="interface in com.aspose.slides">ISlideHeaderFooterManager</a>&nbsp;getHeaderFooterManager()</pre>
<div class="block"><p>
 Returns HeaderFooter manager of the slide.
 Read-only <a href="../../../com/aspose/slides/ISlideHeaderFooterManager.html" title="interface in com.aspose.slides"><code>ISlideHeaderFooterManager</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISlide.html#getHeaderFooterManager--">getHeaderFooterManager</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a></code></dd>
</dl>
</li>
</ul>
<a name="getThemeManager--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getThemeManager</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IOverrideThemeManager.html" title="interface in com.aspose.slides">IOverrideThemeManager</a>&nbsp;getThemeManager()</pre>
<div class="block"><p>
 Returns the overriding theme manager.
 Read-only <a href="../../../com/aspose/slides/IOverrideThemeManager.html" title="interface in com.aspose.slides"><code>IOverrideThemeManager</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IOverrideThemeable.html#getThemeManager--">getThemeManager</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IOverrideThemeable.html" title="interface in com.aspose.slides">IOverrideThemeable</a></code></dd>
</dl>
</li>
</ul>
<a name="getSlideNumber--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSlideNumber</h4>
<pre>public final&nbsp;int&nbsp;getSlideNumber()</pre>
<div class="block"><p>
 Returns a number of slide.
 Index of slide in <a href="../../../com/aspose/slides/Presentation.html#getSlides--"><code>Presentation.getSlides()</code></a> collection is always equal to SlideNumber - Presentation.FirstSlideNumber.
 Read/write int.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISlide.html#getSlideNumber--">getSlideNumber</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a></code></dd>
</dl>
</li>
</ul>
<a name="setSlideNumber-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSlideNumber</h4>
<pre>public final&nbsp;void&nbsp;setSlideNumber(int&nbsp;value)</pre>
<div class="block"><p>
 Returns a number of slide.
 Index of slide in <a href="../../../com/aspose/slides/Presentation.html#getSlides--"><code>Presentation.getSlides()</code></a> collection is always equal to SlideNumber - Presentation.FirstSlideNumber.
 Read/write int.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISlide.html#setSlideNumber-int-">setSlideNumber</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a></code></dd>
</dl>
</li>
</ul>
<a name="getHidden--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHidden</h4>
<pre>public final&nbsp;boolean&nbsp;getHidden()</pre>
<div class="block"><p>
 Determines whether the specified slide is hidden during a slide show.
 Read/write boolean.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISlide.html#getHidden--">getHidden</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a></code></dd>
</dl>
</li>
</ul>
<a name="setHidden-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setHidden</h4>
<pre>public final&nbsp;void&nbsp;setHidden(boolean&nbsp;value)</pre>
<div class="block"><p>
 Determines whether the specified slide is hidden during a slide show.
 Read/write boolean.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISlide.html#setHidden-boolean-">setHidden</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a></code></dd>
</dl>
</li>
</ul>
<a name="getShowMasterShapes--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getShowMasterShapes</h4>
<pre>public&nbsp;boolean&nbsp;getShowMasterShapes()</pre>
<div class="block"><p>
 Specifies if shapes on the master slide should be shown on slides or not.
 Read/write boolean.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IBaseSlide.html#getShowMasterShapes--">getShowMasterShapes</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IBaseSlide.html" title="interface in com.aspose.slides">IBaseSlide</a></code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/BaseSlide.html#getShowMasterShapes--">getShowMasterShapes</a></code>&nbsp;in class&nbsp;<code><a href="../../../com/aspose/slides/BaseSlide.html" title="class in com.aspose.slides">BaseSlide</a></code></dd>
</dl>
</li>
</ul>
<a name="setShowMasterShapes-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setShowMasterShapes</h4>
<pre>public&nbsp;void&nbsp;setShowMasterShapes(boolean&nbsp;value)</pre>
<div class="block"><p>
 Specifies if shapes on the master slide should be shown on slides or not.
 Read/write boolean.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IBaseSlide.html#setShowMasterShapes-boolean-">setShowMasterShapes</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IBaseSlide.html" title="interface in com.aspose.slides">IBaseSlide</a></code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/BaseSlide.html#setShowMasterShapes-boolean-">setShowMasterShapes</a></code>&nbsp;in class&nbsp;<code><a href="../../../com/aspose/slides/BaseSlide.html" title="class in com.aspose.slides">BaseSlide</a></code></dd>
</dl>
</li>
</ul>
<a name="getThumbnail-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getThumbnail</h4>
<pre>@Deprecated
public final&nbsp;java.awt.image.BufferedImage&nbsp;getThumbnail(float&nbsp;scaleX,
                                                                    float&nbsp;scaleY)</pre>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;<span class="deprecationComment">Use getImage(float scaleX, float scaleY) method instead. The method will be removed after release of version 24.7.</span></div>
<div class="block"><p>
 Returns a Thumbnail Bitmap object with custom scaling.
 </p><p><hr><blockquote><pre>
 The following example shows how to generate thumbnails from PowerPoint Presentation.
 <pre>
 // Instantiate a Presentation class that represents the presentation file
 Presentation pres = new Presentation("ThumbnailFromSlide.pptx");
 try {
     // Access the first slide
     ISlide sld = pres.getSlides().get_Item(0);
     // Create a full scale image
     BufferedImage bmp = sld.getThumbnail(1f, 1f);
     // Save the image to disk in PNG format
     ImageIO.write(bmp, "PNG", new File("Thumbnail_out.png"));
 } catch(IOException e) {
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 The following example shows how to converting slides to bitmap and saving the images in PNG.
 <pre>
 Presentation pres = new Presentation("Presentation.pptx");
 try
 {
     for (ISlide slide : pres.getSlides())
     {
         // Converts the slide in the presentation to a Bitmap object
         BufferedImage bmp = slide.getThumbnail();
         // Saves the image in the PNG format
         ImageIO.write(bmp, "PNG", new File("Thumbnail_out_" + slide.getSlideNumber() + ".png"));
     }
 } catch(IOException e) {
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 The following example shows how to convert PowerPoint PPT/PPTX to JPG.
 <pre>
 Presentation pres = new Presentation("Presentation.pptx");
 try
 {
     for (ISlide slide : pres.getSlides())
     {
         // Converts the slide in the presentation to a Bitmap object
         BufferedImage bmp = slide.getThumbnail();
         // Saves the image in the JPG format
         ImageIO.write(bmp, "JPG", new File("Thumbnail_out" + slide.getSlideNumber() + ".jpg"));
     }
 } catch(IOException e) {
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 The following example shows how to convert PowerPoint PPT/PPTX to JPG with customized dimensions.
 <pre>
 Presentation pres = new Presentation("Presentation.pptx");
 try
 {
     // Define dimensions
     int desiredX = 1200;
     int desiredY = 800;
     // Get scaled values of X and Y
     float ScaleX = (float)(1.0 / pres.getSlideSize().getSize().getWidth()) * desiredX;
     float ScaleY = (float)(1.0 / pres.getSlideSize().getSize().getHeight()) * desiredY;
     for (ISlide slide : pres.getSlides())
     {
         // Converts the first slide in the presentation to a Bitmap object
         BufferedImage bmp = slide.getThumbnail(ScaleX, ScaleY);
         // Saves the image in the JPG format
         ImageIO.write(bmp, "JPG", new File("Slide_" + slide.getSlideNumber() + ".jpg"));
     }
 } catch(IOException e) {
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISlide.html#getThumbnail-float-float-">getThumbnail</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>scaleX</code> - The value by which to scale this Thumbnail in the x-axis direction.</dd>
<dd><code>scaleY</code> - The value by which to scale this Thumbnail in the y-axis direction.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Bitmap object.</dd>
</dl>
</li>
</ul>
<a name="getImage-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getImage</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IImage.html" title="interface in com.aspose.slides">IImage</a>&nbsp;getImage(float&nbsp;scaleX,
                             float&nbsp;scaleY)</pre>
<div class="block"><p> 
 Returns a Thumbnail Image object with custom scaling. 
 </p><p><hr><blockquote><pre>
 The following example shows how to generate thumbnails from PowerPoint Presentation.
 <pre>
 Presentation pres = new Presentation("ThumbnailFromSlide.pptx");
 try {
     // Access the first slide
     ISlide sld = pres.getSlides().get_Item(0);
     // Create a full scale image
     IImage bmp = sld.getImage(1f, 1f);
     // Save the image to disk in JPEG format
     bmp.save("Thumbnail_out.jpg", ImageFormat.Jpeg);
 } finally {
     pres.dispose();
 }
 </pre>
 The following example shows how to converting slides to bitmap and saving the images in PNG.
 <pre>
 Presentation pres = new Presentation("Presentation.pptx");
 try {
     // Converts the first slide in the presentation to a Bitmap object
     IImage bmp = pres.getSlides().get_Item(0).getImage();
     // Saves the image in the PNG format
     bmp.save("Slide_0.png", ImageFormat.Png);
 } finally {
     pres.dispose();
 }
 </pre>
 The following example shows how to convert PowerPoint PPT/PPTX to JPG.
 <pre>
 Presentation pres = new Presentation("PowerPoint-Presentation.ppt");
 try {
     for (ISlide sld : pres.getSlides())
     {
         // Create a full scale image
         IImage bmp = sld.getImage(1f, 1f);
         // Save the image to disk in JPEG format
         bmp.save("Slide_"+sld.getSlideNumber()+"0.jpg", ImageFormat.Jpeg);
     }
 } finally {
     pres.dispose();
 }
 </pre>
 The following example shows how to convert PowerPoint PPT/PPTX to JPG with customized dimensions.
 <pre>
 Presentation pres = new Presentation("PowerPoint-Presentation.pptx");
 try {
     // Define dimensions
     int desiredX = 1200;
     int desiredY = 800;
     // Get scaled values of X and Y
     float ScaleX = (float)(1.0 / pres.getSlideSize().getSize().getWidth()) * desiredX;
     float ScaleY = (float)(1.0 / pres.getSlideSize().getSize().getHeight()) * desiredY;
     for (ISlide sld : pres.getSlides())
     {
         // Create a full scale image
         IImage bmp = sld.getImage(ScaleX, ScaleY);
         // Save the image to disk in JPEG format
         bmp.save("Slide.jpg", ImageFormat.Jpeg);
     }
 } finally {
     pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISlide.html#getImage-float-float-">getImage</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>scaleX</code> - The value by which to scale this Thumbnail in the x-axis direction.</dd>
<dd><code>scaleY</code> - The value by which to scale this Thumbnail in the y-axis direction.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>IImage object.</dd>
</dl>
</li>
</ul>
<a name="getThumbnail--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getThumbnail</h4>
<pre>@Deprecated
public final&nbsp;java.awt.image.BufferedImage&nbsp;getThumbnail()</pre>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;<span class="deprecationComment">Use GetImage method instead. The method will be removed after release of version 24.7.</span></div>
<div class="block"><p>
 Returns a Thumbnail Image object (20% of real size).
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISlide.html#getThumbnail--">getThumbnail</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Bitmap object <code>BufferedImage</code></dd>
</dl>
</li>
</ul>
<a name="getImage--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getImage</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IImage.html" title="interface in com.aspose.slides">IImage</a>&nbsp;getImage()</pre>
<div class="block"><p>
 Returns a Thumbnail Image object (20% of real size).
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISlide.html#getImage--">getImage</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Image object <code>BufferedImage</code></dd>
</dl>
</li>
</ul>
<a name="getThumbnail-com.aspose.slides.IRenderingOptions-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getThumbnail</h4>
<pre>@Deprecated
public final&nbsp;java.awt.image.BufferedImage&nbsp;getThumbnail(<a href="../../../com/aspose/slides/IRenderingOptions.html" title="interface in com.aspose.slides">IRenderingOptions</a>&nbsp;options)</pre>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;<span class="deprecationComment">Use GetImage(IRenderingOptions options) method instead. The method will be removed after release of version 24.7.</span></div>
<div class="block"><p>
 Returns a Thumbnail Bitmap object.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISlide.html#getThumbnail-com.aspose.slides.IRenderingOptions-">getThumbnail</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>options</code> - Rendering options.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Bitmap objects.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>com.aspose.ms.System.InvalidOperationException</code> - Thrown when notesCommentsLayouting.NotesPosition takes the value NotesPositions.BottomFull</dd>
</dl>
</li>
</ul>
<a name="getThumbnail-java.awt.Dimension-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getThumbnail</h4>
<pre>@Deprecated
public final&nbsp;java.awt.image.BufferedImage&nbsp;getThumbnail(java.awt.Dimension&nbsp;imageSize)</pre>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;<span class="deprecationComment">Use GetImage(Size imageSize) method instead. The method will be removed after release of version 24.7.</span></div>
<div class="block"><p>
 Returns a Thumbnail Image object with specified size.
 </p><p><hr><blockquote><pre>
 The following example shows how to converting slides to images with custom sizes using Java.
 <pre>
 Presentation pres = new Presentation("Presentation.pptx");
 try {
     // Converts the first slide in the presentation to a Bitmap with the specified size
     IImage image = pres.getSlides().get_Item(0).getImage(new Dimension(1820, 1040));
     // Saves the image in the JPEG format
     image.save("Slide_0.jpg", ImageFormat.Jpeg);
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISlide.html#getThumbnail-java.awt.Dimension-">getThumbnail</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>imageSize</code> - Size of the image to create.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Bitmap object.</dd>
</dl>
</li>
</ul>
<a name="getImage-java.awt.Dimension-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getImage</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IImage.html" title="interface in com.aspose.slides">IImage</a>&nbsp;getImage(java.awt.Dimension&nbsp;imageSize)</pre>
<div class="block"><p>
 Returns a Thumbnail Image object with specified size.
 </p><p><hr><blockquote><pre>
 The following example shows how to converting slides to images with custom sizes using C#.
 <pre>
 Presentation pres = new Presentation("Presentation.pptx");
 try {
     // Converts the first slide in the presentation to a Bitmap with the specified size
     IImage bmp = pres.getSlides().get_Item(0).getImage(new Dimension(1820, 1040));
     // Saves the image in the JPEG format
     bmp.save("Slide_0.jpg", ImageFormat.Jpeg);
 } finally {
     pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISlide.html#getImage-java.awt.Dimension-">getImage</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>imageSize</code> - Size of the image to create.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Image object.</dd>
</dl>
</li>
</ul>
<a name="getThumbnail-com.aspose.slides.ITiffOptions-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getThumbnail</h4>
<pre>@Deprecated
public final&nbsp;java.awt.image.BufferedImage&nbsp;getThumbnail(<a href="../../../com/aspose/slides/ITiffOptions.html" title="interface in com.aspose.slides">ITiffOptions</a>&nbsp;options)</pre>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;<span class="deprecationComment">Use GetImage(ITiffOptions options) method instead. The method will be removed after release of version 24.7.</span></div>
<div class="block"><p>
 Returns a Thumbnail tiff image object with specified parameters.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISlide.html#getThumbnail-com.aspose.slides.ITiffOptions-">getThumbnail</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>options</code> - Tiff options.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>BufferedImage object.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>com.aspose.ms.System.InvalidOperationException</code> - Thrown when options.SlideLayoutOption is NotesCommentsLayoutingOptions and its property NotesPosition takes the value NotesPositions.BottomFull.</dd>
</dl>
</li>
</ul>
<a name="getImage-com.aspose.slides.ITiffOptions-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getImage</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IImage.html" title="interface in com.aspose.slides">IImage</a>&nbsp;getImage(<a href="../../../com/aspose/slides/ITiffOptions.html" title="interface in com.aspose.slides">ITiffOptions</a>&nbsp;options)</pre>
<div class="block"><p>
 Returns a Thumbnail tiff image object with specified parameters.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISlide.html#getImage-com.aspose.slides.ITiffOptions-">getImage</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>options</code> - Tiff options.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Image object.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>com.aspose.ms.System.InvalidOperationException</code> - Thrown when options.SlideLayoutOption is NotesCommentsLayoutingOptions and its property NotesPosition takes the value NotesPositions.BottomFull.</dd>
</dl>
</li>
</ul>
<a name="getImage-com.aspose.slides.IRenderingOptions-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getImage</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IImage.html" title="interface in com.aspose.slides">IImage</a>&nbsp;getImage(<a href="../../../com/aspose/slides/IRenderingOptions.html" title="interface in com.aspose.slides">IRenderingOptions</a>&nbsp;options)</pre>
<div class="block"><p>
 Returns a Thumbnail Image object.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISlide.html#getImage-com.aspose.slides.IRenderingOptions-">getImage</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>options</code> - Rendering options.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Image object.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>com.aspose.ms.System.InvalidOperationException</code> - Thrown when notesCommentsLayouting.NotesPosition takes the value NotesPositions.BottomFull</dd>
</dl>
</li>
</ul>
<a name="getThumbnail-com.aspose.slides.IRenderingOptions-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getThumbnail</h4>
<pre>@Deprecated
public final&nbsp;java.awt.image.BufferedImage&nbsp;getThumbnail(<a href="../../../com/aspose/slides/IRenderingOptions.html" title="interface in com.aspose.slides">IRenderingOptions</a>&nbsp;options,
                                                                    float&nbsp;scaleX,
                                                                    float&nbsp;scaleY)</pre>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;<span class="deprecationComment">Use GetImage(IRenderingOptions options, float scaleX, float scaleY) method instead. The method will be removed after release of version 24.7.</span></div>
<div class="block"><p>
 Returns a Thumbnail Image object with custom scaling.
 </p><p><hr><blockquote><pre>
 The following example shows how to converting slides With notes and comments to Images using Java.
 <pre>
 Presentation pres = new Presentation("PresentationNotesComments.pptx");
 try {
     // Create the rendering options
     IRenderingOptions options = new RenderingOptions();
     // Create notes and comments layouting options
     NotesCommentsLayoutingOptions notesCommentsLayouting = new NotesCommentsLayoutingOptions();
     // Sets the position of the notes on the page
     notesCommentsLayouting.setNotesPosition(NotesPositions.BottomTruncated);
     // Sets the position of the comments on the page
     notesCommentsLayouting.setCommentsPosition(CommentsPositions.Right);
     // Sets the width of the comment output area
     notesCommentsLayouting.setCommentsAreaWidth(500);
     // Sets the color for the comments area
     notesCommentsLayouting.setCommentsAreaColor(Color.WHITE);
     // Set layout options for rendering
     options.setSlidesLayoutOptions(notesCommentsLayouting);
     // Converts the first slide of the presentation to a BufferedImage object
     IImage image = pres.getSlides().get_Item(0).getImage(options, 2f, 2f);
     // Saves the image in the GIF format
     image.save("Slide_Notes_Comments_0.gif", ImageFormat.Gif);
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISlide.html#getThumbnail-com.aspose.slides.IRenderingOptions-float-float-">getThumbnail</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>options</code> - Rendering options.</dd>
<dd><code>scaleX</code> - The value by which to scale this Thumbnail in the x-axis direction.</dd>
<dd><code>scaleY</code> - The value by which to scale this Thumbnail in the y-axis direction.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>BufferedImage objects.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>com.aspose.ms.System.InvalidOperationException</code> - Thrown when notesCommentsLayouting.NotesPosition takes the value NotesPositions.BottomFull</dd>
</dl>
</li>
</ul>
<a name="getImage-com.aspose.slides.IRenderingOptions-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getImage</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IImage.html" title="interface in com.aspose.slides">IImage</a>&nbsp;getImage(<a href="../../../com/aspose/slides/IRenderingOptions.html" title="interface in com.aspose.slides">IRenderingOptions</a>&nbsp;options,
                             float&nbsp;scaleX,
                             float&nbsp;scaleY)</pre>
<div class="block"><p>
 Returns a Thumbnail Image object with custom scaling.
 </p><p><hr><blockquote><pre>
 The following example shows how to converting slides With notes and comments to Images using C#.
 <pre>
 Presentation pres = new Presentation("PresentationNotesComments.pptx");
 try {
     // Create the rendering options
     IRenderingOptions options = new RenderingOptions();
     // Create notes and comments layouting options
     NotesCommentsLayoutingOptions notesCommentsLayouting = new NotesCommentsLayoutingOptions();
     // Sets the position of the notes on the page
     notesCommentsLayouting.setNotesPosition(NotesPositions.BottomTruncated);
     // Sets the position of the comments on the page
     notesCommentsLayouting.setCommentsPosition(CommentsPositions.Right);
     // Sets the width of the comment output area
     notesCommentsLayouting.setCommentsAreaWidth(500);
     // Sets the color for the comments area
     notesCommentsLayouting.setCommentsAreaColor(Color.WHITE);
     // Set layout options for rendering
     options.setSlidesLayoutOptions(notesCommentsLayouting);
     // Converts the first slide of the presentation to a IImage object
     IImage image = pres.getSlides().get_Item(0).getImage(options, 2f, 2f);
     // Saves the image in the GIF format
     image.save("Slide_Notes_Comments_0.gif", ImageFormat.Gif);
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISlide.html#getImage-com.aspose.slides.IRenderingOptions-float-float-">getImage</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>options</code> - Rendering options.</dd>
<dd><code>scaleX</code> - The value by which to scale this Thumbnail in the x-axis direction.</dd>
<dd><code>scaleY</code> - The value by which to scale this Thumbnail in the y-axis direction.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Bitmap objects.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>com.aspose.ms.System.InvalidOperationException</code> - Thrown when notesCommentsLayouting.NotesPosition takes the value NotesPositions.BottomFull</dd>
</dl>
</li>
</ul>
<a name="getThumbnail-com.aspose.slides.IRenderingOptions-java.awt.Dimension-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getThumbnail</h4>
<pre>@Deprecated
public final&nbsp;java.awt.image.BufferedImage&nbsp;getThumbnail(<a href="../../../com/aspose/slides/IRenderingOptions.html" title="interface in com.aspose.slides">IRenderingOptions</a>&nbsp;options,
                                                                    java.awt.Dimension&nbsp;imageSize)</pre>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;<span class="deprecationComment">Use GetImage(IRenderingOptions options, Size imageSize) method instead. The method will be removed after release of version 24.7.</span></div>
<div class="block"><p>
 Returns a Thumbnail BufferedImage object with specified size.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISlide.html#getThumbnail-com.aspose.slides.IRenderingOptions-java.awt.Dimension-">getThumbnail</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>options</code> - Rendering options.</dd>
<dd><code>imageSize</code> - Size of the image to create.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>BufferedImage objects.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>com.aspose.ms.System.InvalidOperationException</code> - Thrown when options.SlideLayoutOption is NotesCommentsLayoutingOptions and its property NotesPosition takes the value NotesPositions.BottomFull.</dd>
</dl>
</li>
</ul>
<a name="getImage-com.aspose.slides.IRenderingOptions-java.awt.Dimension-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getImage</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IImage.html" title="interface in com.aspose.slides">IImage</a>&nbsp;getImage(<a href="../../../com/aspose/slides/IRenderingOptions.html" title="interface in com.aspose.slides">IRenderingOptions</a>&nbsp;options,
                             java.awt.Dimension&nbsp;imageSize)</pre>
<div class="block"><p>
 Returns a Thumbnail Image object with specified size.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISlide.html#getImage-com.aspose.slides.IRenderingOptions-java.awt.Dimension-">getImage</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>options</code> - Rendering options.</dd>
<dd><code>imageSize</code> - Size of the image to create.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Image object.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>com.aspose.ms.System.InvalidOperationException</code> - Thrown when options.SlideLayoutOption is NotesCommentsLayoutingOptions and its property NotesPosition takes the value NotesPositions.BottomFull.</dd>
</dl>
</li>
</ul>
<a name="renderToGraphics-com.aspose.slides.IRenderingOptions-java.awt.Graphics2D-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>renderToGraphics</h4>
<pre>@Deprecated
public final&nbsp;void&nbsp;renderToGraphics(<a href="../../../com/aspose/slides/IRenderingOptions.html" title="interface in com.aspose.slides">IRenderingOptions</a>&nbsp;options,
                                                java.awt.Graphics2D&nbsp;graphics)</pre>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;<span class="deprecationComment">The method will be removed after release of version 24.7.</span></div>
<div class="block"><p>
 Renders certain slide to a Graphics object.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISlide.html#renderToGraphics-com.aspose.slides.IRenderingOptions-java.awt.Graphics2D-">renderToGraphics</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>options</code> - Rendering options.</dd>
<dd><code>graphics</code> - The object where to render to.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>com.aspose.ms.System.InvalidOperationException</code> - Thrown when notesCommentsLayouting.NotesPosition takes the value NotesPositions.BottomFull</dd>
</dl>
</li>
</ul>
<a name="renderToGraphics-com.aspose.slides.IRenderingOptions-java.awt.Graphics2D-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>renderToGraphics</h4>
<pre>@Deprecated
public final&nbsp;void&nbsp;renderToGraphics(<a href="../../../com/aspose/slides/IRenderingOptions.html" title="interface in com.aspose.slides">IRenderingOptions</a>&nbsp;options,
                                                java.awt.Graphics2D&nbsp;graphics,
                                                float&nbsp;scaleX,
                                                float&nbsp;scaleY)</pre>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;<span class="deprecationComment">The method will be removed after release of version 24.7.</span></div>
<div class="block"><p>
 Renders certain slide to a Graphics object with custom scaling.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISlide.html#renderToGraphics-com.aspose.slides.IRenderingOptions-java.awt.Graphics2D-float-float-">renderToGraphics</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>options</code> - Rendering options.</dd>
<dd><code>graphics</code> - The object where to render to.</dd>
<dd><code>scaleX</code> - The scale for rendering the slide (1.0 is 100%) in the x-axis direction.</dd>
<dd><code>scaleY</code> - The scale for rendering the slide (1.0 is 100%) in the y-axis direction.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>com.aspose.ms.System.InvalidOperationException</code> - Thrown when notesCommentsLayouting.NotesPosition takes the value NotesPositions.BottomFull</dd>
</dl>
</li>
</ul>
<a name="renderToGraphics-com.aspose.slides.IRenderingOptions-java.awt.Graphics2D-java.awt.Dimension-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>renderToGraphics</h4>
<pre>@Deprecated
public final&nbsp;void&nbsp;renderToGraphics(<a href="../../../com/aspose/slides/IRenderingOptions.html" title="interface in com.aspose.slides">IRenderingOptions</a>&nbsp;options,
                                                java.awt.Graphics2D&nbsp;graphics,
                                                java.awt.Dimension&nbsp;renderingSize)</pre>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;<span class="deprecationComment">The method will be removed after release of version 24.7.</span></div>
<div class="block"><p>
 Renders certain slide to a Graphics object using specified size.
 </p><p><hr><blockquote><pre>
 The following example shows how to convert the first slide to the framed image with the RenderToGraphics method.
 <pre>
 Presentation pres = new Presentation("Presentation.pptx");
 try {
     // Gets the presentation slide size
     Dimension slideSize = new Dimension(1820, 1040);
     // Creates a Bitmap with the slide size
     BufferedImage image = new BufferedImage((int)slideSize.getWidth() + 50, (int)slideSize.getHeight() + 50, BufferedImage.TYPE_INT_ARGB);
     java.awt.Graphics graphics = image.createGraphics();
     try
     {
         graphics.setColor(Color.RED);
         graphics.fillRect(0, 0, (int)pres.getSlideSize().getSize().getWidth(), (int)pres.getSlideSize().getSize().getHeight());
         graphics.translate(25, 25);
         pres.getSlides().get_Item(0).renderToGraphics(new RenderingOptions(), (Graphics2D) graphics);
     }
     finally
     {
         if (graphics != null) graphics.dispose();
     }
     ImageIO.write(image, "PNG", new File("Slide_0.png"));
 } catch(IOException e) {
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 The following example shows how to conversion process for a slide with notes using the RenderToGraphics.
 <pre>
 Presentation pres = new Presentation("Presentation.pptx");
 try {
     // Gets the presentation slide size
     Dimension notesSize = new Dimension((int)pres.getNotesSize().getSize().getWidth(), (int)pres.getNotesSize().getSize().getHeight());
     IRenderingOptions options = new RenderingOptions();
     // Sets the position of the notes
     options.getNotesCommentsLayouting().setNotesPosition(NotesPositions.BottomTruncated);
     // Creates a Bitmap with the slide size
     BufferedImage image = new BufferedImage((int)notesSize.getWidth(), (int)notesSize.getHeight(), BufferedImage.TYPE_INT_ARGB);
     java.awt.Graphics graphics = image.createGraphics();
     try
     {
         graphics.setColor(Color.RED);
         graphics.fillRect(0, 0, (int)pres.getSlideSize().getSize().getWidth(), (int)pres.getSlideSize().getSize().getHeight());
         graphics.translate(25, 25);
         pres.getSlides().get_Item(0).renderToGraphics(options, (Graphics2D) graphics, notesSize);
     }
     finally
     {
         if (graphics != null) graphics.dispose();
     }
     ImageIO.write(image, "PNG", new File("Slide_0.png"));
 } catch(IOException e) {
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre> 
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISlide.html#renderToGraphics-com.aspose.slides.IRenderingOptions-java.awt.Graphics2D-java.awt.Dimension-">renderToGraphics</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>options</code> - Rendering options.</dd>
<dd><code>graphics</code> - The object where to render to.</dd>
<dd><code>renderingSize</code> - The maximum dimensions (in pixels) that can be occupied by the rendered slide.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>com.aspose.ms.System.InvalidOperationException</code> - Thrown when notesCommentsLayouting.NotesPosition takes the value NotesPositions.BottomFull</dd>
</dl>
</li>
</ul>
<a name="writeAsSvg-java.io.OutputStream-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>writeAsSvg</h4>
<pre>public final&nbsp;void&nbsp;writeAsSvg(java.io.OutputStream&nbsp;stream)</pre>
<div class="block"><p>
 Saves the slide content as an SVG file.
 </p><p><hr><blockquote><pre>
 The following code example demonstrates how to convert the first slide from a PowerPoint presentation into an SVG file.
 <pre>
 Presentation pres = new Presentation("pres.pptx");
 try {
     FileOutputStream fileStream = new FileOutputStream("slide_1.svg");
     {
         // Saves the first slide as an SVG file
         pres.getSlides().get_Item(0).writeAsSvg(fileStream);
     }
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISlide.html#writeAsSvg-java.io.OutputStream-">writeAsSvg</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>stream</code> - Target stream</dd>
</dl>
</li>
</ul>
<a name="writeAsSvg-java.io.OutputStream-com.aspose.slides.ISVGOptions-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>writeAsSvg</h4>
<pre>public final&nbsp;void&nbsp;writeAsSvg(java.io.OutputStream&nbsp;stream,
                             <a href="../../../com/aspose/slides/ISVGOptions.html" title="interface in com.aspose.slides">ISVGOptions</a>&nbsp;svgOptions)</pre>
<div class="block"><p>
 Saves the slide content as an SVG file.
 </p><p><hr><blockquote><pre>
 The following code example demonstrates how to convert the first slide from a PowerPoint presentation into an SVG file with options.
 <pre>
 Presentation pres = new Presentation("pres.pptx");
 try {
     FileOutputStream fileStream = new FileOutputStream("slide1.svg");
     SVGOptions options = new SVGOptions();
     options.setVectorizeText(true);
     // Saves the first slide as an SVG file
     pres.getSlides().get_Item(0).writeAsSvg(fileStream, options);
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISlide.html#writeAsSvg-java.io.OutputStream-com.aspose.slides.ISVGOptions-">writeAsSvg</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>stream</code> - Target stream</dd>
<dd><code>svgOptions</code> - SVG generation options</dd>
</dl>
</li>
</ul>
<a name="writeAsEmf-java.io.OutputStream-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>writeAsEmf</h4>
<pre>public final&nbsp;void&nbsp;writeAsEmf(java.io.OutputStream&nbsp;stream)</pre>
<div class="block"><p>
 Saves the slide content as an EMF file.
 </p><p><hr><blockquote><pre>
 The following code example demonstrates how to convert the first slide from a PowerPoint presentation into a metafile.
 <pre>
 Presentation pres = new Presentation("pres.pptx");
 try {
     FileOutputStream fileStream = new FileOutputStream("slide_1.emf");
     {
         // Saves the first slide as a metafille
         pres.getSlides().get_Item(0).writeAsEmf(fileStream);
     }
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISlide.html#writeAsEmf-java.io.OutputStream-">writeAsEmf</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>stream</code> - Target stream</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>com.aspose.ms.System.ArgumentNullException</code> - Target stream is <code>null</code></dd>
</dl>
</li>
</ul>
<a name="remove--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>remove</h4>
<pre>public final&nbsp;void&nbsp;remove()</pre>
<div class="block"><p>
 Removes slide from presentation.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISlide.html#remove--">remove</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a></code></dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../com/aspose/slides/PptxEditException.html" title="class in com.aspose.slides">PptxEditException</a></code> - Thrown if slide is already removed from presentation.</dd>
</dl>
</li>
</ul>
<a name="getLayoutSlide--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLayoutSlide</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/ILayoutSlide.html" title="interface in com.aspose.slides">ILayoutSlide</a>&nbsp;getLayoutSlide()</pre>
<div class="block"><p>
 Returns or sets the layout slide for the current slide.
 Read/write <a href="../../../com/aspose/slides/ILayoutSlide.html" title="interface in com.aspose.slides"><code>ILayoutSlide</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISlide.html#getLayoutSlide--">getLayoutSlide</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a></code></dd>
</dl>
</li>
</ul>
<a name="setLayoutSlide-com.aspose.slides.ILayoutSlide-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLayoutSlide</h4>
<pre>public final&nbsp;void&nbsp;setLayoutSlide(<a href="../../../com/aspose/slides/ILayoutSlide.html" title="interface in com.aspose.slides">ILayoutSlide</a>&nbsp;value)</pre>
<div class="block"><p>
 Returns or sets the layout slide for the current slide.
 Read/write <a href="../../../com/aspose/slides/ILayoutSlide.html" title="interface in com.aspose.slides"><code>ILayoutSlide</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISlide.html#setLayoutSlide-com.aspose.slides.ILayoutSlide-">setLayoutSlide</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a></code></dd>
</dl>
</li>
</ul>
<a name="reset--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>reset</h4>
<pre>public final&nbsp;void&nbsp;reset()</pre>
<div class="block"><p>
 Resets position, size and formatting of every shape that has a prototype on LayoutSlide.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISlide.html#reset--">reset</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a></code></dd>
</dl>
</li>
</ul>
<a name="getNotesSlideManager--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNotesSlideManager</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/INotesSlideManager.html" title="interface in com.aspose.slides">INotesSlideManager</a>&nbsp;getNotesSlideManager()</pre>
<div class="block"><p>
 Allow to access notes slide, add and remove it.
 Read-only <a href="../../../com/aspose/slides/INotesSlideManager.html" title="interface in com.aspose.slides"><code>INotesSlideManager</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISlide.html#getNotesSlideManager--">getNotesSlideManager</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a></code></dd>
</dl>
</li>
</ul>
<a name="getSlideComments-com.aspose.slides.ICommentAuthor-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSlideComments</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IComment.html" title="interface in com.aspose.slides">IComment</a>[]&nbsp;getSlideComments(<a href="../../../com/aspose/slides/ICommentAuthor.html" title="interface in com.aspose.slides">ICommentAuthor</a>&nbsp;author)</pre>
<div class="block"><p>
 Returns all slide comments added by specific author.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISlide.html#getSlideComments-com.aspose.slides.ICommentAuthor-">getSlideComments</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>author</code> - Author of comments to find or null to return all comments.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Array of <a href="../../../com/aspose/slides/Comment.html" title="class in com.aspose.slides"><code>Comment</code></a>.</dd>
</dl>
</li>
</ul>
<a name="joinPortionsWithSameFormatting--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>joinPortionsWithSameFormatting</h4>
<pre>public&nbsp;void&nbsp;joinPortionsWithSameFormatting()</pre>
<div class="block"><p>
 Joins runs with same formatting in all paragraphs in all acceptable shapes.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IBaseSlide.html#joinPortionsWithSameFormatting--">joinPortionsWithSameFormatting</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IBaseSlide.html" title="interface in com.aspose.slides">IBaseSlide</a></code></dd>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../com/aspose/slides/BaseSlide.html#joinPortionsWithSameFormatting--">joinPortionsWithSameFormatting</a></code>&nbsp;in class&nbsp;<code><a href="../../../com/aspose/slides/BaseSlide.html" title="class in com.aspose.slides">BaseSlide</a></code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/SketchFormat.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/SlideCollection.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/Slide.html" target="_top">Frames</a></li>
<li><a href="Slide.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><i>Copyright &copy; 2004-2025 Aspose Pty Ltd. All Rights Reserved.</i></small></p>
</body>
</html>
