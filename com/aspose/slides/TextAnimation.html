<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_421) on Fri Jul 11 05:32:44 CDT 2025 -->
<title>TextAnimation (Aspose.Slides for Java (25.7) API Documentation)</title>
<meta name="date" content="2025-07-11">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="TextAnimation (Aspose.Slides for Java (25.7) API Documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/TextAnchorType.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/TextAnimationCollection.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/TextAnimation.html" target="_top">Frames</a></li>
<li><a href="TextAnimation.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.aspose.slides</div>
<h2 title="Class TextAnimation" class="title">Class TextAnimation</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.aspose.slides.TextAnimation</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../com/aspose/slides/ITextAnimation.html" title="interface in com.aspose.slides">ITextAnimation</a></dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">TextAnimation</span>
extends java.lang.Object
implements <a href="../../../com/aspose/slides/ITextAnimation.html" title="interface in com.aspose.slides">ITextAnimation</a></pre>
<div class="block"><p>
 Represent text animation.
 </p></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TextAnimation.html#TextAnimation--">TextAnimation</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IEffect.html" title="interface in com.aspose.slides">IEffect</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TextAnimation.html#addEffect-int-int-int-">addEffect</a></span>(int&nbsp;effectType,
         int&nbsp;subtype,
         int&nbsp;triggerType)</code>
<div class="block">
 Add new effect to the end of current sequence to end of group text animations.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TextAnimation.html#getBuildType--">getBuildType</a></span>()</code>
<div class="block">
 List of build type (for exp.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IEffect.html" title="interface in com.aspose.slides">IEffect</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TextAnimation.html#getEffectAnimateBackgroundShape--">getEffectAnimateBackgroundShape</a></span>()</code>
<div class="block">
 Linked shape effect with group or not (null).</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TextAnimation.html#setBuildType-int-">setBuildType</a></span>(int&nbsp;value)</code>
<div class="block">
 List of build type (for exp.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TextAnimation.html#setEffectAnimateBackgroundShape-com.aspose.slides.IEffect-">setEffectAnimateBackgroundShape</a></span>(<a href="../../../com/aspose/slides/IEffect.html" title="interface in com.aspose.slides">IEffect</a>&nbsp;value)</code>
<div class="block">
 Linked shape effect with group or not (null).</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="TextAnimation--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>TextAnimation</h4>
<pre>public&nbsp;TextAnimation()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="addEffect-int-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addEffect</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IEffect.html" title="interface in com.aspose.slides">IEffect</a>&nbsp;addEffect(int&nbsp;effectType,
                               int&nbsp;subtype,
                               int&nbsp;triggerType)</pre>
<div class="block"><p>
 Add new effect to the end of current sequence to end of group text animations.
 Only valid if count of text paragraphs equal or greater of counts effect of this group!
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITextAnimation.html#addEffect-int-int-int-">addEffect</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITextAnimation.html" title="interface in com.aspose.slides">ITextAnimation</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>effectType</code> - Type of an animation effect <a href="../../../com/aspose/slides/EffectType.html" title="class in com.aspose.slides"><code>EffectType</code></a></dd>
<dd><code>subtype</code> - Subtypes of animation effect <a href="../../../com/aspose/slides/EffectSubtype.html" title="class in com.aspose.slides"><code>EffectSubtype</code></a></dd>
<dd><code>triggerType</code> - Trigger type of effect <a href="../../../com/aspose/slides/EffectTriggerType.html" title="class in com.aspose.slides"><code>EffectTriggerType</code></a></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>New effect object <a href="../../../com/aspose/slides/IEffect.html" title="interface in com.aspose.slides"><code>IEffect</code></a></dd>
</dl>
</li>
</ul>
<a name="getBuildType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBuildType</h4>
<pre>public final&nbsp;int&nbsp;getBuildType()</pre>
<div class="block"><p>
 List of build type (for exp. Paragraph 1,2,3, All at Once) of text animation.
 Read/write <a href="../../../com/aspose/slides/BuildType.html" title="class in com.aspose.slides"><code>BuildType</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITextAnimation.html#getBuildType--">getBuildType</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITextAnimation.html" title="interface in com.aspose.slides">ITextAnimation</a></code></dd>
</dl>
</li>
</ul>
<a name="setBuildType-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBuildType</h4>
<pre>public final&nbsp;void&nbsp;setBuildType(int&nbsp;value)</pre>
<div class="block"><p>
 List of build type (for exp. Paragraph 1,2,3, All at Once) of text animation.
 Read/write <a href="../../../com/aspose/slides/BuildType.html" title="class in com.aspose.slides"><code>BuildType</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITextAnimation.html#setBuildType-int-">setBuildType</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITextAnimation.html" title="interface in com.aspose.slides">ITextAnimation</a></code></dd>
</dl>
</li>
</ul>
<a name="getEffectAnimateBackgroundShape--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEffectAnimateBackgroundShape</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IEffect.html" title="interface in com.aspose.slides">IEffect</a>&nbsp;getEffectAnimateBackgroundShape()</pre>
<div class="block"><p>
 Linked shape effect with group or not (null).
 Read/write <a href="../../../com/aspose/slides/IEffect.html" title="interface in com.aspose.slides"><code>IEffect</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITextAnimation.html#getEffectAnimateBackgroundShape--">getEffectAnimateBackgroundShape</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITextAnimation.html" title="interface in com.aspose.slides">ITextAnimation</a></code></dd>
</dl>
</li>
</ul>
<a name="setEffectAnimateBackgroundShape-com.aspose.slides.IEffect-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setEffectAnimateBackgroundShape</h4>
<pre>public final&nbsp;void&nbsp;setEffectAnimateBackgroundShape(<a href="../../../com/aspose/slides/IEffect.html" title="interface in com.aspose.slides">IEffect</a>&nbsp;value)</pre>
<div class="block"><p>
 Linked shape effect with group or not (null).
 Read/write <a href="../../../com/aspose/slides/IEffect.html" title="interface in com.aspose.slides"><code>IEffect</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITextAnimation.html#setEffectAnimateBackgroundShape-com.aspose.slides.IEffect-">setEffectAnimateBackgroundShape</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITextAnimation.html" title="interface in com.aspose.slides">ITextAnimation</a></code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/TextAnchorType.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/TextAnimationCollection.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/TextAnimation.html" target="_top">Frames</a></li>
<li><a href="TextAnimation.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><i>Copyright &copy; 2004-2025 Aspose Pty Ltd. All Rights Reserved.</i></small></p>
</body>
</html>
