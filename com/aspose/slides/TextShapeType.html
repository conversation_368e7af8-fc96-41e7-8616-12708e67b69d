<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_421) on Fri Jul 11 05:32:44 CDT 2025 -->
<title>TextShapeType (Aspose.Slides for Java (25.7) API Documentation)</title>
<meta name="date" content="2025-07-11">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="TextShapeType (Aspose.Slides for Java (25.7) API Documentation)";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/TextSearchOptions.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/TextStrikethroughType.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/TextShapeType.html" target="_top">Frames</a></li>
<li><a href="TextShapeType.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.classes.inherited.from.class.com.aspose.ms.System.Enum">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#methods.inherited.from.class.com.aspose.ms.System.Enum">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li>Method</li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.aspose.slides</div>
<h2 title="Class TextShapeType" class="title">Class TextShapeType</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.aspose.ms.System.ValueType&lt;com.aspose.ms.System.Enum&gt;</li>
<li>
<ul class="inheritance">
<li>com.aspose.ms.System.Enum</li>
<li>
<ul class="inheritance">
<li>com.aspose.slides.TextShapeType</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public final class <span class="typeNameLabel">TextShapeType</span>
extends com.aspose.ms.System.Enum</pre>
<div class="block"><p>
 Represents text wrapping shape.
 </p></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.com.aspose.ms.System.Enum">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from class&nbsp;com.aspose.ms.System.Enum</h3>
<code>com.aspose.ms.System.Enum.AbstractEnum, com.aspose.ms.System.Enum.FlaggedEnum, com.aspose.ms.System.Enum.ObjectEnum, com.aspose.ms.System.Enum.SimpleEnum</code></li>
</ul>
</li>
</ul>
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static byte</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TextShapeType.html#ArchDown">ArchDown</a></span></code>
<div class="block">
  Downward Arch</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static byte</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TextShapeType.html#ArchDownPour">ArchDownPour</a></span></code>
<div class="block">
  Downward Pour Arch</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static byte</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TextShapeType.html#ArchUp">ArchUp</a></span></code>
<div class="block">
  Upward Arch</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static byte</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TextShapeType.html#ArchUpPour">ArchUpPour</a></span></code>
<div class="block">
  Upward Pour Arch</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static byte</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TextShapeType.html#Button">Button</a></span></code>
<div class="block">
  Button</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static byte</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TextShapeType.html#ButtonPour">ButtonPour</a></span></code>
<div class="block">
  Button Pour</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static byte</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TextShapeType.html#CanDown">CanDown</a></span></code>
<div class="block">
  Downward Can</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static byte</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TextShapeType.html#CanUp">CanUp</a></span></code>
<div class="block">
  Upward Can</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static byte</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TextShapeType.html#CascadeDown">CascadeDown</a></span></code>
<div class="block">
  Downward Cascade</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static byte</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TextShapeType.html#CascadeUp">CascadeUp</a></span></code>
<div class="block">
  Upward Cascade</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static byte</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TextShapeType.html#Chevron">Chevron</a></span></code>
<div class="block">
  Chevron</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static byte</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TextShapeType.html#ChevronInverted">ChevronInverted</a></span></code>
<div class="block">
  Inverted Chevron</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static byte</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TextShapeType.html#Circle">Circle</a></span></code>
<div class="block">
  Circle</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static byte</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TextShapeType.html#CirclePour">CirclePour</a></span></code>
<div class="block">
  Circle Pour</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static byte</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TextShapeType.html#CurveDown">CurveDown</a></span></code>
<div class="block">
  Downward Curve</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static byte</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TextShapeType.html#CurveUp">CurveUp</a></span></code>
<div class="block">
  Upward Curve</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static byte</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TextShapeType.html#Custom">Custom</a></span></code>
<div class="block">
 Custom</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static byte</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TextShapeType.html#Deflate">Deflate</a></span></code>
<div class="block">
  Deflate</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static byte</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TextShapeType.html#DeflateBottom">DeflateBottom</a></span></code>
<div class="block">
  Bottom Deflate</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static byte</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TextShapeType.html#DeflateInflate">DeflateInflate</a></span></code>
<div class="block">
  Deflate-Inflate</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static byte</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TextShapeType.html#DeflateInflateDeflate">DeflateInflateDeflate</a></span></code>
<div class="block">
  Deflate-Inflate-Deflate</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static byte</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TextShapeType.html#DeflateTop">DeflateTop</a></span></code>
<div class="block">
  Top Deflate</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static byte</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TextShapeType.html#DoubleWave1">DoubleWave1</a></span></code>
<div class="block">
  Double Wave 1</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static byte</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TextShapeType.html#FadeDown">FadeDown</a></span></code>
<div class="block">
  Downward Fade</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static byte</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TextShapeType.html#FadeLeft">FadeLeft</a></span></code>
<div class="block">
  Left Fade</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static byte</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TextShapeType.html#FadeRight">FadeRight</a></span></code>
<div class="block">
  Right Fade</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static byte</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TextShapeType.html#FadeUp">FadeUp</a></span></code>
<div class="block">
  Upward Fade</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static byte</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TextShapeType.html#Inflate">Inflate</a></span></code>
<div class="block">
  Inflate</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static byte</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TextShapeType.html#InflateBottom">InflateBottom</a></span></code>
<div class="block">
  Bottom Inflate</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static byte</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TextShapeType.html#InflateTop">InflateTop</a></span></code>
<div class="block">
  Top Inflate</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static byte</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TextShapeType.html#None">None</a></span></code>
<div class="block">
  No shape</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static byte</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TextShapeType.html#NotDefined">NotDefined</a></span></code>
<div class="block">
 Not defined</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static byte</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TextShapeType.html#Plain">Plain</a></span></code>
<div class="block">
  Plain</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static byte</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TextShapeType.html#RingInside">RingInside</a></span></code>
<div class="block">
  Inside Ring</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static byte</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TextShapeType.html#RingOutside">RingOutside</a></span></code>
<div class="block">
  Outside Ring</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static byte</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TextShapeType.html#SlantDown">SlantDown</a></span></code>
<div class="block">
  Downward Slant</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static byte</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TextShapeType.html#SlantUp">SlantUp</a></span></code>
<div class="block">
  Upward Slant</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static byte</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TextShapeType.html#Stop">Stop</a></span></code>
<div class="block">
  Stop Sign</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static byte</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TextShapeType.html#Triangle">Triangle</a></span></code>
<div class="block">
  Triangle</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static byte</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TextShapeType.html#TriangleInverted">TriangleInverted</a></span></code>
<div class="block">
  Inverted Triangle</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static byte</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TextShapeType.html#Wave1">Wave1</a></span></code>
<div class="block">
  Wave 1</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static byte</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TextShapeType.html#Wave2">Wave2</a></span></code>
<div class="block">
  Wave 2</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static byte</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TextShapeType.html#Wave4">Wave4</a></span></code>
<div class="block">
  Wave 4</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.com.aspose.ms.System.Enum">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;com.aspose.ms.System.Enum</h3>
<code>EnumSeparatorCharArray</code></li>
</ul>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.aspose.ms.System.Enum">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;com.aspose.ms.System.Enum</h3>
<code>Clone, CloneTo, format, format, get_Caption, get_Value, getName, getName, getNames, getNames, getUnderlyingType, getUnderlyingType, getValue, getValues, isDefined, isDefined, isDefined, isDefined, parse, parse, parse, parse, register, toObject, toString</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="NotDefined">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NotDefined</h4>
<pre>public static final&nbsp;byte NotDefined</pre>
<div class="block"><p>
 Not defined
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TextShapeType.NotDefined">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="None">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>None</h4>
<pre>public static final&nbsp;byte None</pre>
<div class="block"><p>
  No shape
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TextShapeType.None">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Plain">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Plain</h4>
<pre>public static final&nbsp;byte Plain</pre>
<div class="block"><p>
  Plain
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TextShapeType.Plain">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Stop">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Stop</h4>
<pre>public static final&nbsp;byte Stop</pre>
<div class="block"><p>
  Stop Sign
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TextShapeType.Stop">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Triangle">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Triangle</h4>
<pre>public static final&nbsp;byte Triangle</pre>
<div class="block"><p>
  Triangle
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TextShapeType.Triangle">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="TriangleInverted">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TriangleInverted</h4>
<pre>public static final&nbsp;byte TriangleInverted</pre>
<div class="block"><p>
  Inverted Triangle
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TextShapeType.TriangleInverted">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Chevron">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Chevron</h4>
<pre>public static final&nbsp;byte Chevron</pre>
<div class="block"><p>
  Chevron
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TextShapeType.Chevron">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="ChevronInverted">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ChevronInverted</h4>
<pre>public static final&nbsp;byte ChevronInverted</pre>
<div class="block"><p>
  Inverted Chevron
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TextShapeType.ChevronInverted">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="RingInside">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RingInside</h4>
<pre>public static final&nbsp;byte RingInside</pre>
<div class="block"><p>
  Inside Ring
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TextShapeType.RingInside">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="RingOutside">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RingOutside</h4>
<pre>public static final&nbsp;byte RingOutside</pre>
<div class="block"><p>
  Outside Ring
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TextShapeType.RingOutside">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="ArchUp">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ArchUp</h4>
<pre>public static final&nbsp;byte ArchUp</pre>
<div class="block"><p>
  Upward Arch
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TextShapeType.ArchUp">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="ArchDown">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ArchDown</h4>
<pre>public static final&nbsp;byte ArchDown</pre>
<div class="block"><p>
  Downward Arch
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TextShapeType.ArchDown">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Circle">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Circle</h4>
<pre>public static final&nbsp;byte Circle</pre>
<div class="block"><p>
  Circle
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TextShapeType.Circle">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Button">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Button</h4>
<pre>public static final&nbsp;byte Button</pre>
<div class="block"><p>
  Button
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TextShapeType.Button">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="ArchUpPour">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ArchUpPour</h4>
<pre>public static final&nbsp;byte ArchUpPour</pre>
<div class="block"><p>
  Upward Pour Arch
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TextShapeType.ArchUpPour">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="ArchDownPour">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ArchDownPour</h4>
<pre>public static final&nbsp;byte ArchDownPour</pre>
<div class="block"><p>
  Downward Pour Arch
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TextShapeType.ArchDownPour">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CirclePour">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CirclePour</h4>
<pre>public static final&nbsp;byte CirclePour</pre>
<div class="block"><p>
  Circle Pour
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TextShapeType.CirclePour">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="ButtonPour">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ButtonPour</h4>
<pre>public static final&nbsp;byte ButtonPour</pre>
<div class="block"><p>
  Button Pour
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TextShapeType.ButtonPour">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CurveUp">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CurveUp</h4>
<pre>public static final&nbsp;byte CurveUp</pre>
<div class="block"><p>
  Upward Curve
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TextShapeType.CurveUp">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CurveDown">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CurveDown</h4>
<pre>public static final&nbsp;byte CurveDown</pre>
<div class="block"><p>
  Downward Curve
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TextShapeType.CurveDown">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CanUp">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CanUp</h4>
<pre>public static final&nbsp;byte CanUp</pre>
<div class="block"><p>
  Upward Can
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TextShapeType.CanUp">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CanDown">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CanDown</h4>
<pre>public static final&nbsp;byte CanDown</pre>
<div class="block"><p>
  Downward Can
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TextShapeType.CanDown">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Wave1">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Wave1</h4>
<pre>public static final&nbsp;byte Wave1</pre>
<div class="block"><p>
  Wave 1
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TextShapeType.Wave1">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Wave2">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Wave2</h4>
<pre>public static final&nbsp;byte Wave2</pre>
<div class="block"><p>
  Wave 2
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TextShapeType.Wave2">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DoubleWave1">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DoubleWave1</h4>
<pre>public static final&nbsp;byte DoubleWave1</pre>
<div class="block"><p>
  Double Wave 1
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TextShapeType.DoubleWave1">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Wave4">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Wave4</h4>
<pre>public static final&nbsp;byte Wave4</pre>
<div class="block"><p>
  Wave 4
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TextShapeType.Wave4">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Inflate">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Inflate</h4>
<pre>public static final&nbsp;byte Inflate</pre>
<div class="block"><p>
  Inflate
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TextShapeType.Inflate">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Deflate">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Deflate</h4>
<pre>public static final&nbsp;byte Deflate</pre>
<div class="block"><p>
  Deflate
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TextShapeType.Deflate">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="InflateBottom">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>InflateBottom</h4>
<pre>public static final&nbsp;byte InflateBottom</pre>
<div class="block"><p>
  Bottom Inflate
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TextShapeType.InflateBottom">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DeflateBottom">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DeflateBottom</h4>
<pre>public static final&nbsp;byte DeflateBottom</pre>
<div class="block"><p>
  Bottom Deflate
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TextShapeType.DeflateBottom">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="InflateTop">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>InflateTop</h4>
<pre>public static final&nbsp;byte InflateTop</pre>
<div class="block"><p>
  Top Inflate
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TextShapeType.InflateTop">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DeflateTop">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DeflateTop</h4>
<pre>public static final&nbsp;byte DeflateTop</pre>
<div class="block"><p>
  Top Deflate
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TextShapeType.DeflateTop">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DeflateInflate">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DeflateInflate</h4>
<pre>public static final&nbsp;byte DeflateInflate</pre>
<div class="block"><p>
  Deflate-Inflate
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TextShapeType.DeflateInflate">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DeflateInflateDeflate">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DeflateInflateDeflate</h4>
<pre>public static final&nbsp;byte DeflateInflateDeflate</pre>
<div class="block"><p>
  Deflate-Inflate-Deflate
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TextShapeType.DeflateInflateDeflate">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="FadeRight">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FadeRight</h4>
<pre>public static final&nbsp;byte FadeRight</pre>
<div class="block"><p>
  Right Fade
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TextShapeType.FadeRight">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="FadeLeft">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FadeLeft</h4>
<pre>public static final&nbsp;byte FadeLeft</pre>
<div class="block"><p>
  Left Fade
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TextShapeType.FadeLeft">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="FadeUp">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FadeUp</h4>
<pre>public static final&nbsp;byte FadeUp</pre>
<div class="block"><p>
  Upward Fade
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TextShapeType.FadeUp">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="FadeDown">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FadeDown</h4>
<pre>public static final&nbsp;byte FadeDown</pre>
<div class="block"><p>
  Downward Fade
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TextShapeType.FadeDown">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="SlantUp">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SlantUp</h4>
<pre>public static final&nbsp;byte SlantUp</pre>
<div class="block"><p>
  Upward Slant
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TextShapeType.SlantUp">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="SlantDown">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SlantDown</h4>
<pre>public static final&nbsp;byte SlantDown</pre>
<div class="block"><p>
  Downward Slant
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TextShapeType.SlantDown">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CascadeUp">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CascadeUp</h4>
<pre>public static final&nbsp;byte CascadeUp</pre>
<div class="block"><p>
  Upward Cascade
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TextShapeType.CascadeUp">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CascadeDown">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CascadeDown</h4>
<pre>public static final&nbsp;byte CascadeDown</pre>
<div class="block"><p>
  Downward Cascade
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TextShapeType.CascadeDown">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Custom">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>Custom</h4>
<pre>public static final&nbsp;byte Custom</pre>
<div class="block"><p>
 Custom
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TextShapeType.Custom">Constant Field Values</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/TextSearchOptions.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/TextStrikethroughType.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/TextShapeType.html" target="_top">Frames</a></li>
<li><a href="TextShapeType.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.classes.inherited.from.class.com.aspose.ms.System.Enum">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#methods.inherited.from.class.com.aspose.ms.System.Enum">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li>Method</li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><i>Copyright &copy; 2004-2025 Aspose Pty Ltd. All Rights Reserved.</i></small></p>
</body>
</html>
