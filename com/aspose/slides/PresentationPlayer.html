<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_421) on Fri Jul 11 05:32:43 CDT 2025 -->
<title>PresentationPlayer (Aspose.Slides for Java (25.7) API Documentation)</title>
<meta name="date" content="2025-07-11">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="PresentationPlayer (Aspose.Slides for Java (25.7) API Documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/PresentationLockingBehavior.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/PresentationPlayer.FrameTick.html" title="interface in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/PresentationPlayer.html" target="_top">Frames</a></li>
<li><a href="PresentationPlayer.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.aspose.slides</div>
<h2 title="Class PresentationPlayer" class="title">Class PresentationPlayer</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.aspose.slides.PresentationPlayer</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd>com.aspose.ms.System.IDisposable</dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">PresentationPlayer</span>
extends java.lang.Object
implements com.aspose.ms.System.IDisposable</pre>
<div class="block"><p>
 Represents the player of animations associated with the <a href="../../../com/aspose/slides/Presentation.html" title="class in com.aspose.slides"><code>Presentation</code></a>. 
 </p><p><hr><blockquote><pre>
 <pre>
 Presentation pres = new Presentation("pres.pptx");
 try {
     PresentationAnimationsGenerator animationsGenerator = new PresentationAnimationsGenerator(pres);
     try {
         // Play animation with 33 FPS
         PresentationPlayer player33 = new PresentationPlayer(animationsGenerator, 33);
         try {
             player33.setFrameTick((sender, args) -&gt;
             {
                 try {
                     ImageIO.write(args.getFrame(), "PNG", new java.io.File("33fps/frame_" + sender.getFrameIndex() + ".png"));
                 } catch (IOException e) {
                     throw new RuntimeException(e);
                 }
             });
             animationsGenerator.run(pres.getSlides());
         } finally {
             if (player33 != null) player33.dispose();
         }
         // Play animation with 45 FPS
         PresentationPlayer player45 = new PresentationPlayer(animationsGenerator, 45);
         try {
             player45.setFrameTick((sender, args) -&gt;
             {
                 try {
                     ImageIO.write(args.getFrame(), "PNG", new java.io.File("45fps/frame_" + sender.getFrameIndex() + ".png"));
                 } catch (IOException e) {
                     throw new RuntimeException(e);
                 }
             });
             animationsGenerator.run(pres.getSlides());
         } finally {
             if (player45 != null) player45.dispose();
         }
     } finally {
         if (animationsGenerator != null) animationsGenerator.dispose();
     }
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Nested Class Summary table, listing nested classes, and an explanation">
<caption><span>Nested Classes</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static interface&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresentationPlayer.FrameTick.html" title="interface in com.aspose.slides">PresentationPlayer.FrameTick</a></span></code>
<div class="block">
 Represents the frame tick handler of <a href="../../../com/aspose/slides/PresentationPlayer.html#FrameTickDelegate"><code>FrameTickDelegate</code></a> event.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresentationPlayer.html#PresentationPlayer-com.aspose.slides.PresentationAnimationsGenerator-double-">PresentationPlayer</a></span>(<a href="../../../com/aspose/slides/PresentationAnimationsGenerator.html" title="class in com.aspose.slides">PresentationAnimationsGenerator</a>&nbsp;generator,
                  double&nbsp;fps)</code>
<div class="block">
 Creates new instance of the <a href="../../../com/aspose/slides/PresentationPlayer.html" title="class in com.aspose.slides"><code>PresentationPlayer</code></a>.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresentationPlayer.html#dispose--">dispose</a></span>()</code>
<div class="block">
 Disposes the instance of the <a href="../../../com/aspose/slides/PresentationPlayer.html" title="class in com.aspose.slides"><code>PresentationPlayer</code></a>.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresentationPlayer.html#getFrameIndex--">getFrameIndex</a></span>()</code>
<div class="block">
 Gets the frame index.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresentationPlayer.html#setFrameTick-com.aspose.slides.PresentationPlayer.FrameTick-">setFrameTick</a></span>(<a href="../../../com/aspose/slides/PresentationPlayer.FrameTick.html" title="interface in com.aspose.slides">PresentationPlayer.FrameTick</a>&nbsp;event)</code>
<div class="block">
 Set a new frame tick event.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="PresentationPlayer-com.aspose.slides.PresentationAnimationsGenerator-double-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>PresentationPlayer</h4>
<pre>public&nbsp;PresentationPlayer(<a href="../../../com/aspose/slides/PresentationAnimationsGenerator.html" title="class in com.aspose.slides">PresentationAnimationsGenerator</a>&nbsp;generator,
                          double&nbsp;fps)</pre>
<div class="block"><p>
 Creates new instance of the <a href="../../../com/aspose/slides/PresentationPlayer.html" title="class in com.aspose.slides"><code>PresentationPlayer</code></a>.
 </p></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>generator</code> - Presentation animations generator</dd>
<dd><code>fps</code> - Frames per second (FPS)</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="dispose--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>dispose</h4>
<pre>public final&nbsp;void&nbsp;dispose()</pre>
<div class="block"><p>
 Disposes the instance of the <a href="../../../com/aspose/slides/PresentationPlayer.html" title="class in com.aspose.slides"><code>PresentationPlayer</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>dispose</code>&nbsp;in interface&nbsp;<code>com.aspose.ms.System.IDisposable</code></dd>
</dl>
</li>
</ul>
<a name="getFrameIndex--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFrameIndex</h4>
<pre>public final&nbsp;int&nbsp;getFrameIndex()</pre>
<div class="block"><p>
 Gets the frame index.
 </p><p><hr><blockquote><pre>
 <pre>
 Presentation pres = new Presentation("pres.pptx");
 try {
     PresentationAnimationsGenerator animationsGenerator = new PresentationAnimationsGenerator(pres);
     try {
         PresentationPlayer player = new PresentationPlayer(animationsGenerator, 33);
         try {
             player.setFrameTick((sender, args) -&gt;
             {
                 try {
                     ImageIO.write(args.getFrame(), "PNG", new java.io.File("frame_" + sender.getFrameIndex() + ".png"));
                 } catch (IOException e) {
                     throw new RuntimeException(e);
                 }
             });
             animationsGenerator.run(pres.getSlides());
         } finally {
             if (player != null) player.dispose();
         }
     } finally {
         if (animationsGenerator != null) animationsGenerator.dispose();
     }
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
</li>
</ul>
<a name="setFrameTick-com.aspose.slides.PresentationPlayer.FrameTick-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setFrameTick</h4>
<pre>public&nbsp;void&nbsp;setFrameTick(<a href="../../../com/aspose/slides/PresentationPlayer.FrameTick.html" title="interface in com.aspose.slides">PresentationPlayer.FrameTick</a>&nbsp;event)</pre>
<div class="block"><p>
 Set a new frame tick event.
 </p><p><hr><blockquote><pre>
 <pre>
 Presentation pres = new Presentation("pres.pptx");
 try {
     PresentationAnimationsGenerator animationsGenerator = new PresentationAnimationsGenerator(pres);
     try {
         PresentationPlayer player = new PresentationPlayer(animationsGenerator, 33);
         try {
             player.setFrameTick((sender, args) -&gt;
             {
                 try {
                     ImageIO.write(args.getFrame(), "PNG", new java.io.File("frame_" + sender.getFrameIndex() + ".png"));
                 } catch (IOException e) {
                     throw new RuntimeException(e);
                 }
             });
             animationsGenerator.run(pres.getSlides());
         } finally {
             if (player != null) player.dispose();
         }
     } finally {
         if (animationsGenerator != null) animationsGenerator.dispose();
     }
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p><p><hr>
 Occurs when each frame of the animation created by <a href="../../../com/aspose/slides/PresentationAnimationsGenerator.html" title="class in com.aspose.slides"><code>PresentationAnimationsGenerator</code></a> is generated by the player.
 </hr></p></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>event</code> - Frame tick event.</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/PresentationLockingBehavior.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/PresentationPlayer.FrameTick.html" title="interface in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/PresentationPlayer.html" target="_top">Frames</a></li>
<li><a href="PresentationPlayer.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><i>Copyright &copy; 2004-2025 Aspose Pty Ltd. All Rights Reserved.</i></small></p>
</body>
</html>
