<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_421) on Fri Jul 11 05:32:44 CDT 2025 -->
<title>SaveOptions (Aspose.Slides for Java (25.7) API Documentation)</title>
<meta name="date" content="2025-07-11">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="SaveOptions (Aspose.Slides for Java (25.7) API Documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/SaveFormat.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/SaveOptionsFactory.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/SaveOptions.html" target="_top">Frames</a></li>
<li><a href="SaveOptions.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.aspose.slides</div>
<h2 title="Class SaveOptions" class="title">Class SaveOptions</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.aspose.slides.SaveOptions</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../com/aspose/slides/ISaveOptions.html" title="interface in com.aspose.slides">ISaveOptions</a></dd>
</dl>
<dl>
<dt>Direct Known Subclasses:</dt>
<dd><a href="../../../com/aspose/slides/GifOptions.html" title="class in com.aspose.slides">GifOptions</a>, <a href="../../../com/aspose/slides/Html5Options.html" title="class in com.aspose.slides">Html5Options</a>, <a href="../../../com/aspose/slides/HtmlOptions.html" title="class in com.aspose.slides">HtmlOptions</a>, <a href="../../../com/aspose/slides/MarkdownSaveOptions.html" title="class in com.aspose.slides">MarkdownSaveOptions</a>, <a href="../../../com/aspose/slides/PdfOptions.html" title="class in com.aspose.slides">PdfOptions</a>, <a href="../../../com/aspose/slides/PptOptions.html" title="class in com.aspose.slides">PptOptions</a>, <a href="../../../com/aspose/slides/PptxOptions.html" title="class in com.aspose.slides">PptxOptions</a>, <a href="../../../com/aspose/slides/RenderingOptions.html" title="class in com.aspose.slides">RenderingOptions</a>, <a href="../../../com/aspose/slides/SVGOptions.html" title="class in com.aspose.slides">SVGOptions</a>, <a href="../../../com/aspose/slides/SwfOptions.html" title="class in com.aspose.slides">SwfOptions</a>, <a href="../../../com/aspose/slides/TiffOptions.html" title="class in com.aspose.slides">TiffOptions</a>, <a href="../../../com/aspose/slides/XamlOptions.html" title="class in com.aspose.slides">XamlOptions</a>, <a href="../../../com/aspose/slides/XpsOptions.html" title="class in com.aspose.slides">XpsOptions</a></dd>
</dl>
<hr>
<br>
<pre>public abstract class <span class="typeNameLabel">SaveOptions</span>
extends java.lang.Object
implements <a href="../../../com/aspose/slides/ISaveOptions.html" title="interface in com.aspose.slides">ISaveOptions</a></pre>
<div class="block"><p>
 Abstract class with options that control how a presentation is saved.
 </p></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SaveOptions.html#SaveOptions--">SaveOptions</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SaveOptions.html#getDefaultRegularFont--">getDefaultRegularFont</a></span>()</code>
<div class="block">
 Returns or sets font used in case source font is not found.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SaveOptions.html#getGradientStyle--">getGradientStyle</a></span>()</code>
<div class="block">
 Returns or sets the visual style of the gradient.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IProgressCallback.html" title="interface in com.aspose.slides">IProgressCallback</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SaveOptions.html#getProgressCallback--">getProgressCallback</a></span>()</code>
<div class="block">
 Represents a callback object for saving progress updates in percentage.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SaveOptions.html#getSkipJavaScriptLinks--">getSkipJavaScriptLinks</a></span>()</code>
<div class="block">
 Specifies whether to skip hyperlinks with JavaScript calls when saving the presentation.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IWarningCallback.html" title="interface in com.aspose.slides">IWarningCallback</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SaveOptions.html#getWarningCallback--">getWarningCallback</a></span>()</code>
<div class="block">
 Returns of sets an object which receives warnings and decides whether loading process will continue or will be aborted.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SaveOptions.html#setDefaultRegularFont-java.lang.String-">setDefaultRegularFont</a></span>(java.lang.String&nbsp;value)</code>
<div class="block">
 Returns or sets font used in case source font is not found.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SaveOptions.html#setGradientStyle-int-">setGradientStyle</a></span>(int&nbsp;value)</code>
<div class="block">
 Returns or sets the visual style of the gradient.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SaveOptions.html#setProgressCallback-com.aspose.slides.IProgressCallback-">setProgressCallback</a></span>(<a href="../../../com/aspose/slides/IProgressCallback.html" title="interface in com.aspose.slides">IProgressCallback</a>&nbsp;value)</code>
<div class="block">
 Represents a callback object for saving progress updates in percentage.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SaveOptions.html#setSkipJavaScriptLinks-boolean-">setSkipJavaScriptLinks</a></span>(boolean&nbsp;value)</code>
<div class="block">
 Specifies whether to skip hyperlinks with JavaScript calls when saving the presentation.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SaveOptions.html#setWarningCallback-com.aspose.slides.IWarningCallback-">setWarningCallback</a></span>(<a href="../../../com/aspose/slides/IWarningCallback.html" title="interface in com.aspose.slides">IWarningCallback</a>&nbsp;value)</code>
<div class="block">
 Returns of sets an object which receives warnings and decides whether loading process will continue or will be aborted.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="SaveOptions--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>SaveOptions</h4>
<pre>public&nbsp;SaveOptions()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getWarningCallback--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWarningCallback</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IWarningCallback.html" title="interface in com.aspose.slides">IWarningCallback</a>&nbsp;getWarningCallback()</pre>
<div class="block"><p>
 Returns of sets an object which receives warnings and decides whether loading process will continue or will be aborted.
 Read/write <a href="../../../com/aspose/slides/IWarningCallback.html" title="interface in com.aspose.slides"><code>IWarningCallback</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISaveOptions.html#getWarningCallback--">getWarningCallback</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISaveOptions.html" title="interface in com.aspose.slides">ISaveOptions</a></code></dd>
</dl>
</li>
</ul>
<a name="setWarningCallback-com.aspose.slides.IWarningCallback-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setWarningCallback</h4>
<pre>public final&nbsp;void&nbsp;setWarningCallback(<a href="../../../com/aspose/slides/IWarningCallback.html" title="interface in com.aspose.slides">IWarningCallback</a>&nbsp;value)</pre>
<div class="block"><p>
 Returns of sets an object which receives warnings and decides whether loading process will continue or will be aborted.
 Read/write <a href="../../../com/aspose/slides/IWarningCallback.html" title="interface in com.aspose.slides"><code>IWarningCallback</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISaveOptions.html#setWarningCallback-com.aspose.slides.IWarningCallback-">setWarningCallback</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISaveOptions.html" title="interface in com.aspose.slides">ISaveOptions</a></code></dd>
</dl>
</li>
</ul>
<a name="getProgressCallback--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProgressCallback</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IProgressCallback.html" title="interface in com.aspose.slides">IProgressCallback</a>&nbsp;getProgressCallback()</pre>
<div class="block"><p>
 Represents a callback object for saving progress updates in percentage.
 See <a href="../../../com/aspose/slides/IProgressCallback.html" title="interface in com.aspose.slides"><code>IProgressCallback</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISaveOptions.html#getProgressCallback--">getProgressCallback</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISaveOptions.html" title="interface in com.aspose.slides">ISaveOptions</a></code></dd>
</dl>
</li>
</ul>
<a name="setProgressCallback-com.aspose.slides.IProgressCallback-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setProgressCallback</h4>
<pre>public final&nbsp;void&nbsp;setProgressCallback(<a href="../../../com/aspose/slides/IProgressCallback.html" title="interface in com.aspose.slides">IProgressCallback</a>&nbsp;value)</pre>
<div class="block"><p>
 Represents a callback object for saving progress updates in percentage.
 See <a href="../../../com/aspose/slides/IProgressCallback.html" title="interface in com.aspose.slides"><code>IProgressCallback</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISaveOptions.html#setProgressCallback-com.aspose.slides.IProgressCallback-">setProgressCallback</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISaveOptions.html" title="interface in com.aspose.slides">ISaveOptions</a></code></dd>
</dl>
</li>
</ul>
<a name="getDefaultRegularFont--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDefaultRegularFont</h4>
<pre>public final&nbsp;java.lang.String&nbsp;getDefaultRegularFont()</pre>
<div class="block"><p>
 Returns or sets font used in case source font is not found.
 Read-write <code>String</code>.
 </p><p><hr><blockquote><pre>
 <pre>
 Presentation pres = new Presentation("SomePresentation.pptx");
 try
 {
     HtmlOptions htmlOpts = new HtmlOptions();
     htmlOpts.setDefaultRegularFont("Arial Black");
     pres.save("SomePresentation-out-ArialBlack.html", SaveFormat.Html, htmlOpts);
     htmlOpts.setDefaultRegularFont("Lucida Console");
     pres.save("Somepresentation-out-LucidaConsole.html", SaveFormat.Html, htmlOpts);
     PdfOptions pdfOpts = new PdfOptions();
     pdfOpts.setDefaultRegularFont("Arial Black");
     pres.save("SomePresentation-out-ArialBlack.pdf", SaveFormat.Pdf, pdfOpts);
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISaveOptions.html#getDefaultRegularFont--">getDefaultRegularFont</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISaveOptions.html" title="interface in com.aspose.slides">ISaveOptions</a></code></dd>
</dl>
</li>
</ul>
<a name="setDefaultRegularFont-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDefaultRegularFont</h4>
<pre>public final&nbsp;void&nbsp;setDefaultRegularFont(java.lang.String&nbsp;value)</pre>
<div class="block"><p>
 Returns or sets font used in case source font is not found.
 Read-write <code>String</code>.
 </p><p><hr><blockquote><pre>
 <pre>
 Presentation pres = new Presentation("SomePresentation.pptx");
 try
 {
     HtmlOptions htmlOpts = new HtmlOptions();
     htmlOpts.setDefaultRegularFont("Arial Black");
     pres.save("SomePresentation-out-ArialBlack.html", SaveFormat.Html, htmlOpts);
     htmlOpts.setDefaultRegularFont("Lucida Console");
     pres.save("Somepresentation-out-LucidaConsole.html", SaveFormat.Html, htmlOpts);
     PdfOptions pdfOpts = new PdfOptions();
     pdfOpts.setDefaultRegularFont("Arial Black");
     pres.save("SomePresentation-out-ArialBlack.pdf", SaveFormat.Pdf, pdfOpts);
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISaveOptions.html#setDefaultRegularFont-java.lang.String-">setDefaultRegularFont</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISaveOptions.html" title="interface in com.aspose.slides">ISaveOptions</a></code></dd>
</dl>
</li>
</ul>
<a name="getGradientStyle--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getGradientStyle</h4>
<pre>public final&nbsp;int&nbsp;getGradientStyle()</pre>
<div class="block"><p>
 Returns or sets the visual style of the gradient.
 Read/write <a href="../../../com/aspose/slides/GradientStyle.html" title="class in com.aspose.slides"><code>GradientStyle</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISaveOptions.html#getGradientStyle--">getGradientStyle</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISaveOptions.html" title="interface in com.aspose.slides">ISaveOptions</a></code></dd>
</dl>
</li>
</ul>
<a name="setGradientStyle-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setGradientStyle</h4>
<pre>public final&nbsp;void&nbsp;setGradientStyle(int&nbsp;value)</pre>
<div class="block"><p>
 Returns or sets the visual style of the gradient.
 Read/write <a href="../../../com/aspose/slides/GradientStyle.html" title="class in com.aspose.slides"><code>GradientStyle</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISaveOptions.html#setGradientStyle-int-">setGradientStyle</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISaveOptions.html" title="interface in com.aspose.slides">ISaveOptions</a></code></dd>
</dl>
</li>
</ul>
<a name="getSkipJavaScriptLinks--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSkipJavaScriptLinks</h4>
<pre>public final&nbsp;boolean&nbsp;getSkipJavaScriptLinks()</pre>
<div class="block"><p>
 Specifies whether to skip hyperlinks with JavaScript calls when saving the presentation. 
 Read/write boolean. The default value is false.
 </p><p><hr><blockquote><pre> Example:
 <pre>
 Presentation pres = new Presentation("demo.pptx");
 try {
     HtmlOptions htmlOptions = new HtmlOptions();
     htmlOptions.setSkipJavaScriptLinks(true);
     pres.save("result_without_JavaScript_links.html", SaveFormat.Html, htmlOptions);
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p><p><hr>
 <p>When this property is set to true, hyperlinks with JavaScript calls will be ignored while saving.</p>
 <p>When this property is set to false, all hyperlinks will be saved.</p>
 </hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISaveOptions.html#getSkipJavaScriptLinks--">getSkipJavaScriptLinks</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISaveOptions.html" title="interface in com.aspose.slides">ISaveOptions</a></code></dd>
</dl>
</li>
</ul>
<a name="setSkipJavaScriptLinks-boolean-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setSkipJavaScriptLinks</h4>
<pre>public final&nbsp;void&nbsp;setSkipJavaScriptLinks(boolean&nbsp;value)</pre>
<div class="block"><p>
 Specifies whether to skip hyperlinks with JavaScript calls when saving the presentation. 
 Read/write boolean. The default value is false.
 </p><p><hr><blockquote><pre> Example:
 <pre>
 Presentation pres = new Presentation("demo.pptx");
 try {
     HtmlOptions htmlOptions = new HtmlOptions();
     htmlOptions.setSkipJavaScriptLinks(true);
     pres.save("result_without_JavaScript_links.html", SaveFormat.Html, htmlOptions);
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p><p><hr>
 <p>When this property is set to true, hyperlinks with JavaScript calls will be ignored while saving.</p>
 <p>When this property is set to false, all hyperlinks will be saved.</p>
 </hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISaveOptions.html#setSkipJavaScriptLinks-boolean-">setSkipJavaScriptLinks</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISaveOptions.html" title="interface in com.aspose.slides">ISaveOptions</a></code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/SaveFormat.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/SaveOptionsFactory.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/SaveOptions.html" target="_top">Frames</a></li>
<li><a href="SaveOptions.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><i>Copyright &copy; 2004-2025 Aspose Pty Ltd. All Rights Reserved.</i></small></p>
</body>
</html>
