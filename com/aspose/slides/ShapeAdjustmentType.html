<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_421) on Fri Jul 11 05:32:44 CDT 2025 -->
<title>ShapeAdjustmentType (Aspose.Slides for Java (25.7) API Documentation)</title>
<meta name="date" content="2025-07-11">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ShapeAdjustmentType (Aspose.Slides for Java (25.7) API Documentation)";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/Shape.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/ShapeBevel.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/ShapeAdjustmentType.html" target="_top">Frames</a></li>
<li><a href="ShapeAdjustmentType.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.classes.inherited.from.class.com.aspose.ms.System.Enum">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#methods.inherited.from.class.com.aspose.ms.System.Enum">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li>Method</li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.aspose.slides</div>
<h2 title="Class ShapeAdjustmentType" class="title">Class ShapeAdjustmentType</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.aspose.ms.System.ValueType&lt;com.aspose.ms.System.Enum&gt;</li>
<li>
<ul class="inheritance">
<li>com.aspose.ms.System.Enum</li>
<li>
<ul class="inheritance">
<li>com.aspose.slides.ShapeAdjustmentType</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public final class <span class="typeNameLabel">ShapeAdjustmentType</span>
extends com.aspose.ms.System.Enum</pre>
<div class="block"><p>
 Specifies different types of shape adjustment values.
 </p></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.com.aspose.ms.System.Enum">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from class&nbsp;com.aspose.ms.System.Enum</h3>
<code>com.aspose.ms.System.Enum.AbstractEnum, com.aspose.ms.System.Enum.FlaggedEnum, com.aspose.ms.System.Enum.ObjectEnum, com.aspose.ms.System.Enum.SimpleEnum</code></li>
</ul>
</li>
</ul>
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeAdjustmentType.html#Angle">Angle</a></span></code>
<div class="block">
 Controls the angle for figure or its part.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeAdjustmentType.html#ArrowheadLength">ArrowheadLength</a></span></code>
<div class="block">
 Controls the length of the arrowhead.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeAdjustmentType.html#ArrowheadWidth">ArrowheadWidth</a></span></code>
<div class="block">
 Controls the width of the arrowhead.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeAdjustmentType.html#ArrowTailThickness">ArrowTailThickness</a></span></code>
<div class="block">
 Controls the thickness of the arrow tail.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeAdjustmentType.html#AttachX">AttachX</a></span></code>
<div class="block">
 Controls the horizontal attachment point of the figure.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeAdjustmentType.html#AttachY">AttachY</a></span></code>
<div class="block">
 Controls the vertical attachment point of the figure.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeAdjustmentType.html#CalloutHeight">CalloutHeight</a></span></code>
<div class="block">
 Controls the vertical size of the callout.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeAdjustmentType.html#CalloutSize">CalloutSize</a></span></code>
<div class="block">
 Controls the size of the callout.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeAdjustmentType.html#CalloutWidth">CalloutWidth</a></span></code>
<div class="block">
 Controls the horizontal size of the callout.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeAdjustmentType.html#ConnectorBendPositionX">ConnectorBendPositionX</a></span></code>
<div class="block">
 Controls the horizontal position of the bend in the connector.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeAdjustmentType.html#ConnectorBendPositionY">ConnectorBendPositionY</a></span></code>
<div class="block">
 Controls the vertical position of the bend in the connector.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeAdjustmentType.html#ConnectorPoint1X">ConnectorPoint1X</a></span></code>
<div class="block">
 Controls the horizontal position of the callout angle adjustment point.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeAdjustmentType.html#ConnectorPoint1Y">ConnectorPoint1Y</a></span></code>
<div class="block">
 Controls the vertical position of the callout angle adjustment point.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeAdjustmentType.html#ConnectorPoint2X">ConnectorPoint2X</a></span></code>
<div class="block">
 Controls the horizontal position of the callout angle adjustment point.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeAdjustmentType.html#ConnectorPoint2Y">ConnectorPoint2Y</a></span></code>
<div class="block">
 Controls the vertical position of the callout angle adjustment point.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeAdjustmentType.html#CornerSize">CornerSize</a></span></code>
<div class="block">
 Controls the size of the corners.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeAdjustmentType.html#Curvature">Curvature</a></span></code>
<div class="block">
 Curvature of a bent, braces, curved arrow or shape.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeAdjustmentType.html#Custom">Custom</a></span></code>
<div class="block">Custom type (unknown adjustment)</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeAdjustmentType.html#Depth">Depth</a></span></code>
<div class="block">
 Controls the depth of the gear teeth or the bevel effect.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeAdjustmentType.html#DownCorners">DownCorners</a></span></code>
<div class="block">
 Controls the down corners.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeAdjustmentType.html#EndAngle">EndAngle</a></span></code>
<div class="block">
 End angle for pie and arc shapes.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeAdjustmentType.html#HorizontalSize">HorizontalSize</a></span></code>
<div class="block">
 Controls the horizontal size of the shape.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeAdjustmentType.html#HorizontalThickness">HorizontalThickness</a></span></code>
<div class="block">
 Controls the horizontal thickness of the corner shape.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeAdjustmentType.html#LeftUpCorner">LeftUpCorner</a></span></code>
<div class="block">
 Controls the up left corner.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeAdjustmentType.html#Radius">Radius</a></span></code>
<div class="block">
 Controls the size of the radius of the shape or its part.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeAdjustmentType.html#RightUpCorner">RightUpCorner</a></span></code>
<div class="block">
 Controls the up right corner.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeAdjustmentType.html#ShapePartOffset">ShapePartOffset</a></span></code>
<div class="block">
 Controls the offset of one part of the figure relative to another.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeAdjustmentType.html#StartAngle">StartAngle</a></span></code>
<div class="block">
 Start angle for pie and arc shapes.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeAdjustmentType.html#StartPointX">StartPointX</a></span></code>
<div class="block">
 Controls the callout start point X position.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeAdjustmentType.html#StartPointY">StartPointY</a></span></code>
<div class="block">
 Controls the callout start point Y position.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeAdjustmentType.html#Thickness">Thickness</a></span></code>
<div class="block">
 Controls the thickness of the figure.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeAdjustmentType.html#Top">Top</a></span></code>
<div class="block">
 Controls the top side of a shape.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeAdjustmentType.html#UpCorners">UpCorners</a></span></code>
<div class="block">
 Controls the upper corners.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeAdjustmentType.html#VerticalSize">VerticalSize</a></span></code>
<div class="block">
 Controls the vertical size of the shape.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeAdjustmentType.html#VerticalThickness">VerticalThickness</a></span></code>
<div class="block">
 Controls the vertical thickness of the corner shape.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.com.aspose.ms.System.Enum">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;com.aspose.ms.System.Enum</h3>
<code>EnumSeparatorCharArray</code></li>
</ul>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.aspose.ms.System.Enum">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;com.aspose.ms.System.Enum</h3>
<code>Clone, CloneTo, format, format, get_Caption, get_Value, getName, getName, getNames, getNames, getUnderlyingType, getUnderlyingType, getValue, getValues, isDefined, isDefined, isDefined, isDefined, parse, parse, parse, parse, register, toObject, toString</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="Custom">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Custom</h4>
<pre>public static final&nbsp;int Custom</pre>
<div class="block"><p>Custom type (unknown adjustment)</p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeAdjustmentType.Custom">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CornerSize">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CornerSize</h4>
<pre>public static final&nbsp;int CornerSize</pre>
<div class="block"><p>
 Controls the size of the corners.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeAdjustmentType.CornerSize">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="LeftUpCorner">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LeftUpCorner</h4>
<pre>public static final&nbsp;int LeftUpCorner</pre>
<div class="block"><p>
 Controls the up left corner.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeAdjustmentType.LeftUpCorner">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="RightUpCorner">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RightUpCorner</h4>
<pre>public static final&nbsp;int RightUpCorner</pre>
<div class="block"><p>
 Controls the up right corner.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeAdjustmentType.RightUpCorner">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="UpCorners">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UpCorners</h4>
<pre>public static final&nbsp;int UpCorners</pre>
<div class="block"><p>
 Controls the upper corners.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeAdjustmentType.UpCorners">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DownCorners">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DownCorners</h4>
<pre>public static final&nbsp;int DownCorners</pre>
<div class="block"><p>
 Controls the down corners.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeAdjustmentType.DownCorners">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Thickness">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Thickness</h4>
<pre>public static final&nbsp;int Thickness</pre>
<div class="block"><p>
 Controls the thickness of the figure.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeAdjustmentType.Thickness">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="ArrowTailThickness">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ArrowTailThickness</h4>
<pre>public static final&nbsp;int ArrowTailThickness</pre>
<div class="block"><p>
 Controls the thickness of the arrow tail.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeAdjustmentType.ArrowTailThickness">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="ArrowheadLength">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ArrowheadLength</h4>
<pre>public static final&nbsp;int ArrowheadLength</pre>
<div class="block"><p>
 Controls the length of the arrowhead.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeAdjustmentType.ArrowheadLength">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="ArrowheadWidth">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ArrowheadWidth</h4>
<pre>public static final&nbsp;int ArrowheadWidth</pre>
<div class="block"><p>
 Controls the width of the arrowhead.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeAdjustmentType.ArrowheadWidth">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Radius">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Radius</h4>
<pre>public static final&nbsp;int Radius</pre>
<div class="block"><p>
 Controls the size of the radius of the shape or its part.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeAdjustmentType.Radius">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Depth">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Depth</h4>
<pre>public static final&nbsp;int Depth</pre>
<div class="block"><p>
 Controls the depth of the gear teeth or the bevel effect.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeAdjustmentType.Depth">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="ShapePartOffset">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ShapePartOffset</h4>
<pre>public static final&nbsp;int ShapePartOffset</pre>
<div class="block"><p>
 Controls the offset of one part of the figure relative to another.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeAdjustmentType.ShapePartOffset">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Angle">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Angle</h4>
<pre>public static final&nbsp;int Angle</pre>
<div class="block"><p>
 Controls the angle for figure or its part.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeAdjustmentType.Angle">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="StartAngle">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>StartAngle</h4>
<pre>public static final&nbsp;int StartAngle</pre>
<div class="block"><p>
 Start angle for pie and arc shapes.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeAdjustmentType.StartAngle">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="EndAngle">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>EndAngle</h4>
<pre>public static final&nbsp;int EndAngle</pre>
<div class="block"><p>
 End angle for pie and arc shapes.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeAdjustmentType.EndAngle">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="StartPointX">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>StartPointX</h4>
<pre>public static final&nbsp;int StartPointX</pre>
<div class="block"><p>
 Controls the callout start point X position.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeAdjustmentType.StartPointX">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="StartPointY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>StartPointY</h4>
<pre>public static final&nbsp;int StartPointY</pre>
<div class="block"><p>
 Controls the callout start point Y position.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeAdjustmentType.StartPointY">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="ConnectorPoint1X">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ConnectorPoint1X</h4>
<pre>public static final&nbsp;int ConnectorPoint1X</pre>
<div class="block"><p>
 Controls the horizontal position of the callout angle adjustment point.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeAdjustmentType.ConnectorPoint1X">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="ConnectorPoint1Y">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ConnectorPoint1Y</h4>
<pre>public static final&nbsp;int ConnectorPoint1Y</pre>
<div class="block"><p>
 Controls the vertical position of the callout angle adjustment point.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeAdjustmentType.ConnectorPoint1Y">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="ConnectorPoint2X">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ConnectorPoint2X</h4>
<pre>public static final&nbsp;int ConnectorPoint2X</pre>
<div class="block"><p>
 Controls the horizontal position of the callout angle adjustment point.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeAdjustmentType.ConnectorPoint2X">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="ConnectorPoint2Y">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ConnectorPoint2Y</h4>
<pre>public static final&nbsp;int ConnectorPoint2Y</pre>
<div class="block"><p>
 Controls the vertical position of the callout angle adjustment point.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeAdjustmentType.ConnectorPoint2Y">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="ConnectorBendPositionX">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ConnectorBendPositionX</h4>
<pre>public static final&nbsp;int ConnectorBendPositionX</pre>
<div class="block"><p>
 Controls the horizontal position of the bend in the connector.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeAdjustmentType.ConnectorBendPositionX">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="ConnectorBendPositionY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ConnectorBendPositionY</h4>
<pre>public static final&nbsp;int ConnectorBendPositionY</pre>
<div class="block"><p>
 Controls the vertical position of the bend in the connector.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeAdjustmentType.ConnectorBendPositionY">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="AttachY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>AttachY</h4>
<pre>public static final&nbsp;int AttachY</pre>
<div class="block"><p>
 Controls the vertical attachment point of the figure.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeAdjustmentType.AttachY">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="AttachX">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>AttachX</h4>
<pre>public static final&nbsp;int AttachX</pre>
<div class="block"><p>
 Controls the horizontal attachment point of the figure.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeAdjustmentType.AttachX">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CalloutHeight">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CalloutHeight</h4>
<pre>public static final&nbsp;int CalloutHeight</pre>
<div class="block"><p>
 Controls the vertical size of the callout.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeAdjustmentType.CalloutHeight">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CalloutWidth">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CalloutWidth</h4>
<pre>public static final&nbsp;int CalloutWidth</pre>
<div class="block"><p>
 Controls the horizontal size of the callout.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeAdjustmentType.CalloutWidth">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CalloutSize">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CalloutSize</h4>
<pre>public static final&nbsp;int CalloutSize</pre>
<div class="block"><p>
 Controls the size of the callout.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeAdjustmentType.CalloutSize">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Curvature">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Curvature</h4>
<pre>public static final&nbsp;int Curvature</pre>
<div class="block"><p>
 Curvature of a bent, braces, curved arrow or shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeAdjustmentType.Curvature">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="HorizontalThickness">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>HorizontalThickness</h4>
<pre>public static final&nbsp;int HorizontalThickness</pre>
<div class="block"><p>
 Controls the horizontal thickness of the corner shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeAdjustmentType.HorizontalThickness">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="VerticalThickness">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>VerticalThickness</h4>
<pre>public static final&nbsp;int VerticalThickness</pre>
<div class="block"><p>
 Controls the vertical thickness of the corner shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeAdjustmentType.VerticalThickness">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="VerticalSize">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>VerticalSize</h4>
<pre>public static final&nbsp;int VerticalSize</pre>
<div class="block"><p>
 Controls the vertical size of the shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeAdjustmentType.VerticalSize">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="HorizontalSize">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>HorizontalSize</h4>
<pre>public static final&nbsp;int HorizontalSize</pre>
<div class="block"><p>
 Controls the horizontal size of the shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeAdjustmentType.HorizontalSize">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Top">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>Top</h4>
<pre>public static final&nbsp;int Top</pre>
<div class="block"><p>
 Controls the top side of a shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeAdjustmentType.Top">Constant Field Values</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/Shape.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/ShapeBevel.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/ShapeAdjustmentType.html" target="_top">Frames</a></li>
<li><a href="ShapeAdjustmentType.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.classes.inherited.from.class.com.aspose.ms.System.Enum">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#methods.inherited.from.class.com.aspose.ms.System.Enum">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li>Method</li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><i>Copyright &copy; 2004-2025 Aspose Pty Ltd. All Rights Reserved.</i></small></p>
</body>
</html>
