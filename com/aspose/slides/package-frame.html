<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_421) on Fri Jul 11 05:32:45 CDT 2025 -->
<title>com.aspose.slides (Aspose.Slides for Java (25.7) API Documentation)</title>
<meta name="date" content="2025-07-11">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<h1 class="bar"><a href="../../../com/aspose/slides/package-summary.html" target="classFrame">com.aspose.slides</a></h1>
<div class="indexContainer">
<h2 title="Interfaces">Interfaces</h2>
<ul title="Interfaces">
<li><a href="Convert.GetOutPathCallback.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">Convert.GetOutPathCallback</span></a></li>
<li><a href="ForEach.ForEachLayoutSlideCallback.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ForEach.ForEachLayoutSlideCallback</span></a></li>
<li><a href="ForEach.ForEachMasterSlideCallback.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ForEach.ForEachMasterSlideCallback</span></a></li>
<li><a href="ForEach.ForEachParagraphCallback.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ForEach.ForEachParagraphCallback</span></a></li>
<li><a href="ForEach.ForEachPortionCallback.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ForEach.ForEachPortionCallback</span></a></li>
<li><a href="ForEach.ForEachShapeCallback.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ForEach.ForEachShapeCallback</span></a></li>
<li><a href="ForEach.ForEachSlideCallback.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ForEach.ForEachSlideCallback</span></a></li>
<li><a href="IAccessiblePVIObject.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IAccessiblePVIObject</span></a></li>
<li><a href="IActualLayout.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IActualLayout</span></a></li>
<li><a href="IAdjustValue.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IAdjustValue</span></a></li>
<li><a href="IAdjustValueCollection.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IAdjustValueCollection</span></a></li>
<li><a href="IAIConversation.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IAIConversation</span></a></li>
<li><a href="IAIWebClient.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IAIWebClient</span></a></li>
<li><a href="IAlphaBiLevel.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IAlphaBiLevel</span></a></li>
<li><a href="IAlphaBiLevelEffectiveData.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IAlphaBiLevelEffectiveData</span></a></li>
<li><a href="IAlphaCeiling.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IAlphaCeiling</span></a></li>
<li><a href="IAlphaCeilingEffectiveData.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IAlphaCeilingEffectiveData</span></a></li>
<li><a href="IAlphaFloor.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IAlphaFloor</span></a></li>
<li><a href="IAlphaFloorEffectiveData.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IAlphaFloorEffectiveData</span></a></li>
<li><a href="IAlphaInverse.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IAlphaInverse</span></a></li>
<li><a href="IAlphaInverseEffectiveData.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IAlphaInverseEffectiveData</span></a></li>
<li><a href="IAlphaModulate.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IAlphaModulate</span></a></li>
<li><a href="IAlphaModulateEffectiveData.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IAlphaModulateEffectiveData</span></a></li>
<li><a href="IAlphaModulateFixed.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IAlphaModulateFixed</span></a></li>
<li><a href="IAlphaModulateFixedEffectiveData.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IAlphaModulateFixedEffectiveData</span></a></li>
<li><a href="IAlphaReplace.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IAlphaReplace</span></a></li>
<li><a href="IAlphaReplaceEffectiveData.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IAlphaReplaceEffectiveData</span></a></li>
<li><a href="IAnimationTimeLine.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IAnimationTimeLine</span></a></li>
<li><a href="IAudio.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IAudio</span></a></li>
<li><a href="IAudioCollection.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IAudioCollection</span></a></li>
<li><a href="IAudioFrame.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IAudioFrame</span></a></li>
<li><a href="IAutoShape.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IAutoShape</span></a></li>
<li><a href="IAutoShapeLock.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IAutoShapeLock</span></a></li>
<li><a href="IAxesManager.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IAxesManager</span></a></li>
<li><a href="IAxis.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IAxis</span></a></li>
<li><a href="IAxisFormat.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IAxisFormat</span></a></li>
<li><a href="IBackdrop3DScene.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IBackdrop3DScene</span></a></li>
<li><a href="IBackground.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IBackground</span></a></li>
<li><a href="IBackgroundEffectiveData.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IBackgroundEffectiveData</span></a></li>
<li><a href="IBaseChartValue.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IBaseChartValue</span></a></li>
<li><a href="IBaseHandoutNotesSlideHeaderFooterManag.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IBaseHandoutNotesSlideHeaderFooterManag</span></a></li>
<li><a href="IBaseHeaderFooterManager.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IBaseHeaderFooterManager</span></a></li>
<li><a href="IBasePortionFormat.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IBasePortionFormat</span></a></li>
<li><a href="IBasePortionFormatEffectiveData.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IBasePortionFormatEffectiveData</span></a></li>
<li><a href="IBaseShapeLock.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IBaseShapeLock</span></a></li>
<li><a href="IBaseSlide.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IBaseSlide</span></a></li>
<li><a href="IBaseSlideHeaderFooterManager.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IBaseSlideHeaderFooterManager</span></a></li>
<li><a href="IBaseTableFormatEffectiveData.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IBaseTableFormatEffectiveData</span></a></li>
<li><a href="IBehavior.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IBehavior</span></a></li>
<li><a href="IBehaviorCollection.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IBehaviorCollection</span></a></li>
<li><a href="IBehaviorFactory.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IBehaviorFactory</span></a></li>
<li><a href="IBehaviorProperty.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IBehaviorProperty</span></a></li>
<li><a href="IBehaviorPropertyCollection.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IBehaviorPropertyCollection</span></a></li>
<li><a href="IBiLevel.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IBiLevel</span></a></li>
<li><a href="IBiLevelEffectiveData.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IBiLevelEffectiveData</span></a></li>
<li><a href="IBlobManagementOptions.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IBlobManagementOptions</span></a></li>
<li><a href="IBlur.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IBlur</span></a></li>
<li><a href="IBlurEffectiveData.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IBlurEffectiveData</span></a></li>
<li><a href="IBulkTextFormattable.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IBulkTextFormattable</span></a></li>
<li><a href="IBulletFormat.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IBulletFormat</span></a></li>
<li><a href="IBulletFormatEffectiveData.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IBulletFormatEffectiveData</span></a></li>
<li><a href="ICamera.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ICamera</span></a></li>
<li><a href="ICameraEffectiveData.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ICameraEffectiveData</span></a></li>
<li><a href="ICaptions.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ICaptions</span></a></li>
<li><a href="ICaptionsCollection.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ICaptionsCollection</span></a></li>
<li><a href="ICell.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ICell</span></a></li>
<li><a href="ICellCollection.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ICellCollection</span></a></li>
<li><a href="ICellFormat.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ICellFormat</span></a></li>
<li><a href="ICellFormatEffectiveData.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ICellFormatEffectiveData</span></a></li>
<li><a href="IChart.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IChart</span></a></li>
<li><a href="IChartCategory.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IChartCategory</span></a></li>
<li><a href="IChartCategoryCollection.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IChartCategoryCollection</span></a></li>
<li><a href="IChartCategoryLevelsManager.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IChartCategoryLevelsManager</span></a></li>
<li><a href="IChartCellCollection.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IChartCellCollection</span></a></li>
<li><a href="IChartComponent.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IChartComponent</span></a></li>
<li><a href="IChartData.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IChartData</span></a></li>
<li><a href="IChartDataCell.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IChartDataCell</span></a></li>
<li><a href="IChartDataPoint.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IChartDataPoint</span></a></li>
<li><a href="IChartDataPointCollection.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IChartDataPointCollection</span></a></li>
<li><a href="IChartDataPointLevel.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IChartDataPointLevel</span></a></li>
<li><a href="IChartDataPointLevelsManager.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IChartDataPointLevelsManager</span></a></li>
<li><a href="IChartDataWorkbook.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IChartDataWorkbook</span></a></li>
<li><a href="IChartDataWorksheet.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IChartDataWorksheet</span></a></li>
<li><a href="IChartDataWorksheetCollection.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IChartDataWorksheetCollection</span></a></li>
<li><a href="IChartLinesFormat.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IChartLinesFormat</span></a></li>
<li><a href="IChartParagraphFormat.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IChartParagraphFormat</span></a></li>
<li><a href="IChartPlotArea.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IChartPlotArea</span></a></li>
<li><a href="IChartPortionFormat.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IChartPortionFormat</span></a></li>
<li><a href="IChartSeries.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IChartSeries</span></a></li>
<li><a href="IChartSeriesCollection.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IChartSeriesCollection</span></a></li>
<li><a href="IChartSeriesGroup.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IChartSeriesGroup</span></a></li>
<li><a href="IChartSeriesGroupCollection.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IChartSeriesGroupCollection</span></a></li>
<li><a href="IChartSeriesReadonlyCollection.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IChartSeriesReadonlyCollection</span></a></li>
<li><a href="IChartTextBlockFormat.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IChartTextBlockFormat</span></a></li>
<li><a href="IChartTextFormat.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IChartTextFormat</span></a></li>
<li><a href="IChartTitle.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IChartTitle</span></a></li>
<li><a href="IChartWall.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IChartWall</span></a></li>
<li><a href="IColorChange.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IColorChange</span></a></li>
<li><a href="IColorChangeEffectiveData.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IColorChangeEffectiveData</span></a></li>
<li><a href="IColorEffect.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IColorEffect</span></a></li>
<li><a href="IColorFormat.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IColorFormat</span></a></li>
<li><a href="IColorOffset.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IColorOffset</span></a></li>
<li><a href="IColorOperation.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IColorOperation</span></a></li>
<li><a href="IColorOperationCollection.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IColorOperationCollection</span></a></li>
<li><a href="IColorReplace.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IColorReplace</span></a></li>
<li><a href="IColorReplaceEffectiveData.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IColorReplaceEffectiveData</span></a></li>
<li><a href="IColorScheme.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IColorScheme</span></a></li>
<li><a href="IColorSchemeEffectiveData.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IColorSchemeEffectiveData</span></a></li>
<li><a href="IColumn.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IColumn</span></a></li>
<li><a href="IColumnCollection.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IColumnCollection</span></a></li>
<li><a href="IColumnFormat.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IColumnFormat</span></a></li>
<li><a href="IColumnFormatEffectiveData.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IColumnFormatEffectiveData</span></a></li>
<li><a href="ICommandEffect.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ICommandEffect</span></a></li>
<li><a href="IComment.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IComment</span></a></li>
<li><a href="ICommentAuthor.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ICommentAuthor</span></a></li>
<li><a href="ICommentAuthorCollection.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ICommentAuthorCollection</span></a></li>
<li><a href="ICommentCollection.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ICommentCollection</span></a></li>
<li><a href="ICommonSlideViewProperties.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ICommonSlideViewProperties</span></a></li>
<li><a href="IConnector.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IConnector</span></a></li>
<li><a href="IConnectorLock.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IConnectorLock</span></a></li>
<li><a href="IControl.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IControl</span></a></li>
<li><a href="IControlCollection.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IControlCollection</span></a></li>
<li><a href="IControlPropertiesCollection.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IControlPropertiesCollection</span></a></li>
<li><a href="ICornerDirectionTransition.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ICornerDirectionTransition</span></a></li>
<li><a href="ICustomData.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ICustomData</span></a></li>
<li><a href="ICustomXmlPart.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ICustomXmlPart</span></a></li>
<li><a href="ICustomXmlPartCollection.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ICustomXmlPartCollection</span></a></li>
<li><a href="IDataLabel.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IDataLabel</span></a></li>
<li><a href="IDataLabelCollection.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IDataLabelCollection</span></a></li>
<li><a href="IDataLabelFormat.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IDataLabelFormat</span></a></li>
<li><a href="IDataSourceTypeForErrorBarsCustomValues.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IDataSourceTypeForErrorBarsCustomValues</span></a></li>
<li><a href="IDataTable.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IDataTable</span></a></li>
<li><a href="IDigitalSignature.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IDigitalSignature</span></a></li>
<li><a href="IDigitalSignatureCollection.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IDigitalSignatureCollection</span></a></li>
<li><a href="IDocumentProperties.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IDocumentProperties</span></a></li>
<li><a href="IDoubleChartValue.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IDoubleChartValue</span></a></li>
<li><a href="IDrawingGuide.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IDrawingGuide</span></a></li>
<li><a href="IDrawingGuidesCollection.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IDrawingGuidesCollection</span></a></li>
<li><a href="IDuotone.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IDuotone</span></a></li>
<li><a href="IDuotoneEffectiveData.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IDuotoneEffectiveData</span></a></li>
<li><a href="IEffect.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IEffect</span></a></li>
<li><a href="IEffectEffectiveData.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IEffectEffectiveData</span></a></li>
<li><a href="IEffectFactory.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IEffectFactory</span></a></li>
<li><a href="IEffectFormat.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IEffectFormat</span></a></li>
<li><a href="IEffectFormatEffectiveData.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IEffectFormatEffectiveData</span></a></li>
<li><a href="IEffectParamSource.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IEffectParamSource</span></a></li>
<li><a href="IEffectStyle.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IEffectStyle</span></a></li>
<li><a href="IEffectStyleCollection.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IEffectStyleCollection</span></a></li>
<li><a href="IEffectStyleCollectionEffectiveData.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IEffectStyleCollectionEffectiveData</span></a></li>
<li><a href="IEffectStyleEffectiveData.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IEffectStyleEffectiveData</span></a></li>
<li><a href="IEightDirectionTransition.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IEightDirectionTransition</span></a></li>
<li><a href="IEmbeddedEotFontsHtmlController.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IEmbeddedEotFontsHtmlController</span></a></li>
<li><a href="IEmbeddedWoffFontsHtmlController.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IEmbeddedWoffFontsHtmlController</span></a></li>
<li><a href="IEmptyTransition.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IEmptyTransition</span></a></li>
<li><a href="IErrorBarsCustomValues.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IErrorBarsCustomValues</span></a></li>
<li><a href="IErrorBarsFormat.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IErrorBarsFormat</span></a></li>
<li><a href="IExternalResourceResolver.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IExternalResourceResolver</span></a></li>
<li><a href="IExtraColorScheme.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IExtraColorScheme</span></a></li>
<li><a href="IExtraColorSchemeCollection.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IExtraColorSchemeCollection</span></a></li>
<li><a href="IField.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IField</span></a></li>
<li><a href="IFieldType.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IFieldType</span></a></li>
<li><a href="IFillFormat.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IFillFormat</span></a></li>
<li><a href="IFillFormatCollection.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IFillFormatCollection</span></a></li>
<li><a href="IFillFormatCollectionEffectiveData.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IFillFormatCollectionEffectiveData</span></a></li>
<li><a href="IFillFormatEffectiveData.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IFillFormatEffectiveData</span></a></li>
<li><a href="IFillOverlay.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IFillOverlay</span></a></li>
<li><a href="IFillOverlayEffectiveData.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IFillOverlayEffectiveData</span></a></li>
<li><a href="IFillParamSource.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IFillParamSource</span></a></li>
<li><a href="IFilterEffect.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IFilterEffect</span></a></li>
<li><a href="IFindResultCallback.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IFindResultCallback</span></a></li>
<li><a href="IFlyThroughTransition.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IFlyThroughTransition</span></a></li>
<li><a href="IFontData.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IFontData</span></a></li>
<li><a href="IFontDataFactory.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IFontDataFactory</span></a></li>
<li><a href="IFontFallBackRule.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IFontFallBackRule</span></a></li>
<li><a href="IFontFallBackRulesCollection.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IFontFallBackRulesCollection</span></a></li>
<li><a href="IFonts.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IFonts</span></a></li>
<li><a href="IFontScheme.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IFontScheme</span></a></li>
<li><a href="IFontSchemeEffectiveData.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IFontSchemeEffectiveData</span></a></li>
<li><a href="IFontsEffectiveData.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IFontsEffectiveData</span></a></li>
<li><a href="IFontsLoader.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IFontsLoader</span></a></li>
<li><a href="IFontsManager.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IFontsManager</span></a></li>
<li><a href="IFontSources.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IFontSources</span></a></li>
<li><a href="IFontSubstRule.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IFontSubstRule</span></a></li>
<li><a href="IFontSubstRuleCollection.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IFontSubstRuleCollection</span></a></li>
<li><a href="IFormat.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IFormat</span></a></li>
<li><a href="IFormatFactory.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IFormatFactory</span></a></li>
<li><a href="IFormatScheme.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IFormatScheme</span></a></li>
<li><a href="IFormatSchemeEffectiveData.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IFormatSchemeEffectiveData</span></a></li>
<li><a href="IFormattedTextContainer.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IFormattedTextContainer</span></a></li>
<li><a href="IGenericCloneable.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IGenericCloneable</span></a></li>
<li><a href="IGenericCollection.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IGenericCollection</span></a></li>
<li><a href="IGeometryPath.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IGeometryPath</span></a></li>
<li><a href="IGeometryShape.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IGeometryShape</span></a></li>
<li><a href="IGifOptions.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IGifOptions</span></a></li>
<li><a href="IGlitterTransition.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IGlitterTransition</span></a></li>
<li><a href="IGlobalLayoutSlideCollection.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IGlobalLayoutSlideCollection</span></a></li>
<li><a href="IGlow.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IGlow</span></a></li>
<li><a href="IGlowEffectiveData.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IGlowEffectiveData</span></a></li>
<li><a href="IGradientFormat.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IGradientFormat</span></a></li>
<li><a href="IGradientFormatEffectiveData.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IGradientFormatEffectiveData</span></a></li>
<li><a href="IGradientStop.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IGradientStop</span></a></li>
<li><a href="IGradientStopCollection.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IGradientStopCollection</span></a></li>
<li><a href="IGradientStopCollectionEffectiveData.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IGradientStopCollectionEffectiveData</span></a></li>
<li><a href="IGradientStopEffectiveData.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IGradientStopEffectiveData</span></a></li>
<li><a href="IGraphicalObject.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IGraphicalObject</span></a></li>
<li><a href="IGraphicalObjectLock.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IGraphicalObjectLock</span></a></li>
<li><a href="IGrayScale.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IGrayScale</span></a></li>
<li><a href="IGrayScaleEffectiveData.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IGrayScaleEffectiveData</span></a></li>
<li><a href="IGroupShape.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IGroupShape</span></a></li>
<li><a href="IGroupShapeLock.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IGroupShapeLock</span></a></li>
<li><a href="IHeadingPair.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IHeadingPair</span></a></li>
<li><a href="IHSL.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IHSL</span></a></li>
<li><a href="IHSLEffectiveData.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IHSLEffectiveData</span></a></li>
<li><a href="IHtml5Options.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IHtml5Options</span></a></li>
<li><a href="IHtmlExternalResolver.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IHtmlExternalResolver</span></a></li>
<li><a href="IHtmlFormatter.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IHtmlFormatter</span></a></li>
<li><a href="IHtmlFormattingController.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IHtmlFormattingController</span></a></li>
<li><a href="IHtmlGenerator.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IHtmlGenerator</span></a></li>
<li><a href="IHtmlOptions.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IHtmlOptions</span></a></li>
<li><a href="IHyperlink.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IHyperlink</span></a></li>
<li><a href="IHyperlinkContainer.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IHyperlinkContainer</span></a></li>
<li><a href="IHyperlinkManager.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IHyperlinkManager</span></a></li>
<li><a href="IHyperlinkQueries.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IHyperlinkQueries</span></a></li>
<li><a href="IImage.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IImage</span></a></li>
<li><a href="IImageCollection.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IImageCollection</span></a></li>
<li><a href="IImageTransformOCollectionEffectiveData.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IImageTransformOCollectionEffectiveData</span></a></li>
<li><a href="IImageTransformOperation.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IImageTransformOperation</span></a></li>
<li><a href="IImageTransformOperationCollection.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IImageTransformOperationCollection</span></a></li>
<li><a href="IImageTransformOperationFactory.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IImageTransformOperationFactory</span></a></li>
<li><a href="IInk.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IInk</span></a></li>
<li><a href="IInkActions.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IInkActions</span></a></li>
<li><a href="IInkBrush.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IInkBrush</span></a></li>
<li><a href="IInkOptions.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IInkOptions</span></a></li>
<li><a href="IInkTrace.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IInkTrace</span></a></li>
<li><a href="IInnerShadow.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IInnerShadow</span></a></li>
<li><a href="IInnerShadowEffectiveData.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IInnerShadowEffectiveData</span></a></li>
<li><a href="IInOutTransition.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IInOutTransition</span></a></li>
<li><a href="IInterruptionToken.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IInterruptionToken</span></a></li>
<li><a href="IInterruptionTokenSource.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IInterruptionTokenSource</span></a></li>
<li><a href="IKnownIssueWarningInfo.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IKnownIssueWarningInfo</span></a></li>
<li><a href="ILayoutable.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ILayoutable</span></a></li>
<li><a href="ILayoutPlaceholderManager.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ILayoutPlaceholderManager</span></a></li>
<li><a href="ILayoutSlide.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ILayoutSlide</span></a></li>
<li><a href="ILayoutSlideCollection.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ILayoutSlideCollection</span></a></li>
<li><a href="ILayoutSlideHeaderFooterManager.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ILayoutSlideHeaderFooterManager</span></a></li>
<li><a href="ILeftRightDirectionTransition.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ILeftRightDirectionTransition</span></a></li>
<li><a href="ILegacyDiagram.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ILegacyDiagram</span></a></li>
<li><a href="ILegend.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ILegend</span></a></li>
<li><a href="ILegendEntryCollection.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ILegendEntryCollection</span></a></li>
<li><a href="ILegendEntryProperties.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ILegendEntryProperties</span></a></li>
<li><a href="ILicense.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ILicense</span></a></li>
<li><a href="ILightRig.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ILightRig</span></a></li>
<li><a href="ILightRigEffectiveData.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ILightRigEffectiveData</span></a></li>
<li><a href="ILineFillFormat.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ILineFillFormat</span></a></li>
<li><a href="ILineFillFormatEffectiveData.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ILineFillFormatEffectiveData</span></a></li>
<li><a href="ILineFormat.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ILineFormat</span></a></li>
<li><a href="ILineFormatCollection.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ILineFormatCollection</span></a></li>
<li><a href="ILineFormatCollectionEffectiveData.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ILineFormatCollectionEffectiveData</span></a></li>
<li><a href="ILineFormatEffectiveData.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ILineFormatEffectiveData</span></a></li>
<li><a href="ILineParamSource.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ILineParamSource</span></a></li>
<li><a href="ILinkEmbedController.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ILinkEmbedController</span></a></li>
<li><a href="ILoadOptions.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ILoadOptions</span></a></li>
<li><a href="ILuminance.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ILuminance</span></a></li>
<li><a href="ILuminanceEffectiveData.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ILuminanceEffectiveData</span></a></li>
<li><a href="IMarker.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IMarker</span></a></li>
<li><a href="IMasterHandoutSlide.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IMasterHandoutSlide</span></a></li>
<li><a href="IMasterHandoutSlideHeaderFooterManager.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IMasterHandoutSlideHeaderFooterManager</span></a></li>
<li><a href="IMasterHandoutSlideManager.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IMasterHandoutSlideManager</span></a></li>
<li><a href="IMasterLayoutSlideCollection.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IMasterLayoutSlideCollection</span></a></li>
<li><a href="IMasterNotesSlide.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IMasterNotesSlide</span></a></li>
<li><a href="IMasterNotesSlideHeaderFooterManager.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IMasterNotesSlideHeaderFooterManager</span></a></li>
<li><a href="IMasterNotesSlideManager.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IMasterNotesSlideManager</span></a></li>
<li><a href="IMasterSlide.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IMasterSlide</span></a></li>
<li><a href="IMasterSlideCollection.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IMasterSlideCollection</span></a></li>
<li><a href="IMasterSlideHeaderFooterManager.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IMasterSlideHeaderFooterManager</span></a></li>
<li><a href="IMasterTheme.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IMasterTheme</span></a></li>
<li><a href="IMasterThemeable.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IMasterThemeable</span></a></li>
<li><a href="IMasterThemeManager.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IMasterThemeManager</span></a></li>
<li><a href="IMathAccent.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IMathAccent</span></a></li>
<li><a href="IMathAccentFactory.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IMathAccentFactory</span></a></li>
<li><a href="IMathArray.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IMathArray</span></a></li>
<li><a href="IMathArrayFactory.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IMathArrayFactory</span></a></li>
<li><a href="IMathBar.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IMathBar</span></a></li>
<li><a href="IMathBarFactory.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IMathBarFactory</span></a></li>
<li><a href="IMathBlock.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IMathBlock</span></a></li>
<li><a href="IMathBlockCollection.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IMathBlockCollection</span></a></li>
<li><a href="IMathBlockFactory.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IMathBlockFactory</span></a></li>
<li><a href="IMathBorderBox.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IMathBorderBox</span></a></li>
<li><a href="IMathBorderBoxFactory.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IMathBorderBoxFactory</span></a></li>
<li><a href="IMathBox.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IMathBox</span></a></li>
<li><a href="IMathBoxFactory.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IMathBoxFactory</span></a></li>
<li><a href="IMathDelimiter.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IMathDelimiter</span></a></li>
<li><a href="IMathDelimiterFactory.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IMathDelimiterFactory</span></a></li>
<li><a href="IMathElement.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IMathElement</span></a></li>
<li><a href="IMathElementCollection.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IMathElementCollection</span></a></li>
<li><a href="IMathematicalText.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IMathematicalText</span></a></li>
<li><a href="IMathematicalTextFactory.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IMathematicalTextFactory</span></a></li>
<li><a href="IMathFraction.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IMathFraction</span></a></li>
<li><a href="IMathFractionFactory.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IMathFractionFactory</span></a></li>
<li><a href="IMathFunction.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IMathFunction</span></a></li>
<li><a href="IMathFunctionFactory.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IMathFunctionFactory</span></a></li>
<li><a href="IMathGroupingCharacter.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IMathGroupingCharacter</span></a></li>
<li><a href="IMathGroupingCharacterFactory.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IMathGroupingCharacterFactory</span></a></li>
<li><a href="IMathLeftSubSuperscriptElement.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IMathLeftSubSuperscriptElement</span></a></li>
<li><a href="IMathLimit.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IMathLimit</span></a></li>
<li><a href="IMathLimitFactory.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IMathLimitFactory</span></a></li>
<li><a href="IMathMatrix.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IMathMatrix</span></a></li>
<li><a href="IMathMatrixFactory.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IMathMatrixFactory</span></a></li>
<li><a href="IMathNaryOperator.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IMathNaryOperator</span></a></li>
<li><a href="IMathNaryOperatorFactory.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IMathNaryOperatorFactory</span></a></li>
<li><a href="IMathNaryOperatorProperties.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IMathNaryOperatorProperties</span></a></li>
<li><a href="IMathParagraph.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IMathParagraph</span></a></li>
<li><a href="IMathParagraphFactory.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IMathParagraphFactory</span></a></li>
<li><a href="IMathPortion.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IMathPortion</span></a></li>
<li><a href="IMathRadical.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IMathRadical</span></a></li>
<li><a href="IMathRadicalFactory.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IMathRadicalFactory</span></a></li>
<li><a href="IMathRightSubSuperscriptElement.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IMathRightSubSuperscriptElement</span></a></li>
<li><a href="IMathRightSubSuperscriptElementFactory.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IMathRightSubSuperscriptElementFactory</span></a></li>
<li><a href="IMathSubscriptElement.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IMathSubscriptElement</span></a></li>
<li><a href="IMathSubscriptElementFactory.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IMathSubscriptElementFactory</span></a></li>
<li><a href="IMathSuperscriptElement.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IMathSuperscriptElement</span></a></li>
<li><a href="IMathSuperscriptElementFactory.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IMathSuperscriptElementFactory</span></a></li>
<li><a href="IMetered.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IMetered</span></a></li>
<li><a href="IModernComment.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IModernComment</span></a></li>
<li><a href="IMorphTransition.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IMorphTransition</span></a></li>
<li><a href="IMotionCmdPath.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IMotionCmdPath</span></a></li>
<li><a href="IMotionEffect.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IMotionEffect</span></a></li>
<li><a href="IMotionPath.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IMotionPath</span></a></li>
<li><a href="IMultipleCellChartValue.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IMultipleCellChartValue</span></a></li>
<li><a href="INormalViewProperties.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">INormalViewProperties</span></a></li>
<li><a href="INormalViewRestoredProperties.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">INormalViewRestoredProperties</span></a></li>
<li><a href="INotesCommentsLayoutingOptions.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">INotesCommentsLayoutingOptions</span></a></li>
<li><a href="INotesSize.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">INotesSize</span></a></li>
<li><a href="INotesSlide.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">INotesSlide</span></a></li>
<li><a href="INotesSlideHeaderFooterManager.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">INotesSlideHeaderFooterManager</span></a></li>
<li><a href="INotesSlideManager.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">INotesSlideManager</span></a></li>
<li><a href="INotImplementedWarningInfo.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">INotImplementedWarningInfo</span></a></li>
<li><a href="IObsoletePresLockingBehaviorWarningInfo.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IObsoletePresLockingBehaviorWarningInfo</span></a></li>
<li><a href="IOleEmbeddedDataInfo.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IOleEmbeddedDataInfo</span></a></li>
<li><a href="IOleObjectFrame.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IOleObjectFrame</span></a></li>
<li><a href="IOptionalBlackTransition.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IOptionalBlackTransition</span></a></li>
<li><a href="IOrientationTransition.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IOrientationTransition</span></a></li>
<li><a href="IOuterShadow.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IOuterShadow</span></a></li>
<li><a href="IOuterShadowEffectiveData.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IOuterShadowEffectiveData</span></a></li>
<li><a href="IOutputFile.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IOutputFile</span></a></li>
<li><a href="IOutputSaver.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IOutputSaver</span></a></li>
<li><a href="IOverridableText.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IOverridableText</span></a></li>
<li><a href="IOverrideTheme.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IOverrideTheme</span></a></li>
<li><a href="IOverrideThemeable.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IOverrideThemeable</span></a></li>
<li><a href="IOverrideThemeManager.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IOverrideThemeManager</span></a></li>
<li><a href="IParagraph.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IParagraph</span></a></li>
<li><a href="IParagraphCollection.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IParagraphCollection</span></a></li>
<li><a href="IParagraphFactory.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IParagraphFactory</span></a></li>
<li><a href="IParagraphFormat.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IParagraphFormat</span></a></li>
<li><a href="IParagraphFormatEffectiveData.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IParagraphFormatEffectiveData</span></a></li>
<li><a href="IPathSegment.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IPathSegment</span></a></li>
<li><a href="IPatternFormat.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IPatternFormat</span></a></li>
<li><a href="IPatternFormatEffectiveData.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IPatternFormatEffectiveData</span></a></li>
<li><a href="IPdfOptions.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IPdfOptions</span></a></li>
<li><a href="IPictureEffectiveData.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IPictureEffectiveData</span></a></li>
<li><a href="IPictureFillFormat.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IPictureFillFormat</span></a></li>
<li><a href="IPictureFillFormatEffectiveData.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IPictureFillFormatEffectiveData</span></a></li>
<li><a href="IPictureFrame.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IPictureFrame</span></a></li>
<li><a href="IPictureFrameLock.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IPictureFrameLock</span></a></li>
<li><a href="IPieSplitCustomPointCollection.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IPieSplitCustomPointCollection</span></a></li>
<li><a href="IPlaceholder.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IPlaceholder</span></a></li>
<li><a href="IPoint.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IPoint</span></a></li>
<li><a href="IPointCollection.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IPointCollection</span></a></li>
<li><a href="IPortion.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IPortion</span></a></li>
<li><a href="IPortionCollection.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IPortionCollection</span></a></li>
<li><a href="IPortionFactory.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IPortionFactory</span></a></li>
<li><a href="IPortionFormat.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IPortionFormat</span></a></li>
<li><a href="IPortionFormatEffectiveData.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IPortionFormatEffectiveData</span></a></li>
<li><a href="IPPImage.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IPPImage</span></a></li>
<li><a href="IPptOptions.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IPptOptions</span></a></li>
<li><a href="IPptxOptions.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IPptxOptions</span></a></li>
<li><a href="IPresentation.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IPresentation</span></a></li>
<li><a href="IPresentationAnimationPlayer.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IPresentationAnimationPlayer</span></a></li>
<li><a href="IPresentationComponent.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IPresentationComponent</span></a></li>
<li><a href="IPresentationFactory.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IPresentationFactory</span></a></li>
<li><a href="IPresentationHeaderFooterManager.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IPresentationHeaderFooterManager</span></a></li>
<li><a href="IPresentationInfo.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IPresentationInfo</span></a></li>
<li><a href="IPresentationSignedWarningInfo.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IPresentationSignedWarningInfo</span></a></li>
<li><a href="IPresentationText.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IPresentationText</span></a></li>
<li><a href="IPresetShadow.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IPresetShadow</span></a></li>
<li><a href="IPresetShadowEffectiveData.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IPresetShadowEffectiveData</span></a></li>
<li><a href="IProgressCallback.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IProgressCallback</span></a></li>
<li><a href="IPropertyEffect.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IPropertyEffect</span></a></li>
<li><a href="IProtectionManager.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IProtectionManager</span></a></li>
<li><a href="IReflection.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IReflection</span></a></li>
<li><a href="IReflectionEffectiveData.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IReflectionEffectiveData</span></a></li>
<li><a href="IRenderingOptions.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IRenderingOptions</span></a></li>
<li><a href="IResourceLoadingArgs.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IResourceLoadingArgs</span></a></li>
<li><a href="IResourceLoadingCallback.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IResourceLoadingCallback</span></a></li>
<li><a href="IResponsiveHtmlController.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IResponsiveHtmlController</span></a></li>
<li><a href="IRevealTransition.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IRevealTransition</span></a></li>
<li><a href="IRippleTransition.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IRippleTransition</span></a></li>
<li><a href="IRotation3D.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IRotation3D</span></a></li>
<li><a href="IRotationEffect.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IRotationEffect</span></a></li>
<li><a href="IRow.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IRow</span></a></li>
<li><a href="IRowCollection.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IRowCollection</span></a></li>
<li><a href="IRowFormat.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IRowFormat</span></a></li>
<li><a href="IRowFormatEffectiveData.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IRowFormatEffectiveData</span></a></li>
<li><a href="ISaveOptions.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ISaveOptions</span></a></li>
<li><a href="ISaveOptionsFactory.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ISaveOptionsFactory</span></a></li>
<li><a href="IScaleEffect.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IScaleEffect</span></a></li>
<li><a href="ISection.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ISection</span></a></li>
<li><a href="ISectionCollection.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ISectionCollection</span></a></li>
<li><a href="ISectionSlideCollection.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ISectionSlideCollection</span></a></li>
<li><a href="ISectionZoomFrame.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ISectionZoomFrame</span></a></li>
<li><a href="ISequence.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ISequence</span></a></li>
<li><a href="ISequenceCollection.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ISequenceCollection</span></a></li>
<li><a href="ISetEffect.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ISetEffect</span></a></li>
<li><a href="IShape.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IShape</span></a></li>
<li><a href="IShapeBevel.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IShapeBevel</span></a></li>
<li><a href="IShapeBevelEffectiveData.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IShapeBevelEffectiveData</span></a></li>
<li><a href="IShapeCollection.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IShapeCollection</span></a></li>
<li><a href="IShapeElement.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IShapeElement</span></a></li>
<li><a href="IShapeFrame.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IShapeFrame</span></a></li>
<li><a href="IShapeStyle.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IShapeStyle</span></a></li>
<li><a href="IShredTransition.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IShredTransition</span></a></li>
<li><a href="ISideDirectionTransition.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ISideDirectionTransition</span></a></li>
<li><a href="ISingleCellChartValue.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ISingleCellChartValue</span></a></li>
<li><a href="ISketchFormat.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ISketchFormat</span></a></li>
<li><a href="ISketchFormatEffectiveData.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ISketchFormatEffectiveData</span></a></li>
<li><a href="ISlide.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ISlide</span></a></li>
<li><a href="ISlideCollection.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ISlideCollection</span></a></li>
<li><a href="ISlideComponent.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ISlideComponent</span></a></li>
<li><a href="ISlideHeaderFooterManager.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ISlideHeaderFooterManager</span></a></li>
<li><a href="ISlideImageFormat.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ISlideImageFormat</span></a></li>
<li><a href="ISlideShowTransition.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ISlideShowTransition</span></a></li>
<li><a href="ISlideSize.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ISlideSize</span></a></li>
<li><a href="ISlidesLayoutOptions.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ISlidesLayoutOptions</span></a></li>
<li><a href="ISlidesPicture.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ISlidesPicture</span></a></li>
<li><a href="ISlideText.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ISlideText</span></a></li>
<li><a href="ISmartArt.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ISmartArt</span></a></li>
<li><a href="ISmartArtNode.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ISmartArtNode</span></a></li>
<li><a href="ISmartArtNodeCollection.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ISmartArtNodeCollection</span></a></li>
<li><a href="ISmartArtShape.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ISmartArtShape</span></a></li>
<li><a href="ISmartArtShapeCollection.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ISmartArtShapeCollection</span></a></li>
<li><a href="ISoftEdge.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ISoftEdge</span></a></li>
<li><a href="ISoftEdgeEffectiveData.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ISoftEdgeEffectiveData</span></a></li>
<li><a href="ISplitTransition.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ISplitTransition</span></a></li>
<li><a href="ISpreadsheetOptions.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ISpreadsheetOptions</span></a></li>
<li><a href="IStringChartValue.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IStringChartValue</span></a></li>
<li><a href="IStringOrDoubleChartValue.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IStringOrDoubleChartValue</span></a></li>
<li><a href="ISummaryZoomFrame.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ISummaryZoomFrame</span></a></li>
<li><a href="ISummaryZoomSection.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ISummaryZoomSection</span></a></li>
<li><a href="ISummaryZoomSectionCollection.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ISummaryZoomSectionCollection</span></a></li>
<li><a href="ISvgImage.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ISvgImage</span></a></li>
<li><a href="ISVGOptions.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ISVGOptions</span></a></li>
<li><a href="ISvgShape.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ISvgShape</span></a></li>
<li><a href="ISvgShapeAndTextFormattingController.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ISvgShapeAndTextFormattingController</span></a></li>
<li><a href="ISvgShapeFormattingController.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ISvgShapeFormattingController</span></a></li>
<li><a href="ISvgTSpan.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ISvgTSpan</span></a></li>
<li><a href="ISwfOptions.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ISwfOptions</span></a></li>
<li><a href="ITab.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ITab</span></a></li>
<li><a href="ITabCollection.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ITabCollection</span></a></li>
<li><a href="ITabEffectiveData.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ITabEffectiveData</span></a></li>
<li><a href="ITabFactory.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ITabFactory</span></a></li>
<li><a href="ITable.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ITable</span></a></li>
<li><a href="ITableFormat.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ITableFormat</span></a></li>
<li><a href="ITableFormatEffectiveData.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ITableFormatEffectiveData</span></a></li>
<li><a href="ITagCollection.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ITagCollection</span></a></li>
<li><a href="ITemplateEngine.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ITemplateEngine</span></a></li>
<li><a href="ITextAnimation.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ITextAnimation</span></a></li>
<li><a href="ITextAnimationCollection.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ITextAnimationCollection</span></a></li>
<li><a href="ITextFrame.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ITextFrame</span></a></li>
<li><a href="ITextFrameFormat.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ITextFrameFormat</span></a></li>
<li><a href="ITextFrameFormatEffectiveData.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ITextFrameFormatEffectiveData</span></a></li>
<li><a href="ITextHighlightingOptions.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ITextHighlightingOptions</span></a></li>
<li><a href="ITextSearchOptions.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ITextSearchOptions</span></a></li>
<li><a href="ITextStyle.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ITextStyle</span></a></li>
<li><a href="ITextStyleEffectiveData.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ITextStyleEffectiveData</span></a></li>
<li><a href="ITextToHtmlConversionOptions.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ITextToHtmlConversionOptions</span></a></li>
<li><a href="ITheme.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ITheme</span></a></li>
<li><a href="IThemeable.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IThemeable</span></a></li>
<li><a href="IThemeEffectiveData.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IThemeEffectiveData</span></a></li>
<li><a href="IThemeManager.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IThemeManager</span></a></li>
<li><a href="IThreeDFormat.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IThreeDFormat</span></a></li>
<li><a href="IThreeDFormatEffectiveData.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IThreeDFormatEffectiveData</span></a></li>
<li><a href="IThreeDParamSource.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IThreeDParamSource</span></a></li>
<li><a href="ITiffOptions.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ITiffOptions</span></a></li>
<li><a href="ITiming.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ITiming</span></a></li>
<li><a href="ITint.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ITint</span></a></li>
<li><a href="ITintEffectiveData.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ITintEffectiveData</span></a></li>
<li><a href="ITransitionValueBase.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ITransitionValueBase</span></a></li>
<li><a href="ITrendline.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ITrendline</span></a></li>
<li><a href="ITrendlineCollection.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">ITrendlineCollection</span></a></li>
<li><a href="IUpDownBarsManager.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IUpDownBarsManager</span></a></li>
<li><a href="IVbaModule.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IVbaModule</span></a></li>
<li><a href="IVbaModuleCollection.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IVbaModuleCollection</span></a></li>
<li><a href="IVbaProject.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IVbaProject</span></a></li>
<li><a href="IVbaProjectFactory.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IVbaProjectFactory</span></a></li>
<li><a href="IVbaReference.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IVbaReference</span></a></li>
<li><a href="IVbaReferenceCollection.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IVbaReferenceCollection</span></a></li>
<li><a href="IVbaReferenceFactory.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IVbaReferenceFactory</span></a></li>
<li><a href="IVbaReferenceOleTwiddledTypeLib.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IVbaReferenceOleTwiddledTypeLib</span></a></li>
<li><a href="IVbaReferenceOleTypeLib.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IVbaReferenceOleTypeLib</span></a></li>
<li><a href="IVbaReferenceProject.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IVbaReferenceProject</span></a></li>
<li><a href="IVideo.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IVideo</span></a></li>
<li><a href="IVideoCollection.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IVideoCollection</span></a></li>
<li><a href="IVideoFrame.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IVideoFrame</span></a></li>
<li><a href="IVideoPlayerHtmlController.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IVideoPlayerHtmlController</span></a></li>
<li><a href="IVideoPlayerHtmlControllerFactory.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IVideoPlayerHtmlControllerFactory</span></a></li>
<li><a href="IViewProperties.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IViewProperties</span></a></li>
<li><a href="IWarningCallback.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IWarningCallback</span></a></li>
<li><a href="IWarningInfo.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IWarningInfo</span></a></li>
<li><a href="IWheelTransition.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IWheelTransition</span></a></li>
<li><a href="IXamlOptions.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IXamlOptions</span></a></li>
<li><a href="IXamlOutputSaver.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IXamlOutputSaver</span></a></li>
<li><a href="IXpsOptions.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IXpsOptions</span></a></li>
<li><a href="IZoomFrame.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IZoomFrame</span></a></li>
<li><a href="IZoomObject.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">IZoomObject</span></a></li>
<li><a href="PresentationAnimationsGenerator.NewAnimation.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">PresentationAnimationsGenerator.NewAnimation</span></a></li>
<li><a href="PresentationPlayer.FrameTick.html" title="interface in com.aspose.slides" target="classFrame"><span class="interfaceName">PresentationPlayer.FrameTick</span></a></li>
</ul>
<h2 title="Classes">Classes</h2>
<ul title="Classes">
<li><a href="AdjustValue.html" title="class in com.aspose.slides" target="classFrame">AdjustValue</a></li>
<li><a href="AdjustValueCollection.html" title="class in com.aspose.slides" target="classFrame">AdjustValueCollection</a></li>
<li><a href="AfterAnimationType.html" title="class in com.aspose.slides" target="classFrame">AfterAnimationType</a></li>
<li><a href="AlphaBiLevel.html" title="class in com.aspose.slides" target="classFrame">AlphaBiLevel</a></li>
<li><a href="AlphaCeiling.html" title="class in com.aspose.slides" target="classFrame">AlphaCeiling</a></li>
<li><a href="AlphaFloor.html" title="class in com.aspose.slides" target="classFrame">AlphaFloor</a></li>
<li><a href="AlphaInverse.html" title="class in com.aspose.slides" target="classFrame">AlphaInverse</a></li>
<li><a href="AlphaModulate.html" title="class in com.aspose.slides" target="classFrame">AlphaModulate</a></li>
<li><a href="AlphaModulateFixed.html" title="class in com.aspose.slides" target="classFrame">AlphaModulateFixed</a></li>
<li><a href="AlphaReplace.html" title="class in com.aspose.slides" target="classFrame">AlphaReplace</a></li>
<li><a href="AnimateTextType.html" title="class in com.aspose.slides" target="classFrame">AnimateTextType</a></li>
<li><a href="AnimationTimeLine.html" title="class in com.aspose.slides" target="classFrame">AnimationTimeLine</a></li>
<li><a href="Audio.html" title="class in com.aspose.slides" target="classFrame">Audio</a></li>
<li><a href="AudioCollection.html" title="class in com.aspose.slides" target="classFrame">AudioCollection</a></li>
<li><a href="AudioFrame.html" title="class in com.aspose.slides" target="classFrame">AudioFrame</a></li>
<li><a href="AudioPlayModePreset.html" title="class in com.aspose.slides" target="classFrame">AudioPlayModePreset</a></li>
<li><a href="AudioVolumeMode.html" title="class in com.aspose.slides" target="classFrame">AudioVolumeMode</a></li>
<li><a href="AutoShape.html" title="class in com.aspose.slides" target="classFrame">AutoShape</a></li>
<li><a href="AutoShapeLock.html" title="class in com.aspose.slides" target="classFrame">AutoShapeLock</a></li>
<li><a href="AxesManager.html" title="class in com.aspose.slides" target="classFrame">AxesManager</a></li>
<li><a href="Axis.html" title="class in com.aspose.slides" target="classFrame">Axis</a></li>
<li><a href="AxisAggregationType.html" title="class in com.aspose.slides" target="classFrame">AxisAggregationType</a></li>
<li><a href="AxisFormat.html" title="class in com.aspose.slides" target="classFrame">AxisFormat</a></li>
<li><a href="AxisPositionType.html" title="class in com.aspose.slides" target="classFrame">AxisPositionType</a></li>
<li><a href="Backdrop3DScene.html" title="class in com.aspose.slides" target="classFrame">Backdrop3DScene</a></li>
<li><a href="Background.html" title="class in com.aspose.slides" target="classFrame">Background</a></li>
<li><a href="BackgroundType.html" title="class in com.aspose.slides" target="classFrame">BackgroundType</a></li>
<li><a href="BaseChartValue.html" title="class in com.aspose.slides" target="classFrame">BaseChartValue</a></li>
<li><a href="BaseHandoutNotesSlideHeaderFooterManager.html" title="class in com.aspose.slides" target="classFrame">BaseHandoutNotesSlideHeaderFooterManager</a></li>
<li><a href="BaseHeaderFooterManager.html" title="class in com.aspose.slides" target="classFrame">BaseHeaderFooterManager</a></li>
<li><a href="BaseOverrideThemeManager.html" title="class in com.aspose.slides" target="classFrame">BaseOverrideThemeManager</a></li>
<li><a href="BasePortionFormat.html" title="class in com.aspose.slides" target="classFrame">BasePortionFormat</a></li>
<li><a href="BaseScript.html" title="class in com.aspose.slides" target="classFrame">BaseScript</a></li>
<li><a href="BaseShapeLock.html" title="class in com.aspose.slides" target="classFrame">BaseShapeLock</a></li>
<li><a href="BaseSlide.html" title="class in com.aspose.slides" target="classFrame">BaseSlide</a></li>
<li><a href="BaseSlideHeaderFooterManager.html" title="class in com.aspose.slides" target="classFrame">BaseSlideHeaderFooterManager</a></li>
<li><a href="BaseThemeManager.html" title="class in com.aspose.slides" target="classFrame">BaseThemeManager</a></li>
<li><a href="Behavior.html" title="class in com.aspose.slides" target="classFrame">Behavior</a></li>
<li><a href="BehaviorAccumulateType.html" title="class in com.aspose.slides" target="classFrame">BehaviorAccumulateType</a></li>
<li><a href="BehaviorAdditiveType.html" title="class in com.aspose.slides" target="classFrame">BehaviorAdditiveType</a></li>
<li><a href="BehaviorCollection.html" title="class in com.aspose.slides" target="classFrame">BehaviorCollection</a></li>
<li><a href="BehaviorFactory.html" title="class in com.aspose.slides" target="classFrame">BehaviorFactory</a></li>
<li><a href="BehaviorProperty.html" title="class in com.aspose.slides" target="classFrame">BehaviorProperty</a></li>
<li><a href="BehaviorPropertyCollection.html" title="class in com.aspose.slides" target="classFrame">BehaviorPropertyCollection</a></li>
<li><a href="BevelPresetType.html" title="class in com.aspose.slides" target="classFrame">BevelPresetType</a></li>
<li><a href="BiLevel.html" title="class in com.aspose.slides" target="classFrame">BiLevel</a></li>
<li><a href="BlackWhiteConversionMode.html" title="class in com.aspose.slides" target="classFrame">BlackWhiteConversionMode</a></li>
<li><a href="BlackWhiteMode.html" title="class in com.aspose.slides" target="classFrame">BlackWhiteMode</a></li>
<li><a href="BlobManagementOptions.html" title="class in com.aspose.slides" target="classFrame">BlobManagementOptions</a></li>
<li><a href="Blur.html" title="class in com.aspose.slides" target="classFrame">Blur</a></li>
<li><a href="BrowsedAtKiosk.html" title="class in com.aspose.slides" target="classFrame">BrowsedAtKiosk</a></li>
<li><a href="BrowsedByIndividual.html" title="class in com.aspose.slides" target="classFrame">BrowsedByIndividual</a></li>
<li><a href="BubbleSizeRepresentationType.html" title="class in com.aspose.slides" target="classFrame">BubbleSizeRepresentationType</a></li>
<li><a href="BuildType.html" title="class in com.aspose.slides" target="classFrame">BuildType</a></li>
<li><a href="BuildVersionInfo.html" title="class in com.aspose.slides" target="classFrame">BuildVersionInfo</a></li>
<li><a href="BulletFormat.html" title="class in com.aspose.slides" target="classFrame">BulletFormat</a></li>
<li><a href="BulletType.html" title="class in com.aspose.slides" target="classFrame">BulletType</a></li>
<li><a href="Camera.html" title="class in com.aspose.slides" target="classFrame">Camera</a></li>
<li><a href="CameraPresetType.html" title="class in com.aspose.slides" target="classFrame">CameraPresetType</a></li>
<li><a href="Captions.html" title="class in com.aspose.slides" target="classFrame">Captions</a></li>
<li><a href="CaptionsCollection.html" title="class in com.aspose.slides" target="classFrame">CaptionsCollection</a></li>
<li><a href="CategoryAxisType.html" title="class in com.aspose.slides" target="classFrame">CategoryAxisType</a></li>
<li><a href="Cell.html" title="class in com.aspose.slides" target="classFrame">Cell</a></li>
<li><a href="CellCollection.html" title="class in com.aspose.slides" target="classFrame">CellCollection</a></li>
<li><a href="CellFormat.html" title="class in com.aspose.slides" target="classFrame">CellFormat</a></li>
<li><a href="Chart.html" title="class in com.aspose.slides" target="classFrame">Chart</a></li>
<li><a href="ChartCategory.html" title="class in com.aspose.slides" target="classFrame">ChartCategory</a></li>
<li><a href="ChartCategoryCollection.html" title="class in com.aspose.slides" target="classFrame">ChartCategoryCollection</a></li>
<li><a href="ChartCategoryLevelsManager.html" title="class in com.aspose.slides" target="classFrame">ChartCategoryLevelsManager</a></li>
<li><a href="ChartCellCollection.html" title="class in com.aspose.slides" target="classFrame">ChartCellCollection</a></li>
<li><a href="ChartData.html" title="class in com.aspose.slides" target="classFrame">ChartData</a></li>
<li><a href="ChartDataCell.html" title="class in com.aspose.slides" target="classFrame">ChartDataCell</a></li>
<li><a href="ChartDataPoint.html" title="class in com.aspose.slides" target="classFrame">ChartDataPoint</a></li>
<li><a href="ChartDataPointCollection.html" title="class in com.aspose.slides" target="classFrame">ChartDataPointCollection</a></li>
<li><a href="ChartDataPointLevel.html" title="class in com.aspose.slides" target="classFrame">ChartDataPointLevel</a></li>
<li><a href="ChartDataPointLevelsManager.html" title="class in com.aspose.slides" target="classFrame">ChartDataPointLevelsManager</a></li>
<li><a href="ChartDataSourceType.html" title="class in com.aspose.slides" target="classFrame">ChartDataSourceType</a></li>
<li><a href="ChartDataWorkbook.html" title="class in com.aspose.slides" target="classFrame">ChartDataWorkbook</a></li>
<li><a href="ChartDataWorksheet.html" title="class in com.aspose.slides" target="classFrame">ChartDataWorksheet</a></li>
<li><a href="ChartDataWorksheetCollection.html" title="class in com.aspose.slides" target="classFrame">ChartDataWorksheetCollection</a></li>
<li><a href="ChartLinesFormat.html" title="class in com.aspose.slides" target="classFrame">ChartLinesFormat</a></li>
<li><a href="ChartPlotArea.html" title="class in com.aspose.slides" target="classFrame">ChartPlotArea</a></li>
<li><a href="ChartPortionFormat.html" title="class in com.aspose.slides" target="classFrame">ChartPortionFormat</a></li>
<li><a href="ChartSeries.html" title="class in com.aspose.slides" target="classFrame">ChartSeries</a></li>
<li><a href="ChartSeriesCollection.html" title="class in com.aspose.slides" target="classFrame">ChartSeriesCollection</a></li>
<li><a href="ChartSeriesGroup.html" title="class in com.aspose.slides" target="classFrame">ChartSeriesGroup</a></li>
<li><a href="ChartShapeType.html" title="class in com.aspose.slides" target="classFrame">ChartShapeType</a></li>
<li><a href="ChartTextFormat.html" title="class in com.aspose.slides" target="classFrame">ChartTextFormat</a></li>
<li><a href="ChartThemeManager.html" title="class in com.aspose.slides" target="classFrame">ChartThemeManager</a></li>
<li><a href="ChartTitle.html" title="class in com.aspose.slides" target="classFrame">ChartTitle</a></li>
<li><a href="ChartType.html" title="class in com.aspose.slides" target="classFrame">ChartType</a></li>
<li><a href="ChartTypeCharacterizer.html" title="class in com.aspose.slides" target="classFrame">ChartTypeCharacterizer</a></li>
<li><a href="ChartWall.html" title="class in com.aspose.slides" target="classFrame">ChartWall</a></li>
<li><a href="Collect.html" title="class in com.aspose.slides" target="classFrame">Collect</a></li>
<li><a href="ColorChange.html" title="class in com.aspose.slides" target="classFrame">ColorChange</a></li>
<li><a href="ColorDirection.html" title="class in com.aspose.slides" target="classFrame">ColorDirection</a></li>
<li><a href="ColorEffect.html" title="class in com.aspose.slides" target="classFrame">ColorEffect</a></li>
<li><a href="ColorFormat.html" title="class in com.aspose.slides" target="classFrame">ColorFormat</a></li>
<li><a href="ColorOffset.html" title="class in com.aspose.slides" target="classFrame">ColorOffset</a></li>
<li><a href="ColorOperation.html" title="class in com.aspose.slides" target="classFrame">ColorOperation</a></li>
<li><a href="ColorOperationCollection.html" title="class in com.aspose.slides" target="classFrame">ColorOperationCollection</a></li>
<li><a href="ColorReplace.html" title="class in com.aspose.slides" target="classFrame">ColorReplace</a></li>
<li><a href="ColorScheme.html" title="class in com.aspose.slides" target="classFrame">ColorScheme</a></li>
<li><a href="ColorSchemeIndex.html" title="class in com.aspose.slides" target="classFrame">ColorSchemeIndex</a></li>
<li><a href="ColorSpace.html" title="class in com.aspose.slides" target="classFrame">ColorSpace</a></li>
<li><a href="ColorStringFormat.html" title="class in com.aspose.slides" target="classFrame">ColorStringFormat</a></li>
<li><a href="ColorTransformOperation.html" title="class in com.aspose.slides" target="classFrame">ColorTransformOperation</a></li>
<li><a href="ColorType.html" title="class in com.aspose.slides" target="classFrame">ColorType</a></li>
<li><a href="Column.html" title="class in com.aspose.slides" target="classFrame">Column</a></li>
<li><a href="ColumnCollection.html" title="class in com.aspose.slides" target="classFrame">ColumnCollection</a></li>
<li><a href="ColumnFormat.html" title="class in com.aspose.slides" target="classFrame">ColumnFormat</a></li>
<li><a href="CombinableSeriesTypesGroup.html" title="class in com.aspose.slides" target="classFrame">CombinableSeriesTypesGroup</a></li>
<li><a href="CommandEffect.html" title="class in com.aspose.slides" target="classFrame">CommandEffect</a></li>
<li><a href="CommandEffectType.html" title="class in com.aspose.slides" target="classFrame">CommandEffectType</a></li>
<li><a href="Comment.html" title="class in com.aspose.slides" target="classFrame">Comment</a></li>
<li><a href="CommentAuthor.html" title="class in com.aspose.slides" target="classFrame">CommentAuthor</a></li>
<li><a href="CommentAuthorCollection.html" title="class in com.aspose.slides" target="classFrame">CommentAuthorCollection</a></li>
<li><a href="CommentCollection.html" title="class in com.aspose.slides" target="classFrame">CommentCollection</a></li>
<li><a href="CommentsPositions.html" title="class in com.aspose.slides" target="classFrame">CommentsPositions</a></li>
<li><a href="CommonSlideViewProperties.html" title="class in com.aspose.slides" target="classFrame">CommonSlideViewProperties</a></li>
<li><a href="Compress.html" title="class in com.aspose.slides" target="classFrame">Compress</a></li>
<li><a href="Conformance.html" title="class in com.aspose.slides" target="classFrame">Conformance</a></li>
<li><a href="Connector.html" title="class in com.aspose.slides" target="classFrame">Connector</a></li>
<li><a href="ConnectorLock.html" title="class in com.aspose.slides" target="classFrame">ConnectorLock</a></li>
<li><a href="Control.html" title="class in com.aspose.slides" target="classFrame">Control</a></li>
<li><a href="ControlCollection.html" title="class in com.aspose.slides" target="classFrame">ControlCollection</a></li>
<li><a href="ControlPropertiesCollection.html" title="class in com.aspose.slides" target="classFrame">ControlPropertiesCollection</a></li>
<li><a href="ControlType.html" title="class in com.aspose.slides" target="classFrame">ControlType</a></li>
<li><a href="Convert.html" title="class in com.aspose.slides" target="classFrame">Convert</a></li>
<li><a href="CornerDirectionTransition.html" title="class in com.aspose.slides" target="classFrame">CornerDirectionTransition</a></li>
<li><a href="CrossesType.html" title="class in com.aspose.slides" target="classFrame">CrossesType</a></li>
<li><a href="CurrentThreadSettings.html" title="class in com.aspose.slides" target="classFrame">CurrentThreadSettings</a></li>
<li><a href="CustomData.html" title="class in com.aspose.slides" target="classFrame">CustomData</a></li>
<li><a href="CustomXmlPart.html" title="class in com.aspose.slides" target="classFrame">CustomXmlPart</a></li>
<li><a href="CustomXmlPartCollection.html" title="class in com.aspose.slides" target="classFrame">CustomXmlPartCollection</a></li>
<li><a href="DataLabel.html" title="class in com.aspose.slides" target="classFrame">DataLabel</a></li>
<li><a href="DataLabelCollection.html" title="class in com.aspose.slides" target="classFrame">DataLabelCollection</a></li>
<li><a href="DataLabelFormat.html" title="class in com.aspose.slides" target="classFrame">DataLabelFormat</a></li>
<li><a href="DataSourceType.html" title="class in com.aspose.slides" target="classFrame">DataSourceType</a></li>
<li><a href="DataSourceTypeForErrorBarsCustomValues.html" title="class in com.aspose.slides" target="classFrame">DataSourceTypeForErrorBarsCustomValues</a></li>
<li><a href="DataTable.html" title="class in com.aspose.slides" target="classFrame">DataTable</a></li>
<li><a href="DigitalSignature.html" title="class in com.aspose.slides" target="classFrame">DigitalSignature</a></li>
<li><a href="DigitalSignatureCollection.html" title="class in com.aspose.slides" target="classFrame">DigitalSignatureCollection</a></li>
<li><a href="DisplayBlanksAsType.html" title="class in com.aspose.slides" target="classFrame">DisplayBlanksAsType</a></li>
<li><a href="DisplayUnitType.html" title="class in com.aspose.slides" target="classFrame">DisplayUnitType</a></li>
<li><a href="DocumentProperties.html" title="class in com.aspose.slides" target="classFrame">DocumentProperties</a></li>
<li><a href="DomObject.html" title="class in com.aspose.slides" target="classFrame">DomObject</a></li>
<li><a href="DoubleChartValue.html" title="class in com.aspose.slides" target="classFrame">DoubleChartValue</a></li>
<li><a href="DrawingGuide.html" title="class in com.aspose.slides" target="classFrame">DrawingGuide</a></li>
<li><a href="DrawingGuidesCollection.html" title="class in com.aspose.slides" target="classFrame">DrawingGuidesCollection</a></li>
<li><a href="Duotone.html" title="class in com.aspose.slides" target="classFrame">Duotone</a></li>
<li><a href="Effect.html" title="class in com.aspose.slides" target="classFrame">Effect</a></li>
<li><a href="EffectChartMajorGroupingType.html" title="class in com.aspose.slides" target="classFrame">EffectChartMajorGroupingType</a></li>
<li><a href="EffectChartMinorGroupingType.html" title="class in com.aspose.slides" target="classFrame">EffectChartMinorGroupingType</a></li>
<li><a href="EffectFactory.html" title="class in com.aspose.slides" target="classFrame">EffectFactory</a></li>
<li><a href="EffectFillType.html" title="class in com.aspose.slides" target="classFrame">EffectFillType</a></li>
<li><a href="EffectFormat.html" title="class in com.aspose.slides" target="classFrame">EffectFormat</a></li>
<li><a href="EffectPresetClassType.html" title="class in com.aspose.slides" target="classFrame">EffectPresetClassType</a></li>
<li><a href="EffectRestartType.html" title="class in com.aspose.slides" target="classFrame">EffectRestartType</a></li>
<li><a href="EffectStyle.html" title="class in com.aspose.slides" target="classFrame">EffectStyle</a></li>
<li><a href="EffectStyleCollection.html" title="class in com.aspose.slides" target="classFrame">EffectStyleCollection</a></li>
<li><a href="EffectSubtype.html" title="class in com.aspose.slides" target="classFrame">EffectSubtype</a></li>
<li><a href="EffectTriggerType.html" title="class in com.aspose.slides" target="classFrame">EffectTriggerType</a></li>
<li><a href="EffectType.html" title="class in com.aspose.slides" target="classFrame">EffectType</a></li>
<li><a href="EightDirectionTransition.html" title="class in com.aspose.slides" target="classFrame">EightDirectionTransition</a></li>
<li><a href="EmbedAllFontsHtmlController.html" title="class in com.aspose.slides" target="classFrame">EmbedAllFontsHtmlController</a></li>
<li><a href="EmbeddedEotFontsHtmlController.html" title="class in com.aspose.slides" target="classFrame">EmbeddedEotFontsHtmlController</a></li>
<li><a href="EmbeddedWoffFontsHtmlController.html" title="class in com.aspose.slides" target="classFrame">EmbeddedWoffFontsHtmlController</a></li>
<li><a href="EmbeddingLevel.html" title="class in com.aspose.slides" target="classFrame">EmbeddingLevel</a></li>
<li><a href="EmbedFontCharacters.html" title="class in com.aspose.slides" target="classFrame">EmbedFontCharacters</a></li>
<li><a href="EmptyTransition.html" title="class in com.aspose.slides" target="classFrame">EmptyTransition</a></li>
<li><a href="ErrorBarsCustomValues.html" title="class in com.aspose.slides" target="classFrame">ErrorBarsCustomValues</a></li>
<li><a href="ErrorBarsFormat.html" title="class in com.aspose.slides" target="classFrame">ErrorBarsFormat</a></li>
<li><a href="ErrorBarType.html" title="class in com.aspose.slides" target="classFrame">ErrorBarType</a></li>
<li><a href="ErrorBarValueType.html" title="class in com.aspose.slides" target="classFrame">ErrorBarValueType</a></li>
<li><a href="ExternalResourceResolver.html" title="class in com.aspose.slides" target="classFrame">ExternalResourceResolver</a></li>
<li><a href="ExtraColorScheme.html" title="class in com.aspose.slides" target="classFrame">ExtraColorScheme</a></li>
<li><a href="ExtraColorSchemeCollection.html" title="class in com.aspose.slides" target="classFrame">ExtraColorSchemeCollection</a></li>
<li><a href="Field.html" title="class in com.aspose.slides" target="classFrame">Field</a></li>
<li><a href="FieldType.html" title="class in com.aspose.slides" target="classFrame">FieldType</a></li>
<li><a href="FillBlendMode.html" title="class in com.aspose.slides" target="classFrame">FillBlendMode</a></li>
<li><a href="FillFormat.html" title="class in com.aspose.slides" target="classFrame">FillFormat</a></li>
<li><a href="FillFormatCollection.html" title="class in com.aspose.slides" target="classFrame">FillFormatCollection</a></li>
<li><a href="FillOverlay.html" title="class in com.aspose.slides" target="classFrame">FillOverlay</a></li>
<li><a href="FillType.html" title="class in com.aspose.slides" target="classFrame">FillType</a></li>
<li><a href="FilterEffect.html" title="class in com.aspose.slides" target="classFrame">FilterEffect</a></li>
<li><a href="FilterEffectRevealType.html" title="class in com.aspose.slides" target="classFrame">FilterEffectRevealType</a></li>
<li><a href="FilterEffectSubtype.html" title="class in com.aspose.slides" target="classFrame">FilterEffectSubtype</a></li>
<li><a href="FilterEffectType.html" title="class in com.aspose.slides" target="classFrame">FilterEffectType</a></li>
<li><a href="Flavor.html" title="class in com.aspose.slides" target="classFrame">Flavor</a></li>
<li><a href="FlyThroughTransition.html" title="class in com.aspose.slides" target="classFrame">FlyThroughTransition</a></li>
<li><a href="FontAlignment.html" title="class in com.aspose.slides" target="classFrame">FontAlignment</a></li>
<li><a href="FontCollectionIndex.html" title="class in com.aspose.slides" target="classFrame">FontCollectionIndex</a></li>
<li><a href="FontData.html" title="class in com.aspose.slides" target="classFrame">FontData</a></li>
<li><a href="FontDataFactory.html" title="class in com.aspose.slides" target="classFrame">FontDataFactory</a></li>
<li><a href="FontFallBackRule.html" title="class in com.aspose.slides" target="classFrame">FontFallBackRule</a></li>
<li><a href="FontFallBackRulesCollection.html" title="class in com.aspose.slides" target="classFrame">FontFallBackRulesCollection</a></li>
<li><a href="Fonts.html" title="class in com.aspose.slides" target="classFrame">Fonts</a></li>
<li><a href="FontScheme.html" title="class in com.aspose.slides" target="classFrame">FontScheme</a></li>
<li><a href="FontsLoader.html" title="class in com.aspose.slides" target="classFrame">FontsLoader</a></li>
<li><a href="FontsManager.html" title="class in com.aspose.slides" target="classFrame">FontsManager</a></li>
<li><a href="FontSources.html" title="class in com.aspose.slides" target="classFrame">FontSources</a></li>
<li><a href="FontStyle.html" title="class in com.aspose.slides" target="classFrame">FontStyle</a></li>
<li><a href="FontSubstCondition.html" title="class in com.aspose.slides" target="classFrame">FontSubstCondition</a></li>
<li><a href="FontSubstitutionInfo.html" title="class in com.aspose.slides" target="classFrame">FontSubstitutionInfo</a></li>
<li><a href="FontSubstRule.html" title="class in com.aspose.slides" target="classFrame">FontSubstRule</a></li>
<li><a href="FontSubstRuleCollection.html" title="class in com.aspose.slides" target="classFrame">FontSubstRuleCollection</a></li>
<li><a href="ForEach.html" title="class in com.aspose.slides" target="classFrame">ForEach</a></li>
<li><a href="Format.html" title="class in com.aspose.slides" target="classFrame">Format</a></li>
<li><a href="FormatFactory.html" title="class in com.aspose.slides" target="classFrame">FormatFactory</a></li>
<li><a href="FormatScheme.html" title="class in com.aspose.slides" target="classFrame">FormatScheme</a></li>
<li><a href="FrameTickEventArgs.html" title="class in com.aspose.slides" target="classFrame">FrameTickEventArgs</a></li>
<li><a href="GeometryPath.html" title="class in com.aspose.slides" target="classFrame">GeometryPath</a></li>
<li><a href="GeometryShape.html" title="class in com.aspose.slides" target="classFrame">GeometryShape</a></li>
<li><a href="GifOptions.html" title="class in com.aspose.slides" target="classFrame">GifOptions</a></li>
<li><a href="GlitterTransition.html" title="class in com.aspose.slides" target="classFrame">GlitterTransition</a></li>
<li><a href="GlobalLayoutSlideCollection.html" title="class in com.aspose.slides" target="classFrame">GlobalLayoutSlideCollection</a></li>
<li><a href="Glow.html" title="class in com.aspose.slides" target="classFrame">Glow</a></li>
<li><a href="GradientDirection.html" title="class in com.aspose.slides" target="classFrame">GradientDirection</a></li>
<li><a href="GradientFormat.html" title="class in com.aspose.slides" target="classFrame">GradientFormat</a></li>
<li><a href="GradientShape.html" title="class in com.aspose.slides" target="classFrame">GradientShape</a></li>
<li><a href="GradientStop.html" title="class in com.aspose.slides" target="classFrame">GradientStop</a></li>
<li><a href="GradientStopCollection.html" title="class in com.aspose.slides" target="classFrame">GradientStopCollection</a></li>
<li><a href="GradientStopCollectionEffectiveData.html" title="class in com.aspose.slides" target="classFrame">GradientStopCollectionEffectiveData</a></li>
<li><a href="GradientStopEffectiveData.html" title="class in com.aspose.slides" target="classFrame">GradientStopEffectiveData</a></li>
<li><a href="GradientStyle.html" title="class in com.aspose.slides" target="classFrame">GradientStyle</a></li>
<li><a href="GraphicalObject.html" title="class in com.aspose.slides" target="classFrame">GraphicalObject</a></li>
<li><a href="GraphicalObjectLock.html" title="class in com.aspose.slides" target="classFrame">GraphicalObjectLock</a></li>
<li><a href="GrayScale.html" title="class in com.aspose.slides" target="classFrame">GrayScale</a></li>
<li><a href="GroupShape.html" title="class in com.aspose.slides" target="classFrame">GroupShape</a></li>
<li><a href="GroupShapeLock.html" title="class in com.aspose.slides" target="classFrame">GroupShapeLock</a></li>
<li><a href="HandleRepeatedSpaces.html" title="class in com.aspose.slides" target="classFrame">HandleRepeatedSpaces</a></li>
<li><a href="HandoutLayoutingOptions.html" title="class in com.aspose.slides" target="classFrame">HandoutLayoutingOptions</a></li>
<li><a href="HandoutType.html" title="class in com.aspose.slides" target="classFrame">HandoutType</a></li>
<li><a href="HeadingPair.html" title="class in com.aspose.slides" target="classFrame">HeadingPair</a></li>
<li><a href="HSL.html" title="class in com.aspose.slides" target="classFrame">HSL</a></li>
<li><a href="Html5Options.html" title="class in com.aspose.slides" target="classFrame">Html5Options</a></li>
<li><a href="HtmlExternalResolver.html" title="class in com.aspose.slides" target="classFrame">HtmlExternalResolver</a></li>
<li><a href="HtmlFormatter.html" title="class in com.aspose.slides" target="classFrame">HtmlFormatter</a></li>
<li><a href="HtmlGenerator.html" title="class in com.aspose.slides" target="classFrame">HtmlGenerator</a></li>
<li><a href="HtmlOptions.html" title="class in com.aspose.slides" target="classFrame">HtmlOptions</a></li>
<li><a href="Hyperlink.html" title="class in com.aspose.slides" target="classFrame">Hyperlink</a></li>
<li><a href="HyperlinkActionType.html" title="class in com.aspose.slides" target="classFrame">HyperlinkActionType</a></li>
<li><a href="HyperlinkColorSource.html" title="class in com.aspose.slides" target="classFrame">HyperlinkColorSource</a></li>
<li><a href="HyperlinkManager.html" title="class in com.aspose.slides" target="classFrame">HyperlinkManager</a></li>
<li><a href="HyperlinkQueries.html" title="class in com.aspose.slides" target="classFrame">HyperlinkQueries</a></li>
<li><a href="ImageCollection.html" title="class in com.aspose.slides" target="classFrame">ImageCollection</a></li>
<li><a href="ImageFormat.html" title="class in com.aspose.slides" target="classFrame">ImageFormat</a></li>
<li><a href="ImagePixelFormat.html" title="class in com.aspose.slides" target="classFrame">ImagePixelFormat</a></li>
<li><a href="Images.html" title="class in com.aspose.slides" target="classFrame">Images</a></li>
<li><a href="ImageTransformOCollectionEffectiveData.html" title="class in com.aspose.slides" target="classFrame">ImageTransformOCollectionEffectiveData</a></li>
<li><a href="ImageTransformOperation.html" title="class in com.aspose.slides" target="classFrame">ImageTransformOperation</a></li>
<li><a href="ImageTransformOperationCollection.html" title="class in com.aspose.slides" target="classFrame">ImageTransformOperationCollection</a></li>
<li><a href="ImageTransformOperationFactory.html" title="class in com.aspose.slides" target="classFrame">ImageTransformOperationFactory</a></li>
<li><a href="Ink.html" title="class in com.aspose.slides" target="classFrame">Ink</a></li>
<li><a href="InkActions.html" title="class in com.aspose.slides" target="classFrame">InkActions</a></li>
<li><a href="InkBrush.html" title="class in com.aspose.slides" target="classFrame">InkBrush</a></li>
<li><a href="InkEffectType.html" title="class in com.aspose.slides" target="classFrame">InkEffectType</a></li>
<li><a href="InkOptions.html" title="class in com.aspose.slides" target="classFrame">InkOptions</a></li>
<li><a href="InkTrace.html" title="class in com.aspose.slides" target="classFrame">InkTrace</a></li>
<li><a href="InnerShadow.html" title="class in com.aspose.slides" target="classFrame">InnerShadow</a></li>
<li><a href="InOutTransition.html" title="class in com.aspose.slides" target="classFrame">InOutTransition</a></li>
<li><a href="Input.html" title="class in com.aspose.slides" target="classFrame">Input</a></li>
<li><a href="InterruptionToken.html" title="class in com.aspose.slides" target="classFrame">InterruptionToken</a></li>
<li><a href="InterruptionTokenSource.html" title="class in com.aspose.slides" target="classFrame">InterruptionTokenSource</a></li>
<li><a href="LayoutPlaceholderManager.html" title="class in com.aspose.slides" target="classFrame">LayoutPlaceholderManager</a></li>
<li><a href="LayoutSlide.html" title="class in com.aspose.slides" target="classFrame">LayoutSlide</a></li>
<li><a href="LayoutSlideCollection.html" title="class in com.aspose.slides" target="classFrame">LayoutSlideCollection</a></li>
<li><a href="LayoutSlideHeaderFooterManager.html" title="class in com.aspose.slides" target="classFrame">LayoutSlideHeaderFooterManager</a></li>
<li><a href="LayoutSlideThemeManager.html" title="class in com.aspose.slides" target="classFrame">LayoutSlideThemeManager</a></li>
<li><a href="LayoutTargetType.html" title="class in com.aspose.slides" target="classFrame">LayoutTargetType</a></li>
<li><a href="LeftRightDirectionTransition.html" title="class in com.aspose.slides" target="classFrame">LeftRightDirectionTransition</a></li>
<li><a href="LegacyDiagram.html" title="class in com.aspose.slides" target="classFrame">LegacyDiagram</a></li>
<li><a href="Legend.html" title="class in com.aspose.slides" target="classFrame">Legend</a></li>
<li><a href="LegendDataLabelPosition.html" title="class in com.aspose.slides" target="classFrame">LegendDataLabelPosition</a></li>
<li><a href="LegendEntryCollection.html" title="class in com.aspose.slides" target="classFrame">LegendEntryCollection</a></li>
<li><a href="LegendEntryProperties.html" title="class in com.aspose.slides" target="classFrame">LegendEntryProperties</a></li>
<li><a href="LegendPositionType.html" title="class in com.aspose.slides" target="classFrame">LegendPositionType</a></li>
<li><a href="License.html" title="class in com.aspose.slides" target="classFrame">License</a></li>
<li><a href="LightingDirection.html" title="class in com.aspose.slides" target="classFrame">LightingDirection</a></li>
<li><a href="LightRig.html" title="class in com.aspose.slides" target="classFrame">LightRig</a></li>
<li><a href="LightRigPresetType.html" title="class in com.aspose.slides" target="classFrame">LightRigPresetType</a></li>
<li><a href="LineAlignment.html" title="class in com.aspose.slides" target="classFrame">LineAlignment</a></li>
<li><a href="LineArrowheadLength.html" title="class in com.aspose.slides" target="classFrame">LineArrowheadLength</a></li>
<li><a href="LineArrowheadStyle.html" title="class in com.aspose.slides" target="classFrame">LineArrowheadStyle</a></li>
<li><a href="LineArrowheadWidth.html" title="class in com.aspose.slides" target="classFrame">LineArrowheadWidth</a></li>
<li><a href="LineCapStyle.html" title="class in com.aspose.slides" target="classFrame">LineCapStyle</a></li>
<li><a href="LineDashStyle.html" title="class in com.aspose.slides" target="classFrame">LineDashStyle</a></li>
<li><a href="LineFillFormat.html" title="class in com.aspose.slides" target="classFrame">LineFillFormat</a></li>
<li><a href="LineFormat.html" title="class in com.aspose.slides" target="classFrame">LineFormat</a></li>
<li><a href="LineFormatCollection.html" title="class in com.aspose.slides" target="classFrame">LineFormatCollection</a></li>
<li><a href="LineJoinStyle.html" title="class in com.aspose.slides" target="classFrame">LineJoinStyle</a></li>
<li><a href="LineSketchType.html" title="class in com.aspose.slides" target="classFrame">LineSketchType</a></li>
<li><a href="LineStyle.html" title="class in com.aspose.slides" target="classFrame">LineStyle</a></li>
<li><a href="LinkEmbedDecision.html" title="class in com.aspose.slides" target="classFrame">LinkEmbedDecision</a></li>
<li><a href="LoadFormat.html" title="class in com.aspose.slides" target="classFrame">LoadFormat</a></li>
<li><a href="LoadingStreamBehavior.html" title="class in com.aspose.slides" target="classFrame">LoadingStreamBehavior</a></li>
<li><a href="LoadOptions.html" title="class in com.aspose.slides" target="classFrame">LoadOptions</a></li>
<li><a href="Luminance.html" title="class in com.aspose.slides" target="classFrame">Luminance</a></li>
<li><a href="MarkdownExportType.html" title="class in com.aspose.slides" target="classFrame">MarkdownExportType</a></li>
<li><a href="MarkdownSaveOptions.html" title="class in com.aspose.slides" target="classFrame">MarkdownSaveOptions</a></li>
<li><a href="Marker.html" title="class in com.aspose.slides" target="classFrame">Marker</a></li>
<li><a href="MarkerStyleType.html" title="class in com.aspose.slides" target="classFrame">MarkerStyleType</a></li>
<li><a href="MasterHandoutSlide.html" title="class in com.aspose.slides" target="classFrame">MasterHandoutSlide</a></li>
<li><a href="MasterHandoutSlideHeaderFooterManager.html" title="class in com.aspose.slides" target="classFrame">MasterHandoutSlideHeaderFooterManager</a></li>
<li><a href="MasterLayoutSlideCollection.html" title="class in com.aspose.slides" target="classFrame">MasterLayoutSlideCollection</a></li>
<li><a href="MasterNotesSlide.html" title="class in com.aspose.slides" target="classFrame">MasterNotesSlide</a></li>
<li><a href="MasterNotesSlideHeaderFooterManager.html" title="class in com.aspose.slides" target="classFrame">MasterNotesSlideHeaderFooterManager</a></li>
<li><a href="MasterSlide.html" title="class in com.aspose.slides" target="classFrame">MasterSlide</a></li>
<li><a href="MasterSlideCollection.html" title="class in com.aspose.slides" target="classFrame">MasterSlideCollection</a></li>
<li><a href="MasterSlideHeaderFooterManager.html" title="class in com.aspose.slides" target="classFrame">MasterSlideHeaderFooterManager</a></li>
<li><a href="MasterTheme.html" title="class in com.aspose.slides" target="classFrame">MasterTheme</a></li>
<li><a href="MasterThemeManager.html" title="class in com.aspose.slides" target="classFrame">MasterThemeManager</a></li>
<li><a href="MaterialPresetType.html" title="class in com.aspose.slides" target="classFrame">MaterialPresetType</a></li>
<li><a href="MathAccent.html" title="class in com.aspose.slides" target="classFrame">MathAccent</a></li>
<li><a href="MathAccentFactory.html" title="class in com.aspose.slides" target="classFrame">MathAccentFactory</a></li>
<li><a href="MathArray.html" title="class in com.aspose.slides" target="classFrame">MathArray</a></li>
<li><a href="MathArrayFactory.html" title="class in com.aspose.slides" target="classFrame">MathArrayFactory</a></li>
<li><a href="MathBar.html" title="class in com.aspose.slides" target="classFrame">MathBar</a></li>
<li><a href="MathBarFactory.html" title="class in com.aspose.slides" target="classFrame">MathBarFactory</a></li>
<li><a href="MathBlock.html" title="class in com.aspose.slides" target="classFrame">MathBlock</a></li>
<li><a href="MathBlockFactory.html" title="class in com.aspose.slides" target="classFrame">MathBlockFactory</a></li>
<li><a href="MathBorderBox.html" title="class in com.aspose.slides" target="classFrame">MathBorderBox</a></li>
<li><a href="MathBorderBoxFactory.html" title="class in com.aspose.slides" target="classFrame">MathBorderBoxFactory</a></li>
<li><a href="MathBox.html" title="class in com.aspose.slides" target="classFrame">MathBox</a></li>
<li><a href="MathBoxFactory.html" title="class in com.aspose.slides" target="classFrame">MathBoxFactory</a></li>
<li><a href="MathDelimiter.html" title="class in com.aspose.slides" target="classFrame">MathDelimiter</a></li>
<li><a href="MathDelimiterFactory.html" title="class in com.aspose.slides" target="classFrame">MathDelimiterFactory</a></li>
<li><a href="MathDelimiterShape.html" title="class in com.aspose.slides" target="classFrame">MathDelimiterShape</a></li>
<li><a href="MathElementBase.html" title="class in com.aspose.slides" target="classFrame">MathElementBase</a></li>
<li><a href="MathematicalText.html" title="class in com.aspose.slides" target="classFrame">MathematicalText</a></li>
<li><a href="MathematicalTextFactory.html" title="class in com.aspose.slides" target="classFrame">MathematicalTextFactory</a></li>
<li><a href="MathFraction.html" title="class in com.aspose.slides" target="classFrame">MathFraction</a></li>
<li><a href="MathFractionFactory.html" title="class in com.aspose.slides" target="classFrame">MathFractionFactory</a></li>
<li><a href="MathFractionTypes.html" title="class in com.aspose.slides" target="classFrame">MathFractionTypes</a></li>
<li><a href="MathFunction.html" title="class in com.aspose.slides" target="classFrame">MathFunction</a></li>
<li><a href="MathFunctionFactory.html" title="class in com.aspose.slides" target="classFrame">MathFunctionFactory</a></li>
<li><a href="MathFunctionsOfOneArgument.html" title="class in com.aspose.slides" target="classFrame">MathFunctionsOfOneArgument</a></li>
<li><a href="MathFunctionsOfTwoArguments.html" title="class in com.aspose.slides" target="classFrame">MathFunctionsOfTwoArguments</a></li>
<li><a href="MathGroupingCharacter.html" title="class in com.aspose.slides" target="classFrame">MathGroupingCharacter</a></li>
<li><a href="MathGroupingCharacterFactory.html" title="class in com.aspose.slides" target="classFrame">MathGroupingCharacterFactory</a></li>
<li><a href="MathHorizontalAlignment.html" title="class in com.aspose.slides" target="classFrame">MathHorizontalAlignment</a></li>
<li><a href="MathIntegralTypes.html" title="class in com.aspose.slides" target="classFrame">MathIntegralTypes</a></li>
<li><a href="MathJustification.html" title="class in com.aspose.slides" target="classFrame">MathJustification</a></li>
<li><a href="MathLeftSubSuperscriptElement.html" title="class in com.aspose.slides" target="classFrame">MathLeftSubSuperscriptElement</a></li>
<li><a href="MathLimit.html" title="class in com.aspose.slides" target="classFrame">MathLimit</a></li>
<li><a href="MathLimitFactory.html" title="class in com.aspose.slides" target="classFrame">MathLimitFactory</a></li>
<li><a href="MathLimitLocations.html" title="class in com.aspose.slides" target="classFrame">MathLimitLocations</a></li>
<li><a href="MathMatrix.html" title="class in com.aspose.slides" target="classFrame">MathMatrix</a></li>
<li><a href="MathMatrixFactory.html" title="class in com.aspose.slides" target="classFrame">MathMatrixFactory</a></li>
<li><a href="MathNaryOperator.html" title="class in com.aspose.slides" target="classFrame">MathNaryOperator</a></li>
<li><a href="MathNaryOperatorFactory.html" title="class in com.aspose.slides" target="classFrame">MathNaryOperatorFactory</a></li>
<li><a href="MathNaryOperatorTypes.html" title="class in com.aspose.slides" target="classFrame">MathNaryOperatorTypes</a></li>
<li><a href="MathParagraph.html" title="class in com.aspose.slides" target="classFrame">MathParagraph</a></li>
<li><a href="MathParagraphFactory.html" title="class in com.aspose.slides" target="classFrame">MathParagraphFactory</a></li>
<li><a href="MathPortion.html" title="class in com.aspose.slides" target="classFrame">MathPortion</a></li>
<li><a href="MathRadical.html" title="class in com.aspose.slides" target="classFrame">MathRadical</a></li>
<li><a href="MathRadicalFactory.html" title="class in com.aspose.slides" target="classFrame">MathRadicalFactory</a></li>
<li><a href="MathRightSubSuperscriptElement.html" title="class in com.aspose.slides" target="classFrame">MathRightSubSuperscriptElement</a></li>
<li><a href="MathRightSubSuperscriptElementFactory.html" title="class in com.aspose.slides" target="classFrame">MathRightSubSuperscriptElementFactory</a></li>
<li><a href="MathRowSpacingRule.html" title="class in com.aspose.slides" target="classFrame">MathRowSpacingRule</a></li>
<li><a href="MathSpacingRules.html" title="class in com.aspose.slides" target="classFrame">MathSpacingRules</a></li>
<li><a href="MathSubscriptElement.html" title="class in com.aspose.slides" target="classFrame">MathSubscriptElement</a></li>
<li><a href="MathSubscriptElementFactory.html" title="class in com.aspose.slides" target="classFrame">MathSubscriptElementFactory</a></li>
<li><a href="MathSuperscriptElement.html" title="class in com.aspose.slides" target="classFrame">MathSuperscriptElement</a></li>
<li><a href="MathSuperscriptElementFactory.html" title="class in com.aspose.slides" target="classFrame">MathSuperscriptElementFactory</a></li>
<li><a href="MathTopBotPositions.html" title="class in com.aspose.slides" target="classFrame">MathTopBotPositions</a></li>
<li><a href="MathVerticalAlignment.html" title="class in com.aspose.slides" target="classFrame">MathVerticalAlignment</a></li>
<li><a href="Metered.html" title="class in com.aspose.slides" target="classFrame">Metered</a></li>
<li><a href="ModernComment.html" title="class in com.aspose.slides" target="classFrame">ModernComment</a></li>
<li><a href="ModernCommentStatus.html" title="class in com.aspose.slides" target="classFrame">ModernCommentStatus</a></li>
<li><a href="MorphTransition.html" title="class in com.aspose.slides" target="classFrame">MorphTransition</a></li>
<li><a href="MotionCmdPath.html" title="class in com.aspose.slides" target="classFrame">MotionCmdPath</a></li>
<li><a href="MotionCommandPathType.html" title="class in com.aspose.slides" target="classFrame">MotionCommandPathType</a></li>
<li><a href="MotionEffect.html" title="class in com.aspose.slides" target="classFrame">MotionEffect</a></li>
<li><a href="MotionOriginType.html" title="class in com.aspose.slides" target="classFrame">MotionOriginType</a></li>
<li><a href="MotionPath.html" title="class in com.aspose.slides" target="classFrame">MotionPath</a></li>
<li><a href="MotionPathEditMode.html" title="class in com.aspose.slides" target="classFrame">MotionPathEditMode</a></li>
<li><a href="MotionPathPointsType.html" title="class in com.aspose.slides" target="classFrame">MotionPathPointsType</a></li>
<li><a href="NewLineType.html" title="class in com.aspose.slides" target="classFrame">NewLineType</a></li>
<li><a href="NormalViewProperties.html" title="class in com.aspose.slides" target="classFrame">NormalViewProperties</a></li>
<li><a href="NormalViewRestoredProperties.html" title="class in com.aspose.slides" target="classFrame">NormalViewRestoredProperties</a></li>
<li><a href="NotesCommentsLayoutingOptions.html" title="class in com.aspose.slides" target="classFrame">NotesCommentsLayoutingOptions</a></li>
<li><a href="NotesPositions.html" title="class in com.aspose.slides" target="classFrame">NotesPositions</a></li>
<li><a href="NotesSize.html" title="class in com.aspose.slides" target="classFrame">NotesSize</a></li>
<li><a href="NotesSlide.html" title="class in com.aspose.slides" target="classFrame">NotesSlide</a></li>
<li><a href="NotesSlideHeaderFooterManager.html" title="class in com.aspose.slides" target="classFrame">NotesSlideHeaderFooterManager</a></li>
<li><a href="NotesSlideManager.html" title="class in com.aspose.slides" target="classFrame">NotesSlideManager</a></li>
<li><a href="NotesSlideThemeManager.html" title="class in com.aspose.slides" target="classFrame">NotesSlideThemeManager</a></li>
<li><a href="NullableBool.html" title="class in com.aspose.slides" target="classFrame">NullableBool</a></li>
<li><a href="NumberedBulletStyle.html" title="class in com.aspose.slides" target="classFrame">NumberedBulletStyle</a></li>
<li><a href="OleEmbeddedDataInfo.html" title="class in com.aspose.slides" target="classFrame">OleEmbeddedDataInfo</a></li>
<li><a href="OleObjectFrame.html" title="class in com.aspose.slides" target="classFrame">OleObjectFrame</a></li>
<li><a href="OpenAIWebClient.html" title="class in com.aspose.slides" target="classFrame">OpenAIWebClient</a></li>
<li><a href="OptionalBlackTransition.html" title="class in com.aspose.slides" target="classFrame">OptionalBlackTransition</a></li>
<li><a href="OrganizationChartLayoutType.html" title="class in com.aspose.slides" target="classFrame">OrganizationChartLayoutType</a></li>
<li><a href="Orientation.html" title="class in com.aspose.slides" target="classFrame">Orientation</a></li>
<li><a href="OrientationTransition.html" title="class in com.aspose.slides" target="classFrame">OrientationTransition</a></li>
<li><a href="OuterShadow.html" title="class in com.aspose.slides" target="classFrame">OuterShadow</a></li>
<li><a href="Output.html" title="class in com.aspose.slides" target="classFrame">Output</a></li>
<li><a href="OutputFile.html" title="class in com.aspose.slides" target="classFrame">OutputFile</a></li>
<li><a href="OverrideTheme.html" title="class in com.aspose.slides" target="classFrame">OverrideTheme</a></li>
<li><a href="Paragraph.html" title="class in com.aspose.slides" target="classFrame">Paragraph</a></li>
<li><a href="ParagraphCollection.html" title="class in com.aspose.slides" target="classFrame">ParagraphCollection</a></li>
<li><a href="ParagraphFactory.html" title="class in com.aspose.slides" target="classFrame">ParagraphFactory</a></li>
<li><a href="ParagraphFormat.html" title="class in com.aspose.slides" target="classFrame">ParagraphFormat</a></li>
<li><a href="ParentLabelLayoutType.html" title="class in com.aspose.slides" target="classFrame">ParentLabelLayoutType</a></li>
<li><a href="PathCommandType.html" title="class in com.aspose.slides" target="classFrame">PathCommandType</a></li>
<li><a href="PathFillModeType.html" title="class in com.aspose.slides" target="classFrame">PathFillModeType</a></li>
<li><a href="PathSegment.html" title="class in com.aspose.slides" target="classFrame">PathSegment</a></li>
<li><a href="PatternFormat.html" title="class in com.aspose.slides" target="classFrame">PatternFormat</a></li>
<li><a href="PatternStyle.html" title="class in com.aspose.slides" target="classFrame">PatternStyle</a></li>
<li><a href="PdfAccessPermissions.html" title="class in com.aspose.slides" target="classFrame">PdfAccessPermissions</a></li>
<li><a href="PdfCompliance.html" title="class in com.aspose.slides" target="classFrame">PdfCompliance</a></li>
<li><a href="PdfImportOptions.html" title="class in com.aspose.slides" target="classFrame">PdfImportOptions</a></li>
<li><a href="PdfOptions.html" title="class in com.aspose.slides" target="classFrame">PdfOptions</a></li>
<li><a href="PdfTextCompression.html" title="class in com.aspose.slides" target="classFrame">PdfTextCompression</a></li>
<li><a href="PersistenceType.html" title="class in com.aspose.slides" target="classFrame">PersistenceType</a></li>
<li><a href="Picture.html" title="class in com.aspose.slides" target="classFrame">Picture</a></li>
<li><a href="PictureFillFormat.html" title="class in com.aspose.slides" target="classFrame">PictureFillFormat</a></li>
<li><a href="PictureFillMode.html" title="class in com.aspose.slides" target="classFrame">PictureFillMode</a></li>
<li><a href="PictureFrame.html" title="class in com.aspose.slides" target="classFrame">PictureFrame</a></li>
<li><a href="PictureFrameLock.html" title="class in com.aspose.slides" target="classFrame">PictureFrameLock</a></li>
<li><a href="PicturesCompression.html" title="class in com.aspose.slides" target="classFrame">PicturesCompression</a></li>
<li><a href="PictureType.html" title="class in com.aspose.slides" target="classFrame">PictureType</a></li>
<li><a href="PieSplitCustomPointCollection.html" title="class in com.aspose.slides" target="classFrame">PieSplitCustomPointCollection</a></li>
<li><a href="PieSplitType.html" title="class in com.aspose.slides" target="classFrame">PieSplitType</a></li>
<li><a href="Placeholder.html" title="class in com.aspose.slides" target="classFrame">Placeholder</a></li>
<li><a href="PlaceholderSize.html" title="class in com.aspose.slides" target="classFrame">PlaceholderSize</a></li>
<li><a href="PlaceholderType.html" title="class in com.aspose.slides" target="classFrame">PlaceholderType</a></li>
<li><a href="Point.html" title="class in com.aspose.slides" target="classFrame">Point</a></li>
<li><a href="PointCollection.html" title="class in com.aspose.slides" target="classFrame">PointCollection</a></li>
<li><a href="Portion.html" title="class in com.aspose.slides" target="classFrame">Portion</a></li>
<li><a href="PortionCollection.html" title="class in com.aspose.slides" target="classFrame">PortionCollection</a></li>
<li><a href="PortionFactory.html" title="class in com.aspose.slides" target="classFrame">PortionFactory</a></li>
<li><a href="PortionFormat.html" title="class in com.aspose.slides" target="classFrame">PortionFormat</a></li>
<li><a href="PPImage.html" title="class in com.aspose.slides" target="classFrame">PPImage</a></li>
<li><a href="PptOptions.html" title="class in com.aspose.slides" target="classFrame">PptOptions</a></li>
<li><a href="PptxOptions.html" title="class in com.aspose.slides" target="classFrame">PptxOptions</a></li>
<li><a href="Presentation.html" title="class in com.aspose.slides" target="classFrame">Presentation</a></li>
<li><a href="PresentationAnimationsGenerator.html" title="class in com.aspose.slides" target="classFrame">PresentationAnimationsGenerator</a></li>
<li><a href="PresentationContentAmountType.html" title="class in com.aspose.slides" target="classFrame">PresentationContentAmountType</a></li>
<li><a href="PresentationFactory.html" title="class in com.aspose.slides" target="classFrame">PresentationFactory</a></li>
<li><a href="PresentationHeaderFooterManager.html" title="class in com.aspose.slides" target="classFrame">PresentationHeaderFooterManager</a></li>
<li><a href="PresentationInfo.html" title="class in com.aspose.slides" target="classFrame">PresentationInfo</a></li>
<li><a href="PresentationLockingBehavior.html" title="class in com.aspose.slides" target="classFrame">PresentationLockingBehavior</a></li>
<li><a href="PresentationPlayer.html" title="class in com.aspose.slides" target="classFrame">PresentationPlayer</a></li>
<li><a href="PresentationText.html" title="class in com.aspose.slides" target="classFrame">PresentationText</a></li>
<li><a href="PresentedBySpeaker.html" title="class in com.aspose.slides" target="classFrame">PresentedBySpeaker</a></li>
<li><a href="PresetColor.html" title="class in com.aspose.slides" target="classFrame">PresetColor</a></li>
<li><a href="PresetShadow.html" title="class in com.aspose.slides" target="classFrame">PresetShadow</a></li>
<li><a href="PresetShadowType.html" title="class in com.aspose.slides" target="classFrame">PresetShadowType</a></li>
<li><a href="PropertyCalcModeType.html" title="class in com.aspose.slides" target="classFrame">PropertyCalcModeType</a></li>
<li><a href="PropertyEffect.html" title="class in com.aspose.slides" target="classFrame">PropertyEffect</a></li>
<li><a href="PropertyValueType.html" title="class in com.aspose.slides" target="classFrame">PropertyValueType</a></li>
<li><a href="ProtectionManager.html" title="class in com.aspose.slides" target="classFrame">ProtectionManager</a></li>
<li><a href="PVIObject.html" title="class in com.aspose.slides" target="classFrame">PVIObject</a></li>
<li><a href="QuartileMethodType.html" title="class in com.aspose.slides" target="classFrame">QuartileMethodType</a></li>
<li><a href="RectangleAlignment.html" title="class in com.aspose.slides" target="classFrame">RectangleAlignment</a></li>
<li><a href="Reflection.html" title="class in com.aspose.slides" target="classFrame">Reflection</a></li>
<li><a href="RenderingOptions.html" title="class in com.aspose.slides" target="classFrame">RenderingOptions</a></li>
<li><a href="ResourceLoadingAction.html" title="class in com.aspose.slides" target="classFrame">ResourceLoadingAction</a></li>
<li><a href="ResponsiveHtmlController.html" title="class in com.aspose.slides" target="classFrame">ResponsiveHtmlController</a></li>
<li><a href="ReturnAction.html" title="class in com.aspose.slides" target="classFrame">ReturnAction</a></li>
<li><a href="RevealTransition.html" title="class in com.aspose.slides" target="classFrame">RevealTransition</a></li>
<li><a href="RippleTransition.html" title="class in com.aspose.slides" target="classFrame">RippleTransition</a></li>
<li><a href="Rotation3D.html" title="class in com.aspose.slides" target="classFrame">Rotation3D</a></li>
<li><a href="RotationEffect.html" title="class in com.aspose.slides" target="classFrame">RotationEffect</a></li>
<li><a href="Row.html" title="class in com.aspose.slides" target="classFrame">Row</a></li>
<li><a href="RowCollection.html" title="class in com.aspose.slides" target="classFrame">RowCollection</a></li>
<li><a href="RowFormat.html" title="class in com.aspose.slides" target="classFrame">RowFormat</a></li>
<li><a href="SaveFormat.html" title="class in com.aspose.slides" target="classFrame">SaveFormat</a></li>
<li><a href="SaveOptions.html" title="class in com.aspose.slides" target="classFrame">SaveOptions</a></li>
<li><a href="SaveOptionsFactory.html" title="class in com.aspose.slides" target="classFrame">SaveOptionsFactory</a></li>
<li><a href="ScaleEffect.html" title="class in com.aspose.slides" target="classFrame">ScaleEffect</a></li>
<li><a href="SchemeColor.html" title="class in com.aspose.slides" target="classFrame">SchemeColor</a></li>
<li><a href="Section.html" title="class in com.aspose.slides" target="classFrame">Section</a></li>
<li><a href="SectionCollection.html" title="class in com.aspose.slides" target="classFrame">SectionCollection</a></li>
<li><a href="SectionSlideCollection.html" title="class in com.aspose.slides" target="classFrame">SectionSlideCollection</a></li>
<li><a href="SectionZoomFrame.html" title="class in com.aspose.slides" target="classFrame">SectionZoomFrame</a></li>
<li><a href="Sequence.html" title="class in com.aspose.slides" target="classFrame">Sequence</a></li>
<li><a href="SequenceCollection.html" title="class in com.aspose.slides" target="classFrame">SequenceCollection</a></li>
<li><a href="SetEffect.html" title="class in com.aspose.slides" target="classFrame">SetEffect</a></li>
<li><a href="Shape.html" title="class in com.aspose.slides" target="classFrame">Shape</a></li>
<li><a href="ShapeAdjustmentType.html" title="class in com.aspose.slides" target="classFrame">ShapeAdjustmentType</a></li>
<li><a href="ShapeBevel.html" title="class in com.aspose.slides" target="classFrame">ShapeBevel</a></li>
<li><a href="ShapeCollection.html" title="class in com.aspose.slides" target="classFrame">ShapeCollection</a></li>
<li><a href="ShapeElement.html" title="class in com.aspose.slides" target="classFrame">ShapeElement</a></li>
<li><a href="ShapeElementFillSource.html" title="class in com.aspose.slides" target="classFrame">ShapeElementFillSource</a></li>
<li><a href="ShapeElementStrokeSource.html" title="class in com.aspose.slides" target="classFrame">ShapeElementStrokeSource</a></li>
<li><a href="ShapeFrame.html" title="class in com.aspose.slides" target="classFrame">ShapeFrame</a></li>
<li><a href="ShapesAlignmentType.html" title="class in com.aspose.slides" target="classFrame">ShapesAlignmentType</a></li>
<li><a href="ShapeStyle.html" title="class in com.aspose.slides" target="classFrame">ShapeStyle</a></li>
<li><a href="ShapeThumbnailBounds.html" title="class in com.aspose.slides" target="classFrame">ShapeThumbnailBounds</a></li>
<li><a href="ShapeType.html" title="class in com.aspose.slides" target="classFrame">ShapeType</a></li>
<li><a href="ShapeUtil.html" title="class in com.aspose.slides" target="classFrame">ShapeUtil</a></li>
<li><a href="ShredTransition.html" title="class in com.aspose.slides" target="classFrame">ShredTransition</a></li>
<li><a href="SideDirectionTransition.html" title="class in com.aspose.slides" target="classFrame">SideDirectionTransition</a></li>
<li><a href="SketchFormat.html" title="class in com.aspose.slides" target="classFrame">SketchFormat</a></li>
<li><a href="Slide.html" title="class in com.aspose.slides" target="classFrame">Slide</a></li>
<li><a href="SlideCollection.html" title="class in com.aspose.slides" target="classFrame">SlideCollection</a></li>
<li><a href="SlideHeaderFooterManager.html" title="class in com.aspose.slides" target="classFrame">SlideHeaderFooterManager</a></li>
<li><a href="SlideImageFormat.html" title="class in com.aspose.slides" target="classFrame">SlideImageFormat</a></li>
<li><a href="SlideLayoutType.html" title="class in com.aspose.slides" target="classFrame">SlideLayoutType</a></li>
<li><a href="SlideOrientation.html" title="class in com.aspose.slides" target="classFrame">SlideOrientation</a></li>
<li><a href="SlidesAIAgent.html" title="class in com.aspose.slides" target="classFrame">SlidesAIAgent</a></li>
<li><a href="SlideShowSettings.html" title="class in com.aspose.slides" target="classFrame">SlideShowSettings</a></li>
<li><a href="SlideShowTransition.html" title="class in com.aspose.slides" target="classFrame">SlideShowTransition</a></li>
<li><a href="SlideShowType.html" title="class in com.aspose.slides" target="classFrame">SlideShowType</a></li>
<li><a href="SlideSize.html" title="class in com.aspose.slides" target="classFrame">SlideSize</a></li>
<li><a href="SlideSizeScaleType.html" title="class in com.aspose.slides" target="classFrame">SlideSizeScaleType</a></li>
<li><a href="SlideSizeType.html" title="class in com.aspose.slides" target="classFrame">SlideSizeType</a></li>
<li><a href="SlidesRange.html" title="class in com.aspose.slides" target="classFrame">SlidesRange</a></li>
<li><a href="SlideThemeManager.html" title="class in com.aspose.slides" target="classFrame">SlideThemeManager</a></li>
<li><a href="SlideUtil.html" title="class in com.aspose.slides" target="classFrame">SlideUtil</a></li>
<li><a href="SmartArt.html" title="class in com.aspose.slides" target="classFrame">SmartArt</a></li>
<li><a href="SmartArtColorType.html" title="class in com.aspose.slides" target="classFrame">SmartArtColorType</a></li>
<li><a href="SmartArtLayoutType.html" title="class in com.aspose.slides" target="classFrame">SmartArtLayoutType</a></li>
<li><a href="SmartArtNode.html" title="class in com.aspose.slides" target="classFrame">SmartArtNode</a></li>
<li><a href="SmartArtNodeCollection.html" title="class in com.aspose.slides" target="classFrame">SmartArtNodeCollection</a></li>
<li><a href="SmartArtQuickStyleType.html" title="class in com.aspose.slides" target="classFrame">SmartArtQuickStyleType</a></li>
<li><a href="SmartArtShape.html" title="class in com.aspose.slides" target="classFrame">SmartArtShape</a></li>
<li><a href="SmartArtShapeCollection.html" title="class in com.aspose.slides" target="classFrame">SmartArtShapeCollection</a></li>
<li><a href="SoftEdge.html" title="class in com.aspose.slides" target="classFrame">SoftEdge</a></li>
<li><a href="SourceFormat.html" title="class in com.aspose.slides" target="classFrame">SourceFormat</a></li>
<li><a href="SplitterBarStateType.html" title="class in com.aspose.slides" target="classFrame">SplitterBarStateType</a></li>
<li><a href="SplitTransition.html" title="class in com.aspose.slides" target="classFrame">SplitTransition</a></li>
<li><a href="SpreadsheetOptions.html" title="class in com.aspose.slides" target="classFrame">SpreadsheetOptions</a></li>
<li><a href="Storage.html" title="class in com.aspose.slides" target="classFrame">Storage</a></li>
<li><a href="StringChartValue.html" title="class in com.aspose.slides" target="classFrame">StringChartValue</a></li>
<li><a href="StringOrDoubleChartValue.html" title="class in com.aspose.slides" target="classFrame">StringOrDoubleChartValue</a></li>
<li><a href="StyleType.html" title="class in com.aspose.slides" target="classFrame">StyleType</a></li>
<li><a href="SummaryZoomFrame.html" title="class in com.aspose.slides" target="classFrame">SummaryZoomFrame</a></li>
<li><a href="SummaryZoomSection.html" title="class in com.aspose.slides" target="classFrame">SummaryZoomSection</a></li>
<li><a href="SummaryZoomSectionCollection.html" title="class in com.aspose.slides" target="classFrame">SummaryZoomSectionCollection</a></li>
<li><a href="SvgCoordinateUnit.html" title="class in com.aspose.slides" target="classFrame">SvgCoordinateUnit</a></li>
<li><a href="SvgEvent.html" title="class in com.aspose.slides" target="classFrame">SvgEvent</a></li>
<li><a href="SvgExternalFontsHandling.html" title="class in com.aspose.slides" target="classFrame">SvgExternalFontsHandling</a></li>
<li><a href="SvgImage.html" title="class in com.aspose.slides" target="classFrame">SvgImage</a></li>
<li><a href="SVGOptions.html" title="class in com.aspose.slides" target="classFrame">SVGOptions</a></li>
<li><a href="SvgShape.html" title="class in com.aspose.slides" target="classFrame">SvgShape</a></li>
<li><a href="SvgTSpan.html" title="class in com.aspose.slides" target="classFrame">SvgTSpan</a></li>
<li><a href="SwfOptions.html" title="class in com.aspose.slides" target="classFrame">SwfOptions</a></li>
<li><a href="SystemColor.html" title="class in com.aspose.slides" target="classFrame">SystemColor</a></li>
<li><a href="Tab.html" title="class in com.aspose.slides" target="classFrame">Tab</a></li>
<li><a href="TabAlignment.html" title="class in com.aspose.slides" target="classFrame">TabAlignment</a></li>
<li><a href="TabCollection.html" title="class in com.aspose.slides" target="classFrame">TabCollection</a></li>
<li><a href="TabFactory.html" title="class in com.aspose.slides" target="classFrame">TabFactory</a></li>
<li><a href="Table.html" title="class in com.aspose.slides" target="classFrame">Table</a></li>
<li><a href="TableFormat.html" title="class in com.aspose.slides" target="classFrame">TableFormat</a></li>
<li><a href="TableStylePreset.html" title="class in com.aspose.slides" target="classFrame">TableStylePreset</a></li>
<li><a href="TagCollection.html" title="class in com.aspose.slides" target="classFrame">TagCollection</a></li>
<li><a href="TemplateContext.html" title="class in com.aspose.slides" target="classFrame">TemplateContext</a></li>
<li><a href="TextAlignment.html" title="class in com.aspose.slides" target="classFrame">TextAlignment</a></li>
<li><a href="TextAnchorType.html" title="class in com.aspose.slides" target="classFrame">TextAnchorType</a></li>
<li><a href="TextAnimation.html" title="class in com.aspose.slides" target="classFrame">TextAnimation</a></li>
<li><a href="TextAnimationCollection.html" title="class in com.aspose.slides" target="classFrame">TextAnimationCollection</a></li>
<li><a href="TextAutofitType.html" title="class in com.aspose.slides" target="classFrame">TextAutofitType</a></li>
<li><a href="TextCapType.html" title="class in com.aspose.slides" target="classFrame">TextCapType</a></li>
<li><a href="TextExtractionArrangingMode.html" title="class in com.aspose.slides" target="classFrame">TextExtractionArrangingMode</a></li>
<li><a href="TextFrame.html" title="class in com.aspose.slides" target="classFrame">TextFrame</a></li>
<li><a href="TextFrameFormat.html" title="class in com.aspose.slides" target="classFrame">TextFrameFormat</a></li>
<li><a href="TextHighlightingOptions.html" title="class in com.aspose.slides" target="classFrame">TextHighlightingOptions</a></li>
<li><a href="TextInheritanceLimit.html" title="class in com.aspose.slides" target="classFrame">TextInheritanceLimit</a></li>
<li><a href="TextSearchOptions.html" title="class in com.aspose.slides" target="classFrame">TextSearchOptions</a></li>
<li><a href="TextShapeType.html" title="class in com.aspose.slides" target="classFrame">TextShapeType</a></li>
<li><a href="TextStrikethroughType.html" title="class in com.aspose.slides" target="classFrame">TextStrikethroughType</a></li>
<li><a href="TextStyle.html" title="class in com.aspose.slides" target="classFrame">TextStyle</a></li>
<li><a href="TextToHtmlConversionOptions.html" title="class in com.aspose.slides" target="classFrame">TextToHtmlConversionOptions</a></li>
<li><a href="TextUnderlineType.html" title="class in com.aspose.slides" target="classFrame">TextUnderlineType</a></li>
<li><a href="TextVerticalOverflowType.html" title="class in com.aspose.slides" target="classFrame">TextVerticalOverflowType</a></li>
<li><a href="TextVerticalType.html" title="class in com.aspose.slides" target="classFrame">TextVerticalType</a></li>
<li><a href="Theme.html" title="class in com.aspose.slides" target="classFrame">Theme</a></li>
<li><a href="ThreeDFormat.html" title="class in com.aspose.slides" target="classFrame">ThreeDFormat</a></li>
<li><a href="TickLabelPositionType.html" title="class in com.aspose.slides" target="classFrame">TickLabelPositionType</a></li>
<li><a href="TickMarkType.html" title="class in com.aspose.slides" target="classFrame">TickMarkType</a></li>
<li><a href="TiffCompressionTypes.html" title="class in com.aspose.slides" target="classFrame">TiffCompressionTypes</a></li>
<li><a href="TiffOptions.html" title="class in com.aspose.slides" target="classFrame">TiffOptions</a></li>
<li><a href="TileFlip.html" title="class in com.aspose.slides" target="classFrame">TileFlip</a></li>
<li><a href="TimeUnitType.html" title="class in com.aspose.slides" target="classFrame">TimeUnitType</a></li>
<li><a href="Timing.html" title="class in com.aspose.slides" target="classFrame">Timing</a></li>
<li><a href="Tint.html" title="class in com.aspose.slides" target="classFrame">Tint</a></li>
<li><a href="TransitionCornerAndCenterDirectionType.html" title="class in com.aspose.slides" target="classFrame">TransitionCornerAndCenterDirectionType</a></li>
<li><a href="TransitionCornerDirectionType.html" title="class in com.aspose.slides" target="classFrame">TransitionCornerDirectionType</a></li>
<li><a href="TransitionEightDirectionType.html" title="class in com.aspose.slides" target="classFrame">TransitionEightDirectionType</a></li>
<li><a href="TransitionInOutDirectionType.html" title="class in com.aspose.slides" target="classFrame">TransitionInOutDirectionType</a></li>
<li><a href="TransitionLeftRightDirectionType.html" title="class in com.aspose.slides" target="classFrame">TransitionLeftRightDirectionType</a></li>
<li><a href="TransitionMorphType.html" title="class in com.aspose.slides" target="classFrame">TransitionMorphType</a></li>
<li><a href="TransitionPattern.html" title="class in com.aspose.slides" target="classFrame">TransitionPattern</a></li>
<li><a href="TransitionShredPattern.html" title="class in com.aspose.slides" target="classFrame">TransitionShredPattern</a></li>
<li><a href="TransitionSideDirectionType.html" title="class in com.aspose.slides" target="classFrame">TransitionSideDirectionType</a></li>
<li><a href="TransitionSoundMode.html" title="class in com.aspose.slides" target="classFrame">TransitionSoundMode</a></li>
<li><a href="TransitionSpeed.html" title="class in com.aspose.slides" target="classFrame">TransitionSpeed</a></li>
<li><a href="TransitionType.html" title="class in com.aspose.slides" target="classFrame">TransitionType</a></li>
<li><a href="TransitionValueBase.html" title="class in com.aspose.slides" target="classFrame">TransitionValueBase</a></li>
<li><a href="Trendline.html" title="class in com.aspose.slides" target="classFrame">Trendline</a></li>
<li><a href="TrendlineCollection.html" title="class in com.aspose.slides" target="classFrame">TrendlineCollection</a></li>
<li><a href="TrendlineType.html" title="class in com.aspose.slides" target="classFrame">TrendlineType</a></li>
<li><a href="UpDownBarsManager.html" title="class in com.aspose.slides" target="classFrame">UpDownBarsManager</a></li>
<li><a href="VbaModule.html" title="class in com.aspose.slides" target="classFrame">VbaModule</a></li>
<li><a href="VbaModuleCollection.html" title="class in com.aspose.slides" target="classFrame">VbaModuleCollection</a></li>
<li><a href="VbaProject.html" title="class in com.aspose.slides" target="classFrame">VbaProject</a></li>
<li><a href="VbaProjectFactory.html" title="class in com.aspose.slides" target="classFrame">VbaProjectFactory</a></li>
<li><a href="VbaReferenceCollection.html" title="class in com.aspose.slides" target="classFrame">VbaReferenceCollection</a></li>
<li><a href="VbaReferenceFactory.html" title="class in com.aspose.slides" target="classFrame">VbaReferenceFactory</a></li>
<li><a href="VbaReferenceOleTypeLib.html" title="class in com.aspose.slides" target="classFrame">VbaReferenceOleTypeLib</a></li>
<li><a href="Video.html" title="class in com.aspose.slides" target="classFrame">Video</a></li>
<li><a href="VideoCollection.html" title="class in com.aspose.slides" target="classFrame">VideoCollection</a></li>
<li><a href="VideoFrame.html" title="class in com.aspose.slides" target="classFrame">VideoFrame</a></li>
<li><a href="VideoPlayerHtmlController.html" title="class in com.aspose.slides" target="classFrame">VideoPlayerHtmlController</a></li>
<li><a href="VideoPlayerHtmlControllerFactory.html" title="class in com.aspose.slides" target="classFrame">VideoPlayerHtmlControllerFactory</a></li>
<li><a href="VideoPlayModePreset.html" title="class in com.aspose.slides" target="classFrame">VideoPlayModePreset</a></li>
<li><a href="ViewProperties.html" title="class in com.aspose.slides" target="classFrame">ViewProperties</a></li>
<li><a href="ViewType.html" title="class in com.aspose.slides" target="classFrame">ViewType</a></li>
<li><a href="WarningType.html" title="class in com.aspose.slides" target="classFrame">WarningType</a></li>
<li><a href="WebDocument.html" title="class in com.aspose.slides" target="classFrame">WebDocument</a></li>
<li><a href="WebDocumentOptions.html" title="class in com.aspose.slides" target="classFrame">WebDocumentOptions</a></li>
<li><a href="WheelTransition.html" title="class in com.aspose.slides" target="classFrame">WheelTransition</a></li>
<li><a href="XamlOptions.html" title="class in com.aspose.slides" target="classFrame">XamlOptions</a></li>
<li><a href="XpsOptions.html" title="class in com.aspose.slides" target="classFrame">XpsOptions</a></li>
<li><a href="Zip64Mode.html" title="class in com.aspose.slides" target="classFrame">Zip64Mode</a></li>
<li><a href="ZoomFrame.html" title="class in com.aspose.slides" target="classFrame">ZoomFrame</a></li>
<li><a href="ZoomImageType.html" title="class in com.aspose.slides" target="classFrame">ZoomImageType</a></li>
<li><a href="ZoomLayout.html" title="class in com.aspose.slides" target="classFrame">ZoomLayout</a></li>
<li><a href="ZoomObject.html" title="class in com.aspose.slides" target="classFrame">ZoomObject</a></li>
</ul>
<h2 title="Exceptions">Exceptions</h2>
<ul title="Exceptions">
<li><a href="AsposeLicenseException.html" title="class in com.aspose.slides" target="classFrame">AsposeLicenseException</a></li>
<li><a href="AxesCompositionNotCombinableException.html" title="class in com.aspose.slides" target="classFrame">AxesCompositionNotCombinableException</a></li>
<li><a href="CannotCombine2DAnd3DChartsException.html" title="class in com.aspose.slides" target="classFrame">CannotCombine2DAnd3DChartsException</a></li>
<li><a href="CellCircularReferenceException.html" title="class in com.aspose.slides" target="classFrame">CellCircularReferenceException</a></li>
<li><a href="CellInvalidFormulaException.html" title="class in com.aspose.slides" target="classFrame">CellInvalidFormulaException</a></li>
<li><a href="CellInvalidReferenceException.html" title="class in com.aspose.slides" target="classFrame">CellInvalidReferenceException</a></li>
<li><a href="CellUnsupportedDataException.html" title="class in com.aspose.slides" target="classFrame">CellUnsupportedDataException</a></li>
<li><a href="InvalidPasswordException.html" title="class in com.aspose.slides" target="classFrame">InvalidPasswordException</a></li>
<li><a href="OdpException.html" title="class in com.aspose.slides" target="classFrame">OdpException</a></li>
<li><a href="OdpReadException.html" title="class in com.aspose.slides" target="classFrame">OdpReadException</a></li>
<li><a href="OOXMLCorruptFileException.html" title="class in com.aspose.slides" target="classFrame">OOXMLCorruptFileException</a></li>
<li><a href="OOXMLException.html" title="class in com.aspose.slides" target="classFrame">OOXMLException</a></li>
<li><a href="PptCorruptFileException.html" title="class in com.aspose.slides" target="classFrame">PptCorruptFileException</a></li>
<li><a href="PptEditException.html" title="class in com.aspose.slides" target="classFrame">PptEditException</a></li>
<li><a href="PptException.html" title="class in com.aspose.slides" target="classFrame">PptException</a></li>
<li><a href="PptReadException.html" title="class in com.aspose.slides" target="classFrame">PptReadException</a></li>
<li><a href="PptUnsupportedFormatException.html" title="class in com.aspose.slides" target="classFrame">PptUnsupportedFormatException</a></li>
<li><a href="PptxCorruptFileException.html" title="class in com.aspose.slides" target="classFrame">PptxCorruptFileException</a></li>
<li><a href="PptxEditException.html" title="class in com.aspose.slides" target="classFrame">PptxEditException</a></li>
<li><a href="PptxException.html" title="class in com.aspose.slides" target="classFrame">PptxException</a></li>
<li><a href="PptxReadException.html" title="class in com.aspose.slides" target="classFrame">PptxReadException</a></li>
<li><a href="PptxUnsupportedFormatException.html" title="class in com.aspose.slides" target="classFrame">PptxUnsupportedFormatException</a></li>
<li><a href="SlidesAIAgentException.html" title="class in com.aspose.slides" target="classFrame">SlidesAIAgentException</a></li>
</ul>
</div>
</body>
</html>
