<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_421) on Fri Jul 11 05:32:44 CDT 2025 -->
<title>Table (Aspose.Slides for Java (25.7) API Documentation)</title>
<meta name="date" content="2025-07-11">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Table (Aspose.Slides for Java (25.7) API Documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/TabFactory.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/TableFormat.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/Table.html" target="_top">Frames</a></li>
<li><a href="Table.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.aspose.slides</div>
<h2 title="Class Table" class="title">Class Table</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../com/aspose/slides/Shape.html" title="class in com.aspose.slides">com.aspose.slides.Shape</a></li>
<li>
<ul class="inheritance">
<li><a href="../../../com/aspose/slides/GraphicalObject.html" title="class in com.aspose.slides">com.aspose.slides.GraphicalObject</a></li>
<li>
<ul class="inheritance">
<li>com.aspose.slides.Table</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../com/aspose/slides/IBulkTextFormattable.html" title="interface in com.aspose.slides">IBulkTextFormattable</a>, <a href="../../../com/aspose/slides/IGraphicalObject.html" title="interface in com.aspose.slides">IGraphicalObject</a>, <a href="../../../com/aspose/slides/IHyperlinkContainer.html" title="interface in com.aspose.slides">IHyperlinkContainer</a>, <a href="../../../com/aspose/slides/IPresentationComponent.html" title="interface in com.aspose.slides">IPresentationComponent</a>, <a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a>, <a href="../../../com/aspose/slides/ISlideComponent.html" title="interface in com.aspose.slides">ISlideComponent</a>, <a href="../../../com/aspose/slides/ITable.html" title="interface in com.aspose.slides">ITable</a></dd>
</dl>
<hr>
<br>
<pre>public final class <span class="typeNameLabel">Table</span>
extends <a href="../../../com/aspose/slides/GraphicalObject.html" title="class in com.aspose.slides">GraphicalObject</a>
implements <a href="../../../com/aspose/slides/ITable.html" title="interface in com.aspose.slides">ITable</a></pre>
<div class="block"><p>
 Represents a table on a slide.
 </p></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/ICell.html" title="interface in com.aspose.slides">ICell</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Table.html#get_Item-int-int-">get_Item</a></span>(int&nbsp;columnIndex,
        int&nbsp;rowIndex)</code>
<div class="block">
 Returns the cell at the specified column and row indexes.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IColumnCollection.html" title="interface in com.aspose.slides">IColumnCollection</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Table.html#getColumns--">getColumns</a></span>()</code>
<div class="block">
 Returns the collectoin of columns.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IFillFormat.html" title="interface in com.aspose.slides">IFillFormat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Table.html#getFillFormat--">getFillFormat</a></span>()</code>
<div class="block">
 Returns a TableFormat.FillFormat object containing the fill formatting for the Table.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Table.html#getFirstCol--">getFirstCol</a></span>()</code>
<div class="block">
 Determines whether the first column of a table has to be drawn with a special formatting.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Table.html#getFirstRow--">getFirstRow</a></span>()</code>
<div class="block">
 Determines whether the first row of a table has to be drawn with a special formatting.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Table.html#getHorizontalBanding--">getHorizontalBanding</a></span>()</code>
<div class="block">
 Determines whether the even rows has to be drawn with a different formatting.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Table.html#getLastCol--">getLastCol</a></span>()</code>
<div class="block">
 Determines whether the last column of a table has to be drawn with a special formatting.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Table.html#getLastRow--">getLastRow</a></span>()</code>
<div class="block">
 Determines whether the last row of a table has to be drawn with a special formatting.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Table.html#getRightToLeft--">getRightToLeft</a></span>()</code>
<div class="block">
 Determines whether the table has right to left reading order.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IRowCollection.html" title="interface in com.aspose.slides">IRowCollection</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Table.html#getRows--">getRows</a></span>()</code>
<div class="block">
 Returns the collectoin of rows.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Table.html#getStylePreset--">getStylePreset</a></span>()</code>
<div class="block">
 Gets or sets builtin table style.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/ITableFormat.html" title="interface in com.aspose.slides">ITableFormat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Table.html#getTableFormat--">getTableFormat</a></span>()</code>
<div class="block">
 Returns the TableFormat object that contains formatting properties for this table.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Table.html#getVerticalBanding--">getVerticalBanding</a></span>()</code>
<div class="block">
 Determines whether the even columns has to be drawn with a different formatting.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/ICell.html" title="interface in com.aspose.slides">ICell</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Table.html#mergeCells-com.aspose.slides.ICell-com.aspose.slides.ICell-boolean-">mergeCells</a></span>(<a href="../../../com/aspose/slides/ICell.html" title="interface in com.aspose.slides">ICell</a>&nbsp;cell1,
          <a href="../../../com/aspose/slides/ICell.html" title="interface in com.aspose.slides">ICell</a>&nbsp;cell2,
          boolean&nbsp;allowSplitting)</code>
<div class="block">
 Merges neighbour cells.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Table.html#setFirstCol-boolean-">setFirstCol</a></span>(boolean&nbsp;value)</code>
<div class="block">
 Determines whether the first column of a table has to be drawn with a special formatting.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Table.html#setFirstRow-boolean-">setFirstRow</a></span>(boolean&nbsp;value)</code>
<div class="block">
 Determines whether the first row of a table has to be drawn with a special formatting.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Table.html#setHorizontalBanding-boolean-">setHorizontalBanding</a></span>(boolean&nbsp;value)</code>
<div class="block">
 Determines whether the even rows has to be drawn with a different formatting.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Table.html#setLastCol-boolean-">setLastCol</a></span>(boolean&nbsp;value)</code>
<div class="block">
 Determines whether the last column of a table has to be drawn with a special formatting.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Table.html#setLastRow-boolean-">setLastRow</a></span>(boolean&nbsp;value)</code>
<div class="block">
 Determines whether the last row of a table has to be drawn with a special formatting.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Table.html#setRightToLeft-boolean-">setRightToLeft</a></span>(boolean&nbsp;value)</code>
<div class="block">
 Determines whether the table has right to left reading order.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Table.html#setStylePreset-int-">setStylePreset</a></span>(int&nbsp;value)</code>
<div class="block">
 Gets or sets builtin table style.</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Table.html#setTextFormat-com.aspose.slides.IParagraphFormat-">setTextFormat</a></span>(<a href="../../../com/aspose/slides/IParagraphFormat.html" title="interface in com.aspose.slides">IParagraphFormat</a>&nbsp;source)</code>
<div class="block">
 Sets defined paragraph format properties to all table cells' paragraphs.</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Table.html#setTextFormat-com.aspose.slides.IPortionFormat-">setTextFormat</a></span>(<a href="../../../com/aspose/slides/IPortionFormat.html" title="interface in com.aspose.slides">IPortionFormat</a>&nbsp;source)</code>
<div class="block">
 Sets defined portion format properties to all table cells' portions.</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Table.html#setTextFormat-com.aspose.slides.ITextFrameFormat-">setTextFormat</a></span>(<a href="../../../com/aspose/slides/ITextFrameFormat.html" title="interface in com.aspose.slides">ITextFrameFormat</a>&nbsp;source)</code>
<div class="block">
 Sets defined text frame format properties to all table cells' text frames.</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Table.html#setVerticalBanding-boolean-">setVerticalBanding</a></span>(boolean&nbsp;value)</code>
<div class="block">
 Determines whether the even columns has to be drawn with a different formatting.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.aspose.slides.GraphicalObject">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;com.aspose.slides.<a href="../../../com/aspose/slides/GraphicalObject.html" title="class in com.aspose.slides">GraphicalObject</a></h3>
<code><a href="../../../com/aspose/slides/GraphicalObject.html#getGraphicalObjectLock--">getGraphicalObjectLock</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.aspose.slides.Shape">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;com.aspose.slides.<a href="../../../com/aspose/slides/Shape.html" title="class in com.aspose.slides">Shape</a></h3>
<code><a href="../../../com/aspose/slides/Shape.html#addPlaceholder-com.aspose.slides.IPlaceholder-">addPlaceholder</a>, <a href="../../../com/aspose/slides/Shape.html#getAlternativeText--">getAlternativeText</a>, <a href="../../../com/aspose/slides/Shape.html#getAlternativeTextTitle--">getAlternativeTextTitle</a>, <a href="../../../com/aspose/slides/Shape.html#getBasePlaceholder--">getBasePlaceholder</a>, <a href="../../../com/aspose/slides/Shape.html#getBlackWhiteMode--">getBlackWhiteMode</a>, <a href="../../../com/aspose/slides/Shape.html#getConnectionSiteCount--">getConnectionSiteCount</a>, <a href="../../../com/aspose/slides/Shape.html#getCustomData--">getCustomData</a>, <a href="../../../com/aspose/slides/Shape.html#getEffectFormat--">getEffectFormat</a>, <a href="../../../com/aspose/slides/Shape.html#getFrame--">getFrame</a>, <a href="../../../com/aspose/slides/Shape.html#getHeight--">getHeight</a>, <a href="../../../com/aspose/slides/Shape.html#getHidden--">getHidden</a>, <a href="../../../com/aspose/slides/Shape.html#getHyperlinkClick--">getHyperlinkClick</a>, <a href="../../../com/aspose/slides/Shape.html#getHyperlinkManager--">getHyperlinkManager</a>, <a href="../../../com/aspose/slides/Shape.html#getHyperlinkMouseOver--">getHyperlinkMouseOver</a>, <a href="../../../com/aspose/slides/Shape.html#getImage--">getImage</a>, <a href="../../../com/aspose/slides/Shape.html#getImage-int-float-float-">getImage</a>, <a href="../../../com/aspose/slides/Shape.html#getLineFormat--">getLineFormat</a>, <a href="../../../com/aspose/slides/Shape.html#getName--">getName</a>, <a href="../../../com/aspose/slides/Shape.html#getOfficeInteropShapeId--">getOfficeInteropShapeId</a>, <a href="../../../com/aspose/slides/Shape.html#getParent_Immediate--">getParent_Immediate</a>, <a href="../../../com/aspose/slides/Shape.html#getParentGroup--">getParentGroup</a>, <a href="../../../com/aspose/slides/Shape.html#getPlaceholder--">getPlaceholder</a>, <a href="../../../com/aspose/slides/Shape.html#getPresentation--">getPresentation</a>, <a href="../../../com/aspose/slides/Shape.html#getRawFrame--">getRawFrame</a>, <a href="../../../com/aspose/slides/Shape.html#getRotation--">getRotation</a>, <a href="../../../com/aspose/slides/Shape.html#getShapeLock--">getShapeLock</a>, <a href="../../../com/aspose/slides/Shape.html#getSlide--">getSlide</a>, <a href="../../../com/aspose/slides/Shape.html#getThreeDFormat--">getThreeDFormat</a>, <a href="../../../com/aspose/slides/Shape.html#getThumbnail--">getThumbnail</a>, <a href="../../../com/aspose/slides/Shape.html#getThumbnail-int-float-float-">getThumbnail</a>, <a href="../../../com/aspose/slides/Shape.html#getUniqueId--">getUniqueId</a>, <a href="../../../com/aspose/slides/Shape.html#getWidth--">getWidth</a>, <a href="../../../com/aspose/slides/Shape.html#getX--">getX</a>, <a href="../../../com/aspose/slides/Shape.html#getY--">getY</a>, <a href="../../../com/aspose/slides/Shape.html#getZOrderPosition--">getZOrderPosition</a>, <a href="../../../com/aspose/slides/Shape.html#isDecorative--">isDecorative</a>, <a href="../../../com/aspose/slides/Shape.html#isGrouped--">isGrouped</a>, <a href="../../../com/aspose/slides/Shape.html#isTextHolder--">isTextHolder</a>, <a href="../../../com/aspose/slides/Shape.html#removePlaceholder--">removePlaceholder</a>, <a href="../../../com/aspose/slides/Shape.html#setAlternativeText-java.lang.String-">setAlternativeText</a>, <a href="../../../com/aspose/slides/Shape.html#setAlternativeTextTitle-java.lang.String-">setAlternativeTextTitle</a>, <a href="../../../com/aspose/slides/Shape.html#setBlackWhiteMode-byte-">setBlackWhiteMode</a>, <a href="../../../com/aspose/slides/Shape.html#setDecorative-boolean-">setDecorative</a>, <a href="../../../com/aspose/slides/Shape.html#setFrame-com.aspose.slides.IShapeFrame-">setFrame</a>, <a href="../../../com/aspose/slides/Shape.html#setHeight-float-">setHeight</a>, <a href="../../../com/aspose/slides/Shape.html#setHidden-boolean-">setHidden</a>, <a href="../../../com/aspose/slides/Shape.html#setHyperlinkClick-com.aspose.slides.IHyperlink-">setHyperlinkClick</a>, <a href="../../../com/aspose/slides/Shape.html#setHyperlinkMouseOver-com.aspose.slides.IHyperlink-">setHyperlinkMouseOver</a>, <a href="../../../com/aspose/slides/Shape.html#setName-java.lang.String-">setName</a>, <a href="../../../com/aspose/slides/Shape.html#setRawFrame-com.aspose.slides.IShapeFrame-">setRawFrame</a>, <a href="../../../com/aspose/slides/Shape.html#setRotation-float-">setRotation</a>, <a href="../../../com/aspose/slides/Shape.html#setWidth-float-">setWidth</a>, <a href="../../../com/aspose/slides/Shape.html#setX-float-">setX</a>, <a href="../../../com/aspose/slides/Shape.html#setY-float-">setY</a>, <a href="../../../com/aspose/slides/Shape.html#writeAsSvg-java.io.OutputStream-">writeAsSvg</a>, <a href="../../../com/aspose/slides/Shape.html#writeAsSvg-java.io.OutputStream-com.aspose.slides.ISVGOptions-">writeAsSvg</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.aspose.slides.IGraphicalObject">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;com.aspose.slides.<a href="../../../com/aspose/slides/IGraphicalObject.html" title="interface in com.aspose.slides">IGraphicalObject</a></h3>
<code><a href="../../../com/aspose/slides/IGraphicalObject.html#getGraphicalObjectLock--">getGraphicalObjectLock</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.aspose.slides.IShape">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;com.aspose.slides.<a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a></h3>
<code><a href="../../../com/aspose/slides/IShape.html#addPlaceholder-com.aspose.slides.IPlaceholder-">addPlaceholder</a>, <a href="../../../com/aspose/slides/IShape.html#getAlternativeText--">getAlternativeText</a>, <a href="../../../com/aspose/slides/IShape.html#getAlternativeTextTitle--">getAlternativeTextTitle</a>, <a href="../../../com/aspose/slides/IShape.html#getBasePlaceholder--">getBasePlaceholder</a>, <a href="../../../com/aspose/slides/IShape.html#getBlackWhiteMode--">getBlackWhiteMode</a>, <a href="../../../com/aspose/slides/IShape.html#getConnectionSiteCount--">getConnectionSiteCount</a>, <a href="../../../com/aspose/slides/IShape.html#getCustomData--">getCustomData</a>, <a href="../../../com/aspose/slides/IShape.html#getEffectFormat--">getEffectFormat</a>, <a href="../../../com/aspose/slides/IShape.html#getFrame--">getFrame</a>, <a href="../../../com/aspose/slides/IShape.html#getHeight--">getHeight</a>, <a href="../../../com/aspose/slides/IShape.html#getHidden--">getHidden</a>, <a href="../../../com/aspose/slides/IShape.html#getImage--">getImage</a>, <a href="../../../com/aspose/slides/IShape.html#getImage-int-float-float-">getImage</a>, <a href="../../../com/aspose/slides/IShape.html#getLineFormat--">getLineFormat</a>, <a href="../../../com/aspose/slides/IShape.html#getName--">getName</a>, <a href="../../../com/aspose/slides/IShape.html#getOfficeInteropShapeId--">getOfficeInteropShapeId</a>, <a href="../../../com/aspose/slides/IShape.html#getParentGroup--">getParentGroup</a>, <a href="../../../com/aspose/slides/IShape.html#getPlaceholder--">getPlaceholder</a>, <a href="../../../com/aspose/slides/IShape.html#getRawFrame--">getRawFrame</a>, <a href="../../../com/aspose/slides/IShape.html#getRotation--">getRotation</a>, <a href="../../../com/aspose/slides/IShape.html#getShapeLock--">getShapeLock</a>, <a href="../../../com/aspose/slides/IShape.html#getThreeDFormat--">getThreeDFormat</a>, <a href="../../../com/aspose/slides/IShape.html#getThumbnail--">getThumbnail</a>, <a href="../../../com/aspose/slides/IShape.html#getThumbnail-int-float-float-">getThumbnail</a>, <a href="../../../com/aspose/slides/IShape.html#getUniqueId--">getUniqueId</a>, <a href="../../../com/aspose/slides/IShape.html#getWidth--">getWidth</a>, <a href="../../../com/aspose/slides/IShape.html#getX--">getX</a>, <a href="../../../com/aspose/slides/IShape.html#getY--">getY</a>, <a href="../../../com/aspose/slides/IShape.html#getZOrderPosition--">getZOrderPosition</a>, <a href="../../../com/aspose/slides/IShape.html#isDecorative--">isDecorative</a>, <a href="../../../com/aspose/slides/IShape.html#isGrouped--">isGrouped</a>, <a href="../../../com/aspose/slides/IShape.html#isTextHolder--">isTextHolder</a>, <a href="../../../com/aspose/slides/IShape.html#removePlaceholder--">removePlaceholder</a>, <a href="../../../com/aspose/slides/IShape.html#setAlternativeText-java.lang.String-">setAlternativeText</a>, <a href="../../../com/aspose/slides/IShape.html#setAlternativeTextTitle-java.lang.String-">setAlternativeTextTitle</a>, <a href="../../../com/aspose/slides/IShape.html#setBlackWhiteMode-byte-">setBlackWhiteMode</a>, <a href="../../../com/aspose/slides/IShape.html#setDecorative-boolean-">setDecorative</a>, <a href="../../../com/aspose/slides/IShape.html#setFrame-com.aspose.slides.IShapeFrame-">setFrame</a>, <a href="../../../com/aspose/slides/IShape.html#setHeight-float-">setHeight</a>, <a href="../../../com/aspose/slides/IShape.html#setHidden-boolean-">setHidden</a>, <a href="../../../com/aspose/slides/IShape.html#setName-java.lang.String-">setName</a>, <a href="../../../com/aspose/slides/IShape.html#setRawFrame-com.aspose.slides.IShapeFrame-">setRawFrame</a>, <a href="../../../com/aspose/slides/IShape.html#setRotation-float-">setRotation</a>, <a href="../../../com/aspose/slides/IShape.html#setWidth-float-">setWidth</a>, <a href="../../../com/aspose/slides/IShape.html#setX-float-">setX</a>, <a href="../../../com/aspose/slides/IShape.html#setY-float-">setY</a>, <a href="../../../com/aspose/slides/IShape.html#writeAsSvg-java.io.OutputStream-">writeAsSvg</a>, <a href="../../../com/aspose/slides/IShape.html#writeAsSvg-java.io.OutputStream-com.aspose.slides.ISVGOptions-">writeAsSvg</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.aspose.slides.ISlideComponent">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;com.aspose.slides.<a href="../../../com/aspose/slides/ISlideComponent.html" title="interface in com.aspose.slides">ISlideComponent</a></h3>
<code><a href="../../../com/aspose/slides/ISlideComponent.html#getSlide--">getSlide</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.aspose.slides.IPresentationComponent">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;com.aspose.slides.<a href="../../../com/aspose/slides/IPresentationComponent.html" title="interface in com.aspose.slides">IPresentationComponent</a></h3>
<code><a href="../../../com/aspose/slides/IPresentationComponent.html#getPresentation--">getPresentation</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.aspose.slides.IHyperlinkContainer">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;com.aspose.slides.<a href="../../../com/aspose/slides/IHyperlinkContainer.html" title="interface in com.aspose.slides">IHyperlinkContainer</a></h3>
<code><a href="../../../com/aspose/slides/IHyperlinkContainer.html#getHyperlinkClick--">getHyperlinkClick</a>, <a href="../../../com/aspose/slides/IHyperlinkContainer.html#getHyperlinkManager--">getHyperlinkManager</a>, <a href="../../../com/aspose/slides/IHyperlinkContainer.html#getHyperlinkMouseOver--">getHyperlinkMouseOver</a>, <a href="../../../com/aspose/slides/IHyperlinkContainer.html#setHyperlinkClick-com.aspose.slides.IHyperlink-">setHyperlinkClick</a>, <a href="../../../com/aspose/slides/IHyperlinkContainer.html#setHyperlinkMouseOver-com.aspose.slides.IHyperlink-">setHyperlinkMouseOver</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="get_Item-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_Item</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/ICell.html" title="interface in com.aspose.slides">ICell</a>&nbsp;get_Item(int&nbsp;columnIndex,
                            int&nbsp;rowIndex)</pre>
<div class="block"><p>
 Returns the cell at the specified column and row indexes.
 Read-only <a href="../../../com/aspose/slides/Cell.html" title="class in com.aspose.slides"><code>Cell</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITable.html#get_Item-int-int-">get_Item</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITable.html" title="interface in com.aspose.slides">ITable</a></code></dd>
</dl>
</li>
</ul>
<a name="getRows--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRows</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IRowCollection.html" title="interface in com.aspose.slides">IRowCollection</a>&nbsp;getRows()</pre>
<div class="block"><p>
 Returns the collectoin of rows.
 Read-only <a href="../../../com/aspose/slides/IRowCollection.html" title="interface in com.aspose.slides"><code>IRowCollection</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITable.html#getRows--">getRows</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITable.html" title="interface in com.aspose.slides">ITable</a></code></dd>
</dl>
</li>
</ul>
<a name="getColumns--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getColumns</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IColumnCollection.html" title="interface in com.aspose.slides">IColumnCollection</a>&nbsp;getColumns()</pre>
<div class="block"><p>
 Returns the collectoin of columns.
 Read-only <a href="../../../com/aspose/slides/IColumnCollection.html" title="interface in com.aspose.slides"><code>IColumnCollection</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITable.html#getColumns--">getColumns</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITable.html" title="interface in com.aspose.slides">ITable</a></code></dd>
</dl>
</li>
</ul>
<a name="getTableFormat--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTableFormat</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/ITableFormat.html" title="interface in com.aspose.slides">ITableFormat</a>&nbsp;getTableFormat()</pre>
<div class="block"><p>
 Returns the TableFormat object that contains formatting properties for this table.
 Read-only <a href="../../../com/aspose/slides/ITableFormat.html" title="interface in com.aspose.slides"><code>ITableFormat</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITable.html#getTableFormat--">getTableFormat</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITable.html" title="interface in com.aspose.slides">ITable</a></code></dd>
</dl>
</li>
</ul>
<a name="mergeCells-com.aspose.slides.ICell-com.aspose.slides.ICell-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>mergeCells</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/ICell.html" title="interface in com.aspose.slides">ICell</a>&nbsp;mergeCells(<a href="../../../com/aspose/slides/ICell.html" title="interface in com.aspose.slides">ICell</a>&nbsp;cell1,
                              <a href="../../../com/aspose/slides/ICell.html" title="interface in com.aspose.slides">ICell</a>&nbsp;cell2,
                              boolean&nbsp;allowSplitting)</pre>
<div class="block"><p>
 Merges neighbour cells.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITable.html#mergeCells-com.aspose.slides.ICell-com.aspose.slides.ICell-boolean-">mergeCells</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITable.html" title="interface in com.aspose.slides">ITable</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>cell1</code> - Cell to merge.</dd>
<dd><code>cell2</code> - Cell to merge.</dd>
<dd><code>allowSplitting</code> - True to allow cells splitting.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Merged cell.</dd>
</dl>
</li>
</ul>
<a name="getStylePreset--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStylePreset</h4>
<pre>public final&nbsp;int&nbsp;getStylePreset()</pre>
<div class="block"><p>
 Gets or sets builtin table style.
 Read/write <a href="../../../com/aspose/slides/TableStylePreset.html" title="class in com.aspose.slides"><code>TableStylePreset</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITable.html#getStylePreset--">getStylePreset</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITable.html" title="interface in com.aspose.slides">ITable</a></code></dd>
</dl>
</li>
</ul>
<a name="setStylePreset-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setStylePreset</h4>
<pre>public final&nbsp;void&nbsp;setStylePreset(int&nbsp;value)</pre>
<div class="block"><p>
 Gets or sets builtin table style.
 Read/write <a href="../../../com/aspose/slides/TableStylePreset.html" title="class in com.aspose.slides"><code>TableStylePreset</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITable.html#setStylePreset-int-">setStylePreset</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITable.html" title="interface in com.aspose.slides">ITable</a></code></dd>
</dl>
</li>
</ul>
<a name="getRightToLeft--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRightToLeft</h4>
<pre>public final&nbsp;boolean&nbsp;getRightToLeft()</pre>
<div class="block"><p>
 Determines whether the table has right to left reading order.
 Read-write <code>boolean</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITable.html#getRightToLeft--">getRightToLeft</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITable.html" title="interface in com.aspose.slides">ITable</a></code></dd>
</dl>
</li>
</ul>
<a name="setRightToLeft-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRightToLeft</h4>
<pre>public final&nbsp;void&nbsp;setRightToLeft(boolean&nbsp;value)</pre>
<div class="block"><p>
 Determines whether the table has right to left reading order.
 Read-write <code>boolean</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITable.html#setRightToLeft-boolean-">setRightToLeft</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITable.html" title="interface in com.aspose.slides">ITable</a></code></dd>
</dl>
</li>
</ul>
<a name="getFirstRow--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFirstRow</h4>
<pre>public final&nbsp;boolean&nbsp;getFirstRow()</pre>
<div class="block"><p>
 Determines whether the first row of a table has to be drawn with a special formatting.
 Read/write <code>boolean</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITable.html#getFirstRow--">getFirstRow</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITable.html" title="interface in com.aspose.slides">ITable</a></code></dd>
</dl>
</li>
</ul>
<a name="setFirstRow-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFirstRow</h4>
<pre>public final&nbsp;void&nbsp;setFirstRow(boolean&nbsp;value)</pre>
<div class="block"><p>
 Determines whether the first row of a table has to be drawn with a special formatting.
 Read/write <code>boolean</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITable.html#setFirstRow-boolean-">setFirstRow</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITable.html" title="interface in com.aspose.slides">ITable</a></code></dd>
</dl>
</li>
</ul>
<a name="getFirstCol--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFirstCol</h4>
<pre>public final&nbsp;boolean&nbsp;getFirstCol()</pre>
<div class="block"><p>
 Determines whether the first column of a table has to be drawn with a special formatting.
 Read/write <code>boolean</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITable.html#getFirstCol--">getFirstCol</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITable.html" title="interface in com.aspose.slides">ITable</a></code></dd>
</dl>
</li>
</ul>
<a name="setFirstCol-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFirstCol</h4>
<pre>public final&nbsp;void&nbsp;setFirstCol(boolean&nbsp;value)</pre>
<div class="block"><p>
 Determines whether the first column of a table has to be drawn with a special formatting.
 Read/write <code>boolean</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITable.html#setFirstCol-boolean-">setFirstCol</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITable.html" title="interface in com.aspose.slides">ITable</a></code></dd>
</dl>
</li>
</ul>
<a name="getLastRow--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLastRow</h4>
<pre>public final&nbsp;boolean&nbsp;getLastRow()</pre>
<div class="block"><p>
 Determines whether the last row of a table has to be drawn with a special formatting.
 Read/write <code>boolean</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITable.html#getLastRow--">getLastRow</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITable.html" title="interface in com.aspose.slides">ITable</a></code></dd>
</dl>
</li>
</ul>
<a name="setLastRow-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLastRow</h4>
<pre>public final&nbsp;void&nbsp;setLastRow(boolean&nbsp;value)</pre>
<div class="block"><p>
 Determines whether the last row of a table has to be drawn with a special formatting.
 Read/write <code>boolean</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITable.html#setLastRow-boolean-">setLastRow</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITable.html" title="interface in com.aspose.slides">ITable</a></code></dd>
</dl>
</li>
</ul>
<a name="getLastCol--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLastCol</h4>
<pre>public final&nbsp;boolean&nbsp;getLastCol()</pre>
<div class="block"><p>
 Determines whether the last column of a table has to be drawn with a special formatting.
 Read/write <code>boolean</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITable.html#getLastCol--">getLastCol</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITable.html" title="interface in com.aspose.slides">ITable</a></code></dd>
</dl>
</li>
</ul>
<a name="setLastCol-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLastCol</h4>
<pre>public final&nbsp;void&nbsp;setLastCol(boolean&nbsp;value)</pre>
<div class="block"><p>
 Determines whether the last column of a table has to be drawn with a special formatting.
 Read/write <code>boolean</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITable.html#setLastCol-boolean-">setLastCol</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITable.html" title="interface in com.aspose.slides">ITable</a></code></dd>
</dl>
</li>
</ul>
<a name="getHorizontalBanding--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHorizontalBanding</h4>
<pre>public final&nbsp;boolean&nbsp;getHorizontalBanding()</pre>
<div class="block"><p>
 Determines whether the even rows has to be drawn with a different formatting.
 Read/write <code>boolean</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITable.html#getHorizontalBanding--">getHorizontalBanding</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITable.html" title="interface in com.aspose.slides">ITable</a></code></dd>
</dl>
</li>
</ul>
<a name="setHorizontalBanding-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setHorizontalBanding</h4>
<pre>public final&nbsp;void&nbsp;setHorizontalBanding(boolean&nbsp;value)</pre>
<div class="block"><p>
 Determines whether the even rows has to be drawn with a different formatting.
 Read/write <code>boolean</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITable.html#setHorizontalBanding-boolean-">setHorizontalBanding</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITable.html" title="interface in com.aspose.slides">ITable</a></code></dd>
</dl>
</li>
</ul>
<a name="getVerticalBanding--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVerticalBanding</h4>
<pre>public final&nbsp;boolean&nbsp;getVerticalBanding()</pre>
<div class="block"><p>
 Determines whether the even columns has to be drawn with a different formatting.
 Read/write <code>boolean</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITable.html#getVerticalBanding--">getVerticalBanding</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITable.html" title="interface in com.aspose.slides">ITable</a></code></dd>
</dl>
</li>
</ul>
<a name="setVerticalBanding-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setVerticalBanding</h4>
<pre>public final&nbsp;void&nbsp;setVerticalBanding(boolean&nbsp;value)</pre>
<div class="block"><p>
 Determines whether the even columns has to be drawn with a different formatting.
 Read/write <code>boolean</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITable.html#setVerticalBanding-boolean-">setVerticalBanding</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITable.html" title="interface in com.aspose.slides">ITable</a></code></dd>
</dl>
</li>
</ul>
<a name="setTextFormat-com.aspose.slides.IPortionFormat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTextFormat</h4>
<pre>public final&nbsp;void&nbsp;setTextFormat(<a href="../../../com/aspose/slides/IPortionFormat.html" title="interface in com.aspose.slides">IPortionFormat</a>&nbsp;source)</pre>
<div class="block"><p>
 Sets defined portion format properties to all table cells' portions.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IBulkTextFormattable.html#setTextFormat-com.aspose.slides.IPortionFormat-">setTextFormat</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IBulkTextFormattable.html" title="interface in com.aspose.slides">IBulkTextFormattable</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>source</code> - IPortionFormat object with necessary properties set.</dd>
</dl>
</li>
</ul>
<a name="setTextFormat-com.aspose.slides.IParagraphFormat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTextFormat</h4>
<pre>public final&nbsp;void&nbsp;setTextFormat(<a href="../../../com/aspose/slides/IParagraphFormat.html" title="interface in com.aspose.slides">IParagraphFormat</a>&nbsp;source)</pre>
<div class="block"><p>
 Sets defined paragraph format properties to all table cells' paragraphs.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IBulkTextFormattable.html#setTextFormat-com.aspose.slides.IParagraphFormat-">setTextFormat</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IBulkTextFormattable.html" title="interface in com.aspose.slides">IBulkTextFormattable</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>source</code> - IParagraphFormat object with necessary properties set.</dd>
</dl>
</li>
</ul>
<a name="setTextFormat-com.aspose.slides.ITextFrameFormat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTextFormat</h4>
<pre>public final&nbsp;void&nbsp;setTextFormat(<a href="../../../com/aspose/slides/ITextFrameFormat.html" title="interface in com.aspose.slides">ITextFrameFormat</a>&nbsp;source)</pre>
<div class="block"><p>
 Sets defined text frame format properties to all table cells' text frames.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IBulkTextFormattable.html#setTextFormat-com.aspose.slides.ITextFrameFormat-">setTextFormat</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IBulkTextFormattable.html" title="interface in com.aspose.slides">IBulkTextFormattable</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>source</code> - ITextFrameFormat object with necessary properties set.</dd>
</dl>
</li>
</ul>
<a name="getFillFormat--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getFillFormat</h4>
<pre>public&nbsp;<a href="../../../com/aspose/slides/IFillFormat.html" title="interface in com.aspose.slides">IFillFormat</a>&nbsp;getFillFormat()</pre>
<div class="block"><p>
 Returns a TableFormat.FillFormat object containing the fill formatting for the Table.
 Read-only <a href="../../../com/aspose/slides/IFillFormat.html" title="interface in com.aspose.slides"><code>IFillFormat</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShape.html#getFillFormat--">getFillFormat</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a></code></dd>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../com/aspose/slides/Shape.html#getFillFormat--">getFillFormat</a></code>&nbsp;in class&nbsp;<code><a href="../../../com/aspose/slides/Shape.html" title="class in com.aspose.slides">Shape</a></code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/TabFactory.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/TableFormat.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/Table.html" target="_top">Frames</a></li>
<li><a href="Table.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><i>Copyright &copy; 2004-2025 Aspose Pty Ltd. All Rights Reserved.</i></small></p>
</body>
</html>
