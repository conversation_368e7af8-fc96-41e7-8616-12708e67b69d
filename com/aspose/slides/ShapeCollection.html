<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_421) on Fri Jul 11 05:32:44 CDT 2025 -->
<title>ShapeCollection (Aspose.Slides for Java (25.7) API Documentation)</title>
<meta name="date" content="2025-07-11">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ShapeCollection (Aspose.Slides for Java (25.7) API Documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10,"i26":10,"i27":10,"i28":10,"i29":10,"i30":10,"i31":10,"i32":10,"i33":10,"i34":10,"i35":10,"i36":10,"i37":10,"i38":10,"i39":10,"i40":10,"i41":10,"i42":10,"i43":10,"i44":10,"i45":10,"i46":10,"i47":10,"i48":10,"i49":10,"i50":10,"i51":10,"i52":10,"i53":10,"i54":10,"i55":10,"i56":10,"i57":10,"i58":10,"i59":10,"i60":10,"i61":10,"i62":10,"i63":10,"i64":10,"i65":10,"i66":10,"i67":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/ShapeBevel.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/ShapeElement.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/ShapeCollection.html" target="_top">Frames</a></li>
<li><a href="ShapeCollection.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.aspose.slides</div>
<h2 title="Class ShapeCollection" class="title">Class ShapeCollection</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../com/aspose/slides/DomObject.html" title="class in com.aspose.slides">com.aspose.slides.DomObject</a>&lt;<a href="../../../com/aspose/slides/GroupShape.html" title="class in com.aspose.slides">GroupShape</a>&gt;</li>
<li>
<ul class="inheritance">
<li>com.aspose.slides.ShapeCollection</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd>com.aspose.ms.System.Collections.Generic.IGenericEnumerable&lt;<a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a>&gt;, com.aspose.ms.System.Collections.ICollection&lt;<a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a>&gt;, com.aspose.ms.System.Collections.IEnumerable&lt;<a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a>&gt;, <a href="../../../com/aspose/slides/IGenericCollection.html" title="interface in com.aspose.slides">IGenericCollection</a>&lt;<a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a>&gt;, <a href="../../../com/aspose/slides/IShapeCollection.html" title="interface in com.aspose.slides">IShapeCollection</a>, java.lang.Iterable&lt;<a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a>&gt;</dd>
</dl>
<hr>
<br>
<pre>public final class <span class="typeNameLabel">ShapeCollection</span>
extends <a href="../../../com/aspose/slides/DomObject.html" title="class in com.aspose.slides">DomObject</a>&lt;<a href="../../../com/aspose/slides/GroupShape.html" title="class in com.aspose.slides">GroupShape</a>&gt;
implements <a href="../../../com/aspose/slides/IShapeCollection.html" title="interface in com.aspose.slides">IShapeCollection</a></pre>
<div class="block"><p>
 Represents a collection of shapes.
 </p></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IAudioFrame.html" title="interface in com.aspose.slides">IAudioFrame</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeCollection.html#addAudioFrameCD-float-float-float-float-">addAudioFrameCD</a></span>(float&nbsp;x,
               float&nbsp;y,
               float&nbsp;width,
               float&nbsp;height)</code>
<div class="block">
 Creates a new audio frame linked to a CD track and adds it to the end of the shape collection.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IAudioFrame.html" title="interface in com.aspose.slides">IAudioFrame</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeCollection.html#addAudioFrameEmbedded-float-float-float-float-com.aspose.slides.IAudio-">addAudioFrameEmbedded</a></span>(float&nbsp;x,
                     float&nbsp;y,
                     float&nbsp;width,
                     float&nbsp;height,
                     <a href="../../../com/aspose/slides/IAudio.html" title="interface in com.aspose.slides">IAudio</a>&nbsp;audio)</code>
<div class="block">
 Creates a new audio frame and adds it to the end of the shape collection using an
 existing audio object from the Presentation.Audios list.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IAudioFrame.html" title="interface in com.aspose.slides">IAudioFrame</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeCollection.html#addAudioFrameEmbedded-float-float-float-float-java.io.InputStream-">addAudioFrameEmbedded</a></span>(float&nbsp;x,
                     float&nbsp;y,
                     float&nbsp;width,
                     float&nbsp;height,
                     java.io.InputStream&nbsp;audio_stream)</code>
<div class="block">
 Creates a new audio frame with an embedded WAV file and adds it to the end of the
 shape collection.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IAudioFrame.html" title="interface in com.aspose.slides">IAudioFrame</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeCollection.html#addAudioFrameLinked-float-float-float-float-java.lang.String-">addAudioFrameLinked</a></span>(float&nbsp;x,
                   float&nbsp;y,
                   float&nbsp;width,
                   float&nbsp;height,
                   java.lang.String&nbsp;fname)</code>
<div class="block">
 Creates a new audio frame linked to an external audio file and adds it to the end of
 the shape collection.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IAutoShape.html" title="interface in com.aspose.slides">IAutoShape</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeCollection.html#addAutoShape-int-float-float-float-float-">addAutoShape</a></span>(int&nbsp;shapeType,
            float&nbsp;x,
            float&nbsp;y,
            float&nbsp;width,
            float&nbsp;height)</code>
<div class="block">
 Creates a new auto shape with default formatting and adds it to the end of the
 shape collection.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IAutoShape.html" title="interface in com.aspose.slides">IAutoShape</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeCollection.html#addAutoShape-int-float-float-float-float-boolean-">addAutoShape</a></span>(int&nbsp;shapeType,
            float&nbsp;x,
            float&nbsp;y,
            float&nbsp;width,
            float&nbsp;height,
            boolean&nbsp;createFromTemplate)</code>
<div class="block">
 Creates a new auto shape and adds it to the end of the shape collection, optionally
 initializing it with default template formatting.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IChart.html" title="interface in com.aspose.slides">IChart</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeCollection.html#addChart-int-float-float-float-float-">addChart</a></span>(int&nbsp;type,
        float&nbsp;x,
        float&nbsp;y,
        float&nbsp;width,
        float&nbsp;height)</code>
<div class="block">
 Creates a new chart, initializes it with sample series data and settings, and adds
 it to the end of the shape collection.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IChart.html" title="interface in com.aspose.slides">IChart</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeCollection.html#addChart-int-float-float-float-float-boolean-">addChart</a></span>(int&nbsp;type,
        float&nbsp;x,
        float&nbsp;y,
        float&nbsp;width,
        float&nbsp;height,
        boolean&nbsp;initWithSample)</code>
<div class="block">
 Creates a new chart, initializes it with sample series data and settings, and adds
 it to the end of the shape collection.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeCollection.html#addClone-com.aspose.slides.IShape-">addClone</a></span>(<a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a>&nbsp;sourceShape)</code>
<div class="block">
 Creates a copy of the specified shape and adds it to the end of the shape collection.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeCollection.html#addClone-com.aspose.slides.IShape-float-float-">addClone</a></span>(<a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a>&nbsp;sourceShape,
        float&nbsp;x,
        float&nbsp;y)</code>
<div class="block">
 Creates a copy of the specified shape and adds it to the end of the shape collection.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeCollection.html#addClone-com.aspose.slides.IShape-float-float-float-float-">addClone</a></span>(<a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a>&nbsp;sourceShape,
        float&nbsp;x,
        float&nbsp;y,
        float&nbsp;width,
        float&nbsp;height)</code>
<div class="block">
 Creates a copy of the specified shape and adds it to the end of the shape collection.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IConnector.html" title="interface in com.aspose.slides">IConnector</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeCollection.html#addConnector-int-float-float-float-float-">addConnector</a></span>(int&nbsp;shapeType,
            float&nbsp;x,
            float&nbsp;y,
            float&nbsp;width,
            float&nbsp;height)</code>
<div class="block">
 Creates a new connector shape with default template styling and adds it to the end of the
 shape collection.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IConnector.html" title="interface in com.aspose.slides">IConnector</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeCollection.html#addConnector-int-float-float-float-float-boolean-">addConnector</a></span>(int&nbsp;shapeType,
            float&nbsp;x,
            float&nbsp;y,
            float&nbsp;width,
            float&nbsp;height,
            boolean&nbsp;createFromTemplate)</code>
<div class="block">
 Creates a new connector shape and adds it to the end of the shape collection,
 optionally applying default template styling.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IGroupShape.html" title="interface in com.aspose.slides">IGroupShape</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeCollection.html#addGroupShape--">addGroupShape</a></span>()</code>
<div class="block">
 Creates a new empty group shape and adds it to the end of the shape collection.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IGroupShape.html" title="interface in com.aspose.slides">IGroupShape</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeCollection.html#addGroupShape-com.aspose.slides.ISvgImage-float-float-float-float-">addGroupShape</a></span>(<a href="../../../com/aspose/slides/ISvgImage.html" title="interface in com.aspose.slides">ISvgImage</a>&nbsp;svgImage,
             float&nbsp;x,
             float&nbsp;y,
             float&nbsp;width,
             float&nbsp;height)</code>
<div class="block">
 Creates a new group shape, converts the specified SVG image into individual shapes,
 and adds the resulting group to the end of the shape collection.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IAutoShape.html" title="interface in com.aspose.slides">IAutoShape</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeCollection.html#addMathShape-float-float-float-float-">addMathShape</a></span>(float&nbsp;x,
            float&nbsp;y,
            float&nbsp;width,
            float&nbsp;height)</code>
<div class="block">
 Creates a new rectangle auto shape to host mathematical content and adds it to the
 end of the shape collection.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IOleObjectFrame.html" title="interface in com.aspose.slides">IOleObjectFrame</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeCollection.html#addOleObjectFrame-float-float-float-float-com.aspose.slides.IOleEmbeddedDataInfo-">addOleObjectFrame</a></span>(float&nbsp;x,
                 float&nbsp;y,
                 float&nbsp;width,
                 float&nbsp;height,
                 <a href="../../../com/aspose/slides/IOleEmbeddedDataInfo.html" title="interface in com.aspose.slides">IOleEmbeddedDataInfo</a>&nbsp;dataInfo)</code>
<div class="block">
 Creates a new OLE object frame and adds it to the end of the shape collection.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IOleObjectFrame.html" title="interface in com.aspose.slides">IOleObjectFrame</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeCollection.html#addOleObjectFrame-float-float-float-float-java.lang.String-java.lang.String-">addOleObjectFrame</a></span>(float&nbsp;x,
                 float&nbsp;y,
                 float&nbsp;width,
                 float&nbsp;height,
                 java.lang.String&nbsp;className,
                 java.lang.String&nbsp;path)</code>
<div class="block">
 Creates a new OLE object frame and adds it to the end of the shape collection.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IPictureFrame.html" title="interface in com.aspose.slides">IPictureFrame</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeCollection.html#addPictureFrame-int-float-float-float-float-com.aspose.slides.IPPImage-">addPictureFrame</a></span>(int&nbsp;shapeType,
               float&nbsp;x,
               float&nbsp;y,
               float&nbsp;width,
               float&nbsp;height,
               <a href="../../../com/aspose/slides/IPPImage.html" title="interface in com.aspose.slides">IPPImage</a>&nbsp;image)</code>
<div class="block">
 Creates a new picture frame containing the specified image and adds it to the end of the
 shape collection.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/ISectionZoomFrame.html" title="interface in com.aspose.slides">ISectionZoomFrame</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeCollection.html#addSectionZoomFrame-float-float-float-float-com.aspose.slides.ISection-">addSectionZoomFrame</a></span>(float&nbsp;x,
                   float&nbsp;y,
                   float&nbsp;width,
                   float&nbsp;height,
                   <a href="../../../com/aspose/slides/ISection.html" title="interface in com.aspose.slides">ISection</a>&nbsp;section)</code>
<div class="block">
 Creates a new Section Zoom frame and adds it to the end of the shape collection.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/ISectionZoomFrame.html" title="interface in com.aspose.slides">ISectionZoomFrame</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeCollection.html#addSectionZoomFrame-float-float-float-float-com.aspose.slides.ISection-com.aspose.slides.IPPImage-">addSectionZoomFrame</a></span>(float&nbsp;x,
                   float&nbsp;y,
                   float&nbsp;width,
                   float&nbsp;height,
                   <a href="../../../com/aspose/slides/ISection.html" title="interface in com.aspose.slides">ISection</a>&nbsp;section,
                   <a href="../../../com/aspose/slides/IPPImage.html" title="interface in com.aspose.slides">IPPImage</a>&nbsp;image)</code>
<div class="block">
 Creates a new Section Zoom frame with a predefined image and adds it to the end of the shape collection.</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/ISmartArt.html" title="interface in com.aspose.slides">ISmartArt</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeCollection.html#addSmartArt-float-float-float-float-int-">addSmartArt</a></span>(float&nbsp;x,
           float&nbsp;y,
           float&nbsp;width,
           float&nbsp;height,
           int&nbsp;layoutType)</code>
<div class="block">
 Creates a SmartArt diagram and adds it to the end of the shape collection.</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/ISummaryZoomFrame.html" title="interface in com.aspose.slides">ISummaryZoomFrame</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeCollection.html#addSummaryZoomFrame-float-float-float-float-">addSummaryZoomFrame</a></span>(float&nbsp;x,
                   float&nbsp;y,
                   float&nbsp;width,
                   float&nbsp;height)</code>
<div class="block">
 Creates a new Summary Zoom frame and adds it to the end of the shape collection.</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/ITable.html" title="interface in com.aspose.slides">ITable</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeCollection.html#addTable-float-float-double:A-double:A-">addTable</a></span>(float&nbsp;x,
        float&nbsp;y,
        double[]&nbsp;columnWidths,
        double[]&nbsp;rowHeights)</code>
<div class="block">
 Creates a new table and adds it to the end of the shape collection.</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IVideoFrame.html" title="interface in com.aspose.slides">IVideoFrame</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeCollection.html#addVideoFrame-float-float-float-float-com.aspose.slides.IVideo-">addVideoFrame</a></span>(float&nbsp;x,
             float&nbsp;y,
             float&nbsp;width,
             float&nbsp;height,
             <a href="../../../com/aspose/slides/IVideo.html" title="interface in com.aspose.slides">IVideo</a>&nbsp;video)</code>
<div class="block">
 Creates a new video frame and adds it to the end of the shape collection.</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IVideoFrame.html" title="interface in com.aspose.slides">IVideoFrame</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeCollection.html#addVideoFrame-float-float-float-float-java.lang.String-">addVideoFrame</a></span>(float&nbsp;x,
             float&nbsp;y,
             float&nbsp;width,
             float&nbsp;height,
             java.lang.String&nbsp;fname)</code>
<div class="block">
 Creates a new video frame and adds it to the end of the shape collection.</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IZoomFrame.html" title="interface in com.aspose.slides">IZoomFrame</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeCollection.html#addZoomFrame-float-float-float-float-com.aspose.slides.ISlide-">addZoomFrame</a></span>(float&nbsp;x,
            float&nbsp;y,
            float&nbsp;width,
            float&nbsp;height,
            <a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a>&nbsp;slide)</code>
<div class="block">
 Creates a new Zoom frame and adds it to the end of the shape collection.</div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IZoomFrame.html" title="interface in com.aspose.slides">IZoomFrame</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeCollection.html#addZoomFrame-float-float-float-float-com.aspose.slides.ISlide-com.aspose.slides.IPPImage-">addZoomFrame</a></span>(float&nbsp;x,
            float&nbsp;y,
            float&nbsp;width,
            float&nbsp;height,
            <a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a>&nbsp;slide,
            <a href="../../../com/aspose/slides/IPPImage.html" title="interface in com.aspose.slides">IPPImage</a>&nbsp;image)</code>
<div class="block">
 Creates a new Zoom frame and adds it to the end of the shape collection.</div>
</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeCollection.html#clear--">clear</a></span>()</code>
<div class="block">
 Removes all shapes from the shape collection.</div>
</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeCollection.html#copyTo-com.aspose.ms.System.Array-int-">copyTo</a></span>(com.aspose.ms.System.Array&nbsp;array,
      int&nbsp;index)</code>
<div class="block">
 Copies all elements from the collection to the specified array.</div>
</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeCollection.html#get_Item-int-">get_Item</a></span>(int&nbsp;index)</code>
<div class="block">
 Gets the element at the specified index.</div>
</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IGroupShape.html" title="interface in com.aspose.slides">IGroupShape</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeCollection.html#getParentGroup--">getParentGroup</a></span>()</code>
<div class="block">
 Gets the parent group shape object for the shapes collection.</div>
</td>
</tr>
<tr id="i32" class="altColor">
<td class="colFirst"><code>java.lang.Object</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeCollection.html#getSyncRoot--">getSyncRoot</a></span>()</code>
<div class="block">
 Returns a synchronization root.</div>
</td>
</tr>
<tr id="i33" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeCollection.html#indexOf-com.aspose.slides.IShape-">indexOf</a></span>(<a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a>&nbsp;shape)</code>
<div class="block">
 Returns the zero-based index of the first occurrence of the specified shape in the collection.</div>
</td>
</tr>
<tr id="i34" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IAudioFrame.html" title="interface in com.aspose.slides">IAudioFrame</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeCollection.html#insertAudioFrameCD-int-float-float-float-float-">insertAudioFrameCD</a></span>(int&nbsp;index,
                  float&nbsp;x,
                  float&nbsp;y,
                  float&nbsp;width,
                  float&nbsp;height)</code>
<div class="block">
 Creates a new audio frame linked to a CD track and inserts it into the shape collection
 at the specified index.</div>
</td>
</tr>
<tr id="i35" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IAudioFrame.html" title="interface in com.aspose.slides">IAudioFrame</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeCollection.html#insertAudioFrameEmbedded-int-float-float-float-float-com.aspose.slides.IAudio-">insertAudioFrameEmbedded</a></span>(int&nbsp;index,
                        float&nbsp;x,
                        float&nbsp;y,
                        float&nbsp;width,
                        float&nbsp;height,
                        <a href="../../../com/aspose/slides/IAudio.html" title="interface in com.aspose.slides">IAudio</a>&nbsp;audio)</code>
<div class="block">
 Creates a new audio frame and inserts it into the shape collection at the specified index
 using an existing audio object from the Presentation.Audios list.</div>
</td>
</tr>
<tr id="i36" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IAudioFrame.html" title="interface in com.aspose.slides">IAudioFrame</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeCollection.html#insertAudioFrameEmbedded-int-float-float-float-float-java.io.InputStream-">insertAudioFrameEmbedded</a></span>(int&nbsp;index,
                        float&nbsp;x,
                        float&nbsp;y,
                        float&nbsp;width,
                        float&nbsp;height,
                        java.io.InputStream&nbsp;audio_stream)</code>
<div class="block">
 Creates a new audio frame with an embedded WAV file and inserts it into the shape
 collection at the specified index.</div>
</td>
</tr>
<tr id="i37" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IAudioFrame.html" title="interface in com.aspose.slides">IAudioFrame</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeCollection.html#insertAudioFrameLinked-int-float-float-float-float-java.lang.String-">insertAudioFrameLinked</a></span>(int&nbsp;index,
                      float&nbsp;x,
                      float&nbsp;y,
                      float&nbsp;width,
                      float&nbsp;height,
                      java.lang.String&nbsp;fname)</code>
<div class="block">
 Creates a new audio frame linked to an external audio file and inserts it into the shape
 collection at the specified index.</div>
</td>
</tr>
<tr id="i38" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IAutoShape.html" title="interface in com.aspose.slides">IAutoShape</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeCollection.html#insertAutoShape-int-int-float-float-float-float-">insertAutoShape</a></span>(int&nbsp;index,
               int&nbsp;shapeType,
               float&nbsp;x,
               float&nbsp;y,
               float&nbsp;width,
               float&nbsp;height)</code>
<div class="block">
 Creates a new auto shape and inserts it into the shape collection at the specified index,
 applying default template formatting.</div>
</td>
</tr>
<tr id="i39" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IAutoShape.html" title="interface in com.aspose.slides">IAutoShape</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeCollection.html#insertAutoShape-int-int-float-float-float-float-boolean-">insertAutoShape</a></span>(int&nbsp;index,
               int&nbsp;shapeType,
               float&nbsp;x,
               float&nbsp;y,
               float&nbsp;width,
               float&nbsp;height,
               boolean&nbsp;createFromTemplate)</code>
<div class="block">
 Creates a new auto shape and inserts it into the shape collection at the specified index,
 optionally initializing it with default template styling.</div>
</td>
</tr>
<tr id="i40" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IChart.html" title="interface in com.aspose.slides">IChart</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeCollection.html#insertChart-int-float-float-float-float-int-">insertChart</a></span>(int&nbsp;type,
           float&nbsp;x,
           float&nbsp;y,
           float&nbsp;width,
           float&nbsp;height,
           int&nbsp;index)</code>
<div class="block">
 Creates a new chart, initializes it with sample series data and settings,
 and inserts it into the shape collection at the specified index.</div>
</td>
</tr>
<tr id="i41" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IChart.html" title="interface in com.aspose.slides">IChart</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeCollection.html#insertChart-int-float-float-float-float-int-boolean-">insertChart</a></span>(int&nbsp;type,
           float&nbsp;x,
           float&nbsp;y,
           float&nbsp;width,
           float&nbsp;height,
           int&nbsp;index,
           boolean&nbsp;initWithSample)</code>
<div class="block">
 Creates a new chart, initializes it with sample series data and settings,
 and inserts it into the shape collection at the specified index.</div>
</td>
</tr>
<tr id="i42" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeCollection.html#insertClone-int-com.aspose.slides.IShape-">insertClone</a></span>(int&nbsp;index,
           <a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a>&nbsp;sourceShape)</code>
<div class="block">
 Creates a copy of the specified shape and inserts it into the shape collection at the specified index.</div>
</td>
</tr>
<tr id="i43" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeCollection.html#insertClone-int-com.aspose.slides.IShape-float-float-">insertClone</a></span>(int&nbsp;index,
           <a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a>&nbsp;sourceShape,
           float&nbsp;x,
           float&nbsp;y)</code>
<div class="block">
 Creates a copy of the specified shape and inserts it into the shape collection at the specified index.</div>
</td>
</tr>
<tr id="i44" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeCollection.html#insertClone-int-com.aspose.slides.IShape-float-float-float-float-">insertClone</a></span>(int&nbsp;index,
           <a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a>&nbsp;sourceShape,
           float&nbsp;x,
           float&nbsp;y,
           float&nbsp;width,
           float&nbsp;height)</code>
<div class="block">
 Creates a copy of the specified shape and inserts it into the shape collection at the specified index.</div>
</td>
</tr>
<tr id="i45" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IConnector.html" title="interface in com.aspose.slides">IConnector</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeCollection.html#insertConnector-int-int-float-float-float-float-">insertConnector</a></span>(int&nbsp;index,
               int&nbsp;shapeType,
               float&nbsp;x,
               float&nbsp;y,
               float&nbsp;width,
               float&nbsp;height)</code>
<div class="block">
 Creates a new connector shape and inserts it into the shape collection at the specified index,
 applying default template styling.</div>
</td>
</tr>
<tr id="i46" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IConnector.html" title="interface in com.aspose.slides">IConnector</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeCollection.html#insertConnector-int-int-float-float-float-float-boolean-">insertConnector</a></span>(int&nbsp;index,
               int&nbsp;shapeType,
               float&nbsp;x,
               float&nbsp;y,
               float&nbsp;width,
               float&nbsp;height,
               boolean&nbsp;createFromTemplate)</code>
<div class="block">
 Creates a new connector shape and inserts it into the shape collection at the specified index,
 optionally applying default template styling.</div>
</td>
</tr>
<tr id="i47" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IGroupShape.html" title="interface in com.aspose.slides">IGroupShape</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeCollection.html#insertGroupShape-int-">insertGroupShape</a></span>(int&nbsp;index)</code>
<div class="block">
 Creates a new empty group shape and inserts it to the shape collection at the specified index.</div>
</td>
</tr>
<tr id="i48" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IOleObjectFrame.html" title="interface in com.aspose.slides">IOleObjectFrame</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeCollection.html#insertOleObjectFrame-int-float-float-float-float-com.aspose.slides.IOleEmbeddedDataInfo-">insertOleObjectFrame</a></span>(int&nbsp;index,
                    float&nbsp;x,
                    float&nbsp;y,
                    float&nbsp;width,
                    float&nbsp;height,
                    <a href="../../../com/aspose/slides/IOleEmbeddedDataInfo.html" title="interface in com.aspose.slides">IOleEmbeddedDataInfo</a>&nbsp;dataInfo)</code>
<div class="block">
 Creates a new OLE object frame and inserts it into the shape collection at the specified index.</div>
</td>
</tr>
<tr id="i49" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IOleObjectFrame.html" title="interface in com.aspose.slides">IOleObjectFrame</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeCollection.html#insertOleObjectFrame-int-float-float-float-float-java.lang.String-java.lang.String-">insertOleObjectFrame</a></span>(int&nbsp;index,
                    float&nbsp;x,
                    float&nbsp;y,
                    float&nbsp;width,
                    float&nbsp;height,
                    java.lang.String&nbsp;className,
                    java.lang.String&nbsp;path)</code>
<div class="block">
 Creates a new OLE object frame and inserts it into the shape collection at the specified index.</div>
</td>
</tr>
<tr id="i50" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IPictureFrame.html" title="interface in com.aspose.slides">IPictureFrame</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeCollection.html#insertPictureFrame-int-int-float-float-float-float-com.aspose.slides.IPPImage-">insertPictureFrame</a></span>(int&nbsp;index,
                  int&nbsp;shapeType,
                  float&nbsp;x,
                  float&nbsp;y,
                  float&nbsp;width,
                  float&nbsp;height,
                  <a href="../../../com/aspose/slides/IPPImage.html" title="interface in com.aspose.slides">IPPImage</a>&nbsp;image)</code>
<div class="block">
 Creates a new picture frame containing the specified image and inserts it into the shape
 collection at the specified index.</div>
</td>
</tr>
<tr id="i51" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/ISectionZoomFrame.html" title="interface in com.aspose.slides">ISectionZoomFrame</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeCollection.html#insertSectionZoomFrame-int-float-float-float-float-com.aspose.slides.ISection-">insertSectionZoomFrame</a></span>(int&nbsp;index,
                      float&nbsp;x,
                      float&nbsp;y,
                      float&nbsp;width,
                      float&nbsp;height,
                      <a href="../../../com/aspose/slides/ISection.html" title="interface in com.aspose.slides">ISection</a>&nbsp;section)</code>
<div class="block">
 Creates a new Section Zoom frame and inserts it into to the shape collection at the specified index.</div>
</td>
</tr>
<tr id="i52" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/ISectionZoomFrame.html" title="interface in com.aspose.slides">ISectionZoomFrame</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeCollection.html#insertSectionZoomFrame-int-float-float-float-float-com.aspose.slides.ISection-com.aspose.slides.IPPImage-">insertSectionZoomFrame</a></span>(int&nbsp;index,
                      float&nbsp;x,
                      float&nbsp;y,
                      float&nbsp;width,
                      float&nbsp;height,
                      <a href="../../../com/aspose/slides/ISection.html" title="interface in com.aspose.slides">ISection</a>&nbsp;section,
                      <a href="../../../com/aspose/slides/IPPImage.html" title="interface in com.aspose.slides">IPPImage</a>&nbsp;image)</code>
<div class="block">
 Creates a new Section Zoom frame with a predefined image and inserts it into to the shape
 collection at the specified index.</div>
</td>
</tr>
<tr id="i53" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/ISummaryZoomFrame.html" title="interface in com.aspose.slides">ISummaryZoomFrame</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeCollection.html#insertSummaryZoomFrame-int-float-float-float-float-">insertSummaryZoomFrame</a></span>(int&nbsp;index,
                      float&nbsp;x,
                      float&nbsp;y,
                      float&nbsp;width,
                      float&nbsp;height)</code>
<div class="block">
 Creates a new Summary Zoom frame and inserts it into the shape collection at the specified index.</div>
</td>
</tr>
<tr id="i54" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/ITable.html" title="interface in com.aspose.slides">ITable</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeCollection.html#insertTable-int-float-float-double:A-double:A-">insertTable</a></span>(int&nbsp;index,
           float&nbsp;x,
           float&nbsp;y,
           double[]&nbsp;columnWidths,
           double[]&nbsp;rowHeights)</code>
<div class="block">
 Creates a new table and inserts it into the shape collection at the specified index.</div>
</td>
</tr>
<tr id="i55" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IVideoFrame.html" title="interface in com.aspose.slides">IVideoFrame</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeCollection.html#insertVideoFrame-int-float-float-float-float-java.lang.String-">insertVideoFrame</a></span>(int&nbsp;index,
                float&nbsp;x,
                float&nbsp;y,
                float&nbsp;width,
                float&nbsp;height,
                java.lang.String&nbsp;fname)</code>
<div class="block">
 Creates a new video frame and inserts it into the shape collection at the specified index.</div>
</td>
</tr>
<tr id="i56" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IZoomFrame.html" title="interface in com.aspose.slides">IZoomFrame</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeCollection.html#insertZoomFrame-int-float-float-float-float-com.aspose.slides.ISlide-">insertZoomFrame</a></span>(int&nbsp;index,
               float&nbsp;x,
               float&nbsp;y,
               float&nbsp;width,
               float&nbsp;height,
               <a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a>&nbsp;slide)</code>
<div class="block">
 Creates a new Zoom frame and inserts it into the shape collection at the specified index.</div>
</td>
</tr>
<tr id="i57" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IZoomFrame.html" title="interface in com.aspose.slides">IZoomFrame</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeCollection.html#insertZoomFrame-int-float-float-float-float-com.aspose.slides.ISlide-com.aspose.slides.IPPImage-">insertZoomFrame</a></span>(int&nbsp;index,
               float&nbsp;x,
               float&nbsp;y,
               float&nbsp;width,
               float&nbsp;height,
               <a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a>&nbsp;slide,
               <a href="../../../com/aspose/slides/IPPImage.html" title="interface in com.aspose.slides">IPPImage</a>&nbsp;image)</code>
<div class="block">
 Creates a new Zoom frame with a predefined image and inserts it into the shape collection
 at the specified index.</div>
</td>
</tr>
<tr id="i58" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeCollection.html#isSynchronized--">isSynchronized</a></span>()</code>
<div class="block">
 Returns a value indicating whether access to the collection is synchronized (thread-safe).</div>
</td>
</tr>
<tr id="i59" class="rowColor">
<td class="colFirst"><code>com.aspose.ms.System.Collections.Generic.IGenericEnumerator&lt;<a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeCollection.html#iterator--">iterator</a></span>()</code>
<div class="block">
 Returns an enumerator that iterates through the collection.</div>
</td>
</tr>
<tr id="i60" class="altColor">
<td class="colFirst"><code>com.aspose.ms.System.Collections.Generic.IGenericEnumerator&lt;<a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeCollection.html#iteratorJava--">iteratorJava</a></span>()</code>
<div class="block">
 Returns a java iterator for the entire collection.</div>
</td>
</tr>
<tr id="i61" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeCollection.html#remove-com.aspose.slides.IShape-">remove</a></span>(<a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a>&nbsp;shape)</code>
<div class="block">
 Removes the first occurrence of the specified shape from the shape collection.</div>
</td>
</tr>
<tr id="i62" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeCollection.html#removeAt-int-">removeAt</a></span>(int&nbsp;index)</code>
<div class="block">
 Removes the shape at the specified index from the shape collection.</div>
</td>
</tr>
<tr id="i63" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeCollection.html#reorder-int-com.aspose.slides.IShape...-">reorder</a></span>(int&nbsp;index,
       <a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a>...&nbsp;shapes)</code>
<div class="block">
 Moves the specified shapes within the shape collection, placing them starting at the given index.</div>
</td>
</tr>
<tr id="i64" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeCollection.html#reorder-int-com.aspose.slides.IShape-">reorder</a></span>(int&nbsp;index,
       <a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a>&nbsp;shape)</code>
<div class="block">
 Moves the specified shape to a new position within the shape collection.</div>
</td>
</tr>
<tr id="i65" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeCollection.html#size--">size</a></span>()</code>
<div class="block">
 Gets the number of elements actually contained in the collection.</div>
</td>
</tr>
<tr id="i66" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a>[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeCollection.html#toArray--">toArray</a></span>()</code>
<div class="block">
 Creates and returns an array that contains all shapes.</div>
</td>
</tr>
<tr id="i67" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a>[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeCollection.html#toArray-int-int-">toArray</a></span>(int&nbsp;startIndex,
       int&nbsp;count)</code>
<div class="block">
 Creates and returns an array that contains all shapes in the specified range.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.aspose.slides.DomObject">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;com.aspose.slides.<a href="../../../com/aspose/slides/DomObject.html" title="class in com.aspose.slides">DomObject</a></h3>
<code><a href="../../../com/aspose/slides/DomObject.html#getParent_Immediate--">getParent_Immediate</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Iterable">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;java.lang.Iterable</h3>
<code>forEach, spliterator</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="size--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>size</h4>
<pre>public final&nbsp;int&nbsp;size()</pre>
<div class="block"><p>
 Gets the number of elements actually contained in the collection.
 Read-only <code>int</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>size</code>&nbsp;in interface&nbsp;<code>com.aspose.ms.System.Collections.ICollection&lt;<a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a>&gt;</code></dd>
</dl>
</li>
</ul>
<a name="get_Item-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_Item</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a>&nbsp;get_Item(int&nbsp;index)</pre>
<div class="block"><p>
 Gets the element at the specified index.
 Read-only <a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides"><code>IShape</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShapeCollection.html#get_Item-int-">get_Item</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShapeCollection.html" title="interface in com.aspose.slides">IShapeCollection</a></code></dd>
</dl>
</li>
</ul>
<a name="addChart-int-float-float-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addChart</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IChart.html" title="interface in com.aspose.slides">IChart</a>&nbsp;addChart(int&nbsp;type,
                             float&nbsp;x,
                             float&nbsp;y,
                             float&nbsp;width,
                             float&nbsp;height)</pre>
<div class="block"><p>
 Creates a new chart, initializes it with sample series data and settings, and adds
 it to the end of the shape collection.
 </p><p><hr><blockquote><pre>
 The following example shows how to create Chart in PowerPoint Presentation.
 <pre>
 // Instantiates the Presentation class that represents a PPTX file
 Presentation pres = new Presentation();
 try {
     // Accesses the first slide
     ISlide sld = pres.getSlides().get_Item(0);
     // Adds a chart with its default data
     IChart chart = sld.getShapes().addChart(ChartType.ClusteredColumn, 0, 0, 500, 500);
     // Sets the chart title
     chart.getChartTitle().addTextFrameForOverriding("Sample Title");
     chart.getChartTitle().getTextFrameForOverriding().getTextFrameFormat().setCenterText(NullableBool.True);
     chart.getChartTitle().setHeight(20);
     chart.setTitle(true);
     // Sets the first series to show values
     chart.getChartData().getSeries().get_Item(0).getLabels().getDefaultDataLabelFormat().setShowValue(true);
     // Sets the index for the chart data sheet
     int defaultWorksheetIndex = 0;
     // Gets the chart data worksheet
     IChartDataWorkbook fact = chart.getChartData().getChartDataWorkbook();
     // Deletes the default generated series and categories
     chart.getChartData().getSeries().clear();
     chart.getChartData().getCategories().clear();
     // Adds new series
     chart.getChartData().getSeries().add(fact.getCell(defaultWorksheetIndex, 0, 1, "Series 1"), chart.getType());
     chart.getChartData().getSeries().add(fact.getCell(defaultWorksheetIndex, 0, 2, "Series 2"), chart.getType());
     // Adds new categories
     chart.getChartData().getCategories().add(fact.getCell(defaultWorksheetIndex, 1, 0, "Caetegoty 1"));
     chart.getChartData().getCategories().add(fact.getCell(defaultWorksheetIndex, 2, 0, "Caetegoty 2"));
     chart.getChartData().getCategories().add(fact.getCell(defaultWorksheetIndex, 3, 0, "Caetegoty 3"));
     // Takes the first chart series
     IChartSeries series = chart.getChartData().getSeries().get_Item(0);
     // Populates series data
     series.getDataPoints().addDataPointForBarSeries(fact.getCell(defaultWorksheetIndex, 1, 1, 20));
     series.getDataPoints().addDataPointForBarSeries(fact.getCell(defaultWorksheetIndex, 2, 1, 50));
     series.getDataPoints().addDataPointForBarSeries(fact.getCell(defaultWorksheetIndex, 3, 1, 30));
     // Sets the fill color for the series
     series.getFormat().getFill().setFillType(FillType.Solid);
     series.getFormat().getFill().getSolidFillColor().setColor(Color.RED);
     // Takes the second chart series
     series = chart.getChartData().getSeries().get_Item(1);
     // Populates series data
     series.getDataPoints().addDataPointForBarSeries(fact.getCell(defaultWorksheetIndex, 1, 2, 30));
     series.getDataPoints().addDataPointForBarSeries(fact.getCell(defaultWorksheetIndex, 2, 2, 10));
     series.getDataPoints().addDataPointForBarSeries(fact.getCell(defaultWorksheetIndex, 3, 2, 60));
     // Sets the fill color for series
     series.getFormat().getFill().setFillType(FillType.Solid);
     series.getFormat().getFill().getSolidFillColor().setColor(Color.GREEN);
     // Sets the first label to show Category name
     IDataLabel lbl = series.getDataPoints().get_Item(0).getLabel();
     lbl.getDataLabelFormat().setShowCategoryName(true);
     lbl = series.getDataPoints().get_Item(1).getLabel();
     lbl.getDataLabelFormat().setShowSeriesName(true);
     // Sets the series to show the value for the third label
     lbl = series.getDataPoints().get_Item(2).getLabel();
     lbl.getDataLabelFormat().setShowValue(true);
     lbl.getDataLabelFormat().setShowSeriesName(true);
     lbl.getDataLabelFormat().setSeparator("/");
     // Saves the PPTX file to disk
     pres.save("AsposeChart_out.pptx", SaveFormat.Pptx);
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShapeCollection.html#addChart-int-float-float-float-float-">addChart</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShapeCollection.html" title="interface in com.aspose.slides">IShapeCollection</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>type</code> - The type of chart to add.</dd>
<dd><code>x</code> - The x-coordinate of the new chart, in points.</dd>
<dd><code>y</code> - The y-coordinate of the new chart, in points.</dd>
<dd><code>width</code> - The width of the chart, in points.</dd>
<dd><code>height</code> - The height of the chart, in points.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The newly created <a href="../../../com/aspose/slides/IChart.html" title="interface in com.aspose.slides"><code>IChart</code></a>.</dd>
</dl>
</li>
</ul>
<a name="addChart-int-float-float-float-float-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addChart</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IChart.html" title="interface in com.aspose.slides">IChart</a>&nbsp;addChart(int&nbsp;type,
                             float&nbsp;x,
                             float&nbsp;y,
                             float&nbsp;width,
                             float&nbsp;height,
                             boolean&nbsp;initWithSample)</pre>
<div class="block"><p>
 Creates a new chart, initializes it with sample series data and settings, and adds
 it to the end of the shape collection.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShapeCollection.html#addChart-int-float-float-float-float-boolean-">addChart</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShapeCollection.html" title="interface in com.aspose.slides">IShapeCollection</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>type</code> - The type of chart to add.</dd>
<dd><code>x</code> - The x-coordinate of the new chart, in points.</dd>
<dd><code>y</code> - The y-coordinate of the new chart, in points.</dd>
<dd><code>width</code> - The width of the chart, in points.</dd>
<dd><code>height</code> - The height of the chart, in points.</dd>
<dd><code>initWithSample</code> - True to initialize the new chart with sample series data and settings; 
 false to create the chart with no series and only minimal settings, which makes creation
 faster.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The newly created <a href="../../../com/aspose/slides/IChart.html" title="interface in com.aspose.slides"><code>IChart</code></a>.</dd>
</dl>
</li>
</ul>
<a name="addSmartArt-float-float-float-float-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addSmartArt</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/ISmartArt.html" title="interface in com.aspose.slides">ISmartArt</a>&nbsp;addSmartArt(float&nbsp;x,
                                   float&nbsp;y,
                                   float&nbsp;width,
                                   float&nbsp;height,
                                   int&nbsp;layoutType)</pre>
<div class="block"><p>
 Creates a SmartArt diagram and adds it to the end of the shape collection.
 </p><p><hr><blockquote><pre>
 The following example shows how to add smart shape in PowerPoint Presentation.
 <pre>
 Presentation pres = new Presentation();
 try {
     ISlide slide = pres.getSlides().get_Item(0);
     ISmartArt smart = slide.getShapes().addSmartArt(0, 0, 400, 400, SmartArtLayoutType.BasicBlockList);
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShapeCollection.html#addSmartArt-float-float-float-float-int-">addSmartArt</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShapeCollection.html" title="interface in com.aspose.slides">IShapeCollection</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>x</code> - The x-coordinate of the diagram’s frame, in points.</dd>
<dd><code>y</code> - The y-coordinate of the diagram’s frame, in points.</dd>
<dd><code>width</code> - The width of the diagram’s frame, in points.</dd>
<dd><code>height</code> - The height of the diagram’s frame, in points.</dd>
<dd><code>layoutType</code> - The SmartArt layout type.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The newly created <a href="../../../com/aspose/slides/ISmartArt.html" title="interface in com.aspose.slides"><code>ISmartArt</code></a>.</dd>
</dl>
</li>
</ul>
<a name="insertChart-int-float-float-float-float-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>insertChart</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IChart.html" title="interface in com.aspose.slides">IChart</a>&nbsp;insertChart(int&nbsp;type,
                                float&nbsp;x,
                                float&nbsp;y,
                                float&nbsp;width,
                                float&nbsp;height,
                                int&nbsp;index)</pre>
<div class="block"><p>
 Creates a new chart, initializes it with sample series data and settings,
 and inserts it into the shape collection at the specified index.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShapeCollection.html#insertChart-int-float-float-float-float-int-">insertChart</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShapeCollection.html" title="interface in com.aspose.slides">IShapeCollection</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>type</code> - The type of chart to create.</dd>
<dd><code>x</code> - The x-coordinate of the new chart, in points.</dd>
<dd><code>y</code> - The y-coordinate of the new chart, in points.</dd>
<dd><code>width</code> - The width of the new chart, in points.</dd>
<dd><code>height</code> - The height of the new chart, in points.</dd>
<dd><code>index</code> - The zero-based index at which to insert the new chart in the shape collection.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The newly created <a href="../../../com/aspose/slides/IChart.html" title="interface in com.aspose.slides"><code>IChart</code></a>.</dd>
</dl>
</li>
</ul>
<a name="insertChart-int-float-float-float-float-int-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>insertChart</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IChart.html" title="interface in com.aspose.slides">IChart</a>&nbsp;insertChart(int&nbsp;type,
                                float&nbsp;x,
                                float&nbsp;y,
                                float&nbsp;width,
                                float&nbsp;height,
                                int&nbsp;index,
                                boolean&nbsp;initWithSample)</pre>
<div class="block"><p>
 Creates a new chart, initializes it with sample series data and settings,
 and inserts it into the shape collection at the specified index.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShapeCollection.html#insertChart-int-float-float-float-float-int-boolean-">insertChart</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShapeCollection.html" title="interface in com.aspose.slides">IShapeCollection</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>type</code> - The type of chart to create.</dd>
<dd><code>x</code> - The x-coordinate of the new chart, in points.</dd>
<dd><code>y</code> - The y-coordinate of the new chart, in points.</dd>
<dd><code>width</code> - The width of the new chart, in points.</dd>
<dd><code>height</code> - The height of the new chart, in points.</dd>
<dd><code>index</code> - The zero-based index at which to insert the new chart in the shape collection.</dd>
<dd><code>initWithSample</code> - True to initialize the new chart with sample series data and settings; 
 false to create the chart with no series and only minimal settings, which makes creation faster.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The newly created <a href="../../../com/aspose/slides/IChart.html" title="interface in com.aspose.slides"><code>IChart</code></a>.</dd>
</dl>
</li>
</ul>
<a name="addZoomFrame-float-float-float-float-com.aspose.slides.ISlide-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addZoomFrame</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IZoomFrame.html" title="interface in com.aspose.slides">IZoomFrame</a>&nbsp;addZoomFrame(float&nbsp;x,
                                     float&nbsp;y,
                                     float&nbsp;width,
                                     float&nbsp;height,
                                     <a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a>&nbsp;slide)</pre>
<div class="block"><p>
 Creates a new Zoom frame and adds it to the end of the shape collection.
 </p><p><hr><blockquote><pre>
 This example demonstrates adding a Zoom object to the end of a collection
 (assume that there are at least two slides in the "Presentation.pptx" presentation):
 <pre>
 Presentation pres = new Presentation("Presentation.pptx");
 try {
     IZoomFrame zoomFrame = pres.getSlides().get_Item(0).getShapes().addZoomFrame(150, 20, 50, 50, pres.getSlides().get_Item(1));
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShapeCollection.html#addZoomFrame-float-float-float-float-com.aspose.slides.ISlide-">addZoomFrame</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShapeCollection.html" title="interface in com.aspose.slides">IShapeCollection</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>x</code> - The x-coordinate of the new Zoom frame, in points.</dd>
<dd><code>y</code> - The y-coordinate of the new Zoom frame, in points.</dd>
<dd><code>width</code> - The width of the new Zoom frame, in points.</dd>
<dd><code>height</code> - The height of the new Zoom frame, in points.</dd>
<dd><code>slide</code> - The <a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides"><code>ISlide</code></a> referenced by the Zoom frame;
 must belong to this presentation.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The newly created <a href="../../../com/aspose/slides/IZoomFrame.html" title="interface in com.aspose.slides"><code>IZoomFrame</code></a>.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>com.aspose.ms.System.ArgumentException</code> - Thrown if the referenced slide does not belong to the current presentation.</dd>
</dl>
</li>
</ul>
<a name="addZoomFrame-float-float-float-float-com.aspose.slides.ISlide-com.aspose.slides.IPPImage-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addZoomFrame</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IZoomFrame.html" title="interface in com.aspose.slides">IZoomFrame</a>&nbsp;addZoomFrame(float&nbsp;x,
                                     float&nbsp;y,
                                     float&nbsp;width,
                                     float&nbsp;height,
                                     <a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a>&nbsp;slide,
                                     <a href="../../../com/aspose/slides/IPPImage.html" title="interface in com.aspose.slides">IPPImage</a>&nbsp;image)</pre>
<div class="block"><p>
 Creates a new Zoom frame and adds it to the end of the shape collection.
 </p><p><hr><blockquote><pre>
 This example demonstrates adding a Zoom object to the end of a collection
 (assume that there are at least two slides in the "Presentation.pptx" presentation):
 <pre>
 Presentation pres = new Presentation("Presentation.pptx");
 try {
     IPPImage image = pres.getImages().addImage(imageBytes);
     IZoomFrame zoomFrame = pres.getSlides().get_Item(0).getShapes().addZoomFrame(150, 20, 50, 50, pres.getSlides().get_Item(1), image);
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShapeCollection.html#addZoomFrame-float-float-float-float-com.aspose.slides.ISlide-com.aspose.slides.IPPImage-">addZoomFrame</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShapeCollection.html" title="interface in com.aspose.slides">IShapeCollection</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>x</code> - The x-coordinate of the new Zoom frame, in points.</dd>
<dd><code>y</code> - The y-coordinate of the new Zoom frame, in points.</dd>
<dd><code>width</code> - The width of the new Zoom frame, in points.</dd>
<dd><code>height</code> - The height of the new Zoom frame, in points.</dd>
<dd><code>slide</code> - The <a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides"><code>ISlide</code></a> referenced by the Zoom frame;
 must belong to this presentation.</dd>
<dd><code>image</code> - The image for the referenced slide <a href="../../../com/aspose/slides/IPPImage.html" title="interface in com.aspose.slides"><code>IPPImage</code></a>.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The newly created <a href="../../../com/aspose/slides/IZoomFrame.html" title="interface in com.aspose.slides"><code>IZoomFrame</code></a>.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>com.aspose.ms.System.ArgumentException</code> - Thrown if the referenced slide does not belong to the current presentation.</dd>
</dl>
</li>
</ul>
<a name="insertZoomFrame-int-float-float-float-float-com.aspose.slides.ISlide-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>insertZoomFrame</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IZoomFrame.html" title="interface in com.aspose.slides">IZoomFrame</a>&nbsp;insertZoomFrame(int&nbsp;index,
                                        float&nbsp;x,
                                        float&nbsp;y,
                                        float&nbsp;width,
                                        float&nbsp;height,
                                        <a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a>&nbsp;slide)</pre>
<div class="block"><p>
 Creates a new Zoom frame and inserts it into the shape collection at the specified index.
 </p><p><hr><blockquote><pre>
 This example demonstrates creation and inserting a Zoom object at the specified index of a collection
 (assume that there are at least two slides in the "Presentation.pptx" presentation):
 <pre>
 Presentation pres = new Presentation("Presentation.pptx");
 try {
     IZoomFrame zoomFrame = pres.getSlides().get_Item(0).getShapes().insertZoomFrame(2, 150, 20, 50, 50, pres.getSlides().get_Item(1));
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShapeCollection.html#insertZoomFrame-int-float-float-float-float-com.aspose.slides.ISlide-">insertZoomFrame</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShapeCollection.html" title="interface in com.aspose.slides">IShapeCollection</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - The zero-based index at which to insert the Zoom frame.</dd>
<dd><code>x</code> - The x-coordinate of the new Zoom frame, in points.</dd>
<dd><code>y</code> - The y-coordinate of the new Zoom frame, in points.</dd>
<dd><code>width</code> - The width of the new Zoom frame, in points.</dd>
<dd><code>height</code> - The height of the new Zoom frame, in points.</dd>
<dd><code>slide</code> - The <a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides"><code>ISlide</code></a> referenced by the Zoom frame.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The newly created <a href="../../../com/aspose/slides/IZoomFrame.html" title="interface in com.aspose.slides"><code>IZoomFrame</code></a>.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>com.aspose.ms.System.ArgumentException</code> - Thrown if the referenced slide does not belong to the current presentation.</dd>
</dl>
</li>
</ul>
<a name="insertZoomFrame-int-float-float-float-float-com.aspose.slides.ISlide-com.aspose.slides.IPPImage-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>insertZoomFrame</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IZoomFrame.html" title="interface in com.aspose.slides">IZoomFrame</a>&nbsp;insertZoomFrame(int&nbsp;index,
                                        float&nbsp;x,
                                        float&nbsp;y,
                                        float&nbsp;width,
                                        float&nbsp;height,
                                        <a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a>&nbsp;slide,
                                        <a href="../../../com/aspose/slides/IPPImage.html" title="interface in com.aspose.slides">IPPImage</a>&nbsp;image)</pre>
<div class="block"><p>
 Creates a new Zoom frame with a predefined image and inserts it into the shape collection
 at the specified index.
 </p><p><hr><blockquote><pre>
 This example demonstrates creation and inserting a Zoom object at the specified index of a collection
 (assume that there are at least two slides in the "Presentation.pptx" presentation):
 <pre>
 Presentation pres = new Presentation("Presentation.pptx");
 try {
     IPPImage image = pres.getImages().addImage(imageBytes);
     IZoomFrame zoomFrame = pres.getSlides().get_Item(0).getShapes().insertZoomFrame(2, 150, 20, 50, 50, pres.getSlides().get_Item(1), image);
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShapeCollection.html#insertZoomFrame-int-float-float-float-float-com.aspose.slides.ISlide-com.aspose.slides.IPPImage-">insertZoomFrame</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShapeCollection.html" title="interface in com.aspose.slides">IShapeCollection</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - The zero-based index at which to insert the Zoom frame.</dd>
<dd><code>x</code> - The x-coordinate of the new Zoom frame, in points.</dd>
<dd><code>y</code> - The y-coordinate of the new Zoom frame, in points.</dd>
<dd><code>width</code> - The width of the new Zoom frame, in points.</dd>
<dd><code>height</code> - The height of the new Zoom frame, in points.</dd>
<dd><code>slide</code> - The <a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides"><code>ISlide</code></a> referenced by the Zoom frame.</dd>
<dd><code>image</code> - The image for the referenced slide <a href="../../../com/aspose/slides/IPPImage.html" title="interface in com.aspose.slides"><code>IPPImage</code></a>.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The newly created <a href="../../../com/aspose/slides/IZoomFrame.html" title="interface in com.aspose.slides"><code>IZoomFrame</code></a>.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>com.aspose.ms.System.ArgumentException</code> - Thrown if the referenced slide does not belong to the current presentation.</dd>
</dl>
</li>
</ul>
<a name="addSectionZoomFrame-float-float-float-float-com.aspose.slides.ISection-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addSectionZoomFrame</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/ISectionZoomFrame.html" title="interface in com.aspose.slides">ISectionZoomFrame</a>&nbsp;addSectionZoomFrame(float&nbsp;x,
                                                   float&nbsp;y,
                                                   float&nbsp;width,
                                                   float&nbsp;height,
                                                   <a href="../../../com/aspose/slides/ISection.html" title="interface in com.aspose.slides">ISection</a>&nbsp;section)</pre>
<div class="block"><p>
 Creates a new Section Zoom frame and adds it to the end of the shape collection.
 </p><p><hr><blockquote><pre>
 This example demonstrates adding a Section Zoom object to the end of a collection
 (assume that there are at least two sections in the "Presentation.pptx" presentation):
 <pre>
 Presentation pres = new Presentation("Presentation.pptx");
 try {
     ISectionZoomFrame zoomFrame = pres.getSlides().get_Item(0).getShapes().addSectionZoomFrame(150, 20, 50, 50, pres.getSections().get_Item(1));
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShapeCollection.html#addSectionZoomFrame-float-float-float-float-com.aspose.slides.ISection-">addSectionZoomFrame</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShapeCollection.html" title="interface in com.aspose.slides">IShapeCollection</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>x</code> - The x-coordinate of the new Section Zoom frame, in points.</dd>
<dd><code>y</code> - The y-coordinate of the new Section Zoom frame, in points.</dd>
<dd><code>width</code> - The width of the new Section Zoom frame, in points.</dd>
<dd><code>height</code> - The height of the new Section Zoom frame, in points.</dd>
<dd><code>section</code> - The <a href="../../../com/aspose/slides/ISection.html" title="interface in com.aspose.slides"><code>ISection</code></a> referenced by the Section Zoom frame; 
 must belong to this presentation and contain at least one slide.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The newly created <a href="../../../com/aspose/slides/ISectionZoomFrame.html" title="interface in com.aspose.slides"><code>ISectionZoomFrame</code></a>.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>com.aspose.ms.System.ArgumentException</code> - Thrown if the referenced section does not belong to the current presentation or contains no slides.</dd>
</dl>
</li>
</ul>
<a name="addSectionZoomFrame-float-float-float-float-com.aspose.slides.ISection-com.aspose.slides.IPPImage-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addSectionZoomFrame</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/ISectionZoomFrame.html" title="interface in com.aspose.slides">ISectionZoomFrame</a>&nbsp;addSectionZoomFrame(float&nbsp;x,
                                                   float&nbsp;y,
                                                   float&nbsp;width,
                                                   float&nbsp;height,
                                                   <a href="../../../com/aspose/slides/ISection.html" title="interface in com.aspose.slides">ISection</a>&nbsp;section,
                                                   <a href="../../../com/aspose/slides/IPPImage.html" title="interface in com.aspose.slides">IPPImage</a>&nbsp;image)</pre>
<div class="block"><p>
 Creates a new Section Zoom frame with a predefined image and adds it to the end of the shape collection.
 </p><p><hr><blockquote><pre>
 This example demonstrates adding a Section Zoom object to the end of a collection
 (assume that there are at least two sections in the "Presentation.pptx" presentation):
 <pre>
 Presentation pres = new Presentation("Presentation.pptx");
 try {
     IPPImage image = pres.getImages().addImage(Files.readAllBytes(Paths.get("image.png")));
     ISectionZoomFrame zoomFrame = pres.getSlides().get_Item(0).getShapes().addSectionZoomFrame(150, 20, 50, 50, pres.getSections().get_Item(1), image);
 } catch (IOException e) {
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShapeCollection.html#addSectionZoomFrame-float-float-float-float-com.aspose.slides.ISection-com.aspose.slides.IPPImage-">addSectionZoomFrame</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShapeCollection.html" title="interface in com.aspose.slides">IShapeCollection</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>x</code> - The x-coordinate of the new Section Zoom frame, in points.</dd>
<dd><code>y</code> - The y-coordinate of the new Section Zoom frame, in points.</dd>
<dd><code>width</code> - The width of the new Section Zoom frame, in points.</dd>
<dd><code>height</code> - The height of the new Section Zoom frame, in points.</dd>
<dd><code>section</code> - The <a href="../../../com/aspose/slides/ISection.html" title="interface in com.aspose.slides"><code>ISection</code></a> referenced by the Section Zoom frame; 
 must belong to this presentation and contain at least one slide.</dd>
<dd><code>image</code> - The <a href="../../../com/aspose/slides/IPPImage.html" title="interface in com.aspose.slides"><code>IPPImage</code></a> to display within the Section Zoom frame.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The newly created <a href="../../../com/aspose/slides/ISectionZoomFrame.html" title="interface in com.aspose.slides"><code>ISectionZoomFrame</code></a>.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>com.aspose.ms.System.ArgumentException</code> - Thrown if the referenced section does not belong to the current presentation or contains no slides.</dd>
</dl>
</li>
</ul>
<a name="insertSectionZoomFrame-int-float-float-float-float-com.aspose.slides.ISection-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>insertSectionZoomFrame</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/ISectionZoomFrame.html" title="interface in com.aspose.slides">ISectionZoomFrame</a>&nbsp;insertSectionZoomFrame(int&nbsp;index,
                                                      float&nbsp;x,
                                                      float&nbsp;y,
                                                      float&nbsp;width,
                                                      float&nbsp;height,
                                                      <a href="../../../com/aspose/slides/ISection.html" title="interface in com.aspose.slides">ISection</a>&nbsp;section)</pre>
<div class="block"><p>
 Creates a new Section Zoom frame and inserts it into to the shape collection at the specified index.
 </p><p><hr><blockquote><pre>
 This example demonstrates the creation and inserting a Section Zoom object at the specified index of a collection
 (assume that there are at least two sections in the "Presentation.pptx" presentation):
 <pre>
 Presentation pres = new Presentation("Presentation.pptx");
 try {
     ISectionZoomFrame zoomFrame = pres.getSlides().get_Item(0).getShapes().insertSectionZoomFrame(2, 150, 20, 50, 50, pres.getSections().get_Item(1));
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShapeCollection.html#insertSectionZoomFrame-int-float-float-float-float-com.aspose.slides.ISection-">insertSectionZoomFrame</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShapeCollection.html" title="interface in com.aspose.slides">IShapeCollection</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - The zero-based index at which to insert the Section Zoom frame.</dd>
<dd><code>x</code> - The x-coordinate of the new Section Zoom frame, in points.</dd>
<dd><code>y</code> - The y-coordinate of the new Section Zoom frame, in points.</dd>
<dd><code>width</code> - The width of the new Section Zoom frame, in points.</dd>
<dd><code>height</code> - The height of the new Section Zoom frame, in points.</dd>
<dd><code>section</code> - The <a href="../../../com/aspose/slides/ISection.html" title="interface in com.aspose.slides"><code>ISection</code></a> referenced by the Section Zoom frame;
 must belong to this presentation and contain at least one slide.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The newly created <a href="../../../com/aspose/slides/ISectionZoomFrame.html" title="interface in com.aspose.slides"><code>ISectionZoomFrame</code></a>.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>com.aspose.ms.System.ArgumentException</code> - Thrown if the referenced section does not belong to the current presentation or contains no slides.</dd>
</dl>
</li>
</ul>
<a name="insertSectionZoomFrame-int-float-float-float-float-com.aspose.slides.ISection-com.aspose.slides.IPPImage-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>insertSectionZoomFrame</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/ISectionZoomFrame.html" title="interface in com.aspose.slides">ISectionZoomFrame</a>&nbsp;insertSectionZoomFrame(int&nbsp;index,
                                                      float&nbsp;x,
                                                      float&nbsp;y,
                                                      float&nbsp;width,
                                                      float&nbsp;height,
                                                      <a href="../../../com/aspose/slides/ISection.html" title="interface in com.aspose.slides">ISection</a>&nbsp;section,
                                                      <a href="../../../com/aspose/slides/IPPImage.html" title="interface in com.aspose.slides">IPPImage</a>&nbsp;image)</pre>
<div class="block"><p>
 Creates a new Section Zoom frame with a predefined image and inserts it into to the shape
 collection at the specified index.
 </p><p><hr><blockquote><pre>
 This example demonstrates the creation and inserting a Section Zoom object at the specified index of a collection
 (assume that there are at least two sections in the "Presentation.pptx" presentation):
 <pre>
 Presentation pres = new Presentation("Presentation.pptx");
 try {
     IPPImage image = pres.getImages().addImage(Files.readAllBytes(Paths.get("image.png")));
     ISectionZoomFrame zoomFrame = pres.getSlides().get_Item(0).getShapes().insertSectionZoomFrame(2, 150, 20, 50, 50, pres.getSections().get_Item(1), image);
 } catch (IOException e) {
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShapeCollection.html#insertSectionZoomFrame-int-float-float-float-float-com.aspose.slides.ISection-com.aspose.slides.IPPImage-">insertSectionZoomFrame</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShapeCollection.html" title="interface in com.aspose.slides">IShapeCollection</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - The zero-based index at which to insert the Section Zoom frame.</dd>
<dd><code>x</code> - The x-coordinate of the new Section Zoom frame, in points.</dd>
<dd><code>y</code> - The y-coordinate of the new Section Zoom frame, in points.</dd>
<dd><code>width</code> - The width of the new Section Zoom frame, in points.</dd>
<dd><code>height</code> - The height of the new Section Zoom frame, in points.</dd>
<dd><code>section</code> - The <a href="../../../com/aspose/slides/ISection.html" title="interface in com.aspose.slides"><code>ISection</code></a> referenced by the Section Zoom frame;
 must belong to this presentation and contain at least one slide.</dd>
<dd><code>image</code> - The image to display within the Section Zoom frame.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The newly created <a href="../../../com/aspose/slides/ISectionZoomFrame.html" title="interface in com.aspose.slides"><code>ISectionZoomFrame</code></a>.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>com.aspose.ms.System.ArgumentException</code> - Thrown if the referenced section does not belong to the current presentation or contains no slides.</dd>
</dl>
</li>
</ul>
<a name="addSummaryZoomFrame-float-float-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addSummaryZoomFrame</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/ISummaryZoomFrame.html" title="interface in com.aspose.slides">ISummaryZoomFrame</a>&nbsp;addSummaryZoomFrame(float&nbsp;x,
                                                   float&nbsp;y,
                                                   float&nbsp;width,
                                                   float&nbsp;height)</pre>
<div class="block"><p>
 Creates a new Summary Zoom frame and adds it to the end of the shape collection.
 </p><p><hr><blockquote><pre>
 This example demonstrates adding a Summary Zoom object to the end of a collection
 (assume that there are at least two sections in the "Presentation.pptx" presentation):
 <pre>
 Presentation pres = new Presentation("Presentation.pptx");
 try {
     ISummaryZoomFrame zoomFrame = pres.getSlides().get_Item(0).getShapes().addSummaryZoomFrame(150, 20, 500, 250);
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShapeCollection.html#addSummaryZoomFrame-float-float-float-float-">addSummaryZoomFrame</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShapeCollection.html" title="interface in com.aspose.slides">IShapeCollection</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>x</code> - The x-coordinate of the new Summary Zoom frame, in points.</dd>
<dd><code>y</code> - The y-coordinate of the new Summary Zoom frame, in points.</dd>
<dd><code>width</code> - The width of the new Summary Zoom frame, in points.</dd>
<dd><code>height</code> - The height of the new Summary Zoom frame, in points.
 <p><hr>
 This method creates a new Summary Zoom and puts a collection of objects into it for all the sections in this presentation.
 </hr></p></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The newly created <a href="../../../com/aspose/slides/ISummaryZoomFrame.html" title="interface in com.aspose.slides"><code>ISummaryZoomFrame</code></a>.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../com/aspose/slides/PptxEditException.html" title="class in com.aspose.slides">PptxEditException</a></code> - Thrown if there are no sections in the presentation, or if the target slide does not belong to any section.</dd>
</dl>
</li>
</ul>
<a name="insertSummaryZoomFrame-int-float-float-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>insertSummaryZoomFrame</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/ISummaryZoomFrame.html" title="interface in com.aspose.slides">ISummaryZoomFrame</a>&nbsp;insertSummaryZoomFrame(int&nbsp;index,
                                                      float&nbsp;x,
                                                      float&nbsp;y,
                                                      float&nbsp;width,
                                                      float&nbsp;height)</pre>
<div class="block"><p>
 Creates a new Summary Zoom frame and inserts it into the shape collection at the specified index.
 </p><p><hr><blockquote><pre>
 This example demonstrates creation and inserting a Summary Zoom object at the specified index of a collection
 (assume that there are at least two sections in the "Presentation.pptx" presentation):
 <pre>
 Presentation pres = new Presentation("Presentation.pptx");
 try {
     ISummaryZoomFrame zoomFrame = pres.getSlides().get_Item(0).getShapes().insertSummaryZoomFrame(2, 150, 20, 50, 50);
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShapeCollection.html#insertSummaryZoomFrame-int-float-float-float-float-">insertSummaryZoomFrame</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShapeCollection.html" title="interface in com.aspose.slides">IShapeCollection</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - The zero-based index at which to insert the Summary Zoom frame.</dd>
<dd><code>x</code> - The x-coordinate of the new Summary Zoom frame, in points.</dd>
<dd><code>y</code> - The y-coordinate of the new Summary Zoom frame, in points.</dd>
<dd><code>width</code> - The width of the new Summary Zoom frame, in points.</dd>
<dd><code>height</code> - The height of the new Summary Zoom frame, in points.
 <p><hr>
 This method creates a Summary Zoom frame that aggregates summary links for all sections in the presentation.
 </hr></p></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The newly created <a href="../../../com/aspose/slides/ISummaryZoomFrame.html" title="interface in com.aspose.slides"><code>ISummaryZoomFrame</code></a>.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../com/aspose/slides/PptxEditException.html" title="class in com.aspose.slides">PptxEditException</a></code> - Thrown if the presentation contains no sections, or if the target slide does not belong to any section.</dd>
</dl>
</li>
</ul>
<a name="addOleObjectFrame-float-float-float-float-com.aspose.slides.IOleEmbeddedDataInfo-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addOleObjectFrame</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IOleObjectFrame.html" title="interface in com.aspose.slides">IOleObjectFrame</a>&nbsp;addOleObjectFrame(float&nbsp;x,
                                               float&nbsp;y,
                                               float&nbsp;width,
                                               float&nbsp;height,
                                               <a href="../../../com/aspose/slides/IOleEmbeddedDataInfo.html" title="interface in com.aspose.slides">IOleEmbeddedDataInfo</a>&nbsp;dataInfo)</pre>
<div class="block"><p>
 Creates a new OLE object frame and adds it to the end of the shape collection.
 </p><p><hr><blockquote><pre>
 The following examples shows how to adding OLE Object Frames to Slides of PowerPoint Presentation.
 <pre>
 // Instantiate Presentation class that represents the PPTX
 Presentation pres = new Presentation();
 try
 {
     // Access the first slide
     ISlide sld = pres.getSlides().get_Item(0);

     // Load an cel file to stream
     FileInputStream fs = new FileInputStream("book1.xlsx");
     ByteArrayOutputStream mstream = new ByteArrayOutputStream();
     byte[] buf = new byte[4096];

     while (true)
     {
         int bytesRead = fs.read(buf, 0, buf.length);
         if (bytesRead &lt;= 0)
             break;
         mstream.write(buf, 0, bytesRead);
     }
     // Create data object for embedding
     IOleEmbeddedDataInfo dataInfo = new OleEmbeddedDataInfo(mstream.toByteArray(), "xlsx");

     // Add an Ole Object Frame shape
     IOleObjectFrame oleObjectFrame = sld.getShapes().addOleObjectFrame(0, 0, (float)pres.getSlideSize().getSize().getWidth(),
             (float)pres.getSlideSize().getSize().getHeight(), dataInfo);

     //Write the PPTX to disk
     pres.save("OleEmbed_out.pptx", SaveFormat.Pptx);
 }
 catch (IOException e) { }
 finally
 {
     if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShapeCollection.html#addOleObjectFrame-float-float-float-float-com.aspose.slides.IOleEmbeddedDataInfo-">addOleObjectFrame</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShapeCollection.html" title="interface in com.aspose.slides">IShapeCollection</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>x</code> - The x-coordinate of the new OLE frame, in points.</dd>
<dd><code>y</code> - The y-coordinate of the new OLE frame, in points.</dd>
<dd><code>width</code> - The width of the new OLE frame, in points.</dd>
<dd><code>height</code> - The height of the new OLE frame, in points.</dd>
<dd><code>dataInfo</code> - The information about the embedded OLE data (<a href="../../../com/aspose/slides/IOleEmbeddedDataInfo.html" title="interface in com.aspose.slides"><code>IOleEmbeddedDataInfo</code></a>).</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The newly created <a href="../../../com/aspose/slides/IOleObjectFrame.html" title="interface in com.aspose.slides"><code>IOleObjectFrame</code></a>.</dd>
</dl>
</li>
</ul>
<a name="addOleObjectFrame-float-float-float-float-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addOleObjectFrame</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IOleObjectFrame.html" title="interface in com.aspose.slides">IOleObjectFrame</a>&nbsp;addOleObjectFrame(float&nbsp;x,
                                               float&nbsp;y,
                                               float&nbsp;width,
                                               float&nbsp;height,
                                               java.lang.String&nbsp;className,
                                               java.lang.String&nbsp;path)</pre>
<div class="block"><p>
 Creates a new OLE object frame and adds it to the end of the shape collection.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShapeCollection.html#addOleObjectFrame-float-float-float-float-java.lang.String-java.lang.String-">addOleObjectFrame</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShapeCollection.html" title="interface in com.aspose.slides">IShapeCollection</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>x</code> - The x-coordinate of the new OLE frame, in points.</dd>
<dd><code>y</code> - The y-coordinate of the new OLE frame, in points.</dd>
<dd><code>width</code> - The width of the new OLE frame, in points.</dd>
<dd><code>height</code> - The height of the new OLE frame, in points.</dd>
<dd><code>className</code> - The class name of the OLE object.</dd>
<dd><code>path</code> - The path to the linked file. <p>This path is stored verbatim in the presentation.
 If a relative path is specified, the file will be inaccessible when opening
 the presentation from a different directory.</p></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The newly created <a href="../../../com/aspose/slides/IOleObjectFrame.html" title="interface in com.aspose.slides"><code>IOleObjectFrame</code></a>.</dd>
</dl>
</li>
</ul>
<a name="insertOleObjectFrame-int-float-float-float-float-com.aspose.slides.IOleEmbeddedDataInfo-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>insertOleObjectFrame</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IOleObjectFrame.html" title="interface in com.aspose.slides">IOleObjectFrame</a>&nbsp;insertOleObjectFrame(int&nbsp;index,
                                                  float&nbsp;x,
                                                  float&nbsp;y,
                                                  float&nbsp;width,
                                                  float&nbsp;height,
                                                  <a href="../../../com/aspose/slides/IOleEmbeddedDataInfo.html" title="interface in com.aspose.slides">IOleEmbeddedDataInfo</a>&nbsp;dataInfo)</pre>
<div class="block"><p>
 Creates a new OLE object frame and inserts it into the shape collection at the specified index.
 </p><p><hr><blockquote><pre>
 This example demonstrates inserting an OLE object at the second index:
 <pre>
 byte[] fileData = Files.readAllBytes(Paths.get("test.zip"));
 IOleDataInfo dataInfo = new OleDataInfo(fileData, "zip");
 IOleObjectFrame oleObjectFrame = slides.getShapes().addOleObjectFrame(2, 150, 20, 50, 50, dataInfo);
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShapeCollection.html#insertOleObjectFrame-int-float-float-float-float-com.aspose.slides.IOleEmbeddedDataInfo-">insertOleObjectFrame</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShapeCollection.html" title="interface in com.aspose.slides">IShapeCollection</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - The zero-based index at which to insert the OLE object frame.</dd>
<dd><code>x</code> - The x-coordinate of the new OLE frame, in points.</dd>
<dd><code>y</code> - The y-coordinate of the new OLE frame, in points.</dd>
<dd><code>width</code> - The width of the new OLE frame, in points.</dd>
<dd><code>height</code> - The height of the new OLE frame, in points.</dd>
<dd><code>dataInfo</code> - The embedded OLE data information (<a href="../../../com/aspose/slides/IOleEmbeddedDataInfo.html" title="interface in com.aspose.slides"><code>IOleEmbeddedDataInfo</code></a>).</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The newly created <a href="../../../com/aspose/slides/IOleObjectFrame.html" title="interface in com.aspose.slides"><code>IOleObjectFrame</code></a>.</dd>
</dl>
</li>
</ul>
<a name="insertOleObjectFrame-int-float-float-float-float-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>insertOleObjectFrame</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IOleObjectFrame.html" title="interface in com.aspose.slides">IOleObjectFrame</a>&nbsp;insertOleObjectFrame(int&nbsp;index,
                                                  float&nbsp;x,
                                                  float&nbsp;y,
                                                  float&nbsp;width,
                                                  float&nbsp;height,
                                                  java.lang.String&nbsp;className,
                                                  java.lang.String&nbsp;path)</pre>
<div class="block"><p>
 Creates a new OLE object frame and inserts it into the shape collection at the specified index.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShapeCollection.html#insertOleObjectFrame-int-float-float-float-float-java.lang.String-java.lang.String-">insertOleObjectFrame</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShapeCollection.html" title="interface in com.aspose.slides">IShapeCollection</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - The zero-based index at which to insert the OLE object frame.</dd>
<dd><code>x</code> - The x-coordinate of the new OLE frame, in points.</dd>
<dd><code>y</code> - The y-coordinate of the new OLE frame, in points.</dd>
<dd><code>width</code> - The width of the new OLE frame, in points.</dd>
<dd><code>height</code> - The height of the new OLE frame, in points.</dd>
<dd><code>className</code> - The class name of the OLE object.</dd>
<dd><code>path</code> - The path to the linked file. <p>This path is stored verbatim in the presentation.
 If a relative path is specified, the file will be inaccessible when opening
 the presentation from a different directory.</p></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The newly created OLE object frame.</dd>
</dl>
</li>
</ul>
<a name="addVideoFrame-float-float-float-float-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addVideoFrame</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IVideoFrame.html" title="interface in com.aspose.slides">IVideoFrame</a>&nbsp;addVideoFrame(float&nbsp;x,
                                       float&nbsp;y,
                                       float&nbsp;width,
                                       float&nbsp;height,
                                       java.lang.String&nbsp;fname)</pre>
<div class="block"><p>
 Creates a new video frame and adds it to the end of the shape collection.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShapeCollection.html#addVideoFrame-float-float-float-float-java.lang.String-">addVideoFrame</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShapeCollection.html" title="interface in com.aspose.slides">IShapeCollection</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>x</code> - The x-coordinate of the new video frame, in points.</dd>
<dd><code>y</code> - The y-coordinate of the new video frame, in points.</dd>
<dd><code>width</code> - The width of the new video frame, in points.</dd>
<dd><code>height</code> - The height of the new video frame, in points.</dd>
<dd><code>fname</code> - The path or name of the video file to embed.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The newly created <a href="../../../com/aspose/slides/IVideoFrame.html" title="interface in com.aspose.slides"><code>IVideoFrame</code></a>.</dd>
</dl>
</li>
</ul>
<a name="addVideoFrame-float-float-float-float-com.aspose.slides.IVideo-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addVideoFrame</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IVideoFrame.html" title="interface in com.aspose.slides">IVideoFrame</a>&nbsp;addVideoFrame(float&nbsp;x,
                                       float&nbsp;y,
                                       float&nbsp;width,
                                       float&nbsp;height,
                                       <a href="../../../com/aspose/slides/IVideo.html" title="interface in com.aspose.slides">IVideo</a>&nbsp;video)</pre>
<div class="block"><p>
 Creates a new video frame and adds it to the end of the shape collection.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShapeCollection.html#addVideoFrame-float-float-float-float-com.aspose.slides.IVideo-">addVideoFrame</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShapeCollection.html" title="interface in com.aspose.slides">IShapeCollection</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>x</code> - The x-coordinate of the new video frame, in points.</dd>
<dd><code>y</code> - The y-coordinate of the new video frame, in points.</dd>
<dd><code>width</code> - The width of the new video frame, in points.</dd>
<dd><code>height</code> - The height of the new video frame, in points.</dd>
<dd><code>video</code> - The <a href="../../../com/aspose/slides/IVideo.html" title="interface in com.aspose.slides"><code>IVideo</code></a> to embed in the video frame.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The newly created <a href="../../../com/aspose/slides/IVideoFrame.html" title="interface in com.aspose.slides"><code>IVideoFrame</code></a>.</dd>
</dl>
</li>
</ul>
<a name="insertVideoFrame-int-float-float-float-float-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>insertVideoFrame</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IVideoFrame.html" title="interface in com.aspose.slides">IVideoFrame</a>&nbsp;insertVideoFrame(int&nbsp;index,
                                          float&nbsp;x,
                                          float&nbsp;y,
                                          float&nbsp;width,
                                          float&nbsp;height,
                                          java.lang.String&nbsp;fname)</pre>
<div class="block"><p>
 Creates a new video frame and inserts it into the shape collection at the specified index.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShapeCollection.html#insertVideoFrame-int-float-float-float-float-java.lang.String-">insertVideoFrame</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShapeCollection.html" title="interface in com.aspose.slides">IShapeCollection</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - The zero-based index at which to insert the video frame.</dd>
<dd><code>x</code> - The x-coordinate of the new video frame, in points.</dd>
<dd><code>y</code> - The y-coordinate of the new video frame, in points.</dd>
<dd><code>width</code> - The width of the new video frame, in points.</dd>
<dd><code>height</code> - The height of the new video frame, in points.</dd>
<dd><code>fname</code> - The path or name of the video file to embed.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The newly created <a href="../../../com/aspose/slides/IVideoFrame.html" title="interface in com.aspose.slides"><code>IVideoFrame</code></a>.</dd>
</dl>
</li>
</ul>
<a name="addAudioFrameCD-float-float-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addAudioFrameCD</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IAudioFrame.html" title="interface in com.aspose.slides">IAudioFrame</a>&nbsp;addAudioFrameCD(float&nbsp;x,
                                         float&nbsp;y,
                                         float&nbsp;width,
                                         float&nbsp;height)</pre>
<div class="block"><p>
 Creates a new audio frame linked to a CD track and adds it to the end of the shape collection.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShapeCollection.html#addAudioFrameCD-float-float-float-float-">addAudioFrameCD</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShapeCollection.html" title="interface in com.aspose.slides">IShapeCollection</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>x</code> - The x-coordinate of the new audio frame, in points.</dd>
<dd><code>y</code> - The y-coordinate of the new audio frame, in points.</dd>
<dd><code>width</code> - The width of the new audio frame, in points.</dd>
<dd><code>height</code> - The height of the new audio frame, in points.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The newly created <a href="../../../com/aspose/slides/IAudioFrame.html" title="interface in com.aspose.slides"><code>IAudioFrame</code></a>.</dd>
</dl>
</li>
</ul>
<a name="insertAudioFrameCD-int-float-float-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>insertAudioFrameCD</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IAudioFrame.html" title="interface in com.aspose.slides">IAudioFrame</a>&nbsp;insertAudioFrameCD(int&nbsp;index,
                                            float&nbsp;x,
                                            float&nbsp;y,
                                            float&nbsp;width,
                                            float&nbsp;height)</pre>
<div class="block"><p>
 Creates a new audio frame linked to a CD track and inserts it into the shape collection
 at the specified index.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShapeCollection.html#insertAudioFrameCD-int-float-float-float-float-">insertAudioFrameCD</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShapeCollection.html" title="interface in com.aspose.slides">IShapeCollection</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - The zero-based index at which to insert the audio frame.</dd>
<dd><code>x</code> - The x-coordinate of the new audio frame, in points.</dd>
<dd><code>y</code> - The y-coordinate of the new audio frame, in points.</dd>
<dd><code>width</code> - The width of the new audio frame, in points.</dd>
<dd><code>height</code> - The height of the new audio frame, in points.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The newly created <a href="../../../com/aspose/slides/IAudioFrame.html" title="interface in com.aspose.slides"><code>IAudioFrame</code></a>.</dd>
</dl>
</li>
</ul>
<a name="addAudioFrameLinked-float-float-float-float-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addAudioFrameLinked</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IAudioFrame.html" title="interface in com.aspose.slides">IAudioFrame</a>&nbsp;addAudioFrameLinked(float&nbsp;x,
                                             float&nbsp;y,
                                             float&nbsp;width,
                                             float&nbsp;height,
                                             java.lang.String&nbsp;fname)</pre>
<div class="block"><p>
 Creates a new audio frame linked to an external audio file and adds it to the end of
 the shape collection.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShapeCollection.html#addAudioFrameLinked-float-float-float-float-java.lang.String-">addAudioFrameLinked</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShapeCollection.html" title="interface in com.aspose.slides">IShapeCollection</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>x</code> - The x-coordinate of the new audio frame, in points.</dd>
<dd><code>y</code> - The y-coordinate of the new audio frame, in points.</dd>
<dd><code>width</code> - The width of the new audio frame, in points.</dd>
<dd><code>height</code> - The height of the new audio frame, in points.</dd>
<dd><code>fname</code> - The path or name of the external audio file to link.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The newly created <a href="../../../com/aspose/slides/IAudioFrame.html" title="interface in com.aspose.slides"><code>IAudioFrame</code></a>.</dd>
</dl>
</li>
</ul>
<a name="insertAudioFrameLinked-int-float-float-float-float-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>insertAudioFrameLinked</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IAudioFrame.html" title="interface in com.aspose.slides">IAudioFrame</a>&nbsp;insertAudioFrameLinked(int&nbsp;index,
                                                float&nbsp;x,
                                                float&nbsp;y,
                                                float&nbsp;width,
                                                float&nbsp;height,
                                                java.lang.String&nbsp;fname)</pre>
<div class="block"><p>
 Creates a new audio frame linked to an external audio file and inserts it into the shape
 collection at the specified index.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShapeCollection.html#insertAudioFrameLinked-int-float-float-float-float-java.lang.String-">insertAudioFrameLinked</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShapeCollection.html" title="interface in com.aspose.slides">IShapeCollection</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - The zero-based index at which to insert the audio frame.</dd>
<dd><code>x</code> - The x-coordinate of the new audio frame, in points.</dd>
<dd><code>y</code> - The y-coordinate of the new audio frame, in points.</dd>
<dd><code>width</code> - The width of the new audio frame, in points.</dd>
<dd><code>height</code> - The height of the new audio frame, in points.</dd>
<dd><code>fname</code> - The path or name of the external audio file to link.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The newly created <a href="../../../com/aspose/slides/IAudioFrame.html" title="interface in com.aspose.slides"><code>IAudioFrame</code></a>.</dd>
</dl>
</li>
</ul>
<a name="addAudioFrameEmbedded-float-float-float-float-java.io.InputStream-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addAudioFrameEmbedded</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IAudioFrame.html" title="interface in com.aspose.slides">IAudioFrame</a>&nbsp;addAudioFrameEmbedded(float&nbsp;x,
                                               float&nbsp;y,
                                               float&nbsp;width,
                                               float&nbsp;height,
                                               java.io.InputStream&nbsp;audio_stream)</pre>
<div class="block"><p>
 Creates a new audio frame with an embedded WAV file and adds it to the end of the
 shape collection. The embedded audio is added to the Presentation.Audios collection.
 </p><p><hr><blockquote><pre>
 The following examples shows how to create Audio Frame.
 <pre>
 // Instantiates a presentation class that represents a presentation file
 Presentation pres = new Presentation();
 try {
     // Gets the first slide
     ISlide sld = pres.getSlides().get_Item(0);
     // Loads the the wav sound file to stream
     FileInputStream fstr = new FileInputStream("sampleaudio.wav");
     try {
         // Adds the Audio Frame
         IAudioFrame audioFrame = sld.getShapes().addAudioFrameEmbedded(50, 150, 100, 100, fstr);
         // Sets the Play Mode and Volume of the Audio
         audioFrame.setPlayMode(AudioPlayModePreset.Auto);
         audioFrame.setVolume(AudioVolumeMode.Loud);
     } finally {
         if (fstr != null) fstr.close();
     }
     // Writes the PowerPoint file to disk
     pres.save("AudioFrameEmbed_out.pptx", SaveFormat.Pptx);
 } catch(IOException e) {
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShapeCollection.html#addAudioFrameEmbedded-float-float-float-float-java.io.InputStream-">addAudioFrameEmbedded</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShapeCollection.html" title="interface in com.aspose.slides">IShapeCollection</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>x</code> - The x-coordinate of the new audio frame, in points.</dd>
<dd><code>y</code> - The y-coordinate of the new audio frame, in points.</dd>
<dd><code>width</code> - The width of the new audio frame, in points.</dd>
<dd><code>height</code> - The height of the new audio frame, in points.</dd>
<dd><code>audio_stream</code> - An input stream containing WAV audio data to embed.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The newly created <a href="../../../com/aspose/slides/IAudioFrame.html" title="interface in com.aspose.slides"><code>IAudioFrame</code></a>.</dd>
</dl>
</li>
</ul>
<a name="insertAudioFrameEmbedded-int-float-float-float-float-java.io.InputStream-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>insertAudioFrameEmbedded</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IAudioFrame.html" title="interface in com.aspose.slides">IAudioFrame</a>&nbsp;insertAudioFrameEmbedded(int&nbsp;index,
                                                  float&nbsp;x,
                                                  float&nbsp;y,
                                                  float&nbsp;width,
                                                  float&nbsp;height,
                                                  java.io.InputStream&nbsp;audio_stream)</pre>
<div class="block"><p>
 Creates a new audio frame with an embedded WAV file and inserts it into the shape
 collection at the specified index. The embedded audio is added to the Presentation.Audios
 collection.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShapeCollection.html#insertAudioFrameEmbedded-int-float-float-float-float-java.io.InputStream-">insertAudioFrameEmbedded</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShapeCollection.html" title="interface in com.aspose.slides">IShapeCollection</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - The zero-based index at which to insert the audio frame.</dd>
<dd><code>x</code> - The x-coordinate of the new audio frame, in points.</dd>
<dd><code>y</code> - The y-coordinate of the new audio frame, in points.</dd>
<dd><code>width</code> - The width of the new audio frame, in points.</dd>
<dd><code>height</code> - The height of the new audio frame, in points.</dd>
<dd><code>audio_stream</code> - An input stream containing WAV audio data to embed.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The newly created <a href="../../../com/aspose/slides/IAudioFrame.html" title="interface in com.aspose.slides"><code>IAudioFrame</code></a>.</dd>
</dl>
</li>
</ul>
<a name="addAudioFrameEmbedded-float-float-float-float-com.aspose.slides.IAudio-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addAudioFrameEmbedded</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IAudioFrame.html" title="interface in com.aspose.slides">IAudioFrame</a>&nbsp;addAudioFrameEmbedded(float&nbsp;x,
                                               float&nbsp;y,
                                               float&nbsp;width,
                                               float&nbsp;height,
                                               <a href="../../../com/aspose/slides/IAudio.html" title="interface in com.aspose.slides">IAudio</a>&nbsp;audio)</pre>
<div class="block"><p>
 Creates a new audio frame and adds it to the end of the shape collection using an
 existing audio object from the Presentation.Audios list.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShapeCollection.html#addAudioFrameEmbedded-float-float-float-float-com.aspose.slides.IAudio-">addAudioFrameEmbedded</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShapeCollection.html" title="interface in com.aspose.slides">IShapeCollection</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>x</code> - The x-coordinate of the new audio frame, in points.</dd>
<dd><code>y</code> - The y-coordinate of the new audio frame, in points.</dd>
<dd><code>width</code> - The width of the new audio frame, in points.</dd>
<dd><code>height</code> - The height of the new audio frame, in points.</dd>
<dd><code>audio</code> - An <a href="../../../com/aspose/slides/IAudio.html" title="interface in com.aspose.slides"><code>IAudio</code></a> instance from the Presentation.Audios collection.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The newly created <a href="../../../com/aspose/slides/IAudioFrame.html" title="interface in com.aspose.slides"><code>IAudioFrame</code></a>.</dd>
</dl>
</li>
</ul>
<a name="insertAudioFrameEmbedded-int-float-float-float-float-com.aspose.slides.IAudio-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>insertAudioFrameEmbedded</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IAudioFrame.html" title="interface in com.aspose.slides">IAudioFrame</a>&nbsp;insertAudioFrameEmbedded(int&nbsp;index,
                                                  float&nbsp;x,
                                                  float&nbsp;y,
                                                  float&nbsp;width,
                                                  float&nbsp;height,
                                                  <a href="../../../com/aspose/slides/IAudio.html" title="interface in com.aspose.slides">IAudio</a>&nbsp;audio)</pre>
<div class="block"><p>
 Creates a new audio frame and inserts it into the shape collection at the specified index
 using an existing audio object from the Presentation.Audios list.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShapeCollection.html#insertAudioFrameEmbedded-int-float-float-float-float-com.aspose.slides.IAudio-">insertAudioFrameEmbedded</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShapeCollection.html" title="interface in com.aspose.slides">IShapeCollection</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - The zero-based index at which to insert the audio frame.</dd>
<dd><code>x</code> - The x-coordinate of the new audio frame, in points.</dd>
<dd><code>y</code> - The y-coordinate of the new audio frame, in points.</dd>
<dd><code>width</code> - The width of the new audio frame, in points.</dd>
<dd><code>height</code> - The height of the new audio frame, in points.</dd>
<dd><code>audio</code> - An <a href="../../../com/aspose/slides/IAudio.html" title="interface in com.aspose.slides"><code>IAudio</code></a> instance from the Presentation.Audios collection to embed.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The newly created <a href="../../../com/aspose/slides/IAudioFrame.html" title="interface in com.aspose.slides"><code>IAudioFrame</code></a>.</dd>
</dl>
</li>
</ul>
<a name="indexOf-com.aspose.slides.IShape-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>indexOf</h4>
<pre>public final&nbsp;int&nbsp;indexOf(<a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a>&nbsp;shape)</pre>
<div class="block"><p>
 Returns the zero-based index of the first occurrence of the specified shape in the collection.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShapeCollection.html#indexOf-com.aspose.slides.IShape-">indexOf</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShapeCollection.html" title="interface in com.aspose.slides">IShapeCollection</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>shape</code> - The shape to locate in the collection.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The zero-based index of the first occurrence of the shape in the shape collection if found;
 otherwise, –1.</dd>
</dl>
</li>
</ul>
<a name="toArray--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>toArray</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a>[]&nbsp;toArray()</pre>
<div class="block"><p>
 Creates and returns an array that contains all shapes.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShapeCollection.html#toArray--">toArray</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShapeCollection.html" title="interface in com.aspose.slides">IShapeCollection</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>An array of <a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides"><code>IShape</code></a> objects.</dd>
</dl>
</li>
</ul>
<a name="toArray-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>toArray</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a>[]&nbsp;toArray(int&nbsp;startIndex,
                              int&nbsp;count)</pre>
<div class="block"><p>
 Creates and returns an array that contains all shapes in the specified range.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShapeCollection.html#toArray-int-int-">toArray</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShapeCollection.html" title="interface in com.aspose.slides">IShapeCollection</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>startIndex</code> - The index of the first shape to return.</dd>
<dd><code>count</code> - The number of shapes to return.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>An array of <a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides"><code>IShape</code></a> objects.</dd>
</dl>
</li>
</ul>
<a name="reorder-int-com.aspose.slides.IShape-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>reorder</h4>
<pre>public final&nbsp;void&nbsp;reorder(int&nbsp;index,
                          <a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a>&nbsp;shape)</pre>
<div class="block"><p>
 Moves the specified shape to a new position within the shape collection.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShapeCollection.html#reorder-int-com.aspose.slides.IShape-">reorder</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShapeCollection.html" title="interface in com.aspose.slides">IShapeCollection</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - The zero-based target index where the shape will be placed.</dd>
<dd><code>shape</code> - The <a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides"><code>IShape</code></a> to move within the collection.</dd>
</dl>
</li>
</ul>
<a name="reorder-int-com.aspose.slides.IShape...-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>reorder</h4>
<pre>public final&nbsp;void&nbsp;reorder(int&nbsp;index,
                          <a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a>...&nbsp;shapes)</pre>
<div class="block"><p>
 Moves the specified shapes within the shape collection, placing them starting at the given index.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShapeCollection.html#reorder-int-com.aspose.slides.IShape...-">reorder</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShapeCollection.html" title="interface in com.aspose.slides">IShapeCollection</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - The zero-based target index where the first specified shape will be placed; 
 subsequent shapes follow in the order provided.</dd>
<dd><code>shapes</code> - One or more <a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides"><code>IShape</code></a> instances to move within the collection.</dd>
</dl>
</li>
</ul>
<a name="addAutoShape-int-float-float-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addAutoShape</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IAutoShape.html" title="interface in com.aspose.slides">IAutoShape</a>&nbsp;addAutoShape(int&nbsp;shapeType,
                                     float&nbsp;x,
                                     float&nbsp;y,
                                     float&nbsp;width,
                                     float&nbsp;height)</pre>
<div class="block"><p>
 Creates a new auto shape with default formatting and adds it to the end of the
 shape collection.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShapeCollection.html#addAutoShape-int-float-float-float-float-">addAutoShape</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShapeCollection.html" title="interface in com.aspose.slides">IShapeCollection</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>shapeType</code> - The <a href="../../../com/aspose/slides/ShapeType.html" title="class in com.aspose.slides"><code>ShapeType</code></a> of the auto shape to add.</dd>
<dd><code>x</code> - The x-coordinate of the shape’s frame, in points.</dd>
<dd><code>y</code> - The y-coordinate of the shape’s frame, in points.</dd>
<dd><code>width</code> - The width of the shape’s frame, in points.</dd>
<dd><code>height</code> - The height of the shape’s frame, in points.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The newly created <a href="../../../com/aspose/slides/IAutoShape.html" title="interface in com.aspose.slides"><code>IAutoShape</code></a>.</dd>
</dl>
</li>
</ul>
<a name="addAutoShape-int-float-float-float-float-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addAutoShape</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IAutoShape.html" title="interface in com.aspose.slides">IAutoShape</a>&nbsp;addAutoShape(int&nbsp;shapeType,
                                     float&nbsp;x,
                                     float&nbsp;y,
                                     float&nbsp;width,
                                     float&nbsp;height,
                                     boolean&nbsp;createFromTemplate)</pre>
<div class="block"><p>
 Creates a new auto shape and adds it to the end of the shape collection, optionally
 initializing it with default template formatting.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShapeCollection.html#addAutoShape-int-float-float-float-float-boolean-">addAutoShape</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShapeCollection.html" title="interface in com.aspose.slides">IShapeCollection</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>shapeType</code> - The <a href="../../../com/aspose/slides/ShapeType.html" title="class in com.aspose.slides"><code>ShapeType</code></a> of the auto shape to add.</dd>
<dd><code>x</code> - The x-coordinate of the shape’s frame, in points.</dd>
<dd><code>y</code> - The y-coordinate of the shape’s frame, in points.</dd>
<dd><code>width</code> - The width of the shape’s frame, in points.</dd>
<dd><code>height</code> - The height of the shape’s frame, in points.</dd>
<dd><code>createFromTemplate</code> - True to apply default template styling (simple style, centered text, and non-empty name)
 to the new shape; false to create the shape with all properties set to their default values.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The newly created <a href="../../../com/aspose/slides/IAutoShape.html" title="interface in com.aspose.slides"><code>IAutoShape</code></a>.</dd>
</dl>
</li>
</ul>
<a name="addMathShape-float-float-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addMathShape</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IAutoShape.html" title="interface in com.aspose.slides">IAutoShape</a>&nbsp;addMathShape(float&nbsp;x,
                                     float&nbsp;y,
                                     float&nbsp;width,
                                     float&nbsp;height)</pre>
<div class="block"><p>
 Creates a new rectangle auto shape to host mathematical content and adds it to the
 end of the shape collection.
 </p><p><hr><blockquote><pre>
 The following example shows how to add Mathematical Equation in PowerPoint Presentation.
 <pre>
 Presentation pres = new Presentation();
 try {
     IAutoShape mathShape = pres.getSlides().get_Item(0).getShapes().addMathShape(0, 0, 720, 150);
     IMathParagraph mathParagraph = ((MathPortion)mathShape.getTextFrame().getParagraphs().get_Item(0).getPortions().get_Item(0)).getMathParagraph();
     IMathFraction fraction = new MathematicalText("x").divide("y");
     mathParagraph.add(new MathBlock(fraction));
     IMathBlock mathBlock = new MathematicalText("c")
         .setSuperscript("2")
         .join("=")
         .join(new MathematicalText("a").setSuperscript("2"))
         .join("+")
         .join(new MathematicalText("b").setSuperscript("2"));
     mathParagraph.add(mathBlock);
     pres.save("math.pptx", SaveFormat.Pptx);
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShapeCollection.html#addMathShape-float-float-float-float-">addMathShape</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShapeCollection.html" title="interface in com.aspose.slides">IShapeCollection</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>x</code> - The x-coordinate of the shape’s frame, in points.</dd>
<dd><code>y</code> - The y-coordinate of the shape’s frame, in points.</dd>
<dd><code>width</code> - The width of the shape’s frame, in points.</dd>
<dd><code>height</code> - The height of the shape’s frame, in points.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The newly created <a href="../../../com/aspose/slides/IAutoShape.html" title="interface in com.aspose.slides"><code>IAutoShape</code></a>.</dd>
</dl>
</li>
</ul>
<a name="insertAutoShape-int-int-float-float-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>insertAutoShape</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IAutoShape.html" title="interface in com.aspose.slides">IAutoShape</a>&nbsp;insertAutoShape(int&nbsp;index,
                                        int&nbsp;shapeType,
                                        float&nbsp;x,
                                        float&nbsp;y,
                                        float&nbsp;width,
                                        float&nbsp;height)</pre>
<div class="block"><p>
 Creates a new auto shape and inserts it into the shape collection at the specified index,
 applying default template formatting.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShapeCollection.html#insertAutoShape-int-int-float-float-float-float-">insertAutoShape</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShapeCollection.html" title="interface in com.aspose.slides">IShapeCollection</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - The zero-based index at which to insert the new auto shape.</dd>
<dd><code>shapeType</code> - The <a href="../../../com/aspose/slides/ShapeType.html" title="class in com.aspose.slides"><code>ShapeType</code></a> of the auto shape to insert.</dd>
<dd><code>x</code> - The x-coordinate of the shape’s frame, in points.</dd>
<dd><code>y</code> - The y-coordinate of the shape’s frame, in points.</dd>
<dd><code>width</code> - The width of the shape’s frame, in points.</dd>
<dd><code>height</code> - The height of the shape’s frame, in points.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The newly created <a href="../../../com/aspose/slides/IAutoShape.html" title="interface in com.aspose.slides"><code>IAutoShape</code></a>.</dd>
</dl>
</li>
</ul>
<a name="insertAutoShape-int-int-float-float-float-float-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>insertAutoShape</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IAutoShape.html" title="interface in com.aspose.slides">IAutoShape</a>&nbsp;insertAutoShape(int&nbsp;index,
                                        int&nbsp;shapeType,
                                        float&nbsp;x,
                                        float&nbsp;y,
                                        float&nbsp;width,
                                        float&nbsp;height,
                                        boolean&nbsp;createFromTemplate)</pre>
<div class="block"><p>
 Creates a new auto shape and inserts it into the shape collection at the specified index,
 optionally initializing it with default template styling.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShapeCollection.html#insertAutoShape-int-int-float-float-float-float-boolean-">insertAutoShape</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShapeCollection.html" title="interface in com.aspose.slides">IShapeCollection</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - The zero-based index at which to insert the auto shape.</dd>
<dd><code>shapeType</code> - The <a href="../../../com/aspose/slides/ShapeType.html" title="class in com.aspose.slides"><code>ShapeType</code></a> of the auto shape to insert.</dd>
<dd><code>x</code> - The x-coordinate of the shape’s frame, in points.</dd>
<dd><code>y</code> - The y-coordinate of the shape’s frame, in points.</dd>
<dd><code>width</code> - The width of the shape’s frame, in points.</dd>
<dd><code>height</code> - The height of the shape’s frame, in points.</dd>
<dd><code>createFromTemplate</code> - True to apply default template styling (including a non-empty name, simple style, and centered text); 
 false to create the shape with all properties set to their defaults.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The newly created <a href="../../../com/aspose/slides/IAutoShape.html" title="interface in com.aspose.slides"><code>IAutoShape</code></a>.</dd>
</dl>
</li>
</ul>
<a name="addGroupShape--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addGroupShape</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IGroupShape.html" title="interface in com.aspose.slides">IGroupShape</a>&nbsp;addGroupShape()</pre>
<div class="block"><p>
 Creates a new empty group shape and adds it to the end of the shape collection.
 The group’s frame will automatically adjust to fit any shapes added to it.
 </p><p><hr><blockquote><pre>
 The following example shows how to add a group shape to a slide of PowerPoint Presentation.
 <pre>
 // Instantiate Presentation class
 Presentation pres = new Presentation();
 try {
     // Get the first slide
     ISlide sld = pres.getSlides().get_Item(0);
     // Accessing the shape collection of slides
     IShapeCollection slideShapes = sld.getShapes();
     // Adding a group shape to the slide
     IGroupShape groupShape = slideShapes.addGroupShape();
     // Adding shapes inside added group shape
     groupShape.getShapes().addAutoShape(ShapeType.Rectangle, 300, 100, 100, 100);
     groupShape.getShapes().addAutoShape(ShapeType.Rectangle, 500, 100, 100, 100);
     groupShape.getShapes().addAutoShape(ShapeType.Rectangle, 300, 300, 100, 100);
     groupShape.getShapes().addAutoShape(ShapeType.Rectangle, 500, 300, 100, 100);
     // Adding group shape frame
     groupShape.setFrame(new ShapeFrame(100, 300, 500, 40, NullableBool.False, NullableBool.False, 0));
     // Write the PPTX file to disk
     pres.save("GroupShape_out.pptx", SaveFormat.Pptx);
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShapeCollection.html#addGroupShape--">addGroupShape</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShapeCollection.html" title="interface in com.aspose.slides">IShapeCollection</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The newly created <a href="../../../com/aspose/slides/IGroupShape.html" title="interface in com.aspose.slides"><code>IGroupShape</code></a>.</dd>
</dl>
</li>
</ul>
<a name="addGroupShape-com.aspose.slides.ISvgImage-float-float-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addGroupShape</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IGroupShape.html" title="interface in com.aspose.slides">IGroupShape</a>&nbsp;addGroupShape(<a href="../../../com/aspose/slides/ISvgImage.html" title="interface in com.aspose.slides">ISvgImage</a>&nbsp;svgImage,
                                       float&nbsp;x,
                                       float&nbsp;y,
                                       float&nbsp;width,
                                       float&nbsp;height)</pre>
<div class="block"><p>
 Creates a new group shape, converts the specified SVG image into individual shapes,
 and adds the resulting group to the end of the shape collection.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShapeCollection.html#addGroupShape-com.aspose.slides.ISvgImage-float-float-float-float-">addGroupShape</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShapeCollection.html" title="interface in com.aspose.slides">IShapeCollection</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>svgImage</code> - The <a href="../../../com/aspose/slides/ISvgImage.html" title="interface in com.aspose.slides"><code>ISvgImage</code></a> containing vector content to convert into shapes.</dd>
<dd><code>x</code> - The x-coordinate of the group’s frame, in points.</dd>
<dd><code>y</code> - The y-coordinate of the group’s frame, in points.</dd>
<dd><code>width</code> - The width of the group’s frame, in points.</dd>
<dd><code>height</code> - The height of the group’s frame, in points.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The newly created <a href="../../../com/aspose/slides/IGroupShape.html" title="interface in com.aspose.slides"><code>IGroupShape</code></a>.</dd>
</dl>
</li>
</ul>
<a name="insertGroupShape-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>insertGroupShape</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IGroupShape.html" title="interface in com.aspose.slides">IGroupShape</a>&nbsp;insertGroupShape(int&nbsp;index)</pre>
<div class="block"><p>
 Creates a new empty group shape and inserts it to the shape collection at the specified index.
 The group’s frame will automatically adjust to fit any shapes added to it.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShapeCollection.html#insertGroupShape-int-">insertGroupShape</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShapeCollection.html" title="interface in com.aspose.slides">IShapeCollection</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - The zero-based index at which to insert the group shape.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The newly created <a href="../../../com/aspose/slides/IGroupShape.html" title="interface in com.aspose.slides"><code>IGroupShape</code></a>.</dd>
</dl>
</li>
</ul>
<a name="addConnector-int-float-float-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addConnector</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IConnector.html" title="interface in com.aspose.slides">IConnector</a>&nbsp;addConnector(int&nbsp;shapeType,
                                     float&nbsp;x,
                                     float&nbsp;y,
                                     float&nbsp;width,
                                     float&nbsp;height)</pre>
<div class="block"><p>
 Creates a new connector shape with default template styling and adds it to the end of the
 shape collection.
 </p><p><hr><blockquote><pre>
 The following example shows how to add a connector (a bent connector) between two shapes (an ellipse and rectangle) in PowerPoint Presentation.
 <pre>
 // Instantiates a presentation class that represents a PPTX file
 Presentation pres = new Presentation();
 try {
     // Accesses the shapes collection for a specific slide
     IShapeCollection shapes = pres.getSlides().get_Item(0).getShapes();
     // Adds an Ellipse autoshape
     IAutoShape ellipse = shapes.addAutoShape(ShapeType.Ellipse, 0, 100, 100, 100);
     // Adds a Rectangle autoshape
     IAutoShape rectangle = shapes.addAutoShape(ShapeType.Rectangle, 100, 300, 100, 100);
     // Adds a connector shape to the slide shape collection
     IConnector connector = shapes.addConnector(ShapeType.BentConnector2, 0, 0, 10, 10);
     // Connects the shapes using the connector
     connector.setStartShapeConnectedTo(ellipse);
     connector.setEndShapeConnectedTo(rectangle);
     // Calls reroute that sets the automatic shortest path between shapes
     connector.reroute();
     // Saves the presentation
     pres.save("Shapes-connector.pptx", SaveFormat.Pptx);
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShapeCollection.html#addConnector-int-float-float-float-float-">addConnector</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShapeCollection.html" title="interface in com.aspose.slides">IShapeCollection</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>shapeType</code> - The <a href="../../../com/aspose/slides/ShapeType.html" title="class in com.aspose.slides"><code>ShapeType</code></a> of the connector shape to add.</dd>
<dd><code>x</code> - The x-coordinate of the connector’s frame, in points.</dd>
<dd><code>y</code> - The y-coordinate of the connector’s frame, in points.</dd>
<dd><code>width</code> - The width of the connector’s frame, in points.</dd>
<dd><code>height</code> - The height of the connector’s frame, in points.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The newly created <a href="../../../com/aspose/slides/IConnector.html" title="interface in com.aspose.slides"><code>IConnector</code></a>.</dd>
</dl>
</li>
</ul>
<a name="addConnector-int-float-float-float-float-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addConnector</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IConnector.html" title="interface in com.aspose.slides">IConnector</a>&nbsp;addConnector(int&nbsp;shapeType,
                                     float&nbsp;x,
                                     float&nbsp;y,
                                     float&nbsp;width,
                                     float&nbsp;height,
                                     boolean&nbsp;createFromTemplate)</pre>
<div class="block"><p>
 Creates a new connector shape and adds it to the end of the shape collection,
 optionally applying default template styling.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShapeCollection.html#addConnector-int-float-float-float-float-boolean-">addConnector</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShapeCollection.html" title="interface in com.aspose.slides">IShapeCollection</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>shapeType</code> - The <a href="../../../com/aspose/slides/ShapeType.html" title="class in com.aspose.slides"><code>ShapeType</code></a> of the connector shape to create.</dd>
<dd><code>x</code> - The x-coordinate of the connector’s frame, in points.</dd>
<dd><code>y</code> - The y-coordinate of the connector’s frame, in points.</dd>
<dd><code>width</code> - The width of the connector’s frame, in points.</dd>
<dd><code>height</code> - The height of the connector’s frame, in points.</dd>
<dd><code>createFromTemplate</code> - True to apply default template styling (non-empty name, simple style); 
 false to create the connector with default property values.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The newly created <a href="../../../com/aspose/slides/IConnector.html" title="interface in com.aspose.slides"><code>IConnector</code></a>.</dd>
</dl>
</li>
</ul>
<a name="insertConnector-int-int-float-float-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>insertConnector</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IConnector.html" title="interface in com.aspose.slides">IConnector</a>&nbsp;insertConnector(int&nbsp;index,
                                        int&nbsp;shapeType,
                                        float&nbsp;x,
                                        float&nbsp;y,
                                        float&nbsp;width,
                                        float&nbsp;height)</pre>
<div class="block"><p>
 Creates a new connector shape and inserts it into the shape collection at the specified index,
 applying default template styling.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShapeCollection.html#insertConnector-int-int-float-float-float-float-">insertConnector</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShapeCollection.html" title="interface in com.aspose.slides">IShapeCollection</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - The zero-based index at which to insert the connector shape.</dd>
<dd><code>shapeType</code> - The <a href="../../../com/aspose/slides/ShapeType.html" title="class in com.aspose.slides"><code>ShapeType</code></a> of the connector shape to insert.</dd>
<dd><code>x</code> - The x-coordinate of the connector’s frame, in points.</dd>
<dd><code>y</code> - The y-coordinate of the connector’s frame, in points.</dd>
<dd><code>width</code> - The width of the connector’s frame, in points.</dd>
<dd><code>height</code> - The height of the connector’s frame, in points.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The newly created <a href="../../../com/aspose/slides/IConnector.html" title="interface in com.aspose.slides"><code>IConnector</code></a>.</dd>
</dl>
</li>
</ul>
<a name="insertConnector-int-int-float-float-float-float-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>insertConnector</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IConnector.html" title="interface in com.aspose.slides">IConnector</a>&nbsp;insertConnector(int&nbsp;index,
                                        int&nbsp;shapeType,
                                        float&nbsp;x,
                                        float&nbsp;y,
                                        float&nbsp;width,
                                        float&nbsp;height,
                                        boolean&nbsp;createFromTemplate)</pre>
<div class="block"><p>
 Creates a new connector shape and inserts it into the shape collection at the specified index,
 optionally applying default template styling.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShapeCollection.html#insertConnector-int-int-float-float-float-float-boolean-">insertConnector</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShapeCollection.html" title="interface in com.aspose.slides">IShapeCollection</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - The zero-based index at which to insert the connector shape.</dd>
<dd><code>shapeType</code> - The <a href="../../../com/aspose/slides/ShapeType.html" title="class in com.aspose.slides"><code>ShapeType</code></a> of the connector shape to insert.</dd>
<dd><code>x</code> - The x-coordinate of the connector’s frame, in points.</dd>
<dd><code>y</code> - The y-coordinate of the connector’s frame, in points.</dd>
<dd><code>width</code> - The width of the connector’s frame, in points.</dd>
<dd><code>height</code> - The height of the connector’s frame, in points.</dd>
<dd><code>createFromTemplate</code> - True to apply default template styling (non-empty name, simple style);
 false to create the connector with default property values.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The newly created <a href="../../../com/aspose/slides/IConnector.html" title="interface in com.aspose.slides"><code>IConnector</code></a>.</dd>
</dl>
</li>
</ul>
<a name="addPictureFrame-int-float-float-float-float-com.aspose.slides.IPPImage-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addPictureFrame</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IPictureFrame.html" title="interface in com.aspose.slides">IPictureFrame</a>&nbsp;addPictureFrame(int&nbsp;shapeType,
                                           float&nbsp;x,
                                           float&nbsp;y,
                                           float&nbsp;width,
                                           float&nbsp;height,
                                           <a href="../../../com/aspose/slides/IPPImage.html" title="interface in com.aspose.slides">IPPImage</a>&nbsp;image)</pre>
<div class="block"><p>
 Creates a new picture frame containing the specified image and adds it to the end of the
 shape collection.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShapeCollection.html#addPictureFrame-int-float-float-float-float-com.aspose.slides.IPPImage-">addPictureFrame</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShapeCollection.html" title="interface in com.aspose.slides">IShapeCollection</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>shapeType</code> - <p>Specifies the shape type contained in <a href="../../../com/aspose/slides/ShapeType.html" title="class in com.aspose.slides"><code>ShapeType</code></a>,
 except for all kinds of lines:</p>
 <p>    ShapeType.Line,</p>
 <p>    ShapeType.StraightConnector1,</p>
 <p>    ShapeType.BentConnector2,</p>
 <p>    ShapeType.BentConnector3,</p>
 <p>    ShapeType.BentConnector4,</p>
 <p>    ShapeType.BentConnector5,</p>
 <p>    ShapeType.CurvedConnector2,</p>
 <p>    ShapeType.CurvedConnector3,</p>
 <p>    ShapeType.CurvedConnector4,</p>
 <p>    ShapeType.CurvedConnector5.</p></dd>
<dd><code>x</code> - The x-coordinate of the picture frame, in points.</dd>
<dd><code>y</code> - The y-coordinate of the picture frame, in points.</dd>
<dd><code>width</code> - The width of the picture frame, in points.</dd>
<dd><code>height</code> - The height of the picture frame, in points.</dd>
<dd><code>image</code> - The <a href="../../../com/aspose/slides/IPPImage.html" title="interface in com.aspose.slides"><code>IPPImage</code></a> to display in the picture frame.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The newly created <a href="../../../com/aspose/slides/IPictureFrame.html" title="interface in com.aspose.slides"><code>IPictureFrame</code></a>.</dd>
</dl>
</li>
</ul>
<a name="insertPictureFrame-int-int-float-float-float-float-com.aspose.slides.IPPImage-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>insertPictureFrame</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IPictureFrame.html" title="interface in com.aspose.slides">IPictureFrame</a>&nbsp;insertPictureFrame(int&nbsp;index,
                                              int&nbsp;shapeType,
                                              float&nbsp;x,
                                              float&nbsp;y,
                                              float&nbsp;width,
                                              float&nbsp;height,
                                              <a href="../../../com/aspose/slides/IPPImage.html" title="interface in com.aspose.slides">IPPImage</a>&nbsp;image)</pre>
<div class="block"><p>
 Creates a new picture frame containing the specified image and inserts it into the shape
 collection at the specified index.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShapeCollection.html#insertPictureFrame-int-int-float-float-float-float-com.aspose.slides.IPPImage-">insertPictureFrame</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShapeCollection.html" title="interface in com.aspose.slides">IShapeCollection</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - The zero-based index at which to insert the picture frame.</dd>
<dd><code>shapeType</code> - <p>Specifies the shape type contained in <a href="../../../com/aspose/slides/ShapeType.html" title="class in com.aspose.slides"><code>ShapeType</code></a>,
 except for all kinds of lines:</p>
 <p>    ShapeType.Line,</p>
 <p>    ShapeType.StraightConnector1,</p>
 <p>    ShapeType.BentConnector2,</p>
 <p>    ShapeType.BentConnector3,</p>
 <p>    ShapeType.BentConnector4,</p>
 <p>    ShapeType.BentConnector5,</p>
 <p>    ShapeType.CurvedConnector2,</p>
 <p>    ShapeType.CurvedConnector3,</p>
 <p>    ShapeType.CurvedConnector4,</p>
 <p>    ShapeType.CurvedConnector5.</p></dd>
<dd><code>x</code> - The x-coordinate of the picture frame, in points.</dd>
<dd><code>y</code> - The y-coordinate of the picture frame, in points.</dd>
<dd><code>width</code> - The width of the picture frame, in points.</dd>
<dd><code>height</code> - The height of the picture frame, in points.</dd>
<dd><code>image</code> - The <a href="../../../com/aspose/slides/IPPImage.html" title="interface in com.aspose.slides"><code>IPPImage</code></a> to display in the picture frame.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The newly created <a href="../../../com/aspose/slides/IPictureFrame.html" title="interface in com.aspose.slides"><code>IPictureFrame</code></a>.</dd>
</dl>
</li>
</ul>
<a name="addTable-float-float-double:A-double:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addTable</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/ITable.html" title="interface in com.aspose.slides">ITable</a>&nbsp;addTable(float&nbsp;x,
                             float&nbsp;y,
                             double[]&nbsp;columnWidths,
                             double[]&nbsp;rowHeights)</pre>
<div class="block"><p>
 Creates a new table and adds it to the end of the shape collection.
 </p><p><hr><blockquote><pre>
 The following examples shows how to add table in PowerPoint Presentation.
 <pre>
 // Instantiate Presentation class that represents PPTX file
 Presentation pres = new Presentation();
 try
 {
     // Access first slide
     ISlide sld = pres.getSlides().get_Item(0);

     // Define columns with widths and rows with heights
     double[] dblCols = {50, 50, 50};
     double[] dblRows = {50, 30, 30, 30, 30};

     // Add table shape to slide
     ITable tbl = sld.getShapes().addTable(100, 50, dblCols, dblRows);

     // Set border format for each cell
     for (int row = 0; row &lt; tbl.getRows().size(); row++)
     {
         for (int cell = 0; cell &lt; tbl.getRows().get_Item(row).size(); cell++)
         {
             tbl.get_Item(cell, row).getCellFormat().getBorderTop().getFillFormat().setFillType(FillType.Solid);
             tbl.get_Item(cell, row).getCellFormat().getBorderTop().getFillFormat().getSolidFillColor().setColor(Color.RED);
             tbl.get_Item(cell, row).getCellFormat().getBorderTop().setWidth(5);

             tbl.get_Item(cell, row).getCellFormat().getBorderBottom().getFillFormat().setFillType((FillType.Solid));
             tbl.get_Item(cell, row).getCellFormat().getBorderBottom().getFillFormat().getSolidFillColor().setColor(Color.RED);
             tbl.get_Item(cell, row).getCellFormat().getBorderBottom().setWidth(5);

             tbl.get_Item(cell, row).getCellFormat().getBorderLeft().getFillFormat().setFillType(FillType.Solid);
             tbl.get_Item(cell, row).getCellFormat().getBorderLeft().getFillFormat().getSolidFillColor().setColor(Color.RED);
             tbl.get_Item(cell, row).getCellFormat().getBorderLeft().setWidth(5);

             tbl.get_Item(cell, row).getCellFormat().getBorderRight().getFillFormat().setFillType(FillType.Solid);
             tbl.get_Item(cell, row).getCellFormat().getBorderRight().getFillFormat().getSolidFillColor().setColor(Color.RED);
             tbl.get_Item(cell, row).getCellFormat().getBorderRight().setWidth(5);
         }
     }
     // Merge cells 1 &amp; 2 of row 1
     tbl.mergeCells(tbl.get_Item(0, 0), tbl.get_Item(1, 1), false);

     // Add text to the merged cell
     tbl.get_Item(0, 0).getTextFrame().setText("Merged Cells");

     // Save PPTX to Disk
     pres.save("table.pptx", SaveFormat.Pptx);
 }
 finally
 {
     if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShapeCollection.html#addTable-float-float-double:A-double:A-">addTable</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShapeCollection.html" title="interface in com.aspose.slides">IShapeCollection</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>x</code> - The x-coordinate of the table, in points.</dd>
<dd><code>y</code> - The y-coordinate of the table, in points.</dd>
<dd><code>columnWidths</code> - An array of doubles representing the widths of the table’s
 columns, in points.</dd>
<dd><code>rowHeights</code> - An array of doubles representing the heights of the table’s
 rows, in points.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The newly created <a href="../../../com/aspose/slides/ITable.html" title="interface in com.aspose.slides"><code>ITable</code></a>.</dd>
</dl>
</li>
</ul>
<a name="insertTable-int-float-float-double:A-double:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>insertTable</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/ITable.html" title="interface in com.aspose.slides">ITable</a>&nbsp;insertTable(int&nbsp;index,
                                float&nbsp;x,
                                float&nbsp;y,
                                double[]&nbsp;columnWidths,
                                double[]&nbsp;rowHeights)</pre>
<div class="block"><p>
 Creates a new table and inserts it into the shape collection at the specified index.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShapeCollection.html#insertTable-int-float-float-double:A-double:A-">insertTable</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShapeCollection.html" title="interface in com.aspose.slides">IShapeCollection</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - The zero-based index at which to insert the table.</dd>
<dd><code>x</code> - The x-coordinate of the table, in points.</dd>
<dd><code>y</code> - The y-coordinate of the table, in points.</dd>
<dd><code>columnWidths</code> - An array of doubles representing the widths of the table’s
 columns, in points.</dd>
<dd><code>rowHeights</code> - An array of doubles representing the heights of the table’s
 rows, in points.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The newly created <a href="../../../com/aspose/slides/ITable.html" title="interface in com.aspose.slides"><code>ITable</code></a>.</dd>
</dl>
</li>
</ul>
<a name="removeAt-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>removeAt</h4>
<pre>public final&nbsp;void&nbsp;removeAt(int&nbsp;index)</pre>
<div class="block"><p>
 Removes the shape at the specified index from the shape collection.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShapeCollection.html#removeAt-int-">removeAt</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShapeCollection.html" title="interface in com.aspose.slides">IShapeCollection</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - The zero-based index of the shape to remove.</dd>
</dl>
</li>
</ul>
<a name="remove-com.aspose.slides.IShape-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>remove</h4>
<pre>public final&nbsp;void&nbsp;remove(<a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a>&nbsp;shape)</pre>
<div class="block"><p>
 Removes the first occurrence of the specified shape from the shape collection.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShapeCollection.html#remove-com.aspose.slides.IShape-">remove</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShapeCollection.html" title="interface in com.aspose.slides">IShapeCollection</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>shape</code> - The <a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides"><code>IShape</code></a> to remove.</dd>
</dl>
</li>
</ul>
<a name="clear--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>clear</h4>
<pre>public final&nbsp;void&nbsp;clear()</pre>
<div class="block"><p>
 Removes all shapes from the shape collection.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShapeCollection.html#clear--">clear</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShapeCollection.html" title="interface in com.aspose.slides">IShapeCollection</a></code></dd>
</dl>
</li>
</ul>
<a name="iterator--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>iterator</h4>
<pre>public final&nbsp;com.aspose.ms.System.Collections.Generic.IGenericEnumerator&lt;<a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a>&gt;&nbsp;iterator()</pre>
<div class="block"><p>
 Returns an enumerator that iterates through the collection.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>iterator</code>&nbsp;in interface&nbsp;<code>com.aspose.ms.System.Collections.Generic.IGenericEnumerable&lt;<a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a>&gt;</code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>iterator</code>&nbsp;in interface&nbsp;<code>com.aspose.ms.System.Collections.IEnumerable&lt;<a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a>&gt;</code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>iterator</code>&nbsp;in interface&nbsp;<code>java.lang.Iterable&lt;<a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a>&gt;</code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>A <code>IGenericEnumerator</code> that can be used to iterate through the collection.</dd>
</dl>
</li>
</ul>
<a name="iteratorJava--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>iteratorJava</h4>
<pre>public final&nbsp;com.aspose.ms.System.Collections.Generic.IGenericEnumerator&lt;<a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a>&gt;&nbsp;iteratorJava()</pre>
<div class="block"><p>
 Returns a java iterator for the entire collection.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IGenericCollection.html#iteratorJava--">iteratorJava</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IGenericCollection.html" title="interface in com.aspose.slides">IGenericCollection</a>&lt;<a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a>&gt;</code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>An <code>Iterator</code> for the entire collection.</dd>
</dl>
</li>
</ul>
<a name="getParentGroup--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getParentGroup</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IGroupShape.html" title="interface in com.aspose.slides">IGroupShape</a>&nbsp;getParentGroup()</pre>
<div class="block"><p>
 Gets the parent group shape object for the shapes collection.
 Read-only <a href="../../../com/aspose/slides/IGroupShape.html" title="interface in com.aspose.slides"><code>IGroupShape</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShapeCollection.html#getParentGroup--">getParentGroup</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShapeCollection.html" title="interface in com.aspose.slides">IShapeCollection</a></code></dd>
</dl>
</li>
</ul>
<a name="addClone-com.aspose.slides.IShape-float-float-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addClone</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a>&nbsp;addClone(<a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a>&nbsp;sourceShape,
                             float&nbsp;x,
                             float&nbsp;y,
                             float&nbsp;width,
                             float&nbsp;height)</pre>
<div class="block"><p>
 Creates a copy of the specified shape and adds it to the end of the shape collection.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShapeCollection.html#addClone-com.aspose.slides.IShape-float-float-float-float-">addClone</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShapeCollection.html" title="interface in com.aspose.slides">IShapeCollection</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>sourceShape</code> - The shape to clone.</dd>
<dd><code>x</code> - The x-coordinate of the new shape’s frame, in points.</dd>
<dd><code>y</code> - The y-coordinate of the new shape’s frame, in points.</dd>
<dd><code>width</code> - The width of the new shape’s frame, in points.</dd>
<dd><code>height</code> - The height of the new shape’s frame, in points.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The newly created <a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides"><code>IShape</code></a>.</dd>
</dl>
</li>
</ul>
<a name="addClone-com.aspose.slides.IShape-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addClone</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a>&nbsp;addClone(<a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a>&nbsp;sourceShape,
                             float&nbsp;x,
                             float&nbsp;y)</pre>
<div class="block"><p>
 Creates a copy of the specified shape and adds it to the end of the shape collection.
 The new shape retains the width and height of the <code>sourceShape</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShapeCollection.html#addClone-com.aspose.slides.IShape-float-float-">addClone</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShapeCollection.html" title="interface in com.aspose.slides">IShapeCollection</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>sourceShape</code> - The shape to clone.</dd>
<dd><code>x</code> - The x-coordinate of the new shape’s frame, in points.</dd>
<dd><code>y</code> - The y-coordinate of the new shape’s frame, in points.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The newly created <a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides"><code>IShape</code></a>.</dd>
</dl>
</li>
</ul>
<a name="addClone-com.aspose.slides.IShape-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addClone</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a>&nbsp;addClone(<a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a>&nbsp;sourceShape)</pre>
<div class="block"><p>
 Creates a copy of the specified shape and adds it to the end of the shape collection.
 The cloned shape retains the original’s position and size.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShapeCollection.html#addClone-com.aspose.slides.IShape-">addClone</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShapeCollection.html" title="interface in com.aspose.slides">IShapeCollection</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>sourceShape</code> - The <a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides"><code>IShape</code></a> to clone.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The newly created <a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides"><code>IShape</code></a>.</dd>
</dl>
</li>
</ul>
<a name="insertClone-int-com.aspose.slides.IShape-float-float-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>insertClone</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a>&nbsp;insertClone(int&nbsp;index,
                                <a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a>&nbsp;sourceShape,
                                float&nbsp;x,
                                float&nbsp;y,
                                float&nbsp;width,
                                float&nbsp;height)</pre>
<div class="block"><p>
 Creates a copy of the specified shape and inserts it into the shape collection at the specified index.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShapeCollection.html#insertClone-int-com.aspose.slides.IShape-float-float-float-float-">insertClone</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShapeCollection.html" title="interface in com.aspose.slides">IShapeCollection</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - The zero-based index at which to insert the cloned shape.</dd>
<dd><code>sourceShape</code> - The <a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides"><code>IShape</code></a> to clone.</dd>
<dd><code>x</code> - The x-coordinate of the cloned shape’s frame, in points.</dd>
<dd><code>y</code> - The y-coordinate of the cloned shape’s frame, in points.</dd>
<dd><code>width</code> - The width of the cloned shape’s frame, in points.</dd>
<dd><code>height</code> - The height of the cloned shape’s frame, in points.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The newly created <a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides"><code>IShape</code></a>.</dd>
</dl>
</li>
</ul>
<a name="insertClone-int-com.aspose.slides.IShape-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>insertClone</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a>&nbsp;insertClone(int&nbsp;index,
                                <a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a>&nbsp;sourceShape,
                                float&nbsp;x,
                                float&nbsp;y)</pre>
<div class="block"><p>
 Creates a copy of the specified shape and inserts it into the shape collection at the specified index.
 The new shape retains the width and height of the <code>sourceShape</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShapeCollection.html#insertClone-int-com.aspose.slides.IShape-float-float-">insertClone</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShapeCollection.html" title="interface in com.aspose.slides">IShapeCollection</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - The zero-based index at which to insert the cloned shape.</dd>
<dd><code>sourceShape</code> - The <a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides"><code>IShape</code></a> to clone.</dd>
<dd><code>x</code> - The x-coordinate of the cloned shape’s frame, in points.</dd>
<dd><code>y</code> - The y-coordinate of the cloned shape’s frame, in points.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The newly created <a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides"><code>IShape</code></a>.</dd>
</dl>
</li>
</ul>
<a name="insertClone-int-com.aspose.slides.IShape-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>insertClone</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a>&nbsp;insertClone(int&nbsp;index,
                                <a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a>&nbsp;sourceShape)</pre>
<div class="block"><p>
 Creates a copy of the specified shape and inserts it into the shape collection at the specified index.
 The cloned shape retains the original’s position and size.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShapeCollection.html#insertClone-int-com.aspose.slides.IShape-">insertClone</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShapeCollection.html" title="interface in com.aspose.slides">IShapeCollection</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - The zero-based index at which to insert the cloned shape.</dd>
<dd><code>sourceShape</code> - The <a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides"><code>IShape</code></a> to clone.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The newly created <a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides"><code>IShape</code></a>.</dd>
</dl>
</li>
</ul>
<a name="copyTo-com.aspose.ms.System.Array-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>copyTo</h4>
<pre>public final&nbsp;void&nbsp;copyTo(com.aspose.ms.System.Array&nbsp;array,
                         int&nbsp;index)</pre>
<div class="block"><p>
 Copies all elements from the collection to the specified array.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>copyTo</code>&nbsp;in interface&nbsp;<code>com.aspose.ms.System.Collections.ICollection&lt;<a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a>&gt;</code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>array</code> - Target array.</dd>
<dd><code>index</code> - Starting index in the target array.</dd>
</dl>
</li>
</ul>
<a name="isSynchronized--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isSynchronized</h4>
<pre>public final&nbsp;boolean&nbsp;isSynchronized()</pre>
<div class="block"><p>
 Returns a value indicating whether access to the collection is synchronized (thread-safe).
 Read-only <code>boolean</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>isSynchronized</code>&nbsp;in interface&nbsp;<code>com.aspose.ms.System.Collections.ICollection&lt;<a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a>&gt;</code></dd>
</dl>
</li>
</ul>
<a name="getSyncRoot--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getSyncRoot</h4>
<pre>public final&nbsp;java.lang.Object&nbsp;getSyncRoot()</pre>
<div class="block"><p>
 Returns a synchronization root.
 Read-only <code>Object</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>getSyncRoot</code>&nbsp;in interface&nbsp;<code>com.aspose.ms.System.Collections.ICollection&lt;<a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a>&gt;</code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/ShapeBevel.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/ShapeElement.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/ShapeCollection.html" target="_top">Frames</a></li>
<li><a href="ShapeCollection.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><i>Copyright &copy; 2004-2025 Aspose Pty Ltd. All Rights Reserved.</i></small></p>
</body>
</html>
