<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_421) on Fri Jul 11 05:32:45 CDT 2025 -->
<title>WebDocumentOptions (Aspose.Slides for Java (25.7) API Documentation)</title>
<meta name="date" content="2025-07-11">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="WebDocumentOptions (Aspose.Slides for Java (25.7) API Documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/WebDocument.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/WheelTransition.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/WebDocumentOptions.html" target="_top">Frames</a></li>
<li><a href="WebDocumentOptions.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.aspose.slides</div>
<h2 title="Class WebDocumentOptions" class="title">Class WebDocumentOptions</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.aspose.slides.WebDocumentOptions</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">WebDocumentOptions</span>
extends java.lang.Object</pre>
<div class="block"><p>
 Represents an options set for <a href="../../../com/aspose/slides/WebDocument.html" title="class in com.aspose.slides"><code>WebDocument</code></a> saving.
 </p></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/WebDocumentOptions.html#WebDocumentOptions--">WebDocumentOptions</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/WebDocumentOptions.html#getAnimateShapes--">getAnimateShapes</a></span>()</code>
<div class="block">
 Returns or sets shapes animation option.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/WebDocumentOptions.html#getAnimateTransitions--">getAnimateTransitions</a></span>()</code>
<div class="block">
 Returns or sets transitions animation option.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/WebDocumentOptions.html#getEmbedImages--">getEmbedImages</a></span>()</code>
<div class="block">
 Returns or sets images embedding option.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IOutputSaver.html" title="interface in com.aspose.slides">IOutputSaver</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/WebDocumentOptions.html#getOutputSaver--">getOutputSaver</a></span>()</code>
<div class="block">
 Returns or sets output saver.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/ITemplateEngine.html" title="interface in com.aspose.slides">ITemplateEngine</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/WebDocumentOptions.html#getTemplateEngine--">getTemplateEngine</a></span>()</code>
<div class="block">
 Returns or sets templates engine.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/WebDocumentOptions.html#setAnimateShapes-boolean-">setAnimateShapes</a></span>(boolean&nbsp;value)</code>
<div class="block">
 Returns or sets shapes animation option.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/WebDocumentOptions.html#setAnimateTransitions-boolean-">setAnimateTransitions</a></span>(boolean&nbsp;value)</code>
<div class="block">
 Returns or sets transitions animation option.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/WebDocumentOptions.html#setEmbedImages-boolean-">setEmbedImages</a></span>(boolean&nbsp;value)</code>
<div class="block">
 Returns or sets images embedding option.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/WebDocumentOptions.html#setOutputSaver-com.aspose.slides.IOutputSaver-">setOutputSaver</a></span>(<a href="../../../com/aspose/slides/IOutputSaver.html" title="interface in com.aspose.slides">IOutputSaver</a>&nbsp;value)</code>
<div class="block">
 Returns or sets output saver.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/WebDocumentOptions.html#setTemplateEngine-com.aspose.slides.ITemplateEngine-">setTemplateEngine</a></span>(<a href="../../../com/aspose/slides/ITemplateEngine.html" title="interface in com.aspose.slides">ITemplateEngine</a>&nbsp;value)</code>
<div class="block">
 Returns or sets templates engine.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="WebDocumentOptions--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>WebDocumentOptions</h4>
<pre>public&nbsp;WebDocumentOptions()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getTemplateEngine--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTemplateEngine</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/ITemplateEngine.html" title="interface in com.aspose.slides">ITemplateEngine</a>&nbsp;getTemplateEngine()</pre>
<div class="block"><p>
 Returns or sets templates engine.
 Read/write <a href="../../../com/aspose/slides/ITemplateEngine.html" title="interface in com.aspose.slides"><code>ITemplateEngine</code></a>.
 </p></div>
</li>
</ul>
<a name="setTemplateEngine-com.aspose.slides.ITemplateEngine-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTemplateEngine</h4>
<pre>public final&nbsp;void&nbsp;setTemplateEngine(<a href="../../../com/aspose/slides/ITemplateEngine.html" title="interface in com.aspose.slides">ITemplateEngine</a>&nbsp;value)</pre>
<div class="block"><p>
 Returns or sets templates engine.
 Read/write <a href="../../../com/aspose/slides/ITemplateEngine.html" title="interface in com.aspose.slides"><code>ITemplateEngine</code></a>.
 </p></div>
</li>
</ul>
<a name="getOutputSaver--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getOutputSaver</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IOutputSaver.html" title="interface in com.aspose.slides">IOutputSaver</a>&nbsp;getOutputSaver()</pre>
<div class="block"><p>
 Returns or sets output saver.
 Read/write <a href="../../../com/aspose/slides/IOutputSaver.html" title="interface in com.aspose.slides"><code>IOutputSaver</code></a>.
 </p></div>
</li>
</ul>
<a name="setOutputSaver-com.aspose.slides.IOutputSaver-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setOutputSaver</h4>
<pre>public final&nbsp;void&nbsp;setOutputSaver(<a href="../../../com/aspose/slides/IOutputSaver.html" title="interface in com.aspose.slides">IOutputSaver</a>&nbsp;value)</pre>
<div class="block"><p>
 Returns or sets output saver.
 Read/write <a href="../../../com/aspose/slides/IOutputSaver.html" title="interface in com.aspose.slides"><code>IOutputSaver</code></a>.
 </p></div>
</li>
</ul>
<a name="getEmbedImages--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEmbedImages</h4>
<pre>public final&nbsp;boolean&nbsp;getEmbedImages()</pre>
<div class="block"><p>
 Returns or sets images embedding option.
 Read/write <code>boolean</code>.
 </p></div>
</li>
</ul>
<a name="setEmbedImages-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEmbedImages</h4>
<pre>public final&nbsp;void&nbsp;setEmbedImages(boolean&nbsp;value)</pre>
<div class="block"><p>
 Returns or sets images embedding option.
 Read/write <code>boolean</code>.
 </p></div>
</li>
</ul>
<a name="getAnimateTransitions--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAnimateTransitions</h4>
<pre>public final&nbsp;boolean&nbsp;getAnimateTransitions()</pre>
<div class="block"><p>
 Returns or sets transitions animation option.
 Read/write <code>boolean</code>.
 </p></div>
</li>
</ul>
<a name="setAnimateTransitions-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAnimateTransitions</h4>
<pre>public final&nbsp;void&nbsp;setAnimateTransitions(boolean&nbsp;value)</pre>
<div class="block"><p>
 Returns or sets transitions animation option.
 Read/write <code>boolean</code>.
 </p></div>
</li>
</ul>
<a name="getAnimateShapes--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAnimateShapes</h4>
<pre>public final&nbsp;boolean&nbsp;getAnimateShapes()</pre>
<div class="block"><p>
 Returns or sets shapes animation option.
 Read/write <code>boolean</code>.
 </p></div>
</li>
</ul>
<a name="setAnimateShapes-boolean-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setAnimateShapes</h4>
<pre>public final&nbsp;void&nbsp;setAnimateShapes(boolean&nbsp;value)</pre>
<div class="block"><p>
 Returns or sets shapes animation option.
 Read/write <code>boolean</code>.
 </p></div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/WebDocument.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/WheelTransition.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/WebDocumentOptions.html" target="_top">Frames</a></li>
<li><a href="WebDocumentOptions.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><i>Copyright &copy; 2004-2025 Aspose Pty Ltd. All Rights Reserved.</i></small></p>
</body>
</html>
