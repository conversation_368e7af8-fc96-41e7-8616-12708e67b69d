<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_421) on Fri Jul 11 05:32:45 CDT 2025 -->
<title>TransitionValueBase (Aspose.Slides for Java (25.7) API Documentation)</title>
<meta name="date" content="2025-07-11">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="TransitionValueBase (Aspose.Slides for Java (25.7) API Documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/TransitionType.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/Trendline.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/TransitionValueBase.html" target="_top">Frames</a></li>
<li><a href="TransitionValueBase.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.aspose.slides</div>
<h2 title="Class TransitionValueBase" class="title">Class TransitionValueBase</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.aspose.slides.TransitionValueBase</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../com/aspose/slides/ITransitionValueBase.html" title="interface in com.aspose.slides">ITransitionValueBase</a></dd>
</dl>
<dl>
<dt>Direct Known Subclasses:</dt>
<dd><a href="../../../com/aspose/slides/CornerDirectionTransition.html" title="class in com.aspose.slides">CornerDirectionTransition</a>, <a href="../../../com/aspose/slides/EightDirectionTransition.html" title="class in com.aspose.slides">EightDirectionTransition</a>, <a href="../../../com/aspose/slides/EmptyTransition.html" title="class in com.aspose.slides">EmptyTransition</a>, <a href="../../../com/aspose/slides/FlyThroughTransition.html" title="class in com.aspose.slides">FlyThroughTransition</a>, <a href="../../../com/aspose/slides/GlitterTransition.html" title="class in com.aspose.slides">GlitterTransition</a>, <a href="../../../com/aspose/slides/InOutTransition.html" title="class in com.aspose.slides">InOutTransition</a>, <a href="../../../com/aspose/slides/LeftRightDirectionTransition.html" title="class in com.aspose.slides">LeftRightDirectionTransition</a>, <a href="../../../com/aspose/slides/MorphTransition.html" title="class in com.aspose.slides">MorphTransition</a>, <a href="../../../com/aspose/slides/OptionalBlackTransition.html" title="class in com.aspose.slides">OptionalBlackTransition</a>, <a href="../../../com/aspose/slides/OrientationTransition.html" title="class in com.aspose.slides">OrientationTransition</a>, <a href="../../../com/aspose/slides/RevealTransition.html" title="class in com.aspose.slides">RevealTransition</a>, <a href="../../../com/aspose/slides/RippleTransition.html" title="class in com.aspose.slides">RippleTransition</a>, <a href="../../../com/aspose/slides/ShredTransition.html" title="class in com.aspose.slides">ShredTransition</a>, <a href="../../../com/aspose/slides/SideDirectionTransition.html" title="class in com.aspose.slides">SideDirectionTransition</a>, <a href="../../../com/aspose/slides/SplitTransition.html" title="class in com.aspose.slides">SplitTransition</a>, <a href="../../../com/aspose/slides/WheelTransition.html" title="class in com.aspose.slides">WheelTransition</a></dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">TransitionValueBase</span>
extends java.lang.Object
implements <a href="../../../com/aspose/slides/ITransitionValueBase.html" title="interface in com.aspose.slides">ITransitionValueBase</a></pre>
<div class="block"><p>
 Base class for slide transition effects.
 </p></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TransitionValueBase.html#equals-java.lang.Object-">equals</a></span>(java.lang.Object&nbsp;obj)</code>
<div class="block">
 Determines whether the two TransitionValueBase instances are equal.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TransitionValueBase.html#hashCode--">hashCode</a></span>()</code>
<div class="block">
 Serves as a hash function for a particular type, suitable for use
 in hashing algorithms and data structures like a hash table.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>getClass, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="equals-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>equals</h4>
<pre>public&nbsp;boolean&nbsp;equals(java.lang.Object&nbsp;obj)</pre>
<div class="block"><p>
 Determines whether the two TransitionValueBase instances are equal.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code>equals</code>&nbsp;in class&nbsp;<code>java.lang.Object</code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>obj</code> - The TransitionValueBase to compare with the current TransitionValueBase.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd><b>true</b> if the specified TransitionValueBase is equal to the current TransitionValueBase; otherwise, <b>false</b>.</dd>
</dl>
</li>
</ul>
<a name="hashCode--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>hashCode</h4>
<pre>public&nbsp;int&nbsp;hashCode()</pre>
<div class="block"><p>
 Serves as a hash function for a particular type, suitable for use
 in hashing algorithms and data structures like a hash table.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code>hashCode</code>&nbsp;in class&nbsp;<code>java.lang.Object</code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>23454
 <p><hr>
 Overriden to make compiler happy. Always returns constant because object is mutable.
 </hr></p></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/TransitionType.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/Trendline.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/TransitionValueBase.html" target="_top">Frames</a></li>
<li><a href="TransitionValueBase.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><i>Copyright &copy; 2004-2025 Aspose Pty Ltd. All Rights Reserved.</i></small></p>
</body>
</html>
