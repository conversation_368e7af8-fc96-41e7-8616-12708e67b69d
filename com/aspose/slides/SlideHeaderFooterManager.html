<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_421) on Fri Jul 11 05:32:44 CDT 2025 -->
<title>SlideHeaderFooterManager (Aspose.Slides for Java (25.7) API Documentation)</title>
<meta name="date" content="2025-07-11">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="SlideHeaderFooterManager (Aspose.Slides for Java (25.7) API Documentation)";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/SlideCollection.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/SlideImageFormat.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/SlideHeaderFooterManager.html" target="_top">Frames</a></li>
<li><a href="SlideHeaderFooterManager.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#methods.inherited.from.class.com.aspose.slides.BaseSlideHeaderFooterManager">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li>Method</li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.aspose.slides</div>
<h2 title="Class SlideHeaderFooterManager" class="title">Class SlideHeaderFooterManager</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../com/aspose/slides/BaseHeaderFooterManager.html" title="class in com.aspose.slides">com.aspose.slides.BaseHeaderFooterManager</a></li>
<li>
<ul class="inheritance">
<li><a href="../../../com/aspose/slides/BaseSlideHeaderFooterManager.html" title="class in com.aspose.slides">com.aspose.slides.BaseSlideHeaderFooterManager</a></li>
<li>
<ul class="inheritance">
<li>com.aspose.slides.SlideHeaderFooterManager</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../com/aspose/slides/IBaseHeaderFooterManager.html" title="interface in com.aspose.slides">IBaseHeaderFooterManager</a>, <a href="../../../com/aspose/slides/IBaseSlideHeaderFooterManager.html" title="interface in com.aspose.slides">IBaseSlideHeaderFooterManager</a>, <a href="../../../com/aspose/slides/ISlideHeaderFooterManager.html" title="interface in com.aspose.slides">ISlideHeaderFooterManager</a></dd>
</dl>
<hr>
<br>
<pre>public final class <span class="typeNameLabel">SlideHeaderFooterManager</span>
extends <a href="../../../com/aspose/slides/BaseSlideHeaderFooterManager.html" title="class in com.aspose.slides">BaseSlideHeaderFooterManager</a>
implements <a href="../../../com/aspose/slides/ISlideHeaderFooterManager.html" title="interface in com.aspose.slides">ISlideHeaderFooterManager</a></pre>
<div class="block"><p>
 Represents manager which holds behavior of the slide footer, date-time, page number placeholders.
 </p></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.aspose.slides.BaseSlideHeaderFooterManager">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;com.aspose.slides.<a href="../../../com/aspose/slides/BaseSlideHeaderFooterManager.html" title="class in com.aspose.slides">BaseSlideHeaderFooterManager</a></h3>
<code><a href="../../../com/aspose/slides/BaseSlideHeaderFooterManager.html#isDateTimeVisible--">isDateTimeVisible</a>, <a href="../../../com/aspose/slides/BaseSlideHeaderFooterManager.html#isFooterVisible--">isFooterVisible</a>, <a href="../../../com/aspose/slides/BaseSlideHeaderFooterManager.html#isSlideNumberVisible--">isSlideNumberVisible</a>, <a href="../../../com/aspose/slides/BaseSlideHeaderFooterManager.html#setDateTimeText-java.lang.String-">setDateTimeText</a>, <a href="../../../com/aspose/slides/BaseSlideHeaderFooterManager.html#setDateTimeVisibility-boolean-">setDateTimeVisibility</a>, <a href="../../../com/aspose/slides/BaseSlideHeaderFooterManager.html#setFooterText-java.lang.String-">setFooterText</a>, <a href="../../../com/aspose/slides/BaseSlideHeaderFooterManager.html#setFooterVisibility-boolean-">setFooterVisibility</a>, <a href="../../../com/aspose/slides/BaseSlideHeaderFooterManager.html#setSlideNumberVisibility-boolean-">setSlideNumberVisibility</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.aspose.slides.BaseHeaderFooterManager">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;com.aspose.slides.<a href="../../../com/aspose/slides/BaseHeaderFooterManager.html" title="class in com.aspose.slides">BaseHeaderFooterManager</a></h3>
<code><a href="../../../com/aspose/slides/BaseHeaderFooterManager.html#getParent_Immediate--">getParent_Immediate</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.aspose.slides.IBaseSlideHeaderFooterManager">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;com.aspose.slides.<a href="../../../com/aspose/slides/IBaseSlideHeaderFooterManager.html" title="interface in com.aspose.slides">IBaseSlideHeaderFooterManager</a></h3>
<code><a href="../../../com/aspose/slides/IBaseSlideHeaderFooterManager.html#isDateTimeVisible--">isDateTimeVisible</a>, <a href="../../../com/aspose/slides/IBaseSlideHeaderFooterManager.html#isFooterVisible--">isFooterVisible</a>, <a href="../../../com/aspose/slides/IBaseSlideHeaderFooterManager.html#isSlideNumberVisible--">isSlideNumberVisible</a>, <a href="../../../com/aspose/slides/IBaseSlideHeaderFooterManager.html#setDateTimeText-java.lang.String-">setDateTimeText</a>, <a href="../../../com/aspose/slides/IBaseSlideHeaderFooterManager.html#setDateTimeVisibility-boolean-">setDateTimeVisibility</a>, <a href="../../../com/aspose/slides/IBaseSlideHeaderFooterManager.html#setFooterText-java.lang.String-">setFooterText</a>, <a href="../../../com/aspose/slides/IBaseSlideHeaderFooterManager.html#setFooterVisibility-boolean-">setFooterVisibility</a>, <a href="../../../com/aspose/slides/IBaseSlideHeaderFooterManager.html#setSlideNumberVisibility-boolean-">setSlideNumberVisibility</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/SlideCollection.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/SlideImageFormat.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/SlideHeaderFooterManager.html" target="_top">Frames</a></li>
<li><a href="SlideHeaderFooterManager.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#methods.inherited.from.class.com.aspose.slides.BaseSlideHeaderFooterManager">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li>Method</li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><i>Copyright &copy; 2004-2025 Aspose Pty Ltd. All Rights Reserved.</i></small></p>
</body>
</html>
