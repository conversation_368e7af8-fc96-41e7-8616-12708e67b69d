<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_421) on Fri Jul 11 05:32:45 CDT 2025 -->
<title>Trendline (Aspose.Slides for Java (25.7) API Documentation)</title>
<meta name="date" content="2025-07-11">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Trendline (Aspose.Slides for Java (25.7) API Documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10,"i26":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/TransitionValueBase.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/TrendlineCollection.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/Trendline.html" target="_top">Frames</a></li>
<li><a href="Trendline.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.aspose.slides</div>
<h2 title="Class Trendline" class="title">Class Trendline</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../com/aspose/slides/DomObject.html" title="class in com.aspose.slides">com.aspose.slides.DomObject</a>&lt;<a href="../../../com/aspose/slides/TrendlineCollection.html" title="class in com.aspose.slides">TrendlineCollection</a>&gt;</li>
<li>
<ul class="inheritance">
<li>com.aspose.slides.Trendline</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../com/aspose/slides/IChartComponent.html" title="interface in com.aspose.slides">IChartComponent</a>, <a href="../../../com/aspose/slides/IFormattedTextContainer.html" title="interface in com.aspose.slides">IFormattedTextContainer</a>, <a href="../../../com/aspose/slides/IOverridableText.html" title="interface in com.aspose.slides">IOverridableText</a>, <a href="../../../com/aspose/slides/IPresentationComponent.html" title="interface in com.aspose.slides">IPresentationComponent</a>, <a href="../../../com/aspose/slides/ISlideComponent.html" title="interface in com.aspose.slides">ISlideComponent</a>, <a href="../../../com/aspose/slides/ITrendline.html" title="interface in com.aspose.slides">ITrendline</a></dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">Trendline</span>
extends <a href="../../../com/aspose/slides/DomObject.html" title="class in com.aspose.slides">DomObject</a>&lt;<a href="../../../com/aspose/slides/TrendlineCollection.html" title="class in com.aspose.slides">TrendlineCollection</a>&gt;
implements <a href="../../../com/aspose/slides/ITrendline.html" title="interface in com.aspose.slides">ITrendline</a></pre>
<div class="block"><p>
 Class represents trend line of chart series
 </p></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/ITextFrame.html" title="interface in com.aspose.slides">ITextFrame</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Trendline.html#addTextFrameForOverriding-java.lang.String-">addTextFrameForOverriding</a></span>(java.lang.String&nbsp;text)</code>
<div class="block">
 Initialize TextFrameForOverriding with the text in paramener "text".</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Trendline.html#getBackward--">getBackward</a></span>()</code>
<div class="block">
 Specifies the number of categories (or units on a scatter chart) that the trend line extends before
 the data for the series that is being trended.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IChart.html" title="interface in com.aspose.slides">IChart</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Trendline.html#getChart--">getChart</a></span>()</code>
<div class="block">
 Returns the parent chart.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Trendline.html#getDisplayEquation--">getDisplayEquation</a></span>()</code>
<div class="block">
 Specifies that the equation for the trendline is displayed on the chart (in the same label as the Rsquaredvalue).</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Trendline.html#getDisplayRSquaredValue--">getDisplayRSquaredValue</a></span>()</code>
<div class="block">
 Specifies that the R-squared value of the trendline is displayed on the chart (in the same label as the equation).</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IFormat.html" title="interface in com.aspose.slides">IFormat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Trendline.html#getFormat--">getFormat</a></span>()</code>
<div class="block">
 Represents the format of the trend line.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Trendline.html#getForward--">getForward</a></span>()</code>
<div class="block">
 Specifies the number of categories (or units on a scatter chart) that the trendline extends after the
 data for the series that is being trended.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Trendline.html#getIntercept--">getIntercept</a></span>()</code>
<div class="block">
 Specifies the value where the trendline shall cross the y axis.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>byte</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Trendline.html#getOrder--">getOrder</a></span>()</code>
<div class="block">
 Specifies the order of the polynomial trend line.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>byte</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Trendline.html#getPeriod--">getPeriod</a></span>()</code>
<div class="block">
 Specifies the period of the trend line for a moving average trend line.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IPresentation.html" title="interface in com.aspose.slides">IPresentation</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Trendline.html#getPresentation--">getPresentation</a></span>()</code>
<div class="block">
 Returns the parent presentation of a FillFormat.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/ILegendEntryProperties.html" title="interface in com.aspose.slides">ILegendEntryProperties</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Trendline.html#getRelatedLegendEntry--">getRelatedLegendEntry</a></span>()</code>
<div class="block">
 Represents legend entry related with this trendline
 Read-only <a href="../../../com/aspose/slides/ILegendEntryProperties.html" title="interface in com.aspose.slides"><code>ILegendEntryProperties</code></a>.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IBaseSlide.html" title="interface in com.aspose.slides">IBaseSlide</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Trendline.html#getSlide--">getSlide</a></span>()</code>
<div class="block">
 Returns the parent slide of a FillFormat.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IChartTextFormat.html" title="interface in com.aspose.slides">IChartTextFormat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Trendline.html#getTextFormat--">getTextFormat</a></span>()</code>
<div class="block">
 Returns text format.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/ITextFrame.html" title="interface in com.aspose.slides">ITextFrame</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Trendline.html#getTextFrameForOverriding--">getTextFrameForOverriding</a></span>()</code>
<div class="block">
 Can contain a rich formatted text.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Trendline.html#getTrendlineName--">getTrendlineName</a></span>()</code>
<div class="block">
 Gets or sets  name of the trendline.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Trendline.html#getTrendlineType--">getTrendlineType</a></span>()</code>
<div class="block">
 Gets or sets type of trend line.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Trendline.html#setBackward-double-">setBackward</a></span>(double&nbsp;value)</code>
<div class="block">
 Specifies the number of categories (or units on a scatter chart) that the trend line extends before
 the data for the series that is being trended.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Trendline.html#setDisplayEquation-boolean-">setDisplayEquation</a></span>(boolean&nbsp;value)</code>
<div class="block">
 Specifies that the equation for the trendline is displayed on the chart (in the same label as the Rsquaredvalue).</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Trendline.html#setDisplayRSquaredValue-boolean-">setDisplayRSquaredValue</a></span>(boolean&nbsp;value)</code>
<div class="block">
 Specifies that the R-squared value of the trendline is displayed on the chart (in the same label as the equation).</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Trendline.html#setFormat-com.aspose.slides.IFormat-">setFormat</a></span>(<a href="../../../com/aspose/slides/IFormat.html" title="interface in com.aspose.slides">IFormat</a>&nbsp;value)</code>
<div class="block">
 Represents the format of the trend line.</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Trendline.html#setForward-double-">setForward</a></span>(double&nbsp;value)</code>
<div class="block">
 Specifies the number of categories (or units on a scatter chart) that the trendline extends after the
 data for the series that is being trended.</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Trendline.html#setIntercept-double-">setIntercept</a></span>(double&nbsp;value)</code>
<div class="block">
 Specifies the value where the trendline shall cross the y axis.</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Trendline.html#setOrder-byte-">setOrder</a></span>(byte&nbsp;value)</code>
<div class="block">
 Specifies the order of the polynomial trend line.</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Trendline.html#setPeriod-byte-">setPeriod</a></span>(byte&nbsp;value)</code>
<div class="block">
 Specifies the period of the trend line for a moving average trend line.</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Trendline.html#setTrendlineName-java.lang.String-">setTrendlineName</a></span>(java.lang.String&nbsp;value)</code>
<div class="block">
 Gets or sets  name of the trendline.</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Trendline.html#setTrendlineType-int-">setTrendlineType</a></span>(int&nbsp;value)</code>
<div class="block">
 Gets or sets type of trend line.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.aspose.slides.DomObject">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;com.aspose.slides.<a href="../../../com/aspose/slides/DomObject.html" title="class in com.aspose.slides">DomObject</a></h3>
<code><a href="../../../com/aspose/slides/DomObject.html#getParent_Immediate--">getParent_Immediate</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getTrendlineName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTrendlineName</h4>
<pre>public final&nbsp;java.lang.String&nbsp;getTrendlineName()</pre>
<div class="block"><p>
 Gets or sets  name of the trendline.
 Read/write <code>String</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITrendline.html#getTrendlineName--">getTrendlineName</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITrendline.html" title="interface in com.aspose.slides">ITrendline</a></code></dd>
</dl>
</li>
</ul>
<a name="setTrendlineName-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTrendlineName</h4>
<pre>public final&nbsp;void&nbsp;setTrendlineName(java.lang.String&nbsp;value)</pre>
<div class="block"><p>
 Gets or sets  name of the trendline.
 Read/write <code>String</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITrendline.html#setTrendlineName-java.lang.String-">setTrendlineName</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITrendline.html" title="interface in com.aspose.slides">ITrendline</a></code></dd>
</dl>
</li>
</ul>
<a name="getTrendlineType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTrendlineType</h4>
<pre>public final&nbsp;int&nbsp;getTrendlineType()</pre>
<div class="block"><p>
 Gets or sets type of trend line.
 Read/write <a href="../../../com/aspose/slides/TrendlineType.html" title="class in com.aspose.slides"><code>TrendlineType</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITrendline.html#getTrendlineType--">getTrendlineType</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITrendline.html" title="interface in com.aspose.slides">ITrendline</a></code></dd>
</dl>
</li>
</ul>
<a name="setTrendlineType-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTrendlineType</h4>
<pre>public final&nbsp;void&nbsp;setTrendlineType(int&nbsp;value)</pre>
<div class="block"><p>
 Gets or sets type of trend line.
 Read/write <a href="../../../com/aspose/slides/TrendlineType.html" title="class in com.aspose.slides"><code>TrendlineType</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITrendline.html#setTrendlineType-int-">setTrendlineType</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITrendline.html" title="interface in com.aspose.slides">ITrendline</a></code></dd>
</dl>
</li>
</ul>
<a name="getFormat--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFormat</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IFormat.html" title="interface in com.aspose.slides">IFormat</a>&nbsp;getFormat()</pre>
<div class="block"><p>
 Represents the format of the trend line.
 Read/write <a href="../../../com/aspose/slides/IFormat.html" title="interface in com.aspose.slides"><code>IFormat</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITrendline.html#getFormat--">getFormat</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITrendline.html" title="interface in com.aspose.slides">ITrendline</a></code></dd>
</dl>
</li>
</ul>
<a name="setFormat-com.aspose.slides.IFormat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFormat</h4>
<pre>public final&nbsp;void&nbsp;setFormat(<a href="../../../com/aspose/slides/IFormat.html" title="interface in com.aspose.slides">IFormat</a>&nbsp;value)</pre>
<div class="block"><p>
 Represents the format of the trend line.
 Read/write <a href="../../../com/aspose/slides/IFormat.html" title="interface in com.aspose.slides"><code>IFormat</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITrendline.html#setFormat-com.aspose.slides.IFormat-">setFormat</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITrendline.html" title="interface in com.aspose.slides">ITrendline</a></code></dd>
</dl>
</li>
</ul>
<a name="getBackward--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBackward</h4>
<pre>public final&nbsp;double&nbsp;getBackward()</pre>
<div class="block"><p>
 Specifies the number of categories (or units on a scatter chart) that the trend line extends before
 the data for the series that is being trended. On scatter and non-scatter charts, the value shall be any nonnegative
 value.
 Read/write <code>double</code>.
  </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITrendline.html#getBackward--">getBackward</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITrendline.html" title="interface in com.aspose.slides">ITrendline</a></code></dd>
</dl>
</li>
</ul>
<a name="setBackward-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBackward</h4>
<pre>public final&nbsp;void&nbsp;setBackward(double&nbsp;value)</pre>
<div class="block"><p>
 Specifies the number of categories (or units on a scatter chart) that the trend line extends before
 the data for the series that is being trended. On scatter and non-scatter charts, the value shall be any nonnegative
 value.
 Read/write <code>double</code>.
  </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITrendline.html#setBackward-double-">setBackward</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITrendline.html" title="interface in com.aspose.slides">ITrendline</a></code></dd>
</dl>
</li>
</ul>
<a name="getForward--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getForward</h4>
<pre>public final&nbsp;double&nbsp;getForward()</pre>
<div class="block"><p>
 Specifies the number of categories (or units on a scatter chart) that the trendline extends after the
 data for the series that is being trended. On scatter and non-scatter charts, the value shall be any non-negative
 value.
 Read/write <code>double</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITrendline.html#getForward--">getForward</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITrendline.html" title="interface in com.aspose.slides">ITrendline</a></code></dd>
</dl>
</li>
</ul>
<a name="setForward-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setForward</h4>
<pre>public final&nbsp;void&nbsp;setForward(double&nbsp;value)</pre>
<div class="block"><p>
 Specifies the number of categories (or units on a scatter chart) that the trendline extends after the
 data for the series that is being trended. On scatter and non-scatter charts, the value shall be any non-negative
 value.
 Read/write <code>double</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITrendline.html#setForward-double-">setForward</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITrendline.html" title="interface in com.aspose.slides">ITrendline</a></code></dd>
</dl>
</li>
</ul>
<a name="getIntercept--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getIntercept</h4>
<pre>public final&nbsp;double&nbsp;getIntercept()</pre>
<div class="block"><p>
 Specifies the value where the trendline shall cross the y axis. This property shall be supported only
 when the trendline type is exp, linear, or poly.
 Read/write <code>double</code>.
  </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITrendline.html#getIntercept--">getIntercept</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITrendline.html" title="interface in com.aspose.slides">ITrendline</a></code></dd>
</dl>
</li>
</ul>
<a name="setIntercept-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setIntercept</h4>
<pre>public final&nbsp;void&nbsp;setIntercept(double&nbsp;value)</pre>
<div class="block"><p>
 Specifies the value where the trendline shall cross the y axis. This property shall be supported only
 when the trendline type is exp, linear, or poly.
 Read/write <code>double</code>.
  </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITrendline.html#setIntercept-double-">setIntercept</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITrendline.html" title="interface in com.aspose.slides">ITrendline</a></code></dd>
</dl>
</li>
</ul>
<a name="getDisplayEquation--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDisplayEquation</h4>
<pre>public final&nbsp;boolean&nbsp;getDisplayEquation()</pre>
<div class="block"><p>
 Specifies that the equation for the trendline is displayed on the chart (in the same label as the Rsquaredvalue).
 Read/write <code>boolean</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITrendline.html#getDisplayEquation--">getDisplayEquation</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITrendline.html" title="interface in com.aspose.slides">ITrendline</a></code></dd>
</dl>
</li>
</ul>
<a name="setDisplayEquation-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDisplayEquation</h4>
<pre>public final&nbsp;void&nbsp;setDisplayEquation(boolean&nbsp;value)</pre>
<div class="block"><p>
 Specifies that the equation for the trendline is displayed on the chart (in the same label as the Rsquaredvalue).
 Read/write <code>boolean</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITrendline.html#setDisplayEquation-boolean-">setDisplayEquation</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITrendline.html" title="interface in com.aspose.slides">ITrendline</a></code></dd>
</dl>
</li>
</ul>
<a name="getOrder--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getOrder</h4>
<pre>public final&nbsp;byte&nbsp;getOrder()</pre>
<div class="block"><p>
 Specifies the order of the polynomial trend line. It is ignored for other trend line types. Value must be between 2 and 6.
 Read/write <code>byte</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITrendline.html#getOrder--">getOrder</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITrendline.html" title="interface in com.aspose.slides">ITrendline</a></code></dd>
</dl>
</li>
</ul>
<a name="setOrder-byte-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setOrder</h4>
<pre>public final&nbsp;void&nbsp;setOrder(byte&nbsp;value)</pre>
<div class="block"><p>
 Specifies the order of the polynomial trend line. It is ignored for other trend line types. Value must be between 2 and 6.
 Read/write <code>byte</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITrendline.html#setOrder-byte-">setOrder</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITrendline.html" title="interface in com.aspose.slides">ITrendline</a></code></dd>
</dl>
</li>
</ul>
<a name="getPeriod--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPeriod</h4>
<pre>public final&nbsp;byte&nbsp;getPeriod()</pre>
<div class="block"><p>
 Specifies the period of the trend line for a moving average trend line. It is ignored for other trend
 line variants. Value must be between 2 and 255.
 Read/write <code>byte</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITrendline.html#getPeriod--">getPeriod</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITrendline.html" title="interface in com.aspose.slides">ITrendline</a></code></dd>
</dl>
</li>
</ul>
<a name="setPeriod-byte-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPeriod</h4>
<pre>public final&nbsp;void&nbsp;setPeriod(byte&nbsp;value)</pre>
<div class="block"><p>
 Specifies the period of the trend line for a moving average trend line. It is ignored for other trend
 line variants. Value must be between 2 and 255.
 Read/write <code>byte</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITrendline.html#setPeriod-byte-">setPeriod</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITrendline.html" title="interface in com.aspose.slides">ITrendline</a></code></dd>
</dl>
</li>
</ul>
<a name="getDisplayRSquaredValue--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDisplayRSquaredValue</h4>
<pre>public final&nbsp;boolean&nbsp;getDisplayRSquaredValue()</pre>
<div class="block"><p>
 Specifies that the R-squared value of the trendline is displayed on the chart (in the same label as the equation).
 Read/write <code>boolean</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITrendline.html#getDisplayRSquaredValue--">getDisplayRSquaredValue</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITrendline.html" title="interface in com.aspose.slides">ITrendline</a></code></dd>
</dl>
</li>
</ul>
<a name="setDisplayRSquaredValue-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDisplayRSquaredValue</h4>
<pre>public final&nbsp;void&nbsp;setDisplayRSquaredValue(boolean&nbsp;value)</pre>
<div class="block"><p>
 Specifies that the R-squared value of the trendline is displayed on the chart (in the same label as the equation).
 Read/write <code>boolean</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITrendline.html#setDisplayRSquaredValue-boolean-">setDisplayRSquaredValue</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITrendline.html" title="interface in com.aspose.slides">ITrendline</a></code></dd>
</dl>
</li>
</ul>
<a name="getRelatedLegendEntry--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRelatedLegendEntry</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/ILegendEntryProperties.html" title="interface in com.aspose.slides">ILegendEntryProperties</a>&nbsp;getRelatedLegendEntry()</pre>
<div class="block"><p>
 Represents legend entry related with this trendline
 Read-only <a href="../../../com/aspose/slides/ILegendEntryProperties.html" title="interface in com.aspose.slides"><code>ILegendEntryProperties</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITrendline.html#getRelatedLegendEntry--">getRelatedLegendEntry</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITrendline.html" title="interface in com.aspose.slides">ITrendline</a></code></dd>
</dl>
</li>
</ul>
<a name="addTextFrameForOverriding-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addTextFrameForOverriding</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/ITextFrame.html" title="interface in com.aspose.slides">ITextFrame</a>&nbsp;addTextFrameForOverriding(java.lang.String&nbsp;text)</pre>
<div class="block"><p>
 Initialize TextFrameForOverriding with the text in paramener "text".
 If TextFrameForOverriding is already initialized then simply changes its text.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IOverridableText.html#addTextFrameForOverriding-java.lang.String-">addTextFrameForOverriding</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IOverridableText.html" title="interface in com.aspose.slides">IOverridableText</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>text</code> - Text for a new TextFrameForOverriding.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Text frame <a href="../../../com/aspose/slides/ITextFrame.html" title="interface in com.aspose.slides"><code>ITextFrame</code></a></dd>
</dl>
</li>
</ul>
<a name="getTextFrameForOverriding--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTextFrameForOverriding</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/ITextFrame.html" title="interface in com.aspose.slides">ITextFrame</a>&nbsp;getTextFrameForOverriding()</pre>
<div class="block"><p>
 Can contain a rich formatted text. If this property is not null then this 
 formatted text value overrides auto-generated text of data label.
 Auto-generated text of data label means text that is managed by ShowSeriesName, 
 ShowValue, ... properties and is formatted with the TextFormatManager.TextFormat property.
 Read-only <a href="../../../com/aspose/slides/ITextFrame.html" title="interface in com.aspose.slides"><code>ITextFrame</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IOverridableText.html#getTextFrameForOverriding--">getTextFrameForOverriding</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IOverridableText.html" title="interface in com.aspose.slides">IOverridableText</a></code></dd>
</dl>
</li>
</ul>
<a name="getTextFormat--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTextFormat</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IChartTextFormat.html" title="interface in com.aspose.slides">IChartTextFormat</a>&nbsp;getTextFormat()</pre>
<div class="block"><p>
 Returns text format.
 Read-only <a href="../../../com/aspose/slides/IChartTextFormat.html" title="interface in com.aspose.slides"><code>IChartTextFormat</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IFormattedTextContainer.html#getTextFormat--">getTextFormat</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IFormattedTextContainer.html" title="interface in com.aspose.slides">IFormattedTextContainer</a></code></dd>
</dl>
</li>
</ul>
<a name="getChart--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getChart</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IChart.html" title="interface in com.aspose.slides">IChart</a>&nbsp;getChart()</pre>
<div class="block"><p>
 Returns the parent chart.
 Read-only <a href="../../../com/aspose/slides/IChart.html" title="interface in com.aspose.slides"><code>IChart</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IChartComponent.html#getChart--">getChart</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IChartComponent.html" title="interface in com.aspose.slides">IChartComponent</a></code></dd>
</dl>
</li>
</ul>
<a name="getSlide--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSlide</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IBaseSlide.html" title="interface in com.aspose.slides">IBaseSlide</a>&nbsp;getSlide()</pre>
<div class="block"><p>
 Returns the parent slide of a FillFormat.
 Read-only <a href="../../../com/aspose/slides/BaseSlide.html" title="class in com.aspose.slides"><code>BaseSlide</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISlideComponent.html#getSlide--">getSlide</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISlideComponent.html" title="interface in com.aspose.slides">ISlideComponent</a></code></dd>
</dl>
</li>
</ul>
<a name="getPresentation--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getPresentation</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IPresentation.html" title="interface in com.aspose.slides">IPresentation</a>&nbsp;getPresentation()</pre>
<div class="block"><p>
 Returns the parent presentation of a FillFormat.
 Read-only <a href="../../../com/aspose/slides/IPresentation.html" title="interface in com.aspose.slides"><code>IPresentation</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPresentationComponent.html#getPresentation--">getPresentation</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPresentationComponent.html" title="interface in com.aspose.slides">IPresentationComponent</a></code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/TransitionValueBase.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/TrendlineCollection.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/Trendline.html" target="_top">Frames</a></li>
<li><a href="Trendline.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><i>Copyright &copy; 2004-2025 Aspose Pty Ltd. All Rights Reserved.</i></small></p>
</body>
</html>
