<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_421) on Fri Jul 11 05:32:44 CDT 2025 -->
<title>TiffOptions (Aspose.Slides for Java (25.7) API Documentation)</title>
<meta name="date" content="2025-07-11">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="TiffOptions (Aspose.Slides for Java (25.7) API Documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/TiffCompressionTypes.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/TileFlip.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/TiffOptions.html" target="_top">Frames</a></li>
<li><a href="TiffOptions.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.aspose.slides</div>
<h2 title="Class TiffOptions" class="title">Class TiffOptions</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../com/aspose/slides/SaveOptions.html" title="class in com.aspose.slides">com.aspose.slides.SaveOptions</a></li>
<li>
<ul class="inheritance">
<li>com.aspose.slides.TiffOptions</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../com/aspose/slides/ISaveOptions.html" title="interface in com.aspose.slides">ISaveOptions</a>, <a href="../../../com/aspose/slides/ITiffOptions.html" title="interface in com.aspose.slides">ITiffOptions</a></dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">TiffOptions</span>
extends <a href="../../../com/aspose/slides/SaveOptions.html" title="class in com.aspose.slides">SaveOptions</a>
implements <a href="../../../com/aspose/slides/ITiffOptions.html" title="interface in com.aspose.slides">ITiffOptions</a></pre>
<div class="block"><p>
 Provides options that control how a presentation is saved in TIFF format.
 </p><p><hr><blockquote><pre>
 The following example shows how to convert PowerPoint to TIFF with default size.
 <pre>
 // Instantiate a Presentation object that represents a presentation file
 Presentation pres = new Presentation("DemoFile.pptx");
 try {
     // Saving the presentation to TIFF document
     pres.save("Tiffoutput_out.tiff", SaveFormat.Tiff);
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 The following example shows how to convert PowerPoint to TIFF with custom size.
 <pre>
 // Instantiate a Presentation object that represents a Presentation file
 Presentation pres = new Presentation("Convert_Tiff_Custom.pptx");
 try {
     // Instantiate the TiffOptions class
     TiffOptions opts = new TiffOptions();
     // Setting compression type
     opts.setCompressionType(TiffCompressionTypes.Default);
     NotesCommentsLayoutingOptions notesOptions = new NotesCommentsLayoutingOptions();
     notesOptions.setNotesPosition(NotesPositions.BottomFull);
     opts.setSlidesLayoutOptions(notesOptions);
     // Compression Types
     // Default - Specifies the default compression scheme (LZW).
     // None - Specifies no compression.
     // CCITT3
     // CCITT4
     // LZW
     // RLE
     // Depth depends on the compression type and cannot be set manually.
     // Resolution unit  is always equal to 2 (dots per inch)
     // Setting image DPI
     opts.setDpiX(200);
     opts.setDpiY(100);
     // Set Image Size
     opts.setImageSize(new Dimension(1728, 1078));
     // Save the presentation to TIFF with specified image size
     pres.save("TiffWithCustomSize_out.tiff", SaveFormat.Tiff, opts);
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 The following example shows how to convert PowerPoint to TIFF with custom image pixel format.
 <pre>
 // Instantiate a Presentation object that represents a Presentation file
 Presentation pres = new Presentation("DemoFile.pptx");
 try {
     TiffOptions options = new TiffOptions();
     options.setPixelFormat(ImagePixelFormat.Format8bppIndexed);

     //ImagePixelFormat contains the following values (as could be seen from documentation):
     //Format1bppIndexed; // 1 bits per pixel, indexed.
     //Format4bppIndexed; // 4 bits per pixel, indexed.
     //Format8bppIndexed; // 8 bits per pixel, indexed.
     //Format24bppRgb; // 24 bits per pixel, RGB.
     //Format32bppArgb; // 32 bits per pixel, ARGB.

     // Save the presentation to TIFF with specified image size
     pres.save("Tiff_With_Custom_Image_Pixel_Format_out.tiff", SaveFormat.Tiff, options);
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TiffOptions.html#TiffOptions--">TiffOptions</a></span>()</code>
<div class="block">
 Default constructor.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TiffOptions.html#getBwConversionMode--">getBwConversionMode</a></span>()</code>
<div class="block">
 Specifies the algorithm for converting a color image into a black and white image.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TiffOptions.html#getCompressionType--">getCompressionType</a></span>()</code>
<div class="block">
 Specifies the compression type.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TiffOptions.html#getDpiX--">getDpiX</a></span>()</code>
<div class="block">
 Specifies the horizontal resolution in dots per inch.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TiffOptions.html#getDpiY--">getDpiY</a></span>()</code>
<div class="block">
 Specifies the vertical resolution in dots per inch.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>java.awt.Dimension</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TiffOptions.html#getImageSize--">getImageSize</a></span>()</code>
<div class="block">
 Specifies size of a generated TIFF image.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IInkOptions.html" title="interface in com.aspose.slides">IInkOptions</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TiffOptions.html#getInkOptions--">getInkOptions</a></span>()</code>
<div class="block">
 Provides options that control the look of Ink objects in exported document.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TiffOptions.html#getPixelFormat--">getPixelFormat</a></span>()</code>
<div class="block">
 Specifies the pixel format for the generated images.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TiffOptions.html#getShowHiddenSlides--">getShowHiddenSlides</a></span>()</code>
<div class="block">
 Specifies whether the generated document should include hidden slides or not.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/ISlidesLayoutOptions.html" title="interface in com.aspose.slides">ISlidesLayoutOptions</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TiffOptions.html#getSlidesLayoutOptions--">getSlidesLayoutOptions</a></span>()</code>
<div class="block">
 Gets or sets the mode in which slides are placed on the page when exporting a presentation <a href="../../../com/aspose/slides/ISlidesLayoutOptions.html" title="interface in com.aspose.slides"><code>ISlidesLayoutOptions</code></a>.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TiffOptions.html#setBwConversionMode-int-">setBwConversionMode</a></span>(int&nbsp;value)</code>
<div class="block">
 Specifies the algorithm for converting a color image into a black and white image.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TiffOptions.html#setCompressionType-int-">setCompressionType</a></span>(int&nbsp;value)</code>
<div class="block">
 Specifies the compression type.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TiffOptions.html#setDpiX-long-">setDpiX</a></span>(long&nbsp;value)</code>
<div class="block">
 Specifies the horizontal resolution in dots per inch.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TiffOptions.html#setDpiY-long-">setDpiY</a></span>(long&nbsp;value)</code>
<div class="block">
 Specifies the vertical resolution in dots per inch.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TiffOptions.html#setImageSize-java.awt.Dimension-">setImageSize</a></span>(java.awt.Dimension&nbsp;value)</code>
<div class="block">
 Specifies size of a generated TIFF image.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TiffOptions.html#setPixelFormat-int-">setPixelFormat</a></span>(int&nbsp;value)</code>
<div class="block">
 Specifies the pixel format for the generated images.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TiffOptions.html#setShowHiddenSlides-boolean-">setShowHiddenSlides</a></span>(boolean&nbsp;value)</code>
<div class="block">
 Specifies whether the generated document should include hidden slides or not.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TiffOptions.html#setSlidesLayoutOptions-com.aspose.slides.ISlidesLayoutOptions-">setSlidesLayoutOptions</a></span>(<a href="../../../com/aspose/slides/ISlidesLayoutOptions.html" title="interface in com.aspose.slides">ISlidesLayoutOptions</a>&nbsp;value)</code>
<div class="block">
 Gets or sets the mode in which slides are placed on the page when exporting a presentation <a href="../../../com/aspose/slides/ISlidesLayoutOptions.html" title="interface in com.aspose.slides"><code>ISlidesLayoutOptions</code></a>.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.aspose.slides.SaveOptions">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;com.aspose.slides.<a href="../../../com/aspose/slides/SaveOptions.html" title="class in com.aspose.slides">SaveOptions</a></h3>
<code><a href="../../../com/aspose/slides/SaveOptions.html#getDefaultRegularFont--">getDefaultRegularFont</a>, <a href="../../../com/aspose/slides/SaveOptions.html#getGradientStyle--">getGradientStyle</a>, <a href="../../../com/aspose/slides/SaveOptions.html#getProgressCallback--">getProgressCallback</a>, <a href="../../../com/aspose/slides/SaveOptions.html#getSkipJavaScriptLinks--">getSkipJavaScriptLinks</a>, <a href="../../../com/aspose/slides/SaveOptions.html#getWarningCallback--">getWarningCallback</a>, <a href="../../../com/aspose/slides/SaveOptions.html#setDefaultRegularFont-java.lang.String-">setDefaultRegularFont</a>, <a href="../../../com/aspose/slides/SaveOptions.html#setGradientStyle-int-">setGradientStyle</a>, <a href="../../../com/aspose/slides/SaveOptions.html#setProgressCallback-com.aspose.slides.IProgressCallback-">setProgressCallback</a>, <a href="../../../com/aspose/slides/SaveOptions.html#setSkipJavaScriptLinks-boolean-">setSkipJavaScriptLinks</a>, <a href="../../../com/aspose/slides/SaveOptions.html#setWarningCallback-com.aspose.slides.IWarningCallback-">setWarningCallback</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.aspose.slides.ISaveOptions">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;com.aspose.slides.<a href="../../../com/aspose/slides/ISaveOptions.html" title="interface in com.aspose.slides">ISaveOptions</a></h3>
<code><a href="../../../com/aspose/slides/ISaveOptions.html#getDefaultRegularFont--">getDefaultRegularFont</a>, <a href="../../../com/aspose/slides/ISaveOptions.html#getGradientStyle--">getGradientStyle</a>, <a href="../../../com/aspose/slides/ISaveOptions.html#getProgressCallback--">getProgressCallback</a>, <a href="../../../com/aspose/slides/ISaveOptions.html#getSkipJavaScriptLinks--">getSkipJavaScriptLinks</a>, <a href="../../../com/aspose/slides/ISaveOptions.html#getWarningCallback--">getWarningCallback</a>, <a href="../../../com/aspose/slides/ISaveOptions.html#setDefaultRegularFont-java.lang.String-">setDefaultRegularFont</a>, <a href="../../../com/aspose/slides/ISaveOptions.html#setGradientStyle-int-">setGradientStyle</a>, <a href="../../../com/aspose/slides/ISaveOptions.html#setProgressCallback-com.aspose.slides.IProgressCallback-">setProgressCallback</a>, <a href="../../../com/aspose/slides/ISaveOptions.html#setSkipJavaScriptLinks-boolean-">setSkipJavaScriptLinks</a>, <a href="../../../com/aspose/slides/ISaveOptions.html#setWarningCallback-com.aspose.slides.IWarningCallback-">setWarningCallback</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="TiffOptions--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>TiffOptions</h4>
<pre>public&nbsp;TiffOptions()</pre>
<div class="block"><p>
 Default constructor.
 </p></div>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getInkOptions--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getInkOptions</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IInkOptions.html" title="interface in com.aspose.slides">IInkOptions</a>&nbsp;getInkOptions()</pre>
<div class="block"><p>
 Provides options that control the look of Ink objects in exported document.
 Read-only <a href="../../../com/aspose/slides/IInkOptions.html" title="interface in com.aspose.slides"><code>IInkOptions</code></a>
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITiffOptions.html#getInkOptions--">getInkOptions</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITiffOptions.html" title="interface in com.aspose.slides">ITiffOptions</a></code></dd>
</dl>
</li>
</ul>
<a name="getShowHiddenSlides--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getShowHiddenSlides</h4>
<pre>public final&nbsp;boolean&nbsp;getShowHiddenSlides()</pre>
<div class="block"><p>
 Specifies whether the generated document should include hidden slides or not.
 Default is false.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITiffOptions.html#getShowHiddenSlides--">getShowHiddenSlides</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITiffOptions.html" title="interface in com.aspose.slides">ITiffOptions</a></code></dd>
</dl>
</li>
</ul>
<a name="setShowHiddenSlides-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setShowHiddenSlides</h4>
<pre>public final&nbsp;void&nbsp;setShowHiddenSlides(boolean&nbsp;value)</pre>
<div class="block"><p>
 Specifies whether the generated document should include hidden slides or not.
 Default is false.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITiffOptions.html#setShowHiddenSlides-boolean-">setShowHiddenSlides</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITiffOptions.html" title="interface in com.aspose.slides">ITiffOptions</a></code></dd>
</dl>
</li>
</ul>
<a name="getImageSize--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getImageSize</h4>
<pre>public final&nbsp;java.awt.Dimension&nbsp;getImageSize()</pre>
<div class="block"><p>
 Specifies size of a generated TIFF image.
 Default value is 0x0, what means that generated image sizes will be calculated based on presentation slide size value.
 Read/write <code>Dimension</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITiffOptions.html#getImageSize--">getImageSize</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITiffOptions.html" title="interface in com.aspose.slides">ITiffOptions</a></code></dd>
</dl>
</li>
</ul>
<a name="setImageSize-java.awt.Dimension-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setImageSize</h4>
<pre>public final&nbsp;void&nbsp;setImageSize(java.awt.Dimension&nbsp;value)</pre>
<div class="block"><p>
 Specifies size of a generated TIFF image.
 Default value is 0x0, what means that generated image sizes will be calculated based on presentation slide size value.
 Read/write <code>Dimension</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITiffOptions.html#setImageSize-java.awt.Dimension-">setImageSize</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITiffOptions.html" title="interface in com.aspose.slides">ITiffOptions</a></code></dd>
</dl>
</li>
</ul>
<a name="getDpiX--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDpiX</h4>
<pre>public final&nbsp;long&nbsp;getDpiX()</pre>
<div class="block"><p>
 Specifies the horizontal resolution in dots per inch.
 Read/write long.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITiffOptions.html#getDpiX--">getDpiX</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITiffOptions.html" title="interface in com.aspose.slides">ITiffOptions</a></code></dd>
</dl>
</li>
</ul>
<a name="setDpiX-long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDpiX</h4>
<pre>public final&nbsp;void&nbsp;setDpiX(long&nbsp;value)</pre>
<div class="block"><p>
 Specifies the horizontal resolution in dots per inch.
 Read/write long.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITiffOptions.html#setDpiX-long-">setDpiX</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITiffOptions.html" title="interface in com.aspose.slides">ITiffOptions</a></code></dd>
</dl>
</li>
</ul>
<a name="getDpiY--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDpiY</h4>
<pre>public final&nbsp;long&nbsp;getDpiY()</pre>
<div class="block"><p>
 Specifies the vertical resolution in dots per inch.
 Read/write long.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITiffOptions.html#getDpiY--">getDpiY</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITiffOptions.html" title="interface in com.aspose.slides">ITiffOptions</a></code></dd>
</dl>
</li>
</ul>
<a name="setDpiY-long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDpiY</h4>
<pre>public final&nbsp;void&nbsp;setDpiY(long&nbsp;value)</pre>
<div class="block"><p>
 Specifies the vertical resolution in dots per inch.
 Read/write long.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITiffOptions.html#setDpiY-long-">setDpiY</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITiffOptions.html" title="interface in com.aspose.slides">ITiffOptions</a></code></dd>
</dl>
</li>
</ul>
<a name="getCompressionType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCompressionType</h4>
<pre>public final&nbsp;int&nbsp;getCompressionType()</pre>
<div class="block"><p>
 Specifies the compression type.
 Read/write <a href="../../../com/aspose/slides/TiffCompressionTypes.html" title="class in com.aspose.slides"><code>TiffCompressionTypes</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITiffOptions.html#getCompressionType--">getCompressionType</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITiffOptions.html" title="interface in com.aspose.slides">ITiffOptions</a></code></dd>
</dl>
</li>
</ul>
<a name="setCompressionType-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCompressionType</h4>
<pre>public final&nbsp;void&nbsp;setCompressionType(int&nbsp;value)</pre>
<div class="block"><p>
 Specifies the compression type.
 Read/write <a href="../../../com/aspose/slides/TiffCompressionTypes.html" title="class in com.aspose.slides"><code>TiffCompressionTypes</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITiffOptions.html#setCompressionType-int-">setCompressionType</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITiffOptions.html" title="interface in com.aspose.slides">ITiffOptions</a></code></dd>
</dl>
</li>
</ul>
<a name="getPixelFormat--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPixelFormat</h4>
<pre>public final&nbsp;int&nbsp;getPixelFormat()</pre>
<div class="block"><p>
 Specifies the pixel format for the generated images.
 Read/write <a href="../../../com/aspose/slides/ImagePixelFormat.html" title="class in com.aspose.slides"><code>ImagePixelFormat</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITiffOptions.html#getPixelFormat--">getPixelFormat</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITiffOptions.html" title="interface in com.aspose.slides">ITiffOptions</a></code></dd>
</dl>
</li>
</ul>
<a name="setPixelFormat-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPixelFormat</h4>
<pre>public final&nbsp;void&nbsp;setPixelFormat(int&nbsp;value)</pre>
<div class="block"><p>
 Specifies the pixel format for the generated images.
 Read/write <a href="../../../com/aspose/slides/ImagePixelFormat.html" title="class in com.aspose.slides"><code>ImagePixelFormat</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITiffOptions.html#setPixelFormat-int-">setPixelFormat</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITiffOptions.html" title="interface in com.aspose.slides">ITiffOptions</a></code></dd>
</dl>
</li>
</ul>
<a name="getSlidesLayoutOptions--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSlidesLayoutOptions</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/ISlidesLayoutOptions.html" title="interface in com.aspose.slides">ISlidesLayoutOptions</a>&nbsp;getSlidesLayoutOptions()</pre>
<div class="block"><p>
 Gets or sets the mode in which slides are placed on the page when exporting a presentation <a href="../../../com/aspose/slides/ISlidesLayoutOptions.html" title="interface in com.aspose.slides"><code>ISlidesLayoutOptions</code></a>.
 </p><p><hr><blockquote><pre>Example:
 <pre>
 Presentation pres = new Presentation("pres.pptx");
 try {
     TiffOptions options = new TiffOptions();
     HandoutLayoutingOptions slidesLayoutOptions = new HandoutLayoutingOptions();
     slidesLayoutOptions.setHandout(HandoutType.Handouts4Horizontal);
     options.setSlidesLayoutOptions(slidesLayoutOptions);

     pres.save("pres.tiff", SaveFormat.Tiff, options);
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITiffOptions.html#getSlidesLayoutOptions--">getSlidesLayoutOptions</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITiffOptions.html" title="interface in com.aspose.slides">ITiffOptions</a></code></dd>
</dl>
</li>
</ul>
<a name="setSlidesLayoutOptions-com.aspose.slides.ISlidesLayoutOptions-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSlidesLayoutOptions</h4>
<pre>public final&nbsp;void&nbsp;setSlidesLayoutOptions(<a href="../../../com/aspose/slides/ISlidesLayoutOptions.html" title="interface in com.aspose.slides">ISlidesLayoutOptions</a>&nbsp;value)</pre>
<div class="block"><p>
 Gets or sets the mode in which slides are placed on the page when exporting a presentation <a href="../../../com/aspose/slides/ISlidesLayoutOptions.html" title="interface in com.aspose.slides"><code>ISlidesLayoutOptions</code></a>.
 </p><p><hr><blockquote><pre>Example:
 <pre>
 Presentation pres = new Presentation("pres.pptx");
 try {
     TiffOptions options = new TiffOptions();
     HandoutLayoutingOptions slidesLayoutOptions = new HandoutLayoutingOptions();
     slidesLayoutOptions.setHandout(HandoutType.Handouts4Horizontal);
     options.setSlidesLayoutOptions(slidesLayoutOptions);

     pres.save("pres.tiff", SaveFormat.Tiff, options);
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITiffOptions.html#setSlidesLayoutOptions-com.aspose.slides.ISlidesLayoutOptions-">setSlidesLayoutOptions</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITiffOptions.html" title="interface in com.aspose.slides">ITiffOptions</a></code></dd>
</dl>
</li>
</ul>
<a name="getBwConversionMode--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBwConversionMode</h4>
<pre>public final&nbsp;int&nbsp;getBwConversionMode()</pre>
<div class="block"><p>
 Specifies the algorithm for converting a color image into a black and white image.
 This option will applied only if <code>CompressionType</code>(<a href="../../../com/aspose/slides/TiffOptions.html#getCompressionType--"><code>getCompressionType()</code></a>/<a href="../../../com/aspose/slides/TiffOptions.html#setCompressionType-int-"><code>setCompressionType(int)</code></a>) 
 is set to <a href="../../../com/aspose/slides/TiffCompressionTypes.html#CCITT4"><code>TiffCompressionTypes.CCITT4</code></a> or <a href="../../../com/aspose/slides/TiffCompressionTypes.html#CCITT3"><code>TiffCompressionTypes.CCITT3</code></a>
 Read/write <a href="../../../com/aspose/slides/BlackWhiteConversionMode.html" title="class in com.aspose.slides"><code>BlackWhiteConversionMode</code></a>.
 Default is <a href="../../../com/aspose/slides/BlackWhiteConversionMode.html#Default"><code>BlackWhiteConversionMode.Default</code></a>. 
 </p><p><hr><blockquote><pre>
 <pre>
 TiffOptions tiffOptions = new TiffOptions();
 tiffOptions.setCompressionType(TiffCompressionTypes.CCITT4);
 tiffOptions.setBwConversionMode(BlackWhiteConversionMode.Dithering);
 Presentation presentation = new Presentation();
 try {
     presentation.save(tiffFilePath, SaveFormat.Tiff, tiffOptions);
 } finally {
     if (presentation != null) presentation.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITiffOptions.html#getBwConversionMode--">getBwConversionMode</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITiffOptions.html" title="interface in com.aspose.slides">ITiffOptions</a></code></dd>
</dl>
</li>
</ul>
<a name="setBwConversionMode-int-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setBwConversionMode</h4>
<pre>public final&nbsp;void&nbsp;setBwConversionMode(int&nbsp;value)</pre>
<div class="block"><p>
 Specifies the algorithm for converting a color image into a black and white image.
 This option will applied only if <code>CompressionType</code>(<a href="../../../com/aspose/slides/TiffOptions.html#getCompressionType--"><code>getCompressionType()</code></a>/<a href="../../../com/aspose/slides/TiffOptions.html#setCompressionType-int-"><code>setCompressionType(int)</code></a>) 
 is set to <a href="../../../com/aspose/slides/TiffCompressionTypes.html#CCITT4"><code>TiffCompressionTypes.CCITT4</code></a> or <a href="../../../com/aspose/slides/TiffCompressionTypes.html#CCITT3"><code>TiffCompressionTypes.CCITT3</code></a>
 Read/write <a href="../../../com/aspose/slides/BlackWhiteConversionMode.html" title="class in com.aspose.slides"><code>BlackWhiteConversionMode</code></a>.
 Default is <a href="../../../com/aspose/slides/BlackWhiteConversionMode.html#Default"><code>BlackWhiteConversionMode.Default</code></a>. 
 </p><p><hr><blockquote><pre>
 <pre>
 TiffOptions tiffOptions = new TiffOptions();
 tiffOptions.setCompressionType(TiffCompressionTypes.CCITT4);
 tiffOptions.setBwConversionMode(BlackWhiteConversionMode.Dithering);
 Presentation presentation = new Presentation();
 try {
     presentation.save(tiffFilePath, SaveFormat.Tiff, tiffOptions);
 } finally {
     if (presentation != null) presentation.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITiffOptions.html#setBwConversionMode-int-">setBwConversionMode</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITiffOptions.html" title="interface in com.aspose.slides">ITiffOptions</a></code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/TiffCompressionTypes.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/TileFlip.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/TiffOptions.html" target="_top">Frames</a></li>
<li><a href="TiffOptions.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><i>Copyright &copy; 2004-2025 Aspose Pty Ltd. All Rights Reserved.</i></small></p>
</body>
</html>
