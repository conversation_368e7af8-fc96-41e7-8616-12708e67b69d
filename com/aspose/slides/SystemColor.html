<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_421) on Fri Jul 11 05:32:44 CDT 2025 -->
<title>SystemColor (Aspose.Slides for Java (25.7) API Documentation)</title>
<meta name="date" content="2025-07-11">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="SystemColor (Aspose.Slides for Java (25.7) API Documentation)";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/SwfOptions.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/Tab.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/SystemColor.html" target="_top">Frames</a></li>
<li><a href="SystemColor.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.classes.inherited.from.class.com.aspose.ms.System.Enum">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#methods.inherited.from.class.com.aspose.ms.System.Enum">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li>Method</li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.aspose.slides</div>
<h2 title="Class SystemColor" class="title">Class SystemColor</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.aspose.ms.System.ValueType&lt;com.aspose.ms.System.Enum&gt;</li>
<li>
<ul class="inheritance">
<li>com.aspose.ms.System.Enum</li>
<li>
<ul class="inheritance">
<li>com.aspose.slides.SystemColor</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public final class <span class="typeNameLabel">SystemColor</span>
extends com.aspose.ms.System.Enum</pre>
<div class="block"><p>
 Represents predefined system colors.
 </p></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.com.aspose.ms.System.Enum">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from class&nbsp;com.aspose.ms.System.Enum</h3>
<code>com.aspose.ms.System.Enum.AbstractEnum, com.aspose.ms.System.Enum.FlaggedEnum, com.aspose.ms.System.Enum.ObjectEnum, com.aspose.ms.System.Enum.SimpleEnum</code></li>
</ul>
</li>
</ul>
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SystemColor.html#ActiveBorder">ActiveBorder</a></span></code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SystemColor.html#ActiveCaption">ActiveCaption</a></span></code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SystemColor.html#AppWorkspace">AppWorkspace</a></span></code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SystemColor.html#Background">Background</a></span></code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SystemColor.html#BtnFace">BtnFace</a></span></code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SystemColor.html#BtnHighlight">BtnHighlight</a></span></code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SystemColor.html#BtnShadow">BtnShadow</a></span></code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SystemColor.html#BtnText">BtnText</a></span></code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SystemColor.html#CaptionText">CaptionText</a></span></code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SystemColor.html#GradientActiveCaption">GradientActiveCaption</a></span></code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SystemColor.html#GradientInactiveCaption">GradientInactiveCaption</a></span></code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SystemColor.html#GrayText">GrayText</a></span></code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SystemColor.html#Highlight">Highlight</a></span></code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SystemColor.html#HighlightText">HighlightText</a></span></code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SystemColor.html#HotLight">HotLight</a></span></code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SystemColor.html#InactiveBorder">InactiveBorder</a></span></code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SystemColor.html#InactiveCaption">InactiveCaption</a></span></code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SystemColor.html#InactiveCaptionText">InactiveCaptionText</a></span></code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SystemColor.html#InfoBk">InfoBk</a></span></code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SystemColor.html#InfoText">InfoText</a></span></code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SystemColor.html#Menu">Menu</a></span></code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SystemColor.html#MenuBar">MenuBar</a></span></code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SystemColor.html#MenuHighlight">MenuHighlight</a></span></code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SystemColor.html#MenuText">MenuText</a></span></code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SystemColor.html#NotDefined">NotDefined</a></span></code>
<div class="block">
 System color is not defined.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SystemColor.html#ScrollBar">ScrollBar</a></span></code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SystemColor.html#ThreeDDkShadow">ThreeDDkShadow</a></span></code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SystemColor.html#ThreeDLight">ThreeDLight</a></span></code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SystemColor.html#Window">Window</a></span></code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SystemColor.html#WindowFrame">WindowFrame</a></span></code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SystemColor.html#WindowText">WindowText</a></span></code></td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.com.aspose.ms.System.Enum">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;com.aspose.ms.System.Enum</h3>
<code>EnumSeparatorCharArray</code></li>
</ul>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.aspose.ms.System.Enum">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;com.aspose.ms.System.Enum</h3>
<code>Clone, CloneTo, format, format, get_Caption, get_Value, getName, getName, getNames, getNames, getUnderlyingType, getUnderlyingType, getValue, getValues, isDefined, isDefined, isDefined, isDefined, parse, parse, parse, parse, register, toObject, toString</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="NotDefined">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NotDefined</h4>
<pre>public static final&nbsp;int NotDefined</pre>
<div class="block"><p>
 System color is not defined.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SystemColor.NotDefined">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="ScrollBar">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ScrollBar</h4>
<pre>public static final&nbsp;int ScrollBar</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SystemColor.ScrollBar">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Background">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Background</h4>
<pre>public static final&nbsp;int Background</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SystemColor.Background">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="ActiveCaption">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ActiveCaption</h4>
<pre>public static final&nbsp;int ActiveCaption</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SystemColor.ActiveCaption">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="InactiveCaption">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>InactiveCaption</h4>
<pre>public static final&nbsp;int InactiveCaption</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SystemColor.InactiveCaption">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Menu">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Menu</h4>
<pre>public static final&nbsp;int Menu</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SystemColor.Menu">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Window">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Window</h4>
<pre>public static final&nbsp;int Window</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SystemColor.Window">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="WindowFrame">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>WindowFrame</h4>
<pre>public static final&nbsp;int WindowFrame</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SystemColor.WindowFrame">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MenuText">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MenuText</h4>
<pre>public static final&nbsp;int MenuText</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SystemColor.MenuText">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="WindowText">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>WindowText</h4>
<pre>public static final&nbsp;int WindowText</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SystemColor.WindowText">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CaptionText">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CaptionText</h4>
<pre>public static final&nbsp;int CaptionText</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SystemColor.CaptionText">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="ActiveBorder">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ActiveBorder</h4>
<pre>public static final&nbsp;int ActiveBorder</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SystemColor.ActiveBorder">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="InactiveBorder">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>InactiveBorder</h4>
<pre>public static final&nbsp;int InactiveBorder</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SystemColor.InactiveBorder">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="AppWorkspace">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>AppWorkspace</h4>
<pre>public static final&nbsp;int AppWorkspace</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SystemColor.AppWorkspace">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Highlight">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Highlight</h4>
<pre>public static final&nbsp;int Highlight</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SystemColor.Highlight">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="HighlightText">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>HighlightText</h4>
<pre>public static final&nbsp;int HighlightText</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SystemColor.HighlightText">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="BtnFace">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BtnFace</h4>
<pre>public static final&nbsp;int BtnFace</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SystemColor.BtnFace">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="BtnShadow">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BtnShadow</h4>
<pre>public static final&nbsp;int BtnShadow</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SystemColor.BtnShadow">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="GrayText">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>GrayText</h4>
<pre>public static final&nbsp;int GrayText</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SystemColor.GrayText">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="BtnText">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BtnText</h4>
<pre>public static final&nbsp;int BtnText</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SystemColor.BtnText">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="InactiveCaptionText">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>InactiveCaptionText</h4>
<pre>public static final&nbsp;int InactiveCaptionText</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SystemColor.InactiveCaptionText">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="BtnHighlight">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BtnHighlight</h4>
<pre>public static final&nbsp;int BtnHighlight</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SystemColor.BtnHighlight">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="ThreeDDkShadow">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ThreeDDkShadow</h4>
<pre>public static final&nbsp;int ThreeDDkShadow</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SystemColor.ThreeDDkShadow">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="ThreeDLight">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ThreeDLight</h4>
<pre>public static final&nbsp;int ThreeDLight</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SystemColor.ThreeDLight">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="InfoText">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>InfoText</h4>
<pre>public static final&nbsp;int InfoText</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SystemColor.InfoText">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="InfoBk">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>InfoBk</h4>
<pre>public static final&nbsp;int InfoBk</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SystemColor.InfoBk">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="HotLight">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>HotLight</h4>
<pre>public static final&nbsp;int HotLight</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SystemColor.HotLight">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="GradientActiveCaption">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>GradientActiveCaption</h4>
<pre>public static final&nbsp;int GradientActiveCaption</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SystemColor.GradientActiveCaption">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="GradientInactiveCaption">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>GradientInactiveCaption</h4>
<pre>public static final&nbsp;int GradientInactiveCaption</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SystemColor.GradientInactiveCaption">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MenuHighlight">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MenuHighlight</h4>
<pre>public static final&nbsp;int MenuHighlight</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SystemColor.MenuHighlight">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MenuBar">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>MenuBar</h4>
<pre>public static final&nbsp;int MenuBar</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SystemColor.MenuBar">Constant Field Values</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/SwfOptions.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/Tab.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/SystemColor.html" target="_top">Frames</a></li>
<li><a href="SystemColor.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.classes.inherited.from.class.com.aspose.ms.System.Enum">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#methods.inherited.from.class.com.aspose.ms.System.Enum">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li>Method</li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><i>Copyright &copy; 2004-2025 Aspose Pty Ltd. All Rights Reserved.</i></small></p>
</body>
</html>
