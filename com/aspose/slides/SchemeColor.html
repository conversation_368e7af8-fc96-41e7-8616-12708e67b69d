<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_421) on Fri Jul 11 05:32:44 CDT 2025 -->
<title>SchemeColor (Aspose.Slides for Java (25.7) API Documentation)</title>
<meta name="date" content="2025-07-11">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="SchemeColor (Aspose.Slides for Java (25.7) API Documentation)";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/ScaleEffect.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/Section.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/SchemeColor.html" target="_top">Frames</a></li>
<li><a href="SchemeColor.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.classes.inherited.from.class.com.aspose.ms.System.Enum">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#methods.inherited.from.class.com.aspose.ms.System.Enum">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li>Method</li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.aspose.slides</div>
<h2 title="Class SchemeColor" class="title">Class SchemeColor</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.aspose.ms.System.ValueType&lt;com.aspose.ms.System.Enum&gt;</li>
<li>
<ul class="inheritance">
<li>com.aspose.ms.System.Enum</li>
<li>
<ul class="inheritance">
<li>com.aspose.slides.SchemeColor</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public final class <span class="typeNameLabel">SchemeColor</span>
extends com.aspose.ms.System.Enum</pre>
<div class="block"><p>
 Represents colors in a color scheme.
 </p></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.com.aspose.ms.System.Enum">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from class&nbsp;com.aspose.ms.System.Enum</h3>
<code>com.aspose.ms.System.Enum.AbstractEnum, com.aspose.ms.System.Enum.FlaggedEnum, com.aspose.ms.System.Enum.ObjectEnum, com.aspose.ms.System.Enum.SimpleEnum</code></li>
</ul>
</li>
</ul>
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SchemeColor.html#Accent1">Accent1</a></span></code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SchemeColor.html#Accent2">Accent2</a></span></code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SchemeColor.html#Accent3">Accent3</a></span></code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SchemeColor.html#Accent4">Accent4</a></span></code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SchemeColor.html#Accent5">Accent5</a></span></code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SchemeColor.html#Accent6">Accent6</a></span></code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SchemeColor.html#Background1">Background1</a></span></code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SchemeColor.html#Background2">Background2</a></span></code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SchemeColor.html#Dark1">Dark1</a></span></code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SchemeColor.html#Dark2">Dark2</a></span></code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SchemeColor.html#FollowedHyperlink">FollowedHyperlink</a></span></code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SchemeColor.html#Hyperlink">Hyperlink</a></span></code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SchemeColor.html#Light1">Light1</a></span></code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SchemeColor.html#Light2">Light2</a></span></code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SchemeColor.html#NotDefined">NotDefined</a></span></code>
<div class="block">
 Color scheme is not defined.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SchemeColor.html#StyleColor">StyleColor</a></span></code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SchemeColor.html#Text1">Text1</a></span></code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SchemeColor.html#Text2">Text2</a></span></code></td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.com.aspose.ms.System.Enum">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;com.aspose.ms.System.Enum</h3>
<code>EnumSeparatorCharArray</code></li>
</ul>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.aspose.ms.System.Enum">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;com.aspose.ms.System.Enum</h3>
<code>Clone, CloneTo, format, format, get_Caption, get_Value, getName, getName, getNames, getNames, getUnderlyingType, getUnderlyingType, getValue, getValues, isDefined, isDefined, isDefined, isDefined, parse, parse, parse, parse, register, toObject, toString</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="NotDefined">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NotDefined</h4>
<pre>public static final&nbsp;int NotDefined</pre>
<div class="block"><p>
 Color scheme is not defined.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SchemeColor.NotDefined">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Background1">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Background1</h4>
<pre>public static final&nbsp;int Background1</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SchemeColor.Background1">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Text1">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Text1</h4>
<pre>public static final&nbsp;int Text1</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SchemeColor.Text1">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Background2">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Background2</h4>
<pre>public static final&nbsp;int Background2</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SchemeColor.Background2">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Text2">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Text2</h4>
<pre>public static final&nbsp;int Text2</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SchemeColor.Text2">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Accent1">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Accent1</h4>
<pre>public static final&nbsp;int Accent1</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SchemeColor.Accent1">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Accent2">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Accent2</h4>
<pre>public static final&nbsp;int Accent2</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SchemeColor.Accent2">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Accent3">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Accent3</h4>
<pre>public static final&nbsp;int Accent3</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SchemeColor.Accent3">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Accent4">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Accent4</h4>
<pre>public static final&nbsp;int Accent4</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SchemeColor.Accent4">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Accent5">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Accent5</h4>
<pre>public static final&nbsp;int Accent5</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SchemeColor.Accent5">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Accent6">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Accent6</h4>
<pre>public static final&nbsp;int Accent6</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SchemeColor.Accent6">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Hyperlink">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Hyperlink</h4>
<pre>public static final&nbsp;int Hyperlink</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SchemeColor.Hyperlink">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="FollowedHyperlink">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FollowedHyperlink</h4>
<pre>public static final&nbsp;int FollowedHyperlink</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SchemeColor.FollowedHyperlink">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="StyleColor">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>StyleColor</h4>
<pre>public static final&nbsp;int StyleColor</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SchemeColor.StyleColor">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Dark1">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Dark1</h4>
<pre>public static final&nbsp;int Dark1</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SchemeColor.Dark1">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Light1">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Light1</h4>
<pre>public static final&nbsp;int Light1</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SchemeColor.Light1">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Dark2">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Dark2</h4>
<pre>public static final&nbsp;int Dark2</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SchemeColor.Dark2">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Light2">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>Light2</h4>
<pre>public static final&nbsp;int Light2</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SchemeColor.Light2">Constant Field Values</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/ScaleEffect.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/Section.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/SchemeColor.html" target="_top">Frames</a></li>
<li><a href="SchemeColor.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.classes.inherited.from.class.com.aspose.ms.System.Enum">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#methods.inherited.from.class.com.aspose.ms.System.Enum">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li>Method</li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><i>Copyright &copy; 2004-2025 Aspose Pty Ltd. All Rights Reserved.</i></small></p>
</body>
</html>
