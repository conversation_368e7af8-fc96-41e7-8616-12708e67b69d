<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_421) on Fri Jul 11 05:32:43 CDT 2025 -->
<title>PresentationAnimationsGenerator (Aspose.Slides for Java (25.7) API Documentation)</title>
<meta name="date" content="2025-07-11">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="PresentationAnimationsGenerator (Aspose.Slides for Java (25.7) API Documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/Presentation.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/PresentationAnimationsGenerator.NewAnimation.html" title="interface in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/PresentationAnimationsGenerator.html" target="_top">Frames</a></li>
<li><a href="PresentationAnimationsGenerator.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.aspose.slides</div>
<h2 title="Class PresentationAnimationsGenerator" class="title">Class PresentationAnimationsGenerator</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.aspose.slides.PresentationAnimationsGenerator</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd>com.aspose.ms.System.IDisposable</dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">PresentationAnimationsGenerator</span>
extends java.lang.Object
implements com.aspose.ms.System.IDisposable</pre>
<div class="block"><p>
 Represents a generator of the animations in the <a href="../../../com/aspose/slides/Presentation.html" title="class in com.aspose.slides"><code>Presentation</code></a>.
 </p><p><hr><blockquote><pre>
 <pre>
 Presentation pres = new Presentation("animated.pptx");
 try {
     PresentationAnimationsGenerator animationsGenerator = new PresentationAnimationsGenerator(pres);
     try {
         PresentationPlayer player = new PresentationPlayer(animationsGenerator, 33);
         try {
             player.setFrameTick((sender, args) -&gt;
             {
                 try {
                     ImageIO.write(args.getFrame(), "PNG", new java.io.File("frame_" + sender.getFrameIndex() + ".png"));
                 } catch (IOException e) {
                     throw new RuntimeException(e);
                 }
             });
             animationsGenerator.run(pres.getSlides());
         } finally {
             if (player != null) player.dispose();
         }
     } finally {
         if (animationsGenerator != null) animationsGenerator.dispose();
     }
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Nested Class Summary table, listing nested classes, and an explanation">
<caption><span>Nested Classes</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static interface&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresentationAnimationsGenerator.NewAnimation.html" title="interface in com.aspose.slides">PresentationAnimationsGenerator.NewAnimation</a></span></code>
<div class="block">
 An event represents that a new animation was generated.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresentationAnimationsGenerator.html#PresentationAnimationsGenerator-java.awt.Dimension-">PresentationAnimationsGenerator</a></span>(java.awt.Dimension&nbsp;frameSize)</code>
<div class="block">
 Creates a new instance of the <a href="../../../com/aspose/slides/PresentationAnimationsGenerator.html" title="class in com.aspose.slides"><code>PresentationAnimationsGenerator</code></a>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresentationAnimationsGenerator.html#PresentationAnimationsGenerator-java.awt.geom.Dimension2D-">PresentationAnimationsGenerator</a></span>(java.awt.geom.Dimension2D&nbsp;frameSize)</code>
<div class="block">
 Creates a new instance of the <a href="../../../com/aspose/slides/PresentationAnimationsGenerator.html" title="class in com.aspose.slides"><code>PresentationAnimationsGenerator</code></a>.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresentationAnimationsGenerator.html#PresentationAnimationsGenerator-com.aspose.slides.Presentation-">PresentationAnimationsGenerator</a></span>(<a href="../../../com/aspose/slides/Presentation.html" title="class in com.aspose.slides">Presentation</a>&nbsp;presentation)</code>
<div class="block">
 Creates a new instance of the <a href="../../../com/aspose/slides/PresentationAnimationsGenerator.html" title="class in com.aspose.slides"><code>PresentationAnimationsGenerator</code></a>.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresentationAnimationsGenerator.html#dispose--">dispose</a></span>()</code>
<div class="block">
 Disposes the instance of the <a href="../../../com/aspose/slides/PresentationAnimationsGenerator.html" title="class in com.aspose.slides"><code>PresentationAnimationsGenerator</code></a>.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresentationAnimationsGenerator.html#getDefaultDelay--">getDefaultDelay</a></span>()</code>
<div class="block">
 Gets or sets default delay time [ms].</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresentationAnimationsGenerator.html#getExportedSlides--">getExportedSlides</a></span>()</code>
<div class="block">
 Get the number of the exported slides count.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>java.awt.Dimension</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresentationAnimationsGenerator.html#getFrameSize--">getFrameSize</a></span>()</code>
<div class="block">
 Gets the frame size.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresentationAnimationsGenerator.html#getIncludeHiddenSlides--">getIncludeHiddenSlides</a></span>()</code>
<div class="block">
 Get or sets if hidden slides should be included.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresentationAnimationsGenerator.html#run-com.aspose.ms.System.Collections.Generic.IGenericEnumerable-">run</a></span>(com.aspose.ms.System.Collections.Generic.IGenericEnumerable&lt;<a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a>&gt;&nbsp;slides)</code>
<div class="block">
 Run the animation events generation for each slide.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresentationAnimationsGenerator.html#run-com.aspose.ms.System.Collections.Generic.IGenericEnumerable-int-com.aspose.slides.PresentationPlayer.FrameTick-">run</a></span>(com.aspose.ms.System.Collections.Generic.IGenericEnumerable&lt;<a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a>&gt;&nbsp;slides,
   int&nbsp;fps,
   <a href="../../../com/aspose/slides/PresentationPlayer.FrameTick.html" title="interface in com.aspose.slides">PresentationPlayer.FrameTick</a>&nbsp;onFrame)</code>
<div class="block">
 Run the animation events generation for each slide.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresentationAnimationsGenerator.html#setDefaultDelay-int-">setDefaultDelay</a></span>(int&nbsp;value)</code>
<div class="block">
 Gets or sets default delay time [ms].</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresentationAnimationsGenerator.html#setIncludeHiddenSlides-boolean-">setIncludeHiddenSlides</a></span>(boolean&nbsp;value)</code>
<div class="block">
 Get or sets if hidden slides should be included.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresentationAnimationsGenerator.html#setNewAnimation-com.aspose.slides.PresentationAnimationsGenerator.NewAnimation-">setNewAnimation</a></span>(<a href="../../../com/aspose/slides/PresentationAnimationsGenerator.NewAnimation.html" title="interface in com.aspose.slides">PresentationAnimationsGenerator.NewAnimation</a>&nbsp;anim)</code>
<div class="block">
 Set a new animation event.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="PresentationAnimationsGenerator-com.aspose.slides.Presentation-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PresentationAnimationsGenerator</h4>
<pre>public&nbsp;PresentationAnimationsGenerator(<a href="../../../com/aspose/slides/Presentation.html" title="class in com.aspose.slides">Presentation</a>&nbsp;presentation)</pre>
<div class="block"><p>
 Creates a new instance of the <a href="../../../com/aspose/slides/PresentationAnimationsGenerator.html" title="class in com.aspose.slides"><code>PresentationAnimationsGenerator</code></a>.
 </p></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>presentation</code> - The frame size will be set with accordance to the <a href="../../../com/aspose/slides/Presentation.html#getSlideSize--"><code>Presentation.getSlideSize()</code></a></dd>
</dl>
</li>
</ul>
<a name="PresentationAnimationsGenerator-java.awt.Dimension-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PresentationAnimationsGenerator</h4>
<pre>public&nbsp;PresentationAnimationsGenerator(java.awt.Dimension&nbsp;frameSize)</pre>
<div class="block"><p>
 Creates a new instance of the <a href="../../../com/aspose/slides/PresentationAnimationsGenerator.html" title="class in com.aspose.slides"><code>PresentationAnimationsGenerator</code></a>.
 </p></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>frameSize</code> - The frame size.</dd>
</dl>
</li>
</ul>
<a name="PresentationAnimationsGenerator-java.awt.geom.Dimension2D-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>PresentationAnimationsGenerator</h4>
<pre>public&nbsp;PresentationAnimationsGenerator(java.awt.geom.Dimension2D&nbsp;frameSize)</pre>
<div class="block"><p>
 Creates a new instance of the <a href="../../../com/aspose/slides/PresentationAnimationsGenerator.html" title="class in com.aspose.slides"><code>PresentationAnimationsGenerator</code></a>.
 </p></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>frameSize</code> - The frame size.</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="dispose--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>dispose</h4>
<pre>public final&nbsp;void&nbsp;dispose()</pre>
<div class="block"><p>
 Disposes the instance of the <a href="../../../com/aspose/slides/PresentationAnimationsGenerator.html" title="class in com.aspose.slides"><code>PresentationAnimationsGenerator</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>dispose</code>&nbsp;in interface&nbsp;<code>com.aspose.ms.System.IDisposable</code></dd>
</dl>
</li>
</ul>
<a name="getFrameSize--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFrameSize</h4>
<pre>public&nbsp;java.awt.Dimension&nbsp;getFrameSize()</pre>
<div class="block"><p>
 Gets the frame size.
 </p></div>
</li>
</ul>
<a name="getDefaultDelay--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDefaultDelay</h4>
<pre>public final&nbsp;int&nbsp;getDefaultDelay()</pre>
<div class="block"><p>
 Gets or sets default delay time [ms].
 </p><p><hr><blockquote><pre>
 <pre>
 Presentation presentation = new Presentation("animated.pptx");
 try {
     PresentationAnimationsGenerator animationsGenerator = new PresentationAnimationsGenerator(presentation.getSlideSize().getSize());
     try {
         animationsGenerator.setDefaultDelay(1000); // 1s
         // ...
         animationsGenerator.run(presentation.getSlides());
     } finally {
         if (animationsGenerator != null) animationsGenerator.dispose();
     }
 } finally {
     if (presentation != null) presentation.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
</li>
</ul>
<a name="setDefaultDelay-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDefaultDelay</h4>
<pre>public final&nbsp;void&nbsp;setDefaultDelay(int&nbsp;value)</pre>
<div class="block"><p>
 Gets or sets default delay time [ms].
 </p><p><hr><blockquote><pre>
 <pre>
 Presentation presentation = new Presentation("animated.pptx");
 try {
     PresentationAnimationsGenerator animationsGenerator = new PresentationAnimationsGenerator(presentation.getSlideSize().getSize());
     try {
         animationsGenerator.setDefaultDelay(1000); // 1s
         // ...
         animationsGenerator.run(presentation.getSlides());
     } finally {
         if (animationsGenerator != null) animationsGenerator.dispose();
     }
 } finally {
     if (presentation != null) presentation.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
</li>
</ul>
<a name="getIncludeHiddenSlides--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getIncludeHiddenSlides</h4>
<pre>public final&nbsp;boolean&nbsp;getIncludeHiddenSlides()</pre>
<div class="block"><p>
 Get or sets if hidden slides should be included.
 </p><p><hr><blockquote><pre>
 <pre>
 Presentation presentation = new Presentation("animated.pptx");
 try {
     PresentationAnimationsGenerator animationsGenerator = new PresentationAnimationsGenerator(presentation.getSlideSize().getSize());
     try {
         animationsGenerator.setIncludeHiddenSlides(false);
         // ...
         animationsGenerator.run(presentation.getSlides());
     } finally {
         if (animationsGenerator != null) animationsGenerator.dispose();
     }
 } finally {
     if (presentation != null) presentation.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
</li>
</ul>
<a name="setIncludeHiddenSlides-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setIncludeHiddenSlides</h4>
<pre>public final&nbsp;void&nbsp;setIncludeHiddenSlides(boolean&nbsp;value)</pre>
<div class="block"><p>
 Get or sets if hidden slides should be included.
 </p><p><hr><blockquote><pre>
 <pre>
 Presentation presentation = new Presentation("animated.pptx");
 try {
     PresentationAnimationsGenerator animationsGenerator = new PresentationAnimationsGenerator(presentation.getSlideSize().getSize());
     try {
         animationsGenerator.setIncludeHiddenSlides(false);
         // ...
         animationsGenerator.run(presentation.getSlides());
     } finally {
         if (animationsGenerator != null) animationsGenerator.dispose();
     }
 } finally {
     if (presentation != null) presentation.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
</li>
</ul>
<a name="getExportedSlides--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getExportedSlides</h4>
<pre>public final&nbsp;int&nbsp;getExportedSlides()</pre>
<div class="block"><p>
 Get the number of the exported slides count.
 </p></div>
</li>
</ul>
<a name="setNewAnimation-com.aspose.slides.PresentationAnimationsGenerator.NewAnimation-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setNewAnimation</h4>
<pre>public&nbsp;void&nbsp;setNewAnimation(<a href="../../../com/aspose/slides/PresentationAnimationsGenerator.NewAnimation.html" title="interface in com.aspose.slides">PresentationAnimationsGenerator.NewAnimation</a>&nbsp;anim)</pre>
<div class="block"><p>
 Set a new animation event.
 </p><p><hr><blockquote><pre>
 <pre>
 Presentation presentation = new Presentation("SimpleAnimations.pptx");
 try {
     PresentationAnimationsGenerator animationsGenerator = new PresentationAnimationsGenerator(presentation.getSlideSize().getSize());
     try {
         animationsGenerator.setNewAnimation(animationPlayer -&gt; {
             System.out.println(String.format("Animation total duration: %f", animationPlayer.getDuration()));
         });
         animationsGenerator.run(presentation.getSlides());
     } finally {
         if (animationsGenerator != null) animationsGenerator.dispose();
     }
 } finally {
     if (presentation != null) presentation.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>anim</code> - Animation event.</dd>
</dl>
</li>
</ul>
<a name="run-com.aspose.ms.System.Collections.Generic.IGenericEnumerable-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>run</h4>
<pre>public final&nbsp;void&nbsp;run(com.aspose.ms.System.Collections.Generic.IGenericEnumerable&lt;<a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a>&gt;&nbsp;slides)</pre>
<div class="block"><p>
 Run the animation events generation for each slide.
 </p><p><hr><blockquote><pre>
 <pre>
 Presentation presentation = new Presentation("animated.pptx");
 try {
     PresentationAnimationsGenerator animationsGenerator = new PresentationAnimationsGenerator(presentation.getSlideSize().getSize());
     try {
         PresentationPlayer player = new PresentationPlayer(animationsGenerator, 33);
         try {
             animationsGenerator.setNewAnimation(animationPlayer -&gt;
             {
                 // handle new animation
             });
             player.setFrameTick((sender, args) -&gt;
             {
                 // handle frame tick within the new animation
             });
             animationsGenerator.run(presentation.getSlides());
         } finally {
             if (player != null) player.dispose();
         }
     } finally {
         if (animationsGenerator != null) animationsGenerator.dispose();
     }
 } finally {
     if (presentation != null) presentation.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
</li>
</ul>
<a name="run-com.aspose.ms.System.Collections.Generic.IGenericEnumerable-int-com.aspose.slides.PresentationPlayer.FrameTick-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>run</h4>
<pre>public final&nbsp;void&nbsp;run(com.aspose.ms.System.Collections.Generic.IGenericEnumerable&lt;<a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a>&gt;&nbsp;slides,
                      int&nbsp;fps,
                      <a href="../../../com/aspose/slides/PresentationPlayer.FrameTick.html" title="interface in com.aspose.slides">PresentationPlayer.FrameTick</a>&nbsp;onFrame)</pre>
<div class="block"><p>
 Run the animation events generation for each slide.
 </p><p><hr><blockquote><pre>
 <pre>
 Presentation presentation = new Presentation("animated.pptx");
 try {
     PresentationAnimationsGenerator animationsGenerator = new PresentationAnimationsGenerator(presentation.getSlideSize().getSize());
     try {
         animationsGenerator.run(presentation.getSlides(), 33, (player, playerArgs) -&gt;
         {
             player.setFrameTick((sender, args) -&gt;
             {
                 try {
                     ImageIO.write(args.getFrame(), "PNG", new java.io.File("frame_" + sender.getFrameIndex() + ".png"));
                 } catch (IOException e) {
                     throw new RuntimeException(e);
                 }
             });
         });
     } finally {
         if (animationsGenerator != null) animationsGenerator.dispose();
     }
 } finally {
     if (presentation != null) presentation.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/Presentation.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/PresentationAnimationsGenerator.NewAnimation.html" title="interface in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/PresentationAnimationsGenerator.html" target="_top">Frames</a></li>
<li><a href="PresentationAnimationsGenerator.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><i>Copyright &copy; 2004-2025 Aspose Pty Ltd. All Rights Reserved.</i></small></p>
</body>
</html>
