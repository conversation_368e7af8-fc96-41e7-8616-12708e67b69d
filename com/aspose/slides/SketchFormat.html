<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_421) on Fri Jul 11 05:32:44 CDT 2025 -->
<title>SketchFormat (Aspose.Slides for Java (25.7) API Documentation)</title>
<meta name="date" content="2025-07-11">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="SketchFormat (Aspose.Slides for Java (25.7) API Documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/SideDirectionTransition.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/Slide.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/SketchFormat.html" target="_top">Frames</a></li>
<li><a href="SketchFormat.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.aspose.slides</div>
<h2 title="Class SketchFormat" class="title">Class SketchFormat</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../com/aspose/slides/PVIObject.html" title="class in com.aspose.slides">com.aspose.slides.PVIObject</a></li>
<li>
<ul class="inheritance">
<li>com.aspose.slides.SketchFormat</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../com/aspose/slides/IPresentationComponent.html" title="interface in com.aspose.slides">IPresentationComponent</a>, <a href="../../../com/aspose/slides/ISketchFormat.html" title="interface in com.aspose.slides">ISketchFormat</a>, <a href="../../../com/aspose/slides/ISlideComponent.html" title="interface in com.aspose.slides">ISlideComponent</a></dd>
</dl>
<hr>
<br>
<pre>public final class <span class="typeNameLabel">SketchFormat</span>
extends <a href="../../../com/aspose/slides/PVIObject.html" title="class in com.aspose.slides">PVIObject</a>
implements <a href="../../../com/aspose/slides/ISketchFormat.html" title="interface in com.aspose.slides">ISketchFormat</a></pre>
<div class="block"><p>
 Represents properties for lines sketch format.
 </p></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SketchFormat.html#getSketchType--">getSketchType</a></span>()</code>
<div class="block">
 Returns or sets the sketch type.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SketchFormat.html#getVersion--">getVersion</a></span>()</code>
<div class="block">
 Version.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SketchFormat.html#setSketchType-int-">setSketchType</a></span>(int&nbsp;value)</code>
<div class="block">
 Returns or sets the sketch type.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.aspose.slides.PVIObject">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;com.aspose.slides.<a href="../../../com/aspose/slides/PVIObject.html" title="class in com.aspose.slides">PVIObject</a></h3>
<code><a href="../../../com/aspose/slides/PVIObject.html#equals-java.lang.Object-">equals</a>, <a href="../../../com/aspose/slides/PVIObject.html#getParent_Immediate--">getParent_Immediate</a>, <a href="../../../com/aspose/slides/PVIObject.html#getParent_IPresentationComponent--">getParent_IPresentationComponent</a>, <a href="../../../com/aspose/slides/PVIObject.html#getParent_ISlideComponent--">getParent_ISlideComponent</a>, <a href="../../../com/aspose/slides/PVIObject.html#getPresentation--">getPresentation</a>, <a href="../../../com/aspose/slides/PVIObject.html#getSlide--">getSlide</a>, <a href="../../../com/aspose/slides/PVIObject.html#hashCode--">hashCode</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>getClass, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getVersion--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVersion</h4>
<pre>public&nbsp;long&nbsp;getVersion()</pre>
<div class="block"><p>
 Version.
 Read-only <code>long</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../com/aspose/slides/PVIObject.html#getVersion--">getVersion</a></code>&nbsp;in class&nbsp;<code><a href="../../../com/aspose/slides/PVIObject.html" title="class in com.aspose.slides">PVIObject</a></code></dd>
</dl>
</li>
</ul>
<a name="getSketchType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSketchType</h4>
<pre>public final&nbsp;int&nbsp;getSketchType()</pre>
<div class="block"><p>
 Returns or sets the sketch type.
 Read/write <a href="../../../com/aspose/slides/LineSketchType.html" title="class in com.aspose.slides"><code>LineSketchType</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISketchFormat.html#getSketchType--">getSketchType</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISketchFormat.html" title="interface in com.aspose.slides">ISketchFormat</a></code></dd>
</dl>
</li>
</ul>
<a name="setSketchType-int-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setSketchType</h4>
<pre>public final&nbsp;void&nbsp;setSketchType(int&nbsp;value)</pre>
<div class="block"><p>
 Returns or sets the sketch type.
 Read/write <a href="../../../com/aspose/slides/LineSketchType.html" title="class in com.aspose.slides"><code>LineSketchType</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISketchFormat.html#setSketchType-int-">setSketchType</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISketchFormat.html" title="interface in com.aspose.slides">ISketchFormat</a></code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/SideDirectionTransition.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/Slide.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/SketchFormat.html" target="_top">Frames</a></li>
<li><a href="SketchFormat.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><i>Copyright &copy; 2004-2025 Aspose Pty Ltd. All Rights Reserved.</i></small></p>
</body>
</html>
