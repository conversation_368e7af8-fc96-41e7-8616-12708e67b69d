<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_421) on Fri Jul 11 05:32:44 CDT 2025 -->
<title>SmartArtColorType (Aspose.Slides for Java (25.7) API Documentation)</title>
<meta name="date" content="2025-07-11">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="SmartArtColorType (Aspose.Slides for Java (25.7) API Documentation)";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/SmartArt.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/SmartArtLayoutType.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/SmartArtColorType.html" target="_top">Frames</a></li>
<li><a href="SmartArtColorType.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.classes.inherited.from.class.com.aspose.ms.System.Enum">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#methods.inherited.from.class.com.aspose.ms.System.Enum">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li>Method</li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.aspose.slides</div>
<h2 title="Class SmartArtColorType" class="title">Class SmartArtColorType</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.aspose.ms.System.ValueType&lt;com.aspose.ms.System.Enum&gt;</li>
<li>
<ul class="inheritance">
<li>com.aspose.ms.System.Enum</li>
<li>
<ul class="inheritance">
<li>com.aspose.slides.SmartArtColorType</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public final class <span class="typeNameLabel">SmartArtColorType</span>
extends com.aspose.ms.System.Enum</pre>
<div class="block"><p>
 Represents color scheme of a SmartArt diagram.
 </p></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.com.aspose.ms.System.Enum">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from class&nbsp;com.aspose.ms.System.Enum</h3>
<code>com.aspose.ms.System.Enum.AbstractEnum, com.aspose.ms.System.Enum.FlaggedEnum, com.aspose.ms.System.Enum.ObjectEnum, com.aspose.ms.System.Enum.SimpleEnum</code></li>
</ul>
</li>
</ul>
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtColorType.html#ColoredFillAccent1">ColoredFillAccent1</a></span></code>
<div class="block">
 ColoredFillAccent1</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtColorType.html#ColoredFillAccent2">ColoredFillAccent2</a></span></code>
<div class="block">
 ColoredFillAccent2</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtColorType.html#ColoredFillAccent3">ColoredFillAccent3</a></span></code>
<div class="block">
 ColoredFillAccent3</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtColorType.html#ColoredFillAccent4">ColoredFillAccent4</a></span></code>
<div class="block">
 ColoredFillAccent4</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtColorType.html#ColoredFillAccent5">ColoredFillAccent5</a></span></code>
<div class="block">
 ColoredFillAccent5</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtColorType.html#ColoredFillAccent6">ColoredFillAccent6</a></span></code>
<div class="block">
 ColoredFillAccent6</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtColorType.html#ColoredOutlineAccent1">ColoredOutlineAccent1</a></span></code>
<div class="block">
 ColoredOutlineAccent1</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtColorType.html#ColoredOutlineAccent2">ColoredOutlineAccent2</a></span></code>
<div class="block">
 ColoredOutlineAccent2</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtColorType.html#ColoredOutlineAccent3">ColoredOutlineAccent3</a></span></code>
<div class="block">
 ColoredOutlineAccent3</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtColorType.html#ColoredOutlineAccent4">ColoredOutlineAccent4</a></span></code>
<div class="block">
 ColoredOutlineAccent4</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtColorType.html#ColoredOutlineAccent5">ColoredOutlineAccent5</a></span></code>
<div class="block">
 ColoredOutlineAccent5</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtColorType.html#ColoredOutlineAccent6">ColoredOutlineAccent6</a></span></code>
<div class="block">
 ColoredOutlineAccent6</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtColorType.html#ColorfulAccentColors">ColorfulAccentColors</a></span></code>
<div class="block">
 ColorfulAccentColors</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtColorType.html#ColorfulAccentColors2to3">ColorfulAccentColors2to3</a></span></code>
<div class="block">
 ColorfulAccentColors2to3</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtColorType.html#ColorfulAccentColors3to4">ColorfulAccentColors3to4</a></span></code>
<div class="block">
 ColorfulAccentColors3to4</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtColorType.html#ColorfulAccentColors4to5">ColorfulAccentColors4to5</a></span></code>
<div class="block">
 ColorfulAccentColors4to5</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtColorType.html#ColorfulAccentColors5to6">ColorfulAccentColors5to6</a></span></code>
<div class="block">
 ColorfulAccentColors5to6</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtColorType.html#Dark1Outline">Dark1Outline</a></span></code>
<div class="block">
 Dark1Outline</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtColorType.html#Dark2Outline">Dark2Outline</a></span></code>
<div class="block">
 Dark2Outline</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtColorType.html#DarkFill">DarkFill</a></span></code>
<div class="block">
 DarkFill</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtColorType.html#GradientLoopAccent1">GradientLoopAccent1</a></span></code>
<div class="block">
 GradientLoopAccent1</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtColorType.html#GradientLoopAccent2">GradientLoopAccent2</a></span></code>
<div class="block">
 GradientLoopAccent2</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtColorType.html#GradientLoopAccent3">GradientLoopAccent3</a></span></code>
<div class="block">
 GradientLoopAccent3</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtColorType.html#GradientLoopAccent4">GradientLoopAccent4</a></span></code>
<div class="block">
 GradientLoopAccent4</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtColorType.html#GradientLoopAccent5">GradientLoopAccent5</a></span></code>
<div class="block">
 GradientLoopAccent5</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtColorType.html#GradientLoopAccent6">GradientLoopAccent6</a></span></code>
<div class="block">
 GradientLoopAccent6</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtColorType.html#GradientRangeAccent1">GradientRangeAccent1</a></span></code>
<div class="block">
 GradientRangeAccent1</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtColorType.html#GradientRangeAccent2">GradientRangeAccent2</a></span></code>
<div class="block">
 GradientRangeAccent2</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtColorType.html#GradientRangeAccent3">GradientRangeAccent3</a></span></code>
<div class="block">
 GradientRangeAccent3</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtColorType.html#GradientRangeAccent4">GradientRangeAccent4</a></span></code>
<div class="block">
 GradientRangeAccent4</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtColorType.html#GradientRangeAccent5">GradientRangeAccent5</a></span></code>
<div class="block">
 GradientRangeAccent5</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtColorType.html#GradientRangeAccent6">GradientRangeAccent6</a></span></code>
<div class="block">
 GradientRangeAccent6</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtColorType.html#TransparentGradientRangeAccent1">TransparentGradientRangeAccent1</a></span></code>
<div class="block">
 TransparentGradientRangeAccent1</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtColorType.html#TransparentGradientRangeAccent2">TransparentGradientRangeAccent2</a></span></code>
<div class="block">
 TransparentGradientRangeAccent2</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtColorType.html#TransparentGradientRangeAccent3">TransparentGradientRangeAccent3</a></span></code>
<div class="block">
 TransparentGradientRangeAccent3</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtColorType.html#TransparentGradientRangeAccent4">TransparentGradientRangeAccent4</a></span></code>
<div class="block">
 TransparentGradientRangeAccent4</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtColorType.html#TransparentGradientRangeAccent5">TransparentGradientRangeAccent5</a></span></code>
<div class="block">
 TransparentGradientRangeAccent5</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtColorType.html#TransparentGradientRangeAccent6">TransparentGradientRangeAccent6</a></span></code>
<div class="block">
 TransparentGradientRangeAccent6</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.com.aspose.ms.System.Enum">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;com.aspose.ms.System.Enum</h3>
<code>EnumSeparatorCharArray</code></li>
</ul>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.aspose.ms.System.Enum">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;com.aspose.ms.System.Enum</h3>
<code>Clone, CloneTo, format, format, get_Caption, get_Value, getName, getName, getNames, getNames, getUnderlyingType, getUnderlyingType, getValue, getValues, isDefined, isDefined, isDefined, isDefined, parse, parse, parse, parse, register, toObject, toString</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="Dark1Outline">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Dark1Outline</h4>
<pre>public static final&nbsp;int Dark1Outline</pre>
<div class="block"><p>
 Dark1Outline
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtColorType.Dark1Outline">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Dark2Outline">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Dark2Outline</h4>
<pre>public static final&nbsp;int Dark2Outline</pre>
<div class="block"><p>
 Dark2Outline
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtColorType.Dark2Outline">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DarkFill">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DarkFill</h4>
<pre>public static final&nbsp;int DarkFill</pre>
<div class="block"><p>
 DarkFill
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtColorType.DarkFill">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="ColorfulAccentColors">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ColorfulAccentColors</h4>
<pre>public static final&nbsp;int ColorfulAccentColors</pre>
<div class="block"><p>
 ColorfulAccentColors
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtColorType.ColorfulAccentColors">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="ColorfulAccentColors2to3">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ColorfulAccentColors2to3</h4>
<pre>public static final&nbsp;int ColorfulAccentColors2to3</pre>
<div class="block"><p>
 ColorfulAccentColors2to3
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtColorType.ColorfulAccentColors2to3">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="ColorfulAccentColors3to4">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ColorfulAccentColors3to4</h4>
<pre>public static final&nbsp;int ColorfulAccentColors3to4</pre>
<div class="block"><p>
 ColorfulAccentColors3to4
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtColorType.ColorfulAccentColors3to4">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="ColorfulAccentColors4to5">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ColorfulAccentColors4to5</h4>
<pre>public static final&nbsp;int ColorfulAccentColors4to5</pre>
<div class="block"><p>
 ColorfulAccentColors4to5
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtColorType.ColorfulAccentColors4to5">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="ColorfulAccentColors5to6">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ColorfulAccentColors5to6</h4>
<pre>public static final&nbsp;int ColorfulAccentColors5to6</pre>
<div class="block"><p>
 ColorfulAccentColors5to6
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtColorType.ColorfulAccentColors5to6">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="ColoredOutlineAccent1">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ColoredOutlineAccent1</h4>
<pre>public static final&nbsp;int ColoredOutlineAccent1</pre>
<div class="block"><p>
 ColoredOutlineAccent1
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtColorType.ColoredOutlineAccent1">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="ColoredFillAccent1">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ColoredFillAccent1</h4>
<pre>public static final&nbsp;int ColoredFillAccent1</pre>
<div class="block"><p>
 ColoredFillAccent1
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtColorType.ColoredFillAccent1">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="GradientRangeAccent1">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>GradientRangeAccent1</h4>
<pre>public static final&nbsp;int GradientRangeAccent1</pre>
<div class="block"><p>
 GradientRangeAccent1
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtColorType.GradientRangeAccent1">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="GradientLoopAccent1">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>GradientLoopAccent1</h4>
<pre>public static final&nbsp;int GradientLoopAccent1</pre>
<div class="block"><p>
 GradientLoopAccent1
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtColorType.GradientLoopAccent1">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="TransparentGradientRangeAccent1">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TransparentGradientRangeAccent1</h4>
<pre>public static final&nbsp;int TransparentGradientRangeAccent1</pre>
<div class="block"><p>
 TransparentGradientRangeAccent1
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtColorType.TransparentGradientRangeAccent1">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="ColoredOutlineAccent2">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ColoredOutlineAccent2</h4>
<pre>public static final&nbsp;int ColoredOutlineAccent2</pre>
<div class="block"><p>
 ColoredOutlineAccent2
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtColorType.ColoredOutlineAccent2">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="ColoredFillAccent2">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ColoredFillAccent2</h4>
<pre>public static final&nbsp;int ColoredFillAccent2</pre>
<div class="block"><p>
 ColoredFillAccent2
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtColorType.ColoredFillAccent2">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="GradientRangeAccent2">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>GradientRangeAccent2</h4>
<pre>public static final&nbsp;int GradientRangeAccent2</pre>
<div class="block"><p>
 GradientRangeAccent2
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtColorType.GradientRangeAccent2">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="GradientLoopAccent2">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>GradientLoopAccent2</h4>
<pre>public static final&nbsp;int GradientLoopAccent2</pre>
<div class="block"><p>
 GradientLoopAccent2
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtColorType.GradientLoopAccent2">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="TransparentGradientRangeAccent2">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TransparentGradientRangeAccent2</h4>
<pre>public static final&nbsp;int TransparentGradientRangeAccent2</pre>
<div class="block"><p>
 TransparentGradientRangeAccent2
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtColorType.TransparentGradientRangeAccent2">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="ColoredOutlineAccent3">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ColoredOutlineAccent3</h4>
<pre>public static final&nbsp;int ColoredOutlineAccent3</pre>
<div class="block"><p>
 ColoredOutlineAccent3
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtColorType.ColoredOutlineAccent3">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="ColoredFillAccent3">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ColoredFillAccent3</h4>
<pre>public static final&nbsp;int ColoredFillAccent3</pre>
<div class="block"><p>
 ColoredFillAccent3
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtColorType.ColoredFillAccent3">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="GradientRangeAccent3">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>GradientRangeAccent3</h4>
<pre>public static final&nbsp;int GradientRangeAccent3</pre>
<div class="block"><p>
 GradientRangeAccent3
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtColorType.GradientRangeAccent3">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="GradientLoopAccent3">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>GradientLoopAccent3</h4>
<pre>public static final&nbsp;int GradientLoopAccent3</pre>
<div class="block"><p>
 GradientLoopAccent3
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtColorType.GradientLoopAccent3">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="TransparentGradientRangeAccent3">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TransparentGradientRangeAccent3</h4>
<pre>public static final&nbsp;int TransparentGradientRangeAccent3</pre>
<div class="block"><p>
 TransparentGradientRangeAccent3
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtColorType.TransparentGradientRangeAccent3">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="ColoredOutlineAccent4">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ColoredOutlineAccent4</h4>
<pre>public static final&nbsp;int ColoredOutlineAccent4</pre>
<div class="block"><p>
 ColoredOutlineAccent4
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtColorType.ColoredOutlineAccent4">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="ColoredFillAccent4">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ColoredFillAccent4</h4>
<pre>public static final&nbsp;int ColoredFillAccent4</pre>
<div class="block"><p>
 ColoredFillAccent4
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtColorType.ColoredFillAccent4">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="GradientRangeAccent4">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>GradientRangeAccent4</h4>
<pre>public static final&nbsp;int GradientRangeAccent4</pre>
<div class="block"><p>
 GradientRangeAccent4
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtColorType.GradientRangeAccent4">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="GradientLoopAccent4">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>GradientLoopAccent4</h4>
<pre>public static final&nbsp;int GradientLoopAccent4</pre>
<div class="block"><p>
 GradientLoopAccent4
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtColorType.GradientLoopAccent4">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="TransparentGradientRangeAccent4">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TransparentGradientRangeAccent4</h4>
<pre>public static final&nbsp;int TransparentGradientRangeAccent4</pre>
<div class="block"><p>
 TransparentGradientRangeAccent4
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtColorType.TransparentGradientRangeAccent4">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="ColoredOutlineAccent5">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ColoredOutlineAccent5</h4>
<pre>public static final&nbsp;int ColoredOutlineAccent5</pre>
<div class="block"><p>
 ColoredOutlineAccent5
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtColorType.ColoredOutlineAccent5">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="ColoredFillAccent5">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ColoredFillAccent5</h4>
<pre>public static final&nbsp;int ColoredFillAccent5</pre>
<div class="block"><p>
 ColoredFillAccent5
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtColorType.ColoredFillAccent5">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="GradientRangeAccent5">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>GradientRangeAccent5</h4>
<pre>public static final&nbsp;int GradientRangeAccent5</pre>
<div class="block"><p>
 GradientRangeAccent5
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtColorType.GradientRangeAccent5">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="GradientLoopAccent5">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>GradientLoopAccent5</h4>
<pre>public static final&nbsp;int GradientLoopAccent5</pre>
<div class="block"><p>
 GradientLoopAccent5
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtColorType.GradientLoopAccent5">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="TransparentGradientRangeAccent5">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TransparentGradientRangeAccent5</h4>
<pre>public static final&nbsp;int TransparentGradientRangeAccent5</pre>
<div class="block"><p>
 TransparentGradientRangeAccent5
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtColorType.TransparentGradientRangeAccent5">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="ColoredOutlineAccent6">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ColoredOutlineAccent6</h4>
<pre>public static final&nbsp;int ColoredOutlineAccent6</pre>
<div class="block"><p>
 ColoredOutlineAccent6
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtColorType.ColoredOutlineAccent6">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="ColoredFillAccent6">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ColoredFillAccent6</h4>
<pre>public static final&nbsp;int ColoredFillAccent6</pre>
<div class="block"><p>
 ColoredFillAccent6
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtColorType.ColoredFillAccent6">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="GradientRangeAccent6">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>GradientRangeAccent6</h4>
<pre>public static final&nbsp;int GradientRangeAccent6</pre>
<div class="block"><p>
 GradientRangeAccent6
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtColorType.GradientRangeAccent6">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="GradientLoopAccent6">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>GradientLoopAccent6</h4>
<pre>public static final&nbsp;int GradientLoopAccent6</pre>
<div class="block"><p>
 GradientLoopAccent6
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtColorType.GradientLoopAccent6">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="TransparentGradientRangeAccent6">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>TransparentGradientRangeAccent6</h4>
<pre>public static final&nbsp;int TransparentGradientRangeAccent6</pre>
<div class="block"><p>
 TransparentGradientRangeAccent6
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtColorType.TransparentGradientRangeAccent6">Constant Field Values</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/SmartArt.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/SmartArtLayoutType.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/SmartArtColorType.html" target="_top">Frames</a></li>
<li><a href="SmartArtColorType.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.classes.inherited.from.class.com.aspose.ms.System.Enum">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#methods.inherited.from.class.com.aspose.ms.System.Enum">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li>Method</li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><i>Copyright &copy; 2004-2025 Aspose Pty Ltd. All Rights Reserved.</i></small></p>
</body>
</html>
