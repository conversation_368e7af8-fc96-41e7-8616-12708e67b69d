<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_421) on Fri Jul 11 05:32:45 CDT 2025 -->
<title>ViewProperties (Aspose.Slides for Java (25.7) API Documentation)</title>
<meta name="date" content="2025-07-11">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ViewProperties (Aspose.Slides for Java (25.7) API Documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/VideoPlayModePreset.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/ViewType.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/ViewProperties.html" target="_top">Frames</a></li>
<li><a href="ViewProperties.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.aspose.slides</div>
<h2 title="Class ViewProperties" class="title">Class ViewProperties</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.aspose.slides.ViewProperties</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../com/aspose/slides/IViewProperties.html" title="interface in com.aspose.slides">IViewProperties</a></dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">ViewProperties</span>
extends java.lang.Object
implements <a href="../../../com/aspose/slides/IViewProperties.html" title="interface in com.aspose.slides">IViewProperties</a></pre>
<div class="block"><p>
 Presentation wide view properties.
 </p></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ViewProperties.html#getGridSpacing--">getGridSpacing</a></span>()</code>
<div class="block">
 Returns or sets the grid spacing that should be used for the grid underlying the presentation document, in points.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ViewProperties.html#getLastView--">getLastView</a></span>()</code>
<div class="block">
 Specifies the view mode that was used when the presentation document was last saved.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/INormalViewProperties.html" title="interface in com.aspose.slides">INormalViewProperties</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ViewProperties.html#getNormalViewProperties--">getNormalViewProperties</a></span>()</code>
<div class="block">
 Represents normal view properties.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/ICommonSlideViewProperties.html" title="interface in com.aspose.slides">ICommonSlideViewProperties</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ViewProperties.html#getNotesViewProperties--">getNotesViewProperties</a></span>()</code>
<div class="block">
 Specifies common view properties associated with the notes view mode.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>com.aspose.slides.IDOMObject</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ViewProperties.html#getParent_Immediate--">getParent_Immediate</a></span>()</code>
<div class="block">
 Returns Parent_Immediate object.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>byte</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ViewProperties.html#getShowComments--">getShowComments</a></span>()</code>
<div class="block">
 Specifies whether the slide comments should be shown.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/ICommonSlideViewProperties.html" title="interface in com.aspose.slides">ICommonSlideViewProperties</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ViewProperties.html#getSlideViewProperties--">getSlideViewProperties</a></span>()</code>
<div class="block">
 Specifies common view properties associated with the slide view mode.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ViewProperties.html#setGridSpacing-float-">setGridSpacing</a></span>(float&nbsp;value)</code>
<div class="block">
 Returns or sets the grid spacing that should be used for the grid underlying the presentation document, in points.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ViewProperties.html#setLastView-int-">setLastView</a></span>(int&nbsp;value)</code>
<div class="block">
 Specifies the view mode that was used when the presentation document was last saved.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ViewProperties.html#setShowComments-byte-">setShowComments</a></span>(byte&nbsp;value)</code>
<div class="block">
 Specifies whether the slide comments should be shown.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getLastView--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLastView</h4>
<pre>public final&nbsp;int&nbsp;getLastView()</pre>
<div class="block"><p>
 Specifies the view mode that was used when the presentation document was last saved.
 Read/write <a href="../../../com/aspose/slides/ViewType.html" title="class in com.aspose.slides"><code>ViewType</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IViewProperties.html#getLastView--">getLastView</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IViewProperties.html" title="interface in com.aspose.slides">IViewProperties</a></code></dd>
</dl>
</li>
</ul>
<a name="setLastView-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLastView</h4>
<pre>public final&nbsp;void&nbsp;setLastView(int&nbsp;value)</pre>
<div class="block"><p>
 Specifies the view mode that was used when the presentation document was last saved.
 Read/write <a href="../../../com/aspose/slides/ViewType.html" title="class in com.aspose.slides"><code>ViewType</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IViewProperties.html#setLastView-int-">setLastView</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IViewProperties.html" title="interface in com.aspose.slides">IViewProperties</a></code></dd>
</dl>
</li>
</ul>
<a name="getShowComments--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getShowComments</h4>
<pre>public final&nbsp;byte&nbsp;getShowComments()</pre>
<div class="block"><p>
 Specifies whether the slide comments should be shown.
 Read/write <a href="../../../com/aspose/slides/NullableBool.html" title="class in com.aspose.slides"><code>NullableBool</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IViewProperties.html#getShowComments--">getShowComments</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IViewProperties.html" title="interface in com.aspose.slides">IViewProperties</a></code></dd>
</dl>
</li>
</ul>
<a name="setShowComments-byte-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setShowComments</h4>
<pre>public final&nbsp;void&nbsp;setShowComments(byte&nbsp;value)</pre>
<div class="block"><p>
 Specifies whether the slide comments should be shown.
 Read/write <a href="../../../com/aspose/slides/NullableBool.html" title="class in com.aspose.slides"><code>NullableBool</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IViewProperties.html#setShowComments-byte-">setShowComments</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IViewProperties.html" title="interface in com.aspose.slides">IViewProperties</a></code></dd>
</dl>
</li>
</ul>
<a name="getNormalViewProperties--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNormalViewProperties</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/INormalViewProperties.html" title="interface in com.aspose.slides">INormalViewProperties</a>&nbsp;getNormalViewProperties()</pre>
<div class="block"><p>
 Represents normal view properties. The normal view consists of
 three content regions: the slide itself, a side content region, and a bottom content region.
 Read-only <a href="../../../com/aspose/slides/INormalViewProperties.html" title="interface in com.aspose.slides"><code>INormalViewProperties</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IViewProperties.html#getNormalViewProperties--">getNormalViewProperties</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IViewProperties.html" title="interface in com.aspose.slides">IViewProperties</a></code></dd>
</dl>
</li>
</ul>
<a name="getSlideViewProperties--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSlideViewProperties</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/ICommonSlideViewProperties.html" title="interface in com.aspose.slides">ICommonSlideViewProperties</a>&nbsp;getSlideViewProperties()</pre>
<div class="block"><p>
 Specifies common view properties associated with the slide view mode.
 Read-only <a href="../../../com/aspose/slides/ICommonSlideViewProperties.html" title="interface in com.aspose.slides"><code>ICommonSlideViewProperties</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IViewProperties.html#getSlideViewProperties--">getSlideViewProperties</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IViewProperties.html" title="interface in com.aspose.slides">IViewProperties</a></code></dd>
</dl>
</li>
</ul>
<a name="getNotesViewProperties--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNotesViewProperties</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/ICommonSlideViewProperties.html" title="interface in com.aspose.slides">ICommonSlideViewProperties</a>&nbsp;getNotesViewProperties()</pre>
<div class="block"><p>
 Specifies common view properties associated with the notes view mode.
 Read-only <a href="../../../com/aspose/slides/ICommonSlideViewProperties.html" title="interface in com.aspose.slides"><code>ICommonSlideViewProperties</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IViewProperties.html#getNotesViewProperties--">getNotesViewProperties</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IViewProperties.html" title="interface in com.aspose.slides">IViewProperties</a></code></dd>
</dl>
</li>
</ul>
<a name="getGridSpacing--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getGridSpacing</h4>
<pre>public final&nbsp;float&nbsp;getGridSpacing()</pre>
<div class="block"><p>
 Returns or sets the grid spacing that should be used for the grid underlying the presentation document, in points. 
 Read/write float.
 </p><p><hr><blockquote><pre>
 The following sample code shows how to change the grid spacing in a PowerPoint presentation.
 <pre>
 Presentation pres = new Presentation();
 try {
     pres.getViewProperties().setGridSpacing(72f);
     pres.save("GridSpacing_out.pptx", SaveFormat.Pptx);
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p><p><hr>
 The grid spacing value must be a positive number .
 The typical value range is from 1 mm (2.8349607 points) to 2 inches (144 points).
 </hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IViewProperties.html#getGridSpacing--">getGridSpacing</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IViewProperties.html" title="interface in com.aspose.slides">IViewProperties</a></code></dd>
</dl>
</li>
</ul>
<a name="setGridSpacing-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setGridSpacing</h4>
<pre>public final&nbsp;void&nbsp;setGridSpacing(float&nbsp;value)</pre>
<div class="block"><p>
 Returns or sets the grid spacing that should be used for the grid underlying the presentation document, in points. 
 Read/write float.
 </p><p><hr><blockquote><pre>
 The following sample code shows how to change the grid spacing in a PowerPoint presentation.
 <pre>
 Presentation pres = new Presentation();
 try {
     pres.getViewProperties().setGridSpacing(72f);
     pres.save("GridSpacing_out.pptx", SaveFormat.Pptx);
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p><p><hr>
 The grid spacing value must be a positive number .
 The typical value range is from 1 mm (2.8349607 points) to 2 inches (144 points).
 </hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IViewProperties.html#setGridSpacing-float-">setGridSpacing</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IViewProperties.html" title="interface in com.aspose.slides">IViewProperties</a></code></dd>
</dl>
</li>
</ul>
<a name="getParent_Immediate--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getParent_Immediate</h4>
<pre>public final&nbsp;com.aspose.slides.IDOMObject&nbsp;getParent_Immediate()</pre>
<div class="block"><p>
 Returns Parent_Immediate object.
 Read-only <code>IDOMObject</code>.
 </p></div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/VideoPlayModePreset.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/ViewType.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/ViewProperties.html" target="_top">Frames</a></li>
<li><a href="ViewProperties.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><i>Copyright &copy; 2004-2025 Aspose Pty Ltd. All Rights Reserved.</i></small></p>
</body>
</html>
