<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_421) on Fri Jul 11 05:32:44 CDT 2025 -->
<title>TemplateContext (Aspose.Slides for Java (25.7) API Documentation)</title>
<meta name="date" content="2025-07-11">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="TemplateContext (Aspose.Slides for Java (25.7) API Documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/TagCollection.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/TextAlignment.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/TemplateContext.html" target="_top">Frames</a></li>
<li><a href="TemplateContext.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.aspose.slides</div>
<h2 title="Class TemplateContext" class="title">Class TemplateContext&lt;TObject&gt;</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.aspose.slides.TemplateContext&lt;TObject&gt;</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public final class <span class="typeNameLabel">TemplateContext&lt;TObject&gt;</span>
extends java.lang.Object</pre>
<div class="block"><p>
 Represents a model object interface for a template engine.
 </p></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/Storage.html" title="class in com.aspose.slides">Storage</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TemplateContext.html#getGlobal--">getGlobal</a></span>()</code>
<div class="block">
 Returns global storage of the host document.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/Storage.html" title="class in com.aspose.slides">Storage</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TemplateContext.html#getLocal--">getLocal</a></span>()</code>
<div class="block">
 Returns local storage of the current template context.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/TemplateContext.html" title="type parameter in TemplateContext">TObject</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TemplateContext.html#getObject--">getObject</a></span>()</code>
<div class="block">
 Returns the model object.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/Output.html" title="class in com.aspose.slides">Output</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TemplateContext.html#getOutput--">getOutput</a></span>()</code>
<div class="block">
 Returns collection of output elements of the host document.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>&lt;TSubModel&gt;<br><a href="../../../com/aspose/slides/TemplateContext.html" title="class in com.aspose.slides">TemplateContext</a>&lt;TSubModel&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TemplateContext.html#subModel-TSubModel-">subModel</a></span>(TSubModel&nbsp;subModel)</code>
<div class="block">
 Creates a child template context.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="subModel-java.lang.Object-">
<!--   -->
</a><a name="subModel-TSubModel-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>subModel</h4>
<pre>public final&nbsp;&lt;TSubModel&gt;&nbsp;<a href="../../../com/aspose/slides/TemplateContext.html" title="class in com.aspose.slides">TemplateContext</a>&lt;TSubModel&gt;&nbsp;subModel(TSubModel&nbsp;subModel)</pre>
<div class="block"><p>
 Creates a child template context.
 </p></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>subModel</code> - Child model object.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>New template context with given model and parent's output collection and global storage.</dd>
</dl>
</li>
</ul>
<a name="getObject--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getObject</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/TemplateContext.html" title="type parameter in TemplateContext">TObject</a>&nbsp;getObject()</pre>
<div class="block"><p>
 Returns the model object.
 Read-only <code>Object</code>.
 </p></div>
</li>
</ul>
<a name="getOutput--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getOutput</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/Output.html" title="class in com.aspose.slides">Output</a>&nbsp;getOutput()</pre>
<div class="block"><p>
 Returns collection of output elements of the host document.
 Read-only <a href="../../../com/aspose/slides/Output.html" title="class in com.aspose.slides"><code>Output</code></a>(<a href="../../../com/aspose/slides/TemplateContext.html#getOutput--"><code>getOutput()</code></a>).
 </p></div>
</li>
</ul>
<a name="getLocal--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLocal</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/Storage.html" title="class in com.aspose.slides">Storage</a>&nbsp;getLocal()</pre>
<div class="block"><p>
 Returns local storage of the current template context.
 Read-only <a href="../../../com/aspose/slides/Storage.html" title="class in com.aspose.slides"><code>Storage</code></a>.
 </p></div>
</li>
</ul>
<a name="getGlobal--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getGlobal</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/Storage.html" title="class in com.aspose.slides">Storage</a>&nbsp;getGlobal()</pre>
<div class="block"><p>
 Returns global storage of the host document.
 Read-only <a href="../../../com/aspose/slides/Storage.html" title="class in com.aspose.slides"><code>Storage</code></a>.
 </p></div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/TagCollection.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/TextAlignment.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/TemplateContext.html" target="_top">Frames</a></li>
<li><a href="TemplateContext.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><i>Copyright &copy; 2004-2025 Aspose Pty Ltd. All Rights Reserved.</i></small></p>
</body>
</html>
