<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_421) on Fri Jul 11 05:32:44 CDT 2025 -->
<title>SectionCollection (Aspose.Slides for Java (25.7) API Documentation)</title>
<meta name="date" content="2025-07-11">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="SectionCollection (Aspose.Slides for Java (25.7) API Documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/Section.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/SectionSlideCollection.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/SectionCollection.html" target="_top">Frames</a></li>
<li><a href="SectionCollection.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.aspose.slides</div>
<h2 title="Class SectionCollection" class="title">Class SectionCollection</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../com/aspose/slides/DomObject.html" title="class in com.aspose.slides">com.aspose.slides.DomObject</a>&lt;<a href="../../../com/aspose/slides/Presentation.html" title="class in com.aspose.slides">Presentation</a>&gt;</li>
<li>
<ul class="inheritance">
<li>com.aspose.slides.SectionCollection</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd>com.aspose.ms.System.Collections.Generic.IGenericEnumerable&lt;<a href="../../../com/aspose/slides/ISection.html" title="interface in com.aspose.slides">ISection</a>&gt;, com.aspose.ms.System.Collections.ICollection&lt;<a href="../../../com/aspose/slides/ISection.html" title="interface in com.aspose.slides">ISection</a>&gt;, com.aspose.ms.System.Collections.IEnumerable&lt;<a href="../../../com/aspose/slides/ISection.html" title="interface in com.aspose.slides">ISection</a>&gt;, <a href="../../../com/aspose/slides/IGenericCollection.html" title="interface in com.aspose.slides">IGenericCollection</a>&lt;<a href="../../../com/aspose/slides/ISection.html" title="interface in com.aspose.slides">ISection</a>&gt;, <a href="../../../com/aspose/slides/ISectionCollection.html" title="interface in com.aspose.slides">ISectionCollection</a>, java.lang.Iterable&lt;<a href="../../../com/aspose/slides/ISection.html" title="interface in com.aspose.slides">ISection</a>&gt;</dd>
</dl>
<hr>
<br>
<pre>public final class <span class="typeNameLabel">SectionCollection</span>
extends <a href="../../../com/aspose/slides/DomObject.html" title="class in com.aspose.slides">DomObject</a>&lt;<a href="../../../com/aspose/slides/Presentation.html" title="class in com.aspose.slides">Presentation</a>&gt;
implements <a href="../../../com/aspose/slides/ISectionCollection.html" title="interface in com.aspose.slides">ISectionCollection</a></pre>
<div class="block"><p>
 Represents a collection of sections.
 </p></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/ISection.html" title="interface in com.aspose.slides">ISection</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SectionCollection.html#addEmptySection-java.lang.String-int-">addEmptySection</a></span>(java.lang.String&nbsp;name,
               int&nbsp;index)</code>
<div class="block">
 Add empty section to specified position of the collection.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/ISection.html" title="interface in com.aspose.slides">ISection</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SectionCollection.html#addSection-java.lang.String-com.aspose.slides.ISlide-">addSection</a></span>(java.lang.String&nbsp;name,
          <a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a>&nbsp;startedFromSlide)</code>
<div class="block">
 Add slides section started form specific slide.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/ISection.html" title="interface in com.aspose.slides">ISection</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SectionCollection.html#appendEmptySection-java.lang.String-">appendEmptySection</a></span>(java.lang.String&nbsp;name)</code>
<div class="block">
 Add empty section to the end of the collection.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SectionCollection.html#clear--">clear</a></span>()</code>
<div class="block">
 Removes all sections from the collection.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SectionCollection.html#copyTo-com.aspose.ms.System.Array-int-">copyTo</a></span>(com.aspose.ms.System.Array&nbsp;array,
      int&nbsp;index)</code>
<div class="block">
 Copies the entire collection to the specified array.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/ISection.html" title="interface in com.aspose.slides">ISection</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SectionCollection.html#get_Item-int-">get_Item</a></span>(int&nbsp;index)</code>
<div class="block">
 Gets the element at the specified index.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>java.lang.Object</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SectionCollection.html#getSyncRoot--">getSyncRoot</a></span>()</code>
<div class="block">
 Returns a synchronization root.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SectionCollection.html#indexOf-com.aspose.slides.ISection-">indexOf</a></span>(<a href="../../../com/aspose/slides/ISection.html" title="interface in com.aspose.slides">ISection</a>&nbsp;section)</code>
<div class="block">
 Returns an index of the specified section in the collection.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SectionCollection.html#isSynchronized--">isSynchronized</a></span>()</code>
<div class="block">
 Returns a value indicating whether access to the collection is synchronized (thread-safe).</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>com.aspose.ms.System.Collections.Generic.IGenericEnumerator&lt;<a href="../../../com/aspose/slides/ISection.html" title="interface in com.aspose.slides">ISection</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SectionCollection.html#iterator--">iterator</a></span>()</code>
<div class="block">
 Returns an enumerator that iterates through the collection.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>com.aspose.ms.System.Collections.Generic.IGenericEnumerator&lt;<a href="../../../com/aspose/slides/ISection.html" title="interface in com.aspose.slides">ISection</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SectionCollection.html#iteratorJava--">iteratorJava</a></span>()</code>
<div class="block">
 Returns a java iterator for the entire collection.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SectionCollection.html#removeSection-com.aspose.slides.ISection-">removeSection</a></span>(<a href="../../../com/aspose/slides/ISection.html" title="interface in com.aspose.slides">ISection</a>&nbsp;section)</code>
<div class="block">
 Remove section.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SectionCollection.html#removeSectionWithSlides-com.aspose.slides.ISection-">removeSectionWithSlides</a></span>(<a href="../../../com/aspose/slides/ISection.html" title="interface in com.aspose.slides">ISection</a>&nbsp;section)</code>
<div class="block">
 Remove section and slides contained in the section.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SectionCollection.html#reorderSectionWithSlides-com.aspose.slides.ISection-int-">reorderSectionWithSlides</a></span>(<a href="../../../com/aspose/slides/ISection.html" title="interface in com.aspose.slides">ISection</a>&nbsp;section,
                        int&nbsp;index)</code>
<div class="block">
 Moves section and its slides from the collection to the specified position.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SectionCollection.html#size--">size</a></span>()</code>
<div class="block">
 Gets the number of elements actually contained in the collection.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.aspose.slides.DomObject">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;com.aspose.slides.<a href="../../../com/aspose/slides/DomObject.html" title="class in com.aspose.slides">DomObject</a></h3>
<code><a href="../../../com/aspose/slides/DomObject.html#getParent_Immediate--">getParent_Immediate</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Iterable">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;java.lang.Iterable</h3>
<code>forEach, spliterator</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="get_Item-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_Item</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/ISection.html" title="interface in com.aspose.slides">ISection</a>&nbsp;get_Item(int&nbsp;index)</pre>
<div class="block"><p>
 Gets the element at the specified index.
 Read-only <a href="../../../com/aspose/slides/ISection.html" title="interface in com.aspose.slides"><code>ISection</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISectionCollection.html#get_Item-int-">get_Item</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISectionCollection.html" title="interface in com.aspose.slides">ISectionCollection</a></code></dd>
</dl>
</li>
</ul>
<a name="addSection-java.lang.String-com.aspose.slides.ISlide-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addSection</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/ISection.html" title="interface in com.aspose.slides">ISection</a>&nbsp;addSection(java.lang.String&nbsp;name,
                                 <a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a>&nbsp;startedFromSlide)</pre>
<div class="block"><p>
 Add slides section started form specific slide.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISectionCollection.html#addSection-java.lang.String-com.aspose.slides.ISlide-">addSection</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISectionCollection.html" title="interface in com.aspose.slides">ISectionCollection</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - Name of the section</dd>
<dd><code>startedFromSlide</code> - First slide of section</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Added section.</dd>
</dl>
</li>
</ul>
<a name="appendEmptySection-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>appendEmptySection</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/ISection.html" title="interface in com.aspose.slides">ISection</a>&nbsp;appendEmptySection(java.lang.String&nbsp;name)</pre>
<div class="block"><p>
 Add empty section to the end of the collection.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISectionCollection.html#appendEmptySection-java.lang.String-">appendEmptySection</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISectionCollection.html" title="interface in com.aspose.slides">ISectionCollection</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - Name of the section</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Added section.</dd>
</dl>
</li>
</ul>
<a name="addEmptySection-java.lang.String-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addEmptySection</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/ISection.html" title="interface in com.aspose.slides">ISection</a>&nbsp;addEmptySection(java.lang.String&nbsp;name,
                                      int&nbsp;index)</pre>
<div class="block"><p>
 Add empty section to specified position of the collection.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISectionCollection.html#addEmptySection-java.lang.String-int-">addEmptySection</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISectionCollection.html" title="interface in com.aspose.slides">ISectionCollection</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - Name of the section</dd>
<dd><code>index</code> - Index of new section.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Added section.</dd>
</dl>
</li>
</ul>
<a name="size--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>size</h4>
<pre>public final&nbsp;int&nbsp;size()</pre>
<div class="block"><p>
 Gets the number of elements actually contained in the collection.
 Read-only <code>int</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>size</code>&nbsp;in interface&nbsp;<code>com.aspose.ms.System.Collections.ICollection&lt;<a href="../../../com/aspose/slides/ISection.html" title="interface in com.aspose.slides">ISection</a>&gt;</code></dd>
</dl>
</li>
</ul>
<a name="indexOf-com.aspose.slides.ISection-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>indexOf</h4>
<pre>public final&nbsp;int&nbsp;indexOf(<a href="../../../com/aspose/slides/ISection.html" title="interface in com.aspose.slides">ISection</a>&nbsp;section)</pre>
<div class="block"><p>
 Returns an index of the specified section in the collection.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISectionCollection.html#indexOf-com.aspose.slides.ISection-">indexOf</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISectionCollection.html" title="interface in com.aspose.slides">ISectionCollection</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>section</code> - Section to find.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Index of a section or -1 if section not from this collection.</dd>
</dl>
</li>
</ul>
<a name="removeSectionWithSlides-com.aspose.slides.ISection-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>removeSectionWithSlides</h4>
<pre>public final&nbsp;void&nbsp;removeSectionWithSlides(<a href="../../../com/aspose/slides/ISection.html" title="interface in com.aspose.slides">ISection</a>&nbsp;section)</pre>
<div class="block"><p>
 Remove section and slides contained in the section.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISectionCollection.html#removeSectionWithSlides-com.aspose.slides.ISection-">removeSectionWithSlides</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISectionCollection.html" title="interface in com.aspose.slides">ISectionCollection</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>section</code> - The section to remove from the collection.</dd>
</dl>
</li>
</ul>
<a name="removeSection-com.aspose.slides.ISection-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>removeSection</h4>
<pre>public final&nbsp;void&nbsp;removeSection(<a href="../../../com/aspose/slides/ISection.html" title="interface in com.aspose.slides">ISection</a>&nbsp;section)</pre>
<div class="block"><p>
 Remove section. Slides contained in the section will be merged into previous section.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISectionCollection.html#removeSection-com.aspose.slides.ISection-">removeSection</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISectionCollection.html" title="interface in com.aspose.slides">ISectionCollection</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>section</code> - The section to remove from the collection.</dd>
</dl>
</li>
</ul>
<a name="reorderSectionWithSlides-com.aspose.slides.ISection-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>reorderSectionWithSlides</h4>
<pre>public final&nbsp;void&nbsp;reorderSectionWithSlides(<a href="../../../com/aspose/slides/ISection.html" title="interface in com.aspose.slides">ISection</a>&nbsp;section,
                                           int&nbsp;index)</pre>
<div class="block"><p>
 Moves section and its slides from the collection to the specified position.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISectionCollection.html#reorderSectionWithSlides-com.aspose.slides.ISection-int-">reorderSectionWithSlides</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISectionCollection.html" title="interface in com.aspose.slides">ISectionCollection</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - Target index.</dd>
<dd><code>section</code> - Section to move.</dd>
</dl>
</li>
</ul>
<a name="clear--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>clear</h4>
<pre>public final&nbsp;void&nbsp;clear()</pre>
<div class="block"><p>
 Removes all sections from the collection.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISectionCollection.html#clear--">clear</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISectionCollection.html" title="interface in com.aspose.slides">ISectionCollection</a></code></dd>
</dl>
</li>
</ul>
<a name="copyTo-com.aspose.ms.System.Array-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>copyTo</h4>
<pre>public final&nbsp;void&nbsp;copyTo(com.aspose.ms.System.Array&nbsp;array,
                         int&nbsp;index)</pre>
<div class="block"><p>
 Copies the entire collection to the specified array.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>copyTo</code>&nbsp;in interface&nbsp;<code>com.aspose.ms.System.Collections.ICollection&lt;<a href="../../../com/aspose/slides/ISection.html" title="interface in com.aspose.slides">ISection</a>&gt;</code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>array</code> - Target array</dd>
<dd><code>index</code> - Index in the target array.</dd>
</dl>
</li>
</ul>
<a name="isSynchronized--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isSynchronized</h4>
<pre>public final&nbsp;boolean&nbsp;isSynchronized()</pre>
<div class="block"><p>
 Returns a value indicating whether access to the collection is synchronized (thread-safe).
 Read-only <code>boolean</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>isSynchronized</code>&nbsp;in interface&nbsp;<code>com.aspose.ms.System.Collections.ICollection&lt;<a href="../../../com/aspose/slides/ISection.html" title="interface in com.aspose.slides">ISection</a>&gt;</code></dd>
</dl>
</li>
</ul>
<a name="getSyncRoot--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSyncRoot</h4>
<pre>public final&nbsp;java.lang.Object&nbsp;getSyncRoot()</pre>
<div class="block"><p>
 Returns a synchronization root.
 Read-only <code>Object</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>getSyncRoot</code>&nbsp;in interface&nbsp;<code>com.aspose.ms.System.Collections.ICollection&lt;<a href="../../../com/aspose/slides/ISection.html" title="interface in com.aspose.slides">ISection</a>&gt;</code></dd>
</dl>
</li>
</ul>
<a name="iterator--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>iterator</h4>
<pre>public final&nbsp;com.aspose.ms.System.Collections.Generic.IGenericEnumerator&lt;<a href="../../../com/aspose/slides/ISection.html" title="interface in com.aspose.slides">ISection</a>&gt;&nbsp;iterator()</pre>
<div class="block"><p>
 Returns an enumerator that iterates through the collection.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>iterator</code>&nbsp;in interface&nbsp;<code>com.aspose.ms.System.Collections.Generic.IGenericEnumerable&lt;<a href="../../../com/aspose/slides/ISection.html" title="interface in com.aspose.slides">ISection</a>&gt;</code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>iterator</code>&nbsp;in interface&nbsp;<code>com.aspose.ms.System.Collections.IEnumerable&lt;<a href="../../../com/aspose/slides/ISection.html" title="interface in com.aspose.slides">ISection</a>&gt;</code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>iterator</code>&nbsp;in interface&nbsp;<code>java.lang.Iterable&lt;<a href="../../../com/aspose/slides/ISection.html" title="interface in com.aspose.slides">ISection</a>&gt;</code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>A <code>IGenericEnumerator</code> that can be used to iterate through the collection.</dd>
</dl>
</li>
</ul>
<a name="iteratorJava--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>iteratorJava</h4>
<pre>public final&nbsp;com.aspose.ms.System.Collections.Generic.IGenericEnumerator&lt;<a href="../../../com/aspose/slides/ISection.html" title="interface in com.aspose.slides">ISection</a>&gt;&nbsp;iteratorJava()</pre>
<div class="block"><p>
 Returns a java iterator for the entire collection.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IGenericCollection.html#iteratorJava--">iteratorJava</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IGenericCollection.html" title="interface in com.aspose.slides">IGenericCollection</a>&lt;<a href="../../../com/aspose/slides/ISection.html" title="interface in com.aspose.slides">ISection</a>&gt;</code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>An <code>Iterator</code> for the entire collection.</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/Section.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/SectionSlideCollection.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/SectionCollection.html" target="_top">Frames</a></li>
<li><a href="SectionCollection.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><i>Copyright &copy; 2004-2025 Aspose Pty Ltd. All Rights Reserved.</i></small></p>
</body>
</html>
