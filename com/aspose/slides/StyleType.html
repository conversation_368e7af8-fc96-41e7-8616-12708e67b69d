<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_421) on Fri Jul 11 05:32:44 CDT 2025 -->
<title>StyleType (Aspose.Slides for Java (25.7) API Documentation)</title>
<meta name="date" content="2025-07-11">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="StyleType (Aspose.Slides for Java (25.7) API Documentation)";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/StringOrDoubleChartValue.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/SummaryZoomFrame.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/StyleType.html" target="_top">Frames</a></li>
<li><a href="StyleType.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.classes.inherited.from.class.com.aspose.ms.System.Enum">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#methods.inherited.from.class.com.aspose.ms.System.Enum">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li>Method</li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.aspose.slides</div>
<h2 title="Class StyleType" class="title">Class StyleType</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.aspose.ms.System.ValueType&lt;com.aspose.ms.System.Enum&gt;</li>
<li>
<ul class="inheritance">
<li>com.aspose.ms.System.Enum</li>
<li>
<ul class="inheritance">
<li>com.aspose.slides.StyleType</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public final class <span class="typeNameLabel">StyleType</span>
extends com.aspose.ms.System.Enum</pre>
<div class="block"><p>
 Represents chart style.
 </p></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.com.aspose.ms.System.Enum">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from class&nbsp;com.aspose.ms.System.Enum</h3>
<code>com.aspose.ms.System.Enum.AbstractEnum, com.aspose.ms.System.Enum.FlaggedEnum, com.aspose.ms.System.Enum.ObjectEnum, com.aspose.ms.System.Enum.SimpleEnum</code></li>
</ul>
</li>
</ul>
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/StyleType.html#Style1">Style1</a></span></code>
<div class="block">
 Style 1</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/StyleType.html#Style10">Style10</a></span></code>
<div class="block">
 Style 10</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/StyleType.html#Style11">Style11</a></span></code>
<div class="block">
 Style 11</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/StyleType.html#Style12">Style12</a></span></code>
<div class="block">
 Style 12</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/StyleType.html#Style13">Style13</a></span></code>
<div class="block">
 Style 13</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/StyleType.html#Style14">Style14</a></span></code>
<div class="block">
 Style 14</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/StyleType.html#Style15">Style15</a></span></code>
<div class="block">
 Style 15</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/StyleType.html#Style16">Style16</a></span></code>
<div class="block">
 Style 16</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/StyleType.html#Style17">Style17</a></span></code>
<div class="block">
 Style 17</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/StyleType.html#Style18">Style18</a></span></code>
<div class="block">
 Style 18</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/StyleType.html#Style19">Style19</a></span></code>
<div class="block">
 Style 19</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/StyleType.html#Style2">Style2</a></span></code>
<div class="block">
 Style 2</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/StyleType.html#Style20">Style20</a></span></code>
<div class="block">
 Style 20</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/StyleType.html#Style21">Style21</a></span></code>
<div class="block">
 Style 21</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/StyleType.html#Style22">Style22</a></span></code>
<div class="block">
 Style 22</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/StyleType.html#Style23">Style23</a></span></code>
<div class="block">
 Style 23</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/StyleType.html#Style24">Style24</a></span></code>
<div class="block">
 Style 24</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/StyleType.html#Style25">Style25</a></span></code>
<div class="block">
 Style 25</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/StyleType.html#Style26">Style26</a></span></code>
<div class="block">
 Style 26</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/StyleType.html#Style27">Style27</a></span></code>
<div class="block">
 Style 27</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/StyleType.html#Style28">Style28</a></span></code>
<div class="block">
 Style 28</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/StyleType.html#Style29">Style29</a></span></code>
<div class="block">
 Style 29</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/StyleType.html#Style3">Style3</a></span></code>
<div class="block">
 Style 3</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/StyleType.html#Style30">Style30</a></span></code>
<div class="block">
 Style 30</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/StyleType.html#Style31">Style31</a></span></code>
<div class="block">
 Style 31</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/StyleType.html#Style32">Style32</a></span></code>
<div class="block">
 Style 32</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/StyleType.html#Style33">Style33</a></span></code>
<div class="block">
 Style 33</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/StyleType.html#Style34">Style34</a></span></code>
<div class="block">
 Style 34</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/StyleType.html#Style35">Style35</a></span></code>
<div class="block">
 Style 35</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/StyleType.html#Style36">Style36</a></span></code>
<div class="block">
 Style 36</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/StyleType.html#Style37">Style37</a></span></code>
<div class="block">
 Style 37</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/StyleType.html#Style38">Style38</a></span></code>
<div class="block">
 Style 38</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/StyleType.html#Style39">Style39</a></span></code>
<div class="block">
 Style 39</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/StyleType.html#Style4">Style4</a></span></code>
<div class="block">
 Style 4</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/StyleType.html#Style40">Style40</a></span></code>
<div class="block">
 Style 40</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/StyleType.html#Style41">Style41</a></span></code>
<div class="block">
 Style 41</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/StyleType.html#Style42">Style42</a></span></code>
<div class="block">
 Style 42</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/StyleType.html#Style43">Style43</a></span></code>
<div class="block">
 Style 43</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/StyleType.html#Style44">Style44</a></span></code>
<div class="block">
 Style 44</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/StyleType.html#Style45">Style45</a></span></code>
<div class="block">
 Style 45</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/StyleType.html#Style46">Style46</a></span></code>
<div class="block">
 Style 46</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/StyleType.html#Style47">Style47</a></span></code>
<div class="block">
 Style 47</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/StyleType.html#Style48">Style48</a></span></code>
<div class="block">
 Style 48</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/StyleType.html#Style5">Style5</a></span></code>
<div class="block">
 Style 5</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/StyleType.html#Style6">Style6</a></span></code>
<div class="block">
 Style 6</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/StyleType.html#Style7">Style7</a></span></code>
<div class="block">
 Style 7</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/StyleType.html#Style8">Style8</a></span></code>
<div class="block">
 Style 8</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/StyleType.html#Style9">Style9</a></span></code>
<div class="block">
 Style 9</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.com.aspose.ms.System.Enum">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;com.aspose.ms.System.Enum</h3>
<code>EnumSeparatorCharArray</code></li>
</ul>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.aspose.ms.System.Enum">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;com.aspose.ms.System.Enum</h3>
<code>Clone, CloneTo, format, format, get_Caption, get_Value, getName, getName, getNames, getNames, getUnderlyingType, getUnderlyingType, getValue, getValues, isDefined, isDefined, isDefined, isDefined, parse, parse, parse, parse, register, toObject, toString</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="Style1">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Style1</h4>
<pre>public static final&nbsp;int Style1</pre>
<div class="block"><p>
 Style 1
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.StyleType.Style1">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Style2">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Style2</h4>
<pre>public static final&nbsp;int Style2</pre>
<div class="block"><p>
 Style 2
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.StyleType.Style2">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Style3">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Style3</h4>
<pre>public static final&nbsp;int Style3</pre>
<div class="block"><p>
 Style 3
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.StyleType.Style3">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Style4">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Style4</h4>
<pre>public static final&nbsp;int Style4</pre>
<div class="block"><p>
 Style 4
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.StyleType.Style4">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Style5">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Style5</h4>
<pre>public static final&nbsp;int Style5</pre>
<div class="block"><p>
 Style 5
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.StyleType.Style5">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Style6">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Style6</h4>
<pre>public static final&nbsp;int Style6</pre>
<div class="block"><p>
 Style 6
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.StyleType.Style6">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Style7">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Style7</h4>
<pre>public static final&nbsp;int Style7</pre>
<div class="block"><p>
 Style 7
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.StyleType.Style7">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Style8">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Style8</h4>
<pre>public static final&nbsp;int Style8</pre>
<div class="block"><p>
 Style 8
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.StyleType.Style8">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Style9">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Style9</h4>
<pre>public static final&nbsp;int Style9</pre>
<div class="block"><p>
 Style 9
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.StyleType.Style9">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Style10">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Style10</h4>
<pre>public static final&nbsp;int Style10</pre>
<div class="block"><p>
 Style 10
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.StyleType.Style10">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Style11">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Style11</h4>
<pre>public static final&nbsp;int Style11</pre>
<div class="block"><p>
 Style 11
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.StyleType.Style11">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Style12">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Style12</h4>
<pre>public static final&nbsp;int Style12</pre>
<div class="block"><p>
 Style 12
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.StyleType.Style12">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Style13">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Style13</h4>
<pre>public static final&nbsp;int Style13</pre>
<div class="block"><p>
 Style 13
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.StyleType.Style13">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Style14">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Style14</h4>
<pre>public static final&nbsp;int Style14</pre>
<div class="block"><p>
 Style 14
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.StyleType.Style14">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Style15">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Style15</h4>
<pre>public static final&nbsp;int Style15</pre>
<div class="block"><p>
 Style 15
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.StyleType.Style15">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Style16">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Style16</h4>
<pre>public static final&nbsp;int Style16</pre>
<div class="block"><p>
 Style 16
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.StyleType.Style16">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Style17">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Style17</h4>
<pre>public static final&nbsp;int Style17</pre>
<div class="block"><p>
 Style 17
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.StyleType.Style17">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Style18">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Style18</h4>
<pre>public static final&nbsp;int Style18</pre>
<div class="block"><p>
 Style 18
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.StyleType.Style18">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Style19">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Style19</h4>
<pre>public static final&nbsp;int Style19</pre>
<div class="block"><p>
 Style 19
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.StyleType.Style19">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Style20">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Style20</h4>
<pre>public static final&nbsp;int Style20</pre>
<div class="block"><p>
 Style 20
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.StyleType.Style20">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Style21">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Style21</h4>
<pre>public static final&nbsp;int Style21</pre>
<div class="block"><p>
 Style 21
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.StyleType.Style21">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Style22">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Style22</h4>
<pre>public static final&nbsp;int Style22</pre>
<div class="block"><p>
 Style 22
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.StyleType.Style22">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Style23">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Style23</h4>
<pre>public static final&nbsp;int Style23</pre>
<div class="block"><p>
 Style 23
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.StyleType.Style23">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Style24">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Style24</h4>
<pre>public static final&nbsp;int Style24</pre>
<div class="block"><p>
 Style 24
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.StyleType.Style24">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Style25">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Style25</h4>
<pre>public static final&nbsp;int Style25</pre>
<div class="block"><p>
 Style 25
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.StyleType.Style25">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Style26">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Style26</h4>
<pre>public static final&nbsp;int Style26</pre>
<div class="block"><p>
 Style 26
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.StyleType.Style26">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Style27">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Style27</h4>
<pre>public static final&nbsp;int Style27</pre>
<div class="block"><p>
 Style 27
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.StyleType.Style27">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Style28">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Style28</h4>
<pre>public static final&nbsp;int Style28</pre>
<div class="block"><p>
 Style 28
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.StyleType.Style28">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Style29">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Style29</h4>
<pre>public static final&nbsp;int Style29</pre>
<div class="block"><p>
 Style 29
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.StyleType.Style29">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Style30">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Style30</h4>
<pre>public static final&nbsp;int Style30</pre>
<div class="block"><p>
 Style 30
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.StyleType.Style30">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Style31">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Style31</h4>
<pre>public static final&nbsp;int Style31</pre>
<div class="block"><p>
 Style 31
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.StyleType.Style31">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Style32">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Style32</h4>
<pre>public static final&nbsp;int Style32</pre>
<div class="block"><p>
 Style 32
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.StyleType.Style32">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Style33">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Style33</h4>
<pre>public static final&nbsp;int Style33</pre>
<div class="block"><p>
 Style 33
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.StyleType.Style33">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Style34">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Style34</h4>
<pre>public static final&nbsp;int Style34</pre>
<div class="block"><p>
 Style 34
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.StyleType.Style34">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Style35">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Style35</h4>
<pre>public static final&nbsp;int Style35</pre>
<div class="block"><p>
 Style 35
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.StyleType.Style35">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Style36">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Style36</h4>
<pre>public static final&nbsp;int Style36</pre>
<div class="block"><p>
 Style 36
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.StyleType.Style36">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Style37">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Style37</h4>
<pre>public static final&nbsp;int Style37</pre>
<div class="block"><p>
 Style 37
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.StyleType.Style37">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Style38">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Style38</h4>
<pre>public static final&nbsp;int Style38</pre>
<div class="block"><p>
 Style 38
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.StyleType.Style38">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Style39">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Style39</h4>
<pre>public static final&nbsp;int Style39</pre>
<div class="block"><p>
 Style 39
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.StyleType.Style39">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Style40">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Style40</h4>
<pre>public static final&nbsp;int Style40</pre>
<div class="block"><p>
 Style 40
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.StyleType.Style40">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Style41">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Style41</h4>
<pre>public static final&nbsp;int Style41</pre>
<div class="block"><p>
 Style 41
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.StyleType.Style41">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Style42">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Style42</h4>
<pre>public static final&nbsp;int Style42</pre>
<div class="block"><p>
 Style 42
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.StyleType.Style42">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Style43">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Style43</h4>
<pre>public static final&nbsp;int Style43</pre>
<div class="block"><p>
 Style 43
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.StyleType.Style43">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Style44">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Style44</h4>
<pre>public static final&nbsp;int Style44</pre>
<div class="block"><p>
 Style 44
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.StyleType.Style44">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Style45">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Style45</h4>
<pre>public static final&nbsp;int Style45</pre>
<div class="block"><p>
 Style 45
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.StyleType.Style45">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Style46">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Style46</h4>
<pre>public static final&nbsp;int Style46</pre>
<div class="block"><p>
 Style 46
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.StyleType.Style46">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Style47">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Style47</h4>
<pre>public static final&nbsp;int Style47</pre>
<div class="block"><p>
 Style 47
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.StyleType.Style47">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Style48">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>Style48</h4>
<pre>public static final&nbsp;int Style48</pre>
<div class="block"><p>
 Style 48
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.StyleType.Style48">Constant Field Values</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/StringOrDoubleChartValue.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/SummaryZoomFrame.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/StyleType.html" target="_top">Frames</a></li>
<li><a href="StyleType.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.classes.inherited.from.class.com.aspose.ms.System.Enum">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#methods.inherited.from.class.com.aspose.ms.System.Enum">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li>Method</li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><i>Copyright &copy; 2004-2025 Aspose Pty Ltd. All Rights Reserved.</i></small></p>
</body>
</html>
