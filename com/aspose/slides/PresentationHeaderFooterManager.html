<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_421) on Fri Jul 11 05:32:43 CDT 2025 -->
<title>PresentationHeaderFooterManager (Aspose.Slides for Java (25.7) API Documentation)</title>
<meta name="date" content="2025-07-11">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="PresentationHeaderFooterManager (Aspose.Slides for Java (25.7) API Documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/PresentationFactory.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/PresentationInfo.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/PresentationHeaderFooterManager.html" target="_top">Frames</a></li>
<li><a href="PresentationHeaderFooterManager.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.aspose.slides</div>
<h2 title="Class PresentationHeaderFooterManager" class="title">Class PresentationHeaderFooterManager</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../com/aspose/slides/BaseHeaderFooterManager.html" title="class in com.aspose.slides">com.aspose.slides.BaseHeaderFooterManager</a></li>
<li>
<ul class="inheritance">
<li>com.aspose.slides.PresentationHeaderFooterManager</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../com/aspose/slides/IBaseHeaderFooterManager.html" title="interface in com.aspose.slides">IBaseHeaderFooterManager</a>, <a href="../../../com/aspose/slides/IPresentationHeaderFooterManager.html" title="interface in com.aspose.slides">IPresentationHeaderFooterManager</a></dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">PresentationHeaderFooterManager</span>
extends <a href="../../../com/aspose/slides/BaseHeaderFooterManager.html" title="class in com.aspose.slides">BaseHeaderFooterManager</a>
implements <a href="../../../com/aspose/slides/IPresentationHeaderFooterManager.html" title="interface in com.aspose.slides">IPresentationHeaderFooterManager</a></pre>
<div class="block"><p>
 Represents manager which holds behavior of all footer, date-time and page number placeholders of presentation.
 </p></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresentationHeaderFooterManager.html#setAllDateTimesText-java.lang.String-">setAllDateTimesText</a></span>(java.lang.String&nbsp;text)</code>
<div class="block">
 Sets text to all date-time placeholders, including master slides, layout slides, slides, 
 notes master, notes slides and handout master.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresentationHeaderFooterManager.html#setAllDateTimesVisibility-boolean-">setAllDateTimesVisibility</a></span>(boolean&nbsp;isVisible)</code>
<div class="block">
 Changes all date-time placeholders visibility, including master slides, layout slides, slides, 
 notes master, notes slides and handout master.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresentationHeaderFooterManager.html#setAllFootersText-java.lang.String-">setAllFootersText</a></span>(java.lang.String&nbsp;text)</code>
<div class="block">
 Sets text to all footer placeholders, including master slides, layout slides, slides, 
 notes master, notes slides and handout master.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresentationHeaderFooterManager.html#setAllFootersVisibility-boolean-">setAllFootersVisibility</a></span>(boolean&nbsp;isVisible)</code>
<div class="block">
 Changes all footer placeholders visibility, including master slides, layout slides, slides, 
 notes master, notes slides and handout master.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresentationHeaderFooterManager.html#setAllHeadersText-java.lang.String-">setAllHeadersText</a></span>(java.lang.String&nbsp;text)</code>
<div class="block">
 Sets text to all header placeholders, including notes master, notes slides and handout master.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresentationHeaderFooterManager.html#setAllHeadersVisibility-boolean-">setAllHeadersVisibility</a></span>(boolean&nbsp;isVisible)</code>
<div class="block">
 Changes all header placeholders visibility, including notes master, notes slides and handout master.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresentationHeaderFooterManager.html#setAllSlideNumbersVisibility-boolean-">setAllSlideNumbersVisibility</a></span>(boolean&nbsp;isVisible)</code>
<div class="block">
 Changes all page number placeholders visibility, including master slides, layout slides, slides, 
 notes master, notes slides and handout master.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresentationHeaderFooterManager.html#setVisibilityOnAllTitleSlides-boolean-">setVisibilityOnAllTitleSlides</a></span>(boolean&nbsp;isVisible)</code>
<div class="block">
 Changes the footer, date-time and page number placeholders visibility for all title slides and for first layout slide.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.aspose.slides.BaseHeaderFooterManager">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;com.aspose.slides.<a href="../../../com/aspose/slides/BaseHeaderFooterManager.html" title="class in com.aspose.slides">BaseHeaderFooterManager</a></h3>
<code><a href="../../../com/aspose/slides/BaseHeaderFooterManager.html#getParent_Immediate--">getParent_Immediate</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="setAllHeadersVisibility-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAllHeadersVisibility</h4>
<pre>public final&nbsp;void&nbsp;setAllHeadersVisibility(boolean&nbsp;isVisible)</pre>
<div class="block"><p>
 Changes all header placeholders visibility, including notes master, notes slides and handout master.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPresentationHeaderFooterManager.html#setAllHeadersVisibility-boolean-">setAllHeadersVisibility</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPresentationHeaderFooterManager.html" title="interface in com.aspose.slides">IPresentationHeaderFooterManager</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>isVisible</code> - true - makes a header placeholders visible, otherwise - hides them.</dd>
</dl>
</li>
</ul>
<a name="setAllFootersVisibility-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAllFootersVisibility</h4>
<pre>public final&nbsp;void&nbsp;setAllFootersVisibility(boolean&nbsp;isVisible)</pre>
<div class="block"><p>
 Changes all footer placeholders visibility, including master slides, layout slides, slides, 
 notes master, notes slides and handout master.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPresentationHeaderFooterManager.html#setAllFootersVisibility-boolean-">setAllFootersVisibility</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPresentationHeaderFooterManager.html" title="interface in com.aspose.slides">IPresentationHeaderFooterManager</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>isVisible</code> - true - makes a footer placeholders visible, otherwise - hides them.</dd>
</dl>
</li>
</ul>
<a name="setAllSlideNumbersVisibility-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAllSlideNumbersVisibility</h4>
<pre>public final&nbsp;void&nbsp;setAllSlideNumbersVisibility(boolean&nbsp;isVisible)</pre>
<div class="block"><p>
 Changes all page number placeholders visibility, including master slides, layout slides, slides, 
 notes master, notes slides and handout master.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPresentationHeaderFooterManager.html#setAllSlideNumbersVisibility-boolean-">setAllSlideNumbersVisibility</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPresentationHeaderFooterManager.html" title="interface in com.aspose.slides">IPresentationHeaderFooterManager</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>isVisible</code> - true - makes a page number placeholders visible, otherwise - hides them.</dd>
</dl>
</li>
</ul>
<a name="setAllDateTimesVisibility-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAllDateTimesVisibility</h4>
<pre>public final&nbsp;void&nbsp;setAllDateTimesVisibility(boolean&nbsp;isVisible)</pre>
<div class="block"><p>
 Changes all date-time placeholders visibility, including master slides, layout slides, slides, 
 notes master, notes slides and handout master.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPresentationHeaderFooterManager.html#setAllDateTimesVisibility-boolean-">setAllDateTimesVisibility</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPresentationHeaderFooterManager.html" title="interface in com.aspose.slides">IPresentationHeaderFooterManager</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>isVisible</code> - true - makes a date-time placeholders visible, otherwise - hides them.</dd>
</dl>
</li>
</ul>
<a name="setAllHeadersText-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAllHeadersText</h4>
<pre>public final&nbsp;void&nbsp;setAllHeadersText(java.lang.String&nbsp;text)</pre>
<div class="block"><p>
 Sets text to all header placeholders, including notes master, notes slides and handout master.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPresentationHeaderFooterManager.html#setAllHeadersText-java.lang.String-">setAllHeadersText</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPresentationHeaderFooterManager.html" title="interface in com.aspose.slides">IPresentationHeaderFooterManager</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>text</code> - Text to set.</dd>
</dl>
</li>
</ul>
<a name="setAllFootersText-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAllFootersText</h4>
<pre>public final&nbsp;void&nbsp;setAllFootersText(java.lang.String&nbsp;text)</pre>
<div class="block"><p>
 Sets text to all footer placeholders, including master slides, layout slides, slides, 
 notes master, notes slides and handout master.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPresentationHeaderFooterManager.html#setAllFootersText-java.lang.String-">setAllFootersText</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPresentationHeaderFooterManager.html" title="interface in com.aspose.slides">IPresentationHeaderFooterManager</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>text</code> - Text to set.</dd>
</dl>
</li>
</ul>
<a name="setAllDateTimesText-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAllDateTimesText</h4>
<pre>public final&nbsp;void&nbsp;setAllDateTimesText(java.lang.String&nbsp;text)</pre>
<div class="block"><p>
 Sets text to all date-time placeholders, including master slides, layout slides, slides, 
 notes master, notes slides and handout master.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPresentationHeaderFooterManager.html#setAllDateTimesText-java.lang.String-">setAllDateTimesText</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPresentationHeaderFooterManager.html" title="interface in com.aspose.slides">IPresentationHeaderFooterManager</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>text</code> - Text to set.</dd>
</dl>
</li>
</ul>
<a name="setVisibilityOnAllTitleSlides-boolean-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setVisibilityOnAllTitleSlides</h4>
<pre>public final&nbsp;void&nbsp;setVisibilityOnAllTitleSlides(boolean&nbsp;isVisible)</pre>
<div class="block"><p>
 Changes the footer, date-time and page number placeholders visibility for all title slides and for first layout slide.
 Title slides – slides based on first layout slide (regardless of type of this first layout).
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPresentationHeaderFooterManager.html#setVisibilityOnAllTitleSlides-boolean-">setVisibilityOnAllTitleSlides</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPresentationHeaderFooterManager.html" title="interface in com.aspose.slides">IPresentationHeaderFooterManager</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>isVisible</code> - true - makes a placeholders visible, otherwise - hides them.</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/PresentationFactory.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/PresentationInfo.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/PresentationHeaderFooterManager.html" target="_top">Frames</a></li>
<li><a href="PresentationHeaderFooterManager.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><i>Copyright &copy; 2004-2025 Aspose Pty Ltd. All Rights Reserved.</i></small></p>
</body>
</html>
