<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_421) on Fri Jul 11 05:32:44 CDT 2025 -->
<title>ResponsiveHtmlController (Aspose.Slides for Java (25.7) API Documentation)</title>
<meta name="date" content="2025-07-11">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ResponsiveHtmlController (Aspose.Slides for Java (25.7) API Documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/ResourceLoadingAction.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/ReturnAction.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/ResponsiveHtmlController.html" target="_top">Frames</a></li>
<li><a href="ResponsiveHtmlController.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.aspose.slides</div>
<h2 title="Class ResponsiveHtmlController" class="title">Class ResponsiveHtmlController</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.aspose.slides.ResponsiveHtmlController</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../com/aspose/slides/IHtmlFormattingController.html" title="interface in com.aspose.slides">IHtmlFormattingController</a>, <a href="../../../com/aspose/slides/IResponsiveHtmlController.html" title="interface in com.aspose.slides">IResponsiveHtmlController</a></dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">ResponsiveHtmlController</span>
extends java.lang.Object
implements <a href="../../../com/aspose/slides/IResponsiveHtmlController.html" title="interface in com.aspose.slides">IResponsiveHtmlController</a></pre>
<div class="block"><p>
 Responsive HTML Controller
 </p></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ResponsiveHtmlController.html#ResponsiveHtmlController--">ResponsiveHtmlController</a></span>()</code>
<div class="block">
 Creates new instance</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ResponsiveHtmlController.html#ResponsiveHtmlController-com.aspose.slides.IHtmlFormattingController-">ResponsiveHtmlController</a></span>(<a href="../../../com/aspose/slides/IHtmlFormattingController.html" title="interface in com.aspose.slides">IHtmlFormattingController</a>&nbsp;controller)</code>
<div class="block">
 Creates new instance</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ResponsiveHtmlController.html#writeDocumentEnd-com.aspose.slides.IHtmlGenerator-com.aspose.slides.IPresentation-">writeDocumentEnd</a></span>(<a href="../../../com/aspose/slides/IHtmlGenerator.html" title="interface in com.aspose.slides">IHtmlGenerator</a>&nbsp;generator,
                <a href="../../../com/aspose/slides/IPresentation.html" title="interface in com.aspose.slides">IPresentation</a>&nbsp;presentation)</code>
<div class="block">
 Called to write html document footer.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ResponsiveHtmlController.html#writeDocumentStart-com.aspose.slides.IHtmlGenerator-com.aspose.slides.IPresentation-">writeDocumentStart</a></span>(<a href="../../../com/aspose/slides/IHtmlGenerator.html" title="interface in com.aspose.slides">IHtmlGenerator</a>&nbsp;generator,
                  <a href="../../../com/aspose/slides/IPresentation.html" title="interface in com.aspose.slides">IPresentation</a>&nbsp;presentation)</code>
<div class="block">
 Called to write html document header.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ResponsiveHtmlController.html#writeShapeEnd-com.aspose.slides.IHtmlGenerator-com.aspose.slides.IShape-">writeShapeEnd</a></span>(<a href="../../../com/aspose/slides/IHtmlGenerator.html" title="interface in com.aspose.slides">IHtmlGenerator</a>&nbsp;generator,
             <a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a>&nbsp;shape)</code>
<div class="block">
 Called before shape's rendering.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ResponsiveHtmlController.html#writeShapeStart-com.aspose.slides.IHtmlGenerator-com.aspose.slides.IShape-">writeShapeStart</a></span>(<a href="../../../com/aspose/slides/IHtmlGenerator.html" title="interface in com.aspose.slides">IHtmlGenerator</a>&nbsp;generator,
               <a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a>&nbsp;shape)</code>
<div class="block">
 Called before shape's rendering.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ResponsiveHtmlController.html#writeSlideEnd-com.aspose.slides.IHtmlGenerator-com.aspose.slides.ISlide-">writeSlideEnd</a></span>(<a href="../../../com/aspose/slides/IHtmlGenerator.html" title="interface in com.aspose.slides">IHtmlGenerator</a>&nbsp;generator,
             <a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a>&nbsp;slide)</code>
<div class="block">
 Called to write html slide footer.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ResponsiveHtmlController.html#writeSlideStart-com.aspose.slides.IHtmlGenerator-com.aspose.slides.ISlide-">writeSlideStart</a></span>(<a href="../../../com/aspose/slides/IHtmlGenerator.html" title="interface in com.aspose.slides">IHtmlGenerator</a>&nbsp;generator,
               <a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a>&nbsp;slide)</code>
<div class="block">
 Called to write html slide header.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="ResponsiveHtmlController--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ResponsiveHtmlController</h4>
<pre>public&nbsp;ResponsiveHtmlController()</pre>
<div class="block"><p>
 Creates new instance
 </p></div>
</li>
</ul>
<a name="ResponsiveHtmlController-com.aspose.slides.IHtmlFormattingController-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>ResponsiveHtmlController</h4>
<pre>public&nbsp;ResponsiveHtmlController(<a href="../../../com/aspose/slides/IHtmlFormattingController.html" title="interface in com.aspose.slides">IHtmlFormattingController</a>&nbsp;controller)</pre>
<div class="block"><p>
 Creates new instance
 </p></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>controller</code> - HTML formatting controller</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="writeDocumentStart-com.aspose.slides.IHtmlGenerator-com.aspose.slides.IPresentation-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>writeDocumentStart</h4>
<pre>public final&nbsp;void&nbsp;writeDocumentStart(<a href="../../../com/aspose/slides/IHtmlGenerator.html" title="interface in com.aspose.slides">IHtmlGenerator</a>&nbsp;generator,
                                     <a href="../../../com/aspose/slides/IPresentation.html" title="interface in com.aspose.slides">IPresentation</a>&nbsp;presentation)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../com/aspose/slides/IHtmlFormattingController.html#writeDocumentStart-com.aspose.slides.IHtmlGenerator-com.aspose.slides.IPresentation-">IHtmlFormattingController</a></code></span></div>
<div class="block"><p>
 Called to write html document header. Called once per presentation conversion.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IHtmlFormattingController.html#writeDocumentStart-com.aspose.slides.IHtmlGenerator-com.aspose.slides.IPresentation-">writeDocumentStart</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IHtmlFormattingController.html" title="interface in com.aspose.slides">IHtmlFormattingController</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>generator</code> - Output object.</dd>
<dd><code>presentation</code> - Presentation which being currently rendered.</dd>
</dl>
</li>
</ul>
<a name="writeDocumentEnd-com.aspose.slides.IHtmlGenerator-com.aspose.slides.IPresentation-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>writeDocumentEnd</h4>
<pre>public final&nbsp;void&nbsp;writeDocumentEnd(<a href="../../../com/aspose/slides/IHtmlGenerator.html" title="interface in com.aspose.slides">IHtmlGenerator</a>&nbsp;generator,
                                   <a href="../../../com/aspose/slides/IPresentation.html" title="interface in com.aspose.slides">IPresentation</a>&nbsp;presentation)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../com/aspose/slides/IHtmlFormattingController.html#writeDocumentEnd-com.aspose.slides.IHtmlGenerator-com.aspose.slides.IPresentation-">IHtmlFormattingController</a></code></span></div>
<div class="block"><p>
 Called to write html document footer. Called once per presentation conversion.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IHtmlFormattingController.html#writeDocumentEnd-com.aspose.slides.IHtmlGenerator-com.aspose.slides.IPresentation-">writeDocumentEnd</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IHtmlFormattingController.html" title="interface in com.aspose.slides">IHtmlFormattingController</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>generator</code> - Output object.</dd>
<dd><code>presentation</code> - Presentation which being currently rendered.</dd>
</dl>
</li>
</ul>
<a name="writeSlideStart-com.aspose.slides.IHtmlGenerator-com.aspose.slides.ISlide-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>writeSlideStart</h4>
<pre>public final&nbsp;void&nbsp;writeSlideStart(<a href="../../../com/aspose/slides/IHtmlGenerator.html" title="interface in com.aspose.slides">IHtmlGenerator</a>&nbsp;generator,
                                  <a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a>&nbsp;slide)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../com/aspose/slides/IHtmlFormattingController.html#writeSlideStart-com.aspose.slides.IHtmlGenerator-com.aspose.slides.ISlide-">IHtmlFormattingController</a></code></span></div>
<div class="block"><p>
 Called to write html slide header. Called once per each of slides.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IHtmlFormattingController.html#writeSlideStart-com.aspose.slides.IHtmlGenerator-com.aspose.slides.ISlide-">writeSlideStart</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IHtmlFormattingController.html" title="interface in com.aspose.slides">IHtmlFormattingController</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>generator</code> - Output object.</dd>
<dd><code>slide</code> - Slide which being currently rendered.</dd>
</dl>
</li>
</ul>
<a name="writeSlideEnd-com.aspose.slides.IHtmlGenerator-com.aspose.slides.ISlide-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>writeSlideEnd</h4>
<pre>public final&nbsp;void&nbsp;writeSlideEnd(<a href="../../../com/aspose/slides/IHtmlGenerator.html" title="interface in com.aspose.slides">IHtmlGenerator</a>&nbsp;generator,
                                <a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a>&nbsp;slide)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../com/aspose/slides/IHtmlFormattingController.html#writeSlideEnd-com.aspose.slides.IHtmlGenerator-com.aspose.slides.ISlide-">IHtmlFormattingController</a></code></span></div>
<div class="block"><p>
 Called to write html slide footer. Called once per each of slides.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IHtmlFormattingController.html#writeSlideEnd-com.aspose.slides.IHtmlGenerator-com.aspose.slides.ISlide-">writeSlideEnd</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IHtmlFormattingController.html" title="interface in com.aspose.slides">IHtmlFormattingController</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>generator</code> - Output object.</dd>
<dd><code>slide</code> - Slide which being currently rendered.</dd>
</dl>
</li>
</ul>
<a name="writeShapeStart-com.aspose.slides.IHtmlGenerator-com.aspose.slides.IShape-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>writeShapeStart</h4>
<pre>public final&nbsp;void&nbsp;writeShapeStart(<a href="../../../com/aspose/slides/IHtmlGenerator.html" title="interface in com.aspose.slides">IHtmlGenerator</a>&nbsp;generator,
                                  <a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a>&nbsp;shape)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../com/aspose/slides/IHtmlFormattingController.html#writeShapeStart-com.aspose.slides.IHtmlGenerator-com.aspose.slides.IShape-">IHtmlFormattingController</a></code></span></div>
<div class="block"><p>
 Called before shape's rendering. Called once per each of shape. If this function writes anything to generator, current slide image generation will be finished, added html fragment inserted and new image will be started atop of the previous.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IHtmlFormattingController.html#writeShapeStart-com.aspose.slides.IHtmlGenerator-com.aspose.slides.IShape-">writeShapeStart</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IHtmlFormattingController.html" title="interface in com.aspose.slides">IHtmlFormattingController</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>generator</code> - Output object.</dd>
<dd><code>shape</code> - Shape which is about to render.</dd>
</dl>
</li>
</ul>
<a name="writeShapeEnd-com.aspose.slides.IHtmlGenerator-com.aspose.slides.IShape-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>writeShapeEnd</h4>
<pre>public final&nbsp;void&nbsp;writeShapeEnd(<a href="../../../com/aspose/slides/IHtmlGenerator.html" title="interface in com.aspose.slides">IHtmlGenerator</a>&nbsp;generator,
                                <a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a>&nbsp;shape)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../com/aspose/slides/IHtmlFormattingController.html#writeShapeEnd-com.aspose.slides.IHtmlGenerator-com.aspose.slides.IShape-">IHtmlFormattingController</a></code></span></div>
<div class="block"><p>
 Called before shape's rendering. Called once per each of shape. If this function writes anything to generator, current slide image generation will be finished, added html fragment inserted and new image will be started atop of the previous.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IHtmlFormattingController.html#writeShapeEnd-com.aspose.slides.IHtmlGenerator-com.aspose.slides.IShape-">writeShapeEnd</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IHtmlFormattingController.html" title="interface in com.aspose.slides">IHtmlFormattingController</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>generator</code> - Output object.</dd>
<dd><code>shape</code> - Shape which is rendered last.</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/ResourceLoadingAction.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/ReturnAction.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/ResponsiveHtmlController.html" target="_top">Frames</a></li>
<li><a href="ResponsiveHtmlController.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><i>Copyright &copy; 2004-2025 Aspose Pty Ltd. All Rights Reserved.</i></small></p>
</body>
</html>
