<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_421) on Fri Jul 11 05:32:44 CDT 2025 -->
<title>SmartArtShape (Aspose.Slides for Java (25.7) API Documentation)</title>
<meta name="date" content="2025-07-11">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="SmartArtShape (Aspose.Slides for Java (25.7) API Documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/SmartArtQuickStyleType.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/SmartArtShapeCollection.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/SmartArtShape.html" target="_top">Frames</a></li>
<li><a href="SmartArtShape.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.aspose.slides</div>
<h2 title="Class SmartArtShape" class="title">Class SmartArtShape</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../com/aspose/slides/Shape.html" title="class in com.aspose.slides">com.aspose.slides.Shape</a></li>
<li>
<ul class="inheritance">
<li><a href="../../../com/aspose/slides/GeometryShape.html" title="class in com.aspose.slides">com.aspose.slides.GeometryShape</a></li>
<li>
<ul class="inheritance">
<li>com.aspose.slides.SmartArtShape</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../com/aspose/slides/IGeometryShape.html" title="interface in com.aspose.slides">IGeometryShape</a>, <a href="../../../com/aspose/slides/IHyperlinkContainer.html" title="interface in com.aspose.slides">IHyperlinkContainer</a>, <a href="../../../com/aspose/slides/IPresentationComponent.html" title="interface in com.aspose.slides">IPresentationComponent</a>, <a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a>, <a href="../../../com/aspose/slides/ISlideComponent.html" title="interface in com.aspose.slides">ISlideComponent</a>, <a href="../../../com/aspose/slides/ISmartArtShape.html" title="interface in com.aspose.slides">ISmartArtShape</a></dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">SmartArtShape</span>
extends <a href="../../../com/aspose/slides/GeometryShape.html" title="class in com.aspose.slides">GeometryShape</a>
implements <a href="../../../com/aspose/slides/ISmartArtShape.html" title="interface in com.aspose.slides">ISmartArtShape</a></pre>
<div class="block"><p>
 Represents SmartArt shape
 </p></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtShape.html#getShapeType--">getShapeType</a></span>()</code>
<div class="block">
 Returns or sets the geometry preset type.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/ITextFrame.html" title="interface in com.aspose.slides">ITextFrame</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtShape.html#getTextFrame--">getTextFrame</a></span>()</code>
<div class="block">
 Returns text of the SmartArt shape.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtShape.html#setShapeType-int-">setShapeType</a></span>(int&nbsp;value)</code>
<div class="block">
 Returns or sets the geometry preset type.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.aspose.slides.GeometryShape">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;com.aspose.slides.<a href="../../../com/aspose/slides/GeometryShape.html" title="class in com.aspose.slides">GeometryShape</a></h3>
<code><a href="../../../com/aspose/slides/GeometryShape.html#createShapeElements--">createShapeElements</a>, <a href="../../../com/aspose/slides/GeometryShape.html#getAdjustments--">getAdjustments</a>, <a href="../../../com/aspose/slides/GeometryShape.html#getGeometryPaths--">getGeometryPaths</a>, <a href="../../../com/aspose/slides/GeometryShape.html#getShapeStyle--">getShapeStyle</a>, <a href="../../../com/aspose/slides/GeometryShape.html#setGeometryPath-com.aspose.slides.IGeometryPath-">setGeometryPath</a>, <a href="../../../com/aspose/slides/GeometryShape.html#setGeometryPaths-com.aspose.slides.IGeometryPath:A-">setGeometryPaths</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.aspose.slides.Shape">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;com.aspose.slides.<a href="../../../com/aspose/slides/Shape.html" title="class in com.aspose.slides">Shape</a></h3>
<code><a href="../../../com/aspose/slides/Shape.html#addPlaceholder-com.aspose.slides.IPlaceholder-">addPlaceholder</a>, <a href="../../../com/aspose/slides/Shape.html#getAlternativeText--">getAlternativeText</a>, <a href="../../../com/aspose/slides/Shape.html#getAlternativeTextTitle--">getAlternativeTextTitle</a>, <a href="../../../com/aspose/slides/Shape.html#getBasePlaceholder--">getBasePlaceholder</a>, <a href="../../../com/aspose/slides/Shape.html#getBlackWhiteMode--">getBlackWhiteMode</a>, <a href="../../../com/aspose/slides/Shape.html#getConnectionSiteCount--">getConnectionSiteCount</a>, <a href="../../../com/aspose/slides/Shape.html#getCustomData--">getCustomData</a>, <a href="../../../com/aspose/slides/Shape.html#getEffectFormat--">getEffectFormat</a>, <a href="../../../com/aspose/slides/Shape.html#getFillFormat--">getFillFormat</a>, <a href="../../../com/aspose/slides/Shape.html#getFrame--">getFrame</a>, <a href="../../../com/aspose/slides/Shape.html#getHeight--">getHeight</a>, <a href="../../../com/aspose/slides/Shape.html#getHidden--">getHidden</a>, <a href="../../../com/aspose/slides/Shape.html#getHyperlinkClick--">getHyperlinkClick</a>, <a href="../../../com/aspose/slides/Shape.html#getHyperlinkManager--">getHyperlinkManager</a>, <a href="../../../com/aspose/slides/Shape.html#getHyperlinkMouseOver--">getHyperlinkMouseOver</a>, <a href="../../../com/aspose/slides/Shape.html#getImage--">getImage</a>, <a href="../../../com/aspose/slides/Shape.html#getImage-int-float-float-">getImage</a>, <a href="../../../com/aspose/slides/Shape.html#getLineFormat--">getLineFormat</a>, <a href="../../../com/aspose/slides/Shape.html#getName--">getName</a>, <a href="../../../com/aspose/slides/Shape.html#getOfficeInteropShapeId--">getOfficeInteropShapeId</a>, <a href="../../../com/aspose/slides/Shape.html#getParent_Immediate--">getParent_Immediate</a>, <a href="../../../com/aspose/slides/Shape.html#getParentGroup--">getParentGroup</a>, <a href="../../../com/aspose/slides/Shape.html#getPlaceholder--">getPlaceholder</a>, <a href="../../../com/aspose/slides/Shape.html#getPresentation--">getPresentation</a>, <a href="../../../com/aspose/slides/Shape.html#getRawFrame--">getRawFrame</a>, <a href="../../../com/aspose/slides/Shape.html#getRotation--">getRotation</a>, <a href="../../../com/aspose/slides/Shape.html#getShapeLock--">getShapeLock</a>, <a href="../../../com/aspose/slides/Shape.html#getSlide--">getSlide</a>, <a href="../../../com/aspose/slides/Shape.html#getThreeDFormat--">getThreeDFormat</a>, <a href="../../../com/aspose/slides/Shape.html#getThumbnail--">getThumbnail</a>, <a href="../../../com/aspose/slides/Shape.html#getThumbnail-int-float-float-">getThumbnail</a>, <a href="../../../com/aspose/slides/Shape.html#getUniqueId--">getUniqueId</a>, <a href="../../../com/aspose/slides/Shape.html#getWidth--">getWidth</a>, <a href="../../../com/aspose/slides/Shape.html#getX--">getX</a>, <a href="../../../com/aspose/slides/Shape.html#getY--">getY</a>, <a href="../../../com/aspose/slides/Shape.html#getZOrderPosition--">getZOrderPosition</a>, <a href="../../../com/aspose/slides/Shape.html#isDecorative--">isDecorative</a>, <a href="../../../com/aspose/slides/Shape.html#isGrouped--">isGrouped</a>, <a href="../../../com/aspose/slides/Shape.html#isTextHolder--">isTextHolder</a>, <a href="../../../com/aspose/slides/Shape.html#removePlaceholder--">removePlaceholder</a>, <a href="../../../com/aspose/slides/Shape.html#setAlternativeText-java.lang.String-">setAlternativeText</a>, <a href="../../../com/aspose/slides/Shape.html#setAlternativeTextTitle-java.lang.String-">setAlternativeTextTitle</a>, <a href="../../../com/aspose/slides/Shape.html#setBlackWhiteMode-byte-">setBlackWhiteMode</a>, <a href="../../../com/aspose/slides/Shape.html#setDecorative-boolean-">setDecorative</a>, <a href="../../../com/aspose/slides/Shape.html#setFrame-com.aspose.slides.IShapeFrame-">setFrame</a>, <a href="../../../com/aspose/slides/Shape.html#setHeight-float-">setHeight</a>, <a href="../../../com/aspose/slides/Shape.html#setHidden-boolean-">setHidden</a>, <a href="../../../com/aspose/slides/Shape.html#setHyperlinkClick-com.aspose.slides.IHyperlink-">setHyperlinkClick</a>, <a href="../../../com/aspose/slides/Shape.html#setHyperlinkMouseOver-com.aspose.slides.IHyperlink-">setHyperlinkMouseOver</a>, <a href="../../../com/aspose/slides/Shape.html#setName-java.lang.String-">setName</a>, <a href="../../../com/aspose/slides/Shape.html#setRawFrame-com.aspose.slides.IShapeFrame-">setRawFrame</a>, <a href="../../../com/aspose/slides/Shape.html#setRotation-float-">setRotation</a>, <a href="../../../com/aspose/slides/Shape.html#setWidth-float-">setWidth</a>, <a href="../../../com/aspose/slides/Shape.html#setX-float-">setX</a>, <a href="../../../com/aspose/slides/Shape.html#setY-float-">setY</a>, <a href="../../../com/aspose/slides/Shape.html#writeAsSvg-java.io.OutputStream-">writeAsSvg</a>, <a href="../../../com/aspose/slides/Shape.html#writeAsSvg-java.io.OutputStream-com.aspose.slides.ISVGOptions-">writeAsSvg</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.aspose.slides.IGeometryShape">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;com.aspose.slides.<a href="../../../com/aspose/slides/IGeometryShape.html" title="interface in com.aspose.slides">IGeometryShape</a></h3>
<code><a href="../../../com/aspose/slides/IGeometryShape.html#createShapeElements--">createShapeElements</a>, <a href="../../../com/aspose/slides/IGeometryShape.html#getAdjustments--">getAdjustments</a>, <a href="../../../com/aspose/slides/IGeometryShape.html#getGeometryPaths--">getGeometryPaths</a>, <a href="../../../com/aspose/slides/IGeometryShape.html#getShapeStyle--">getShapeStyle</a>, <a href="../../../com/aspose/slides/IGeometryShape.html#setGeometryPath-com.aspose.slides.IGeometryPath-">setGeometryPath</a>, <a href="../../../com/aspose/slides/IGeometryShape.html#setGeometryPaths-com.aspose.slides.IGeometryPath:A-">setGeometryPaths</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.aspose.slides.IShape">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;com.aspose.slides.<a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a></h3>
<code><a href="../../../com/aspose/slides/IShape.html#addPlaceholder-com.aspose.slides.IPlaceholder-">addPlaceholder</a>, <a href="../../../com/aspose/slides/IShape.html#getAlternativeText--">getAlternativeText</a>, <a href="../../../com/aspose/slides/IShape.html#getAlternativeTextTitle--">getAlternativeTextTitle</a>, <a href="../../../com/aspose/slides/IShape.html#getBasePlaceholder--">getBasePlaceholder</a>, <a href="../../../com/aspose/slides/IShape.html#getBlackWhiteMode--">getBlackWhiteMode</a>, <a href="../../../com/aspose/slides/IShape.html#getConnectionSiteCount--">getConnectionSiteCount</a>, <a href="../../../com/aspose/slides/IShape.html#getCustomData--">getCustomData</a>, <a href="../../../com/aspose/slides/IShape.html#getEffectFormat--">getEffectFormat</a>, <a href="../../../com/aspose/slides/IShape.html#getFillFormat--">getFillFormat</a>, <a href="../../../com/aspose/slides/IShape.html#getFrame--">getFrame</a>, <a href="../../../com/aspose/slides/IShape.html#getHeight--">getHeight</a>, <a href="../../../com/aspose/slides/IShape.html#getHidden--">getHidden</a>, <a href="../../../com/aspose/slides/IShape.html#getImage--">getImage</a>, <a href="../../../com/aspose/slides/IShape.html#getImage-int-float-float-">getImage</a>, <a href="../../../com/aspose/slides/IShape.html#getLineFormat--">getLineFormat</a>, <a href="../../../com/aspose/slides/IShape.html#getName--">getName</a>, <a href="../../../com/aspose/slides/IShape.html#getOfficeInteropShapeId--">getOfficeInteropShapeId</a>, <a href="../../../com/aspose/slides/IShape.html#getParentGroup--">getParentGroup</a>, <a href="../../../com/aspose/slides/IShape.html#getPlaceholder--">getPlaceholder</a>, <a href="../../../com/aspose/slides/IShape.html#getRawFrame--">getRawFrame</a>, <a href="../../../com/aspose/slides/IShape.html#getRotation--">getRotation</a>, <a href="../../../com/aspose/slides/IShape.html#getShapeLock--">getShapeLock</a>, <a href="../../../com/aspose/slides/IShape.html#getThreeDFormat--">getThreeDFormat</a>, <a href="../../../com/aspose/slides/IShape.html#getThumbnail--">getThumbnail</a>, <a href="../../../com/aspose/slides/IShape.html#getThumbnail-int-float-float-">getThumbnail</a>, <a href="../../../com/aspose/slides/IShape.html#getUniqueId--">getUniqueId</a>, <a href="../../../com/aspose/slides/IShape.html#getWidth--">getWidth</a>, <a href="../../../com/aspose/slides/IShape.html#getX--">getX</a>, <a href="../../../com/aspose/slides/IShape.html#getY--">getY</a>, <a href="../../../com/aspose/slides/IShape.html#getZOrderPosition--">getZOrderPosition</a>, <a href="../../../com/aspose/slides/IShape.html#isDecorative--">isDecorative</a>, <a href="../../../com/aspose/slides/IShape.html#isGrouped--">isGrouped</a>, <a href="../../../com/aspose/slides/IShape.html#isTextHolder--">isTextHolder</a>, <a href="../../../com/aspose/slides/IShape.html#removePlaceholder--">removePlaceholder</a>, <a href="../../../com/aspose/slides/IShape.html#setAlternativeText-java.lang.String-">setAlternativeText</a>, <a href="../../../com/aspose/slides/IShape.html#setAlternativeTextTitle-java.lang.String-">setAlternativeTextTitle</a>, <a href="../../../com/aspose/slides/IShape.html#setBlackWhiteMode-byte-">setBlackWhiteMode</a>, <a href="../../../com/aspose/slides/IShape.html#setDecorative-boolean-">setDecorative</a>, <a href="../../../com/aspose/slides/IShape.html#setFrame-com.aspose.slides.IShapeFrame-">setFrame</a>, <a href="../../../com/aspose/slides/IShape.html#setHeight-float-">setHeight</a>, <a href="../../../com/aspose/slides/IShape.html#setHidden-boolean-">setHidden</a>, <a href="../../../com/aspose/slides/IShape.html#setName-java.lang.String-">setName</a>, <a href="../../../com/aspose/slides/IShape.html#setRawFrame-com.aspose.slides.IShapeFrame-">setRawFrame</a>, <a href="../../../com/aspose/slides/IShape.html#setRotation-float-">setRotation</a>, <a href="../../../com/aspose/slides/IShape.html#setWidth-float-">setWidth</a>, <a href="../../../com/aspose/slides/IShape.html#setX-float-">setX</a>, <a href="../../../com/aspose/slides/IShape.html#setY-float-">setY</a>, <a href="../../../com/aspose/slides/IShape.html#writeAsSvg-java.io.OutputStream-">writeAsSvg</a>, <a href="../../../com/aspose/slides/IShape.html#writeAsSvg-java.io.OutputStream-com.aspose.slides.ISVGOptions-">writeAsSvg</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.aspose.slides.ISlideComponent">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;com.aspose.slides.<a href="../../../com/aspose/slides/ISlideComponent.html" title="interface in com.aspose.slides">ISlideComponent</a></h3>
<code><a href="../../../com/aspose/slides/ISlideComponent.html#getSlide--">getSlide</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.aspose.slides.IPresentationComponent">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;com.aspose.slides.<a href="../../../com/aspose/slides/IPresentationComponent.html" title="interface in com.aspose.slides">IPresentationComponent</a></h3>
<code><a href="../../../com/aspose/slides/IPresentationComponent.html#getPresentation--">getPresentation</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.aspose.slides.IHyperlinkContainer">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;com.aspose.slides.<a href="../../../com/aspose/slides/IHyperlinkContainer.html" title="interface in com.aspose.slides">IHyperlinkContainer</a></h3>
<code><a href="../../../com/aspose/slides/IHyperlinkContainer.html#getHyperlinkClick--">getHyperlinkClick</a>, <a href="../../../com/aspose/slides/IHyperlinkContainer.html#getHyperlinkManager--">getHyperlinkManager</a>, <a href="../../../com/aspose/slides/IHyperlinkContainer.html#getHyperlinkMouseOver--">getHyperlinkMouseOver</a>, <a href="../../../com/aspose/slides/IHyperlinkContainer.html#setHyperlinkClick-com.aspose.slides.IHyperlink-">setHyperlinkClick</a>, <a href="../../../com/aspose/slides/IHyperlinkContainer.html#setHyperlinkMouseOver-com.aspose.slides.IHyperlink-">setHyperlinkMouseOver</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getShapeType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getShapeType</h4>
<pre>public&nbsp;int&nbsp;getShapeType()</pre>
<div class="block"><p>
 Returns or sets the geometry preset type.
 Note: on value changing all adjustment values will reset to their default values.
 Read/write <a href="../../../com/aspose/slides/ShapeType.html" title="class in com.aspose.slides"><code>ShapeType</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IGeometryShape.html#getShapeType--">getShapeType</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IGeometryShape.html" title="interface in com.aspose.slides">IGeometryShape</a></code></dd>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../com/aspose/slides/GeometryShape.html#getShapeType--">getShapeType</a></code>&nbsp;in class&nbsp;<code><a href="../../../com/aspose/slides/GeometryShape.html" title="class in com.aspose.slides">GeometryShape</a></code></dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>com.aspose.ms.System.ArgumentException</code> - Thrown when value is ShapeType.NotDefined or ShapeType.Custom</dd>
</dl>
</li>
</ul>
<a name="setShapeType-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setShapeType</h4>
<pre>public&nbsp;void&nbsp;setShapeType(int&nbsp;value)</pre>
<div class="block"><p>
 Returns or sets the geometry preset type.
 Note: on value changing all adjustment values will reset to their default values.
 Read/write <a href="../../../com/aspose/slides/ShapeType.html" title="class in com.aspose.slides"><code>ShapeType</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IGeometryShape.html#setShapeType-int-">setShapeType</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IGeometryShape.html" title="interface in com.aspose.slides">IGeometryShape</a></code></dd>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../com/aspose/slides/GeometryShape.html#setShapeType-int-">setShapeType</a></code>&nbsp;in class&nbsp;<code><a href="../../../com/aspose/slides/GeometryShape.html" title="class in com.aspose.slides">GeometryShape</a></code></dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>com.aspose.ms.System.ArgumentException</code> - Thrown when value is ShapeType.NotDefined or ShapeType.Custom</dd>
</dl>
</li>
</ul>
<a name="getTextFrame--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getTextFrame</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/ITextFrame.html" title="interface in com.aspose.slides">ITextFrame</a>&nbsp;getTextFrame()</pre>
<div class="block"><p>
 Returns text of the SmartArt shape.
 Read-only <a href="../../../com/aspose/slides/ITextFrame.html" title="interface in com.aspose.slides"><code>ITextFrame</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISmartArtShape.html#getTextFrame--">getTextFrame</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISmartArtShape.html" title="interface in com.aspose.slides">ISmartArtShape</a></code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/SmartArtQuickStyleType.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/SmartArtShapeCollection.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/SmartArtShape.html" target="_top">Frames</a></li>
<li><a href="SmartArtShape.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><i>Copyright &copy; 2004-2025 Aspose Pty Ltd. All Rights Reserved.</i></small></p>
</body>
</html>
