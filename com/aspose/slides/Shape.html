<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_421) on Fri Jul 11 05:32:44 CDT 2025 -->
<title>Shape (Aspose.Slides for Java (25.7) API Documentation)</title>
<meta name="date" content="2025-07-11">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Shape (Aspose.Slides for Java (25.7) API Documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10,"i26":10,"i27":10,"i28":10,"i29":42,"i30":42,"i31":10,"i32":10,"i33":10,"i34":10,"i35":10,"i36":10,"i37":10,"i38":10,"i39":10,"i40":10,"i41":10,"i42":10,"i43":10,"i44":10,"i45":10,"i46":10,"i47":10,"i48":10,"i49":10,"i50":10,"i51":10,"i52":10,"i53":10,"i54":10,"i55":10,"i56":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"],32:["t6","Deprecated Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/SetEffect.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/ShapeAdjustmentType.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/Shape.html" target="_top">Frames</a></li>
<li><a href="Shape.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.aspose.slides</div>
<h2 title="Class Shape" class="title">Class Shape</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.aspose.slides.Shape</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../com/aspose/slides/IHyperlinkContainer.html" title="interface in com.aspose.slides">IHyperlinkContainer</a>, <a href="../../../com/aspose/slides/IPresentationComponent.html" title="interface in com.aspose.slides">IPresentationComponent</a>, <a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a>, <a href="../../../com/aspose/slides/ISlideComponent.html" title="interface in com.aspose.slides">ISlideComponent</a></dd>
</dl>
<dl>
<dt>Direct Known Subclasses:</dt>
<dd><a href="../../../com/aspose/slides/GeometryShape.html" title="class in com.aspose.slides">GeometryShape</a>, <a href="../../../com/aspose/slides/GraphicalObject.html" title="class in com.aspose.slides">GraphicalObject</a>, <a href="../../../com/aspose/slides/GroupShape.html" title="class in com.aspose.slides">GroupShape</a></dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">Shape</span>
extends java.lang.Object
implements <a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a></pre>
<div class="block"><p>
  Represents a shape on a slide.
 </p></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t6" class="tableTab"><span><a href="javascript:show(32);">Deprecated Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IPlaceholder.html" title="interface in com.aspose.slides">IPlaceholder</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Shape.html#addPlaceholder-com.aspose.slides.IPlaceholder-">addPlaceholder</a></span>(<a href="../../../com/aspose/slides/IPlaceholder.html" title="interface in com.aspose.slides">IPlaceholder</a>&nbsp;placeholderToCopyFrom)</code>
<div class="block">
 Adds a new placeholder if there is no and sets placeholder properties to a specified one.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Shape.html#getAlternativeText--">getAlternativeText</a></span>()</code>
<div class="block">
 Returns or sets the alternative text associated with a shape.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Shape.html#getAlternativeTextTitle--">getAlternativeTextTitle</a></span>()</code>
<div class="block">
 Returns or sets the title of alternative text associated with a shape.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Shape.html#getBasePlaceholder--">getBasePlaceholder</a></span>()</code>
<div class="block">
 Returns a basic placeholder shape (shape from the layout and/or master slide that the current shape is inherited from).</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>byte</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Shape.html#getBlackWhiteMode--">getBlackWhiteMode</a></span>()</code>
<div class="block">
 Property specifies how a shape will render in black-and-white display mode..</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Shape.html#getConnectionSiteCount--">getConnectionSiteCount</a></span>()</code>
<div class="block">
 Returns the number of connection sites on the shape.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/ICustomData.html" title="interface in com.aspose.slides">ICustomData</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Shape.html#getCustomData--">getCustomData</a></span>()</code>
<div class="block">
 Returns the shape's custom data.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IEffectFormat.html" title="interface in com.aspose.slides">IEffectFormat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Shape.html#getEffectFormat--">getEffectFormat</a></span>()</code>
<div class="block">
 Returns the EffectFormat object which contains pixel effects applied to a shape.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IFillFormat.html" title="interface in com.aspose.slides">IFillFormat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Shape.html#getFillFormat--">getFillFormat</a></span>()</code>
<div class="block">
 Returns the FillFormat object that contains fill formatting properties for a shape.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IShapeFrame.html" title="interface in com.aspose.slides">IShapeFrame</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Shape.html#getFrame--">getFrame</a></span>()</code>
<div class="block">
 Returns or sets the shape frame's properties.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Shape.html#getHeight--">getHeight</a></span>()</code>
<div class="block">
 Gets or sets the height of the shape, measured in points.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Shape.html#getHidden--">getHidden</a></span>()</code>
<div class="block">
 Determines whether the shape is hidden.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IHyperlink.html" title="interface in com.aspose.slides">IHyperlink</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Shape.html#getHyperlinkClick--">getHyperlinkClick</a></span>()</code>
<div class="block">
 Returns or sets the hyperlink defined for mouse click.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IHyperlinkManager.html" title="interface in com.aspose.slides">IHyperlinkManager</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Shape.html#getHyperlinkManager--">getHyperlinkManager</a></span>()</code>
<div class="block">
 Returns the hyperlink manager.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IHyperlink.html" title="interface in com.aspose.slides">IHyperlink</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Shape.html#getHyperlinkMouseOver--">getHyperlinkMouseOver</a></span>()</code>
<div class="block">
 Returns or sets the hyperlink defined for mouse over.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IImage.html" title="interface in com.aspose.slides">IImage</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Shape.html#getImage--">getImage</a></span>()</code>
<div class="block">
 Returns shape thumbnail.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IImage.html" title="interface in com.aspose.slides">IImage</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Shape.html#getImage-int-float-float-">getImage</a></span>(int&nbsp;bounds,
        float&nbsp;scaleX,
        float&nbsp;scaleY)</code>
<div class="block">
 Returns shape thumbnail.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/ILineFormat.html" title="interface in com.aspose.slides">ILineFormat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Shape.html#getLineFormat--">getLineFormat</a></span>()</code>
<div class="block">
 Returns the LineFormat object that contains line formatting properties for a shape.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Shape.html#getName--">getName</a></span>()</code>
<div class="block">
 Returns or sets the name of a shape.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Shape.html#getOfficeInteropShapeId--">getOfficeInteropShapeId</a></span>()</code>
<div class="block">
 Returns a slide-scoped unique identifier that remains constant for the lifetime of the shape and
 lets PowerPoint or interop code reliably reference the shape from anywhere in the document.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>com.aspose.slides.IDOMObject</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Shape.html#getParent_Immediate--">getParent_Immediate</a></span>()</code>
<div class="block">
 Returns Parent_Immediate object.</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IGroupShape.html" title="interface in com.aspose.slides">IGroupShape</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Shape.html#getParentGroup--">getParentGroup</a></span>()</code>
<div class="block">
 Returns parent GroupShape object if shape is grouped.</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IPlaceholder.html" title="interface in com.aspose.slides">IPlaceholder</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Shape.html#getPlaceholder--">getPlaceholder</a></span>()</code>
<div class="block">
 Returns the placeholder for a shape.</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IPresentation.html" title="interface in com.aspose.slides">IPresentation</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Shape.html#getPresentation--">getPresentation</a></span>()</code>
<div class="block">
 Returns the parent presentation of a slide.</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IShapeFrame.html" title="interface in com.aspose.slides">IShapeFrame</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Shape.html#getRawFrame--">getRawFrame</a></span>()</code>
<div class="block">
 Returns or sets the raw shape frame's properties.</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Shape.html#getRotation--">getRotation</a></span>()</code>
<div class="block">
 Returns or sets the number of degrees the specified shape is rotated around
 the z-axis.</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IBaseShapeLock.html" title="interface in com.aspose.slides">IBaseShapeLock</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Shape.html#getShapeLock--">getShapeLock</a></span>()</code>
<div class="block">
 Returns shape's locks.</div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IBaseSlide.html" title="interface in com.aspose.slides">IBaseSlide</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Shape.html#getSlide--">getSlide</a></span>()</code>
<div class="block">
 Returns the parent slide of a shape.</div>
</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IThreeDFormat.html" title="interface in com.aspose.slides">IThreeDFormat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Shape.html#getThreeDFormat--">getThreeDFormat</a></span>()</code>
<div class="block">
 Returns the ThreeDFormat object that 3d effect properties for a shape.</div>
</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code>java.awt.image.BufferedImage</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Shape.html#getThumbnail--">getThumbnail</a></span>()</code>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;
<div class="block"><span class="deprecationComment">Use getImage method instead. The method will be removed after release of version 24.7.</span></div>
</div>
</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code>java.awt.image.BufferedImage</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Shape.html#getThumbnail-int-float-float-">getThumbnail</a></span>(int&nbsp;bounds,
            float&nbsp;scaleX,
            float&nbsp;scaleY)</code>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;
<div class="block"><span class="deprecationComment">Use getImage(ShapeThumbnailBounds bounds, float scaleX, float scaleY) method instead. The method will be removed after release of version 24.7.</span></div>
</div>
</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code>long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Shape.html#getUniqueId--">getUniqueId</a></span>()</code>
<div class="block">
 Returns an internal, presentation-scoped identifier intended for use by add-ins or other code.</div>
</td>
</tr>
<tr id="i32" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Shape.html#getWidth--">getWidth</a></span>()</code>
<div class="block">
 Gets or sets the width of the shape, measured in points.</div>
</td>
</tr>
<tr id="i33" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Shape.html#getX--">getX</a></span>()</code>
<div class="block">
 Gets or sets the x-coordinate of the shape's upper-left corner, measured in points.</div>
</td>
</tr>
<tr id="i34" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Shape.html#getY--">getY</a></span>()</code>
<div class="block">
 Gets or sets the y-coordinate of the shape's upper-left corner, measured in points.</div>
</td>
</tr>
<tr id="i35" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Shape.html#getZOrderPosition--">getZOrderPosition</a></span>()</code>
<div class="block">
 Returns the position of a shape in the z-order.</div>
</td>
</tr>
<tr id="i36" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Shape.html#isDecorative--">isDecorative</a></span>()</code>
<div class="block">
 Gets or sets 'Mark as decorative' option
 Reed/write boolean.</div>
</td>
</tr>
<tr id="i37" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Shape.html#isGrouped--">isGrouped</a></span>()</code>
<div class="block">
 Determines whether the shape is grouped.</div>
</td>
</tr>
<tr id="i38" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Shape.html#isTextHolder--">isTextHolder</a></span>()</code>
<div class="block">
 Determines whether the shape is TextHolder_PPT.</div>
</td>
</tr>
<tr id="i39" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Shape.html#removePlaceholder--">removePlaceholder</a></span>()</code>
<div class="block">
 Defines that this shape isn't a placeholder.</div>
</td>
</tr>
<tr id="i40" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Shape.html#setAlternativeText-java.lang.String-">setAlternativeText</a></span>(java.lang.String&nbsp;value)</code>
<div class="block">
 Returns or sets the alternative text associated with a shape.</div>
</td>
</tr>
<tr id="i41" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Shape.html#setAlternativeTextTitle-java.lang.String-">setAlternativeTextTitle</a></span>(java.lang.String&nbsp;value)</code>
<div class="block">
 Returns or sets the title of alternative text associated with a shape.</div>
</td>
</tr>
<tr id="i42" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Shape.html#setBlackWhiteMode-byte-">setBlackWhiteMode</a></span>(byte&nbsp;value)</code>
<div class="block">
 Property specifies how a shape will render in black-and-white display mode..</div>
</td>
</tr>
<tr id="i43" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Shape.html#setDecorative-boolean-">setDecorative</a></span>(boolean&nbsp;value)</code>
<div class="block">
 Gets or sets 'Mark as decorative' option
 Reed/write boolean.</div>
</td>
</tr>
<tr id="i44" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Shape.html#setFrame-com.aspose.slides.IShapeFrame-">setFrame</a></span>(<a href="../../../com/aspose/slides/IShapeFrame.html" title="interface in com.aspose.slides">IShapeFrame</a>&nbsp;value)</code>
<div class="block">
 Returns or sets the shape frame's properties.</div>
</td>
</tr>
<tr id="i45" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Shape.html#setHeight-float-">setHeight</a></span>(float&nbsp;value)</code>
<div class="block">
 Gets or sets the height of the shape, measured in points.</div>
</td>
</tr>
<tr id="i46" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Shape.html#setHidden-boolean-">setHidden</a></span>(boolean&nbsp;value)</code>
<div class="block">
 Determines whether the shape is hidden.</div>
</td>
</tr>
<tr id="i47" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Shape.html#setHyperlinkClick-com.aspose.slides.IHyperlink-">setHyperlinkClick</a></span>(<a href="../../../com/aspose/slides/IHyperlink.html" title="interface in com.aspose.slides">IHyperlink</a>&nbsp;value)</code>
<div class="block">
 Returns or sets the hyperlink defined for mouse click.</div>
</td>
</tr>
<tr id="i48" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Shape.html#setHyperlinkMouseOver-com.aspose.slides.IHyperlink-">setHyperlinkMouseOver</a></span>(<a href="../../../com/aspose/slides/IHyperlink.html" title="interface in com.aspose.slides">IHyperlink</a>&nbsp;value)</code>
<div class="block">
 Returns or sets the hyperlink defined for mouse over.</div>
</td>
</tr>
<tr id="i49" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Shape.html#setName-java.lang.String-">setName</a></span>(java.lang.String&nbsp;value)</code>
<div class="block">
 Returns or sets the name of a shape.</div>
</td>
</tr>
<tr id="i50" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Shape.html#setRawFrame-com.aspose.slides.IShapeFrame-">setRawFrame</a></span>(<a href="../../../com/aspose/slides/IShapeFrame.html" title="interface in com.aspose.slides">IShapeFrame</a>&nbsp;value)</code>
<div class="block">
 Returns or sets the raw shape frame's properties.</div>
</td>
</tr>
<tr id="i51" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Shape.html#setRotation-float-">setRotation</a></span>(float&nbsp;value)</code>
<div class="block">
 Returns or sets the number of degrees the specified shape is rotated around
 the z-axis.</div>
</td>
</tr>
<tr id="i52" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Shape.html#setWidth-float-">setWidth</a></span>(float&nbsp;value)</code>
<div class="block">
 Gets or sets the width of the shape, measured in points.</div>
</td>
</tr>
<tr id="i53" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Shape.html#setX-float-">setX</a></span>(float&nbsp;value)</code>
<div class="block">
 Gets or sets the x-coordinate of the shape's upper-left corner, measured in points.</div>
</td>
</tr>
<tr id="i54" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Shape.html#setY-float-">setY</a></span>(float&nbsp;value)</code>
<div class="block">
 Gets or sets the y-coordinate of the shape's upper-left corner, measured in points.</div>
</td>
</tr>
<tr id="i55" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Shape.html#writeAsSvg-java.io.OutputStream-">writeAsSvg</a></span>(java.io.OutputStream&nbsp;stream)</code>
<div class="block">
 Saves content of Shape as SVG file.</div>
</td>
</tr>
<tr id="i56" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Shape.html#writeAsSvg-java.io.OutputStream-com.aspose.slides.ISVGOptions-">writeAsSvg</a></span>(java.io.OutputStream&nbsp;stream,
          <a href="../../../com/aspose/slides/ISVGOptions.html" title="interface in com.aspose.slides">ISVGOptions</a>&nbsp;svgOptions)</code>
<div class="block">
 Saves content of Shape as SVG file.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="isTextHolder--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isTextHolder</h4>
<pre>public final&nbsp;boolean&nbsp;isTextHolder()</pre>
<div class="block"><p>
 Determines whether the shape is TextHolder_PPT.
 Read-only <code>boolean</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShape.html#isTextHolder--">isTextHolder</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a></code></dd>
</dl>
</li>
</ul>
<a name="getPlaceholder--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPlaceholder</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IPlaceholder.html" title="interface in com.aspose.slides">IPlaceholder</a>&nbsp;getPlaceholder()</pre>
<div class="block"><p>
 Returns the placeholder for a shape. Returns null if the shape has no placeholder.
 Read-only <a href="../../../com/aspose/slides/IPlaceholder.html" title="interface in com.aspose.slides"><code>IPlaceholder</code></a>.
 </p><p><hr><blockquote><pre>
 The following example shows how to change Text in Placeholder.
 <pre>
 // Instantiates a Presentation class
 Presentation pres = new Presentation("ReplacingText.pptx");
 try {
     // Accesses the first slide
     ISlide sld = pres.getSlides().get_Item(0);
     // Iterates through shapes to find the placeholder
     for (IShape shp : sld.getShapes())
         if (shp.getPlaceholder() != null)
         {
             // Changes the text in each placeholder
             ((IAutoShape)shp).getTextFrame().setText("This is a Placeholder");
         }
     // Saves the presentation to disk
     pres.save("output_out.pptx", SaveFormat.Pptx);
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 The following example shows how to set Prompt Text in Placeholder.
 <pre>
 Presentation pres = new Presentation("Presentation2.pptx");
 try {
     ISlide slide = pres.getSlides().get_Item(0);
     for (IShape shape : slide.getSlide().getShapes()) // Iterates through the slide
     {
         if (shape.getPlaceholder() != null &amp;&amp; shape instanceof AutoShape)
         {
             String text = "";
             if (shape.getPlaceholder().getType() == PlaceholderType.CenteredTitle) // PowerPoint displays "Click to add title"
             {
                 text = "Add Title";
             }
             else if (shape.getPlaceholder().getType() == PlaceholderType.Subtitle) // Adds subtitle
             {
                 text = "Add Subtitle";
             }
             ((IAutoShape)shape).getTextFrame().setText(text);
             System.out.println("Placeholder with text: " + text);
         }
     }
     pres.save("Placeholders_PromptText.pptx", SaveFormat.Pptx);
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShape.html#getPlaceholder--">getPlaceholder</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a></code></dd>
</dl>
</li>
</ul>
<a name="removePlaceholder--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>removePlaceholder</h4>
<pre>public final&nbsp;void&nbsp;removePlaceholder()</pre>
<div class="block"><p>
 Defines that this shape isn't a placeholder.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShape.html#removePlaceholder--">removePlaceholder</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a></code></dd>
</dl>
</li>
</ul>
<a name="addPlaceholder-com.aspose.slides.IPlaceholder-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addPlaceholder</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IPlaceholder.html" title="interface in com.aspose.slides">IPlaceholder</a>&nbsp;addPlaceholder(<a href="../../../com/aspose/slides/IPlaceholder.html" title="interface in com.aspose.slides">IPlaceholder</a>&nbsp;placeholderToCopyFrom)</pre>
<div class="block"><p>
 Adds a new placeholder if there is no and sets placeholder properties to a specified one.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShape.html#addPlaceholder-com.aspose.slides.IPlaceholder-">addPlaceholder</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>placeholderToCopyFrom</code> - Placeholder to copy content from.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>New <a href="../../../com/aspose/slides/Shape.html#getPlaceholder--"><code>getPlaceholder()</code></a>.</dd>
</dl>
</li>
</ul>
<a name="getBasePlaceholder--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBasePlaceholder</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a>&nbsp;getBasePlaceholder()</pre>
<div class="block"><p>
 Returns a basic placeholder shape (shape from the layout and/or master slide that the current shape is inherited from).
  
 </p><p><hr><blockquote><pre>
 <pre>
 // get all (master/layout/slide) animated effects of the placeholder shape
 Presentation pres = new Presentation("sample.pptx");
 try {
     ISlide slide = pres.getSlides().get_Item(0);
     IShape shape = slide.getShapes().get_Item(0);
     IEffect[] shapeEffects = slide.getTimeline().getMainSequence().getEffectsByShape(shape);
     IShape layoutShape = shape.getBasePlaceholder();
     IEffect[] layoutShapeEffects = slide.getLayoutSlide().getTimeline().getMainSequence().getEffectsByShape(layoutShape);
     IShape masterShape = layoutShape.getBasePlaceholder();
     IEffect[] masterShapeEffects = slide.getLayoutSlide().getMasterSlide().getTimeline().getMainSequence().getEffectsByShape(masterShape);
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p><p><hr>A null is returned if the current shape is not inherited.</hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShape.html#getBasePlaceholder--">getBasePlaceholder</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a></code></dd>
</dl>
</li>
</ul>
<a name="getCustomData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCustomData</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/ICustomData.html" title="interface in com.aspose.slides">ICustomData</a>&nbsp;getCustomData()</pre>
<div class="block"><p>
 Returns the shape's custom data.
 Read-only <a href="../../../com/aspose/slides/ICustomData.html" title="interface in com.aspose.slides"><code>ICustomData</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShape.html#getCustomData--">getCustomData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a></code></dd>
</dl>
</li>
</ul>
<a name="getRawFrame--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRawFrame</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IShapeFrame.html" title="interface in com.aspose.slides">IShapeFrame</a>&nbsp;getRawFrame()</pre>
<div class="block"><p>
 Returns or sets the raw shape frame's properties.
 Read/write <a href="../../../com/aspose/slides/IShapeFrame.html" title="interface in com.aspose.slides"><code>IShapeFrame</code></a>.
 </p><p><hr><blockquote><pre>
 Code that attempts to assign undefined frame to IShape.getFrame() doesn't make sense in general case (particularly in case when parent GroupShape is multiple nested into other GroupShape-s). For example:
 <pre>
 IShape shape = ...;
 shape.setFrame(new ShapeFrame(Float.NaN, Float.NaN, Float.NaN, Float.NaN, NullableBool.NotDefined, NullableBool.NotDefined, Float.NaN));
 //or
 slide.getShapes().addAutoShape(ShapeType.RoundCornerRectangle, Float.NaN, Float.NaN, Float.NaN, Float.NaN);
 //Such code can lead to unclear situations. So restrictions had been added for using undefined values for IShape.getFrame(). Values of x, y, width, height, flipH, flipV and rotationAngle must be defined (not Float.NaN or NullableBool.NotDefined). Example code above now throws ArgumentException exception.
 //This applies to these use cases:
 IShape shape = ...;
 shape.setFrame(...); // cannot be undefined
 IShapeCollection shapes = ...;
 // x, y, width, height parameters cannot be Float.NaN:
 {
     shapes.addAudioFrameCD(...);
     shapes.addAudioFrameEmbedded(...);
     shapes.addAudioFrameLinked(...);
     shapes.addAutoShape(...);
     shapes.addChart(...);
     shapes.addConnector(...);
     shapes.addOleObjectFrame(...);
     shapes.addPictureFrame(...);
     shapes.addSmartArt(...);
     shapes.addTable(...);
     shapes.addVideoFrame(...);
     shapes.insertAudioFrameEmbedded(...);
     shapes.insertAudioFrameLinked(...);
     shapes.insertAutoShape(...);
     shapes.insertChart(...);
     shapes.insertConnector(...);
     shapes.insertOleObjectFrame(...);
     shapes.insertPictureFrame(...);
     shapes.insertTable(...);
     shapes.insertVideoFrame(...);
 }
 //But IShape.RawFrame frame properties can be undefined. This make sence when shape is linked to placeholder. Then undefined shape frame values is overridden from the parent placeholder shape. If there is no parent placeholder shape for that shape then that shape uses default values when it evaluates effective frame based on its IShape.RawFrame. Default values are 0 and NullableBool.False for x, y, width, height, flipH, flipV and rotationAngle. For example:
 IShape shape = ...; // shape is linked to placeholder
 shape.setRawFrame(new ShapeFrame(Float.NaN, Float.NaN, 100, Float.NaN, NullableBool.NotDefined, NullableBool.NotDefined, 0)); // now shape inherits x, y, height, flipH, flipV values form placeholder and overrides width=100 and rotationAngle=0.{code}
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShape.html#getRawFrame--">getRawFrame</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a></code></dd>
</dl>
</li>
</ul>
<a name="setRawFrame-com.aspose.slides.IShapeFrame-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRawFrame</h4>
<pre>public final&nbsp;void&nbsp;setRawFrame(<a href="../../../com/aspose/slides/IShapeFrame.html" title="interface in com.aspose.slides">IShapeFrame</a>&nbsp;value)</pre>
<div class="block"><p>
 Returns or sets the raw shape frame's properties.
 Read/write <a href="../../../com/aspose/slides/IShapeFrame.html" title="interface in com.aspose.slides"><code>IShapeFrame</code></a>.
 </p><p><hr><blockquote><pre>
 Code that attempts to assign undefined frame to IShape.getFrame() doesn't make sense in general case (particularly in case when parent GroupShape is multiple nested into other GroupShape-s). For example:
 <pre>
 IShape shape = ...;
 shape.setFrame(new ShapeFrame(Float.NaN, Float.NaN, Float.NaN, Float.NaN, NullableBool.NotDefined, NullableBool.NotDefined, Float.NaN));
 //or
 slide.getShapes().addAutoShape(ShapeType.RoundCornerRectangle, Float.NaN, Float.NaN, Float.NaN, Float.NaN);
 //Such code can lead to unclear situations. So restrictions had been added for using undefined values for IShape.getFrame(). Values of x, y, width, height, flipH, flipV and rotationAngle must be defined (not Float.NaN or NullableBool.NotDefined). Example code above now throws ArgumentException exception.
 //This applies to these use cases:
 IShape shape = ...;
 shape.setFrame(...); // cannot be undefined
 IShapeCollection shapes = ...;
 // x, y, width, height parameters cannot be Float.NaN:
 {
     shapes.addAudioFrameCD(...);
     shapes.addAudioFrameEmbedded(...);
     shapes.addAudioFrameLinked(...);
     shapes.addAutoShape(...);
     shapes.addChart(...);
     shapes.addConnector(...);
     shapes.addOleObjectFrame(...);
     shapes.addPictureFrame(...);
     shapes.addSmartArt(...);
     shapes.addTable(...);
     shapes.addVideoFrame(...);
     shapes.insertAudioFrameEmbedded(...);
     shapes.insertAudioFrameLinked(...);
     shapes.insertAutoShape(...);
     shapes.insertChart(...);
     shapes.insertConnector(...);
     shapes.insertOleObjectFrame(...);
     shapes.insertPictureFrame(...);
     shapes.insertTable(...);
     shapes.insertVideoFrame(...);
 }
 //But IShape.RawFrame frame properties can be undefined. This make sence when shape is linked to placeholder. Then undefined shape frame values is overridden from the parent placeholder shape. If there is no parent placeholder shape for that shape then that shape uses default values when it evaluates effective frame based on its IShape.RawFrame. Default values are 0 and NullableBool.False for x, y, width, height, flipH, flipV and rotationAngle. For example:
 IShape shape = ...; // shape is linked to placeholder
 shape.setRawFrame(new ShapeFrame(Float.NaN, Float.NaN, 100, Float.NaN, NullableBool.NotDefined, NullableBool.NotDefined, 0)); // now shape inherits x, y, height, flipH, flipV values form placeholder and overrides width=100 and rotationAngle=0.{code}
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShape.html#setRawFrame-com.aspose.slides.IShapeFrame-">setRawFrame</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a></code></dd>
</dl>
</li>
</ul>
<a name="getFrame--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFrame</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IShapeFrame.html" title="interface in com.aspose.slides">IShapeFrame</a>&nbsp;getFrame()</pre>
<div class="block"><p>
 Returns or sets the shape frame's properties.
 Read/write <a href="../../../com/aspose/slides/IShapeFrame.html" title="interface in com.aspose.slides"><code>IShapeFrame</code></a>.
 </p><p><hr>
 Value of each property of the returned IShapeFrame instance is not 
 undefined (is not NaN or NotDefined).
 Value of each property of the assigned IShapeFrame instance must be not 
 undefined (must be not NaN or NotDefined).
 You can set undefined values for RawFrame instance properties.
 </hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShape.html#getFrame--">getFrame</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a></code></dd>
</dl>
</li>
</ul>
<a name="setFrame-com.aspose.slides.IShapeFrame-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFrame</h4>
<pre>public final&nbsp;void&nbsp;setFrame(<a href="../../../com/aspose/slides/IShapeFrame.html" title="interface in com.aspose.slides">IShapeFrame</a>&nbsp;value)</pre>
<div class="block"><p>
 Returns or sets the shape frame's properties.
 Read/write <a href="../../../com/aspose/slides/IShapeFrame.html" title="interface in com.aspose.slides"><code>IShapeFrame</code></a>.
 </p><p><hr>
 Value of each property of the returned IShapeFrame instance is not 
 undefined (is not NaN or NotDefined).
 Value of each property of the assigned IShapeFrame instance must be not 
 undefined (must be not NaN or NotDefined).
 You can set undefined values for RawFrame instance properties.
 </hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShape.html#setFrame-com.aspose.slides.IShapeFrame-">setFrame</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a></code></dd>
</dl>
</li>
</ul>
<a name="getLineFormat--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLineFormat</h4>
<pre>public&nbsp;<a href="../../../com/aspose/slides/ILineFormat.html" title="interface in com.aspose.slides">ILineFormat</a>&nbsp;getLineFormat()</pre>
<div class="block"><p>
 Returns the LineFormat object that contains line formatting properties for a shape.
 Note: can return null for certain types of shapes which don't have line properties.
 Read-only <a href="../../../com/aspose/slides/ILineFormat.html" title="interface in com.aspose.slides"><code>ILineFormat</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShape.html#getLineFormat--">getLineFormat</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a></code></dd>
</dl>
</li>
</ul>
<a name="getThreeDFormat--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getThreeDFormat</h4>
<pre>public&nbsp;<a href="../../../com/aspose/slides/IThreeDFormat.html" title="interface in com.aspose.slides">IThreeDFormat</a>&nbsp;getThreeDFormat()</pre>
<div class="block"><p>
 Returns the ThreeDFormat object that 3d effect properties for a shape.
 Note: can return null for certain types of shapes which don't have 3d properties.
 Read-only <a href="../../../com/aspose/slides/IThreeDFormat.html" title="interface in com.aspose.slides"><code>IThreeDFormat</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShape.html#getThreeDFormat--">getThreeDFormat</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a></code></dd>
</dl>
</li>
</ul>
<a name="getEffectFormat--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEffectFormat</h4>
<pre>public&nbsp;<a href="../../../com/aspose/slides/IEffectFormat.html" title="interface in com.aspose.slides">IEffectFormat</a>&nbsp;getEffectFormat()</pre>
<div class="block"><p>
 Returns the EffectFormat object which contains pixel effects applied to a shape.
 Note: can return null for certain types of shapes which don't have effect properties.
 Read-only <a href="../../../com/aspose/slides/IEffectFormat.html" title="interface in com.aspose.slides"><code>IEffectFormat</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShape.html#getEffectFormat--">getEffectFormat</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a></code></dd>
</dl>
</li>
</ul>
<a name="getFillFormat--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFillFormat</h4>
<pre>public&nbsp;<a href="../../../com/aspose/slides/IFillFormat.html" title="interface in com.aspose.slides">IFillFormat</a>&nbsp;getFillFormat()</pre>
<div class="block"><p>
 Returns the FillFormat object that contains fill formatting properties for a shape.
 Note: can return null for certain types of shapes which don't have fill properties.
 Read-only <a href="../../../com/aspose/slides/IFillFormat.html" title="interface in com.aspose.slides"><code>IFillFormat</code></a>.
 </p><p><hr><blockquote><pre>
 The following example shows how to change the accent color for a theme of PowerPoint Presentation.
 <pre>
 Presentation pres = new Presentation();
 try {
     IAutoShape shape = pres.getSlides().get_Item(0).getShapes().addAutoShape(ShapeType.Rectangle, 10, 10, 100, 100);
     shape.getFillFormat().setFillType(FillType.Solid);
     shape.getFillFormat().getSolidFillColor().setSchemeColor(SchemeColor.Accent4);
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 The following example demonstrates how to obtain palette colors from the main theme color and then used in shapes.
 <pre>
 Presentation pres = new Presentation();
 try {
     ISlide slide = pres.getSlides().get_Item(0);
     // Accent 4
     IShape shape1 = slide.getShapes().addAutoShape(ShapeType.Rectangle, 10, 10, 50, 50);
     shape1.getFillFormat().setFillType(FillType.Solid);
     shape1.getFillFormat().getSolidFillColor().setSchemeColor(SchemeColor.Accent4);
     // Accent 4, Lighter 80%
     IShape shape2 = slide.getShapes().addAutoShape(ShapeType.Rectangle, 10, 70, 50, 50);
     shape2.getFillFormat().setFillType(FillType.Solid);
     shape2.getFillFormat().getSolidFillColor().setSchemeColor(SchemeColor.Accent4);
     shape2.getFillFormat().getSolidFillColor().getColorTransform().add(ColorTransformOperation.MultiplyLuminance, 0.2f);
     shape2.getFillFormat().getSolidFillColor().getColorTransform().add(ColorTransformOperation.AddLuminance, 0.8f);
     // Accent 4, Lighter 60%
     IShape shape3 = slide.getShapes().addAutoShape(ShapeType.Rectangle, 10, 130, 50, 50);
     shape3.getFillFormat().setFillType(FillType.Solid);
     shape3.getFillFormat().getSolidFillColor().setSchemeColor(SchemeColor.Accent4);
     shape3.getFillFormat().getSolidFillColor().getColorTransform().add(ColorTransformOperation.MultiplyLuminance, 0.4f);
     shape3.getFillFormat().getSolidFillColor().getColorTransform().add(ColorTransformOperation.AddLuminance, 0.6f);
     // Accent 4, Lighter 40%
     IShape shape4 = slide.getShapes().addAutoShape(ShapeType.Rectangle, 10, 190, 50, 50);
     shape4.getFillFormat().setFillType(FillType.Solid);
     shape4.getFillFormat().getSolidFillColor().setSchemeColor(SchemeColor.Accent4);
     shape4.getFillFormat().getSolidFillColor().getColorTransform().add(ColorTransformOperation.MultiplyLuminance, 0.6f);
     shape4.getFillFormat().getSolidFillColor().getColorTransform().add(ColorTransformOperation.AddLuminance, 0.4f);
     // Accent 4, Darker 25%
     IShape shape5 = slide.getShapes().addAutoShape(ShapeType.Rectangle, 10, 250, 50, 50);
     shape5.getFillFormat().setFillType(FillType.Solid);
     shape5.getFillFormat().getSolidFillColor().setSchemeColor(SchemeColor.Accent4);
     shape5.getFillFormat().getSolidFillColor().getColorTransform().add(ColorTransformOperation.MultiplyLuminance, 0.75f);
     // Accent 4, Darker 50%
     IShape shape6 = slide.getShapes().addAutoShape(ShapeType.Rectangle, 10, 310, 50, 50);
     shape6.getFillFormat().setFillType(FillType.Solid);
     shape6.getFillFormat().getSolidFillColor().setSchemeColor(SchemeColor.Accent4);
     shape6.getFillFormat().getSolidFillColor().getColorTransform().add(ColorTransformOperation.MultiplyLuminance, 0.5f);
     pres.save("example_accent4.pptx", SaveFormat.Pptx);
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShape.html#getFillFormat--">getFillFormat</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a></code></dd>
</dl>
</li>
</ul>
<a name="getThumbnail--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getThumbnail</h4>
<pre>@Deprecated
public final&nbsp;java.awt.image.BufferedImage&nbsp;getThumbnail()</pre>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;<span class="deprecationComment">Use getImage method instead. The method will be removed after release of version 24.7.</span></div>
<div class="block"><p>
 Returns shape thumbnail.
 ShapeThumbnailBounds.Shape shape thumbnail bounds type is used by default.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShape.html#getThumbnail--">getThumbnail</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Shape thumbnail.</dd>
</dl>
</li>
</ul>
<a name="getImage--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getImage</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IImage.html" title="interface in com.aspose.slides">IImage</a>&nbsp;getImage()</pre>
<div class="block"><p>
 Returns shape thumbnail.
 ShapeThumbnailBounds.Shape shape thumbnail bounds type is used by default.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShape.html#getImage--">getImage</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Shape thumbnail.</dd>
</dl>
</li>
</ul>
<a name="getThumbnail-int-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getThumbnail</h4>
<pre>@Deprecated
public final&nbsp;java.awt.image.BufferedImage&nbsp;getThumbnail(int&nbsp;bounds,
                                                                    float&nbsp;scaleX,
                                                                    float&nbsp;scaleY)</pre>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;<span class="deprecationComment">Use getImage(ShapeThumbnailBounds bounds, float scaleX, float scaleY) method instead. The method will be removed after release of version 24.7.</span></div>
<div class="block"><p>
 Returns shape thumbnail.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShape.html#getThumbnail-int-float-float-">getThumbnail</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>bounds</code> - Shape thumbnail bounds type.</dd>
<dd><code>scaleX</code> - X scale</dd>
<dd><code>scaleY</code> - Y scale</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Shape thumbnail or null in case when ShapeThumbnailBounds.Appearance is used and a shape doesn't have visible elements.</dd>
</dl>
</li>
</ul>
<a name="getImage-int-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getImage</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IImage.html" title="interface in com.aspose.slides">IImage</a>&nbsp;getImage(int&nbsp;bounds,
                             float&nbsp;scaleX,
                             float&nbsp;scaleY)</pre>
<div class="block"><p>
 Returns shape thumbnail.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShape.html#getImage-int-float-float-">getImage</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>bounds</code> - Shape thumbnail bounds type.</dd>
<dd><code>scaleX</code> - X scale</dd>
<dd><code>scaleY</code> - Y scale</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Shape thumbnail or null in case when ShapeThumbnailBounds.Appearance is used and a shape doesn't have visible elements.</dd>
</dl>
</li>
</ul>
<a name="writeAsSvg-java.io.OutputStream-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>writeAsSvg</h4>
<pre>public final&nbsp;void&nbsp;writeAsSvg(java.io.OutputStream&nbsp;stream)</pre>
<div class="block"><p>
 Saves content of Shape as SVG file.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShape.html#writeAsSvg-java.io.OutputStream-">writeAsSvg</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>stream</code> - Target stream</dd>
</dl>
</li>
</ul>
<a name="writeAsSvg-java.io.OutputStream-com.aspose.slides.ISVGOptions-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>writeAsSvg</h4>
<pre>public final&nbsp;void&nbsp;writeAsSvg(java.io.OutputStream&nbsp;stream,
                             <a href="../../../com/aspose/slides/ISVGOptions.html" title="interface in com.aspose.slides">ISVGOptions</a>&nbsp;svgOptions)</pre>
<div class="block"><p>
 Saves content of Shape as SVG file.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShape.html#writeAsSvg-java.io.OutputStream-com.aspose.slides.ISVGOptions-">writeAsSvg</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>stream</code> - Target stream</dd>
<dd><code>svgOptions</code> - SVG generation options</dd>
</dl>
</li>
</ul>
<a name="getHyperlinkClick--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHyperlinkClick</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IHyperlink.html" title="interface in com.aspose.slides">IHyperlink</a>&nbsp;getHyperlinkClick()</pre>
<div class="block"><p>
 Returns or sets the hyperlink defined for mouse click.
 Read/write <a href="../../../com/aspose/slides/IHyperlink.html" title="interface in com.aspose.slides"><code>IHyperlink</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IHyperlinkContainer.html#getHyperlinkClick--">getHyperlinkClick</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IHyperlinkContainer.html" title="interface in com.aspose.slides">IHyperlinkContainer</a></code></dd>
</dl>
</li>
</ul>
<a name="setHyperlinkClick-com.aspose.slides.IHyperlink-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setHyperlinkClick</h4>
<pre>public final&nbsp;void&nbsp;setHyperlinkClick(<a href="../../../com/aspose/slides/IHyperlink.html" title="interface in com.aspose.slides">IHyperlink</a>&nbsp;value)</pre>
<div class="block"><p>
 Returns or sets the hyperlink defined for mouse click.
 Read/write <a href="../../../com/aspose/slides/IHyperlink.html" title="interface in com.aspose.slides"><code>IHyperlink</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IHyperlinkContainer.html#setHyperlinkClick-com.aspose.slides.IHyperlink-">setHyperlinkClick</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IHyperlinkContainer.html" title="interface in com.aspose.slides">IHyperlinkContainer</a></code></dd>
</dl>
</li>
</ul>
<a name="getHyperlinkMouseOver--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHyperlinkMouseOver</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IHyperlink.html" title="interface in com.aspose.slides">IHyperlink</a>&nbsp;getHyperlinkMouseOver()</pre>
<div class="block"><p>
 Returns or sets the hyperlink defined for mouse over.
 Read/write <a href="../../../com/aspose/slides/IHyperlink.html" title="interface in com.aspose.slides"><code>IHyperlink</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IHyperlinkContainer.html#getHyperlinkMouseOver--">getHyperlinkMouseOver</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IHyperlinkContainer.html" title="interface in com.aspose.slides">IHyperlinkContainer</a></code></dd>
</dl>
</li>
</ul>
<a name="setHyperlinkMouseOver-com.aspose.slides.IHyperlink-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setHyperlinkMouseOver</h4>
<pre>public final&nbsp;void&nbsp;setHyperlinkMouseOver(<a href="../../../com/aspose/slides/IHyperlink.html" title="interface in com.aspose.slides">IHyperlink</a>&nbsp;value)</pre>
<div class="block"><p>
 Returns or sets the hyperlink defined for mouse over.
 Read/write <a href="../../../com/aspose/slides/IHyperlink.html" title="interface in com.aspose.slides"><code>IHyperlink</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IHyperlinkContainer.html#setHyperlinkMouseOver-com.aspose.slides.IHyperlink-">setHyperlinkMouseOver</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IHyperlinkContainer.html" title="interface in com.aspose.slides">IHyperlinkContainer</a></code></dd>
</dl>
</li>
</ul>
<a name="getHyperlinkManager--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHyperlinkManager</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IHyperlinkManager.html" title="interface in com.aspose.slides">IHyperlinkManager</a>&nbsp;getHyperlinkManager()</pre>
<div class="block"><p>
 Returns the hyperlink manager.
 Read-only <a href="../../../com/aspose/slides/IHyperlinkManager.html" title="interface in com.aspose.slides"><code>IHyperlinkManager</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IHyperlinkContainer.html#getHyperlinkManager--">getHyperlinkManager</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IHyperlinkContainer.html" title="interface in com.aspose.slides">IHyperlinkContainer</a></code></dd>
</dl>
</li>
</ul>
<a name="getHidden--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHidden</h4>
<pre>public final&nbsp;boolean&nbsp;getHidden()</pre>
<div class="block"><p>
 Determines whether the shape is hidden.
 Read/write <code>boolean</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShape.html#getHidden--">getHidden</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a></code></dd>
</dl>
</li>
</ul>
<a name="setHidden-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setHidden</h4>
<pre>public final&nbsp;void&nbsp;setHidden(boolean&nbsp;value)</pre>
<div class="block"><p>
 Determines whether the shape is hidden.
 Read/write <code>boolean</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShape.html#setHidden-boolean-">setHidden</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a></code></dd>
</dl>
</li>
</ul>
<a name="getZOrderPosition--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getZOrderPosition</h4>
<pre>public&nbsp;int&nbsp;getZOrderPosition()</pre>
<div class="block"><p>
 Returns the position of a shape in the z-order.
 Shapes[0] returns the shape at the back of the z-order,
 and Shapes[Shapes.Count - 1] returns the shape at the front of the z-order.
 Read-only <code>int</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShape.html#getZOrderPosition--">getZOrderPosition</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a></code></dd>
</dl>
</li>
</ul>
<a name="getConnectionSiteCount--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getConnectionSiteCount</h4>
<pre>public final&nbsp;int&nbsp;getConnectionSiteCount()</pre>
<div class="block"><p>
 Returns the number of connection sites on the shape.
 Read-only <code>int</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShape.html#getConnectionSiteCount--">getConnectionSiteCount</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a></code></dd>
</dl>
</li>
</ul>
<a name="getRotation--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRotation</h4>
<pre>public final&nbsp;float&nbsp;getRotation()</pre>
<div class="block"><p>
 Returns or sets the number of degrees the specified shape is rotated around
 the z-axis. A positive value indicates clockwise rotation; a negative value
 indicates counterclockwise rotation.
 Read/write float.
 </p><p><hr>
 Returned value is always defined (is not Float.NaN).
 Assigned value must be defined (not Float.NaN). You can set undefined values for RawFrame instance properties.
 </hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShape.html#getRotation--">getRotation</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a></code></dd>
</dl>
</li>
</ul>
<a name="setRotation-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRotation</h4>
<pre>public final&nbsp;void&nbsp;setRotation(float&nbsp;value)</pre>
<div class="block"><p>
 Returns or sets the number of degrees the specified shape is rotated around
 the z-axis. A positive value indicates clockwise rotation; a negative value
 indicates counterclockwise rotation.
 Read/write float.
 </p><p><hr>
 Returned value is always defined (is not Float.NaN).
 Assigned value must be defined (not Float.NaN). You can set undefined values for RawFrame instance properties.
 </hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShape.html#setRotation-float-">setRotation</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a></code></dd>
</dl>
</li>
</ul>
<a name="getX--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getX</h4>
<pre>public final&nbsp;float&nbsp;getX()</pre>
<div class="block"><p>
 Gets or sets the x-coordinate of the shape's upper-left corner, measured in points.
 Read/write float.
 </p><p><hr>
 The value returned is always defined and never Float.NaN.
 The value assigned must also be defined; assign Float.NaN only to properties of a RawFrame instance.
 </hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShape.html#getX--">getX</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a></code></dd>
</dl>
</li>
</ul>
<a name="setX-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setX</h4>
<pre>public final&nbsp;void&nbsp;setX(float&nbsp;value)</pre>
<div class="block"><p>
 Gets or sets the x-coordinate of the shape's upper-left corner, measured in points.
 Read/write float.
 </p><p><hr>
 The value returned is always defined and never Float.NaN.
 The value assigned must also be defined; assign Float.NaN only to properties of a RawFrame instance.
 </hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShape.html#setX-float-">setX</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a></code></dd>
</dl>
</li>
</ul>
<a name="getY--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getY</h4>
<pre>public final&nbsp;float&nbsp;getY()</pre>
<div class="block"><p>
 Gets or sets the y-coordinate of the shape's upper-left corner, measured in points.
 Read/write float.
 </p><p><hr>
 The value returned is always defined and never Float.NaN.
 The value assigned must also be defined; assign Float.NaN only to properties of a RawFrame instance.
 </hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShape.html#getY--">getY</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a></code></dd>
</dl>
</li>
</ul>
<a name="setY-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setY</h4>
<pre>public final&nbsp;void&nbsp;setY(float&nbsp;value)</pre>
<div class="block"><p>
 Gets or sets the y-coordinate of the shape's upper-left corner, measured in points.
 Read/write float.
 </p><p><hr>
 The value returned is always defined and never Float.NaN.
 The value assigned must also be defined; assign Float.NaN only to properties of a RawFrame instance.
 </hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShape.html#setY-float-">setY</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a></code></dd>
</dl>
</li>
</ul>
<a name="getWidth--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWidth</h4>
<pre>public final&nbsp;float&nbsp;getWidth()</pre>
<div class="block"><p>
 Gets or sets the width of the shape, measured in points.
 Read/write float.
 </p><p><hr>
 The value returned is always defined and never Float.NaN.
 The value assigned must also be defined; assign Float.NaN only to properties of a RawFrame instance.
 </hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShape.html#getWidth--">getWidth</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a></code></dd>
</dl>
</li>
</ul>
<a name="setWidth-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setWidth</h4>
<pre>public final&nbsp;void&nbsp;setWidth(float&nbsp;value)</pre>
<div class="block"><p>
 Gets or sets the width of the shape, measured in points.
 Read/write float.
 </p><p><hr>
 The value returned is always defined and never Float.NaN.
 The value assigned must also be defined; assign Float.NaN only to properties of a RawFrame instance.
 </hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShape.html#setWidth-float-">setWidth</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a></code></dd>
</dl>
</li>
</ul>
<a name="getHeight--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHeight</h4>
<pre>public final&nbsp;float&nbsp;getHeight()</pre>
<div class="block"><p>
 Gets or sets the height of the shape, measured in points.
 Read/write float.
 </p><p><hr>
 The value returned is always defined and never Float.NaN.
 The value assigned must also be defined; assign Float.NaN only to properties of a RawFrame instance.
 </hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShape.html#getHeight--">getHeight</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a></code></dd>
</dl>
</li>
</ul>
<a name="setHeight-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setHeight</h4>
<pre>public final&nbsp;void&nbsp;setHeight(float&nbsp;value)</pre>
<div class="block"><p>
 Gets or sets the height of the shape, measured in points.
 Read/write float.
 </p><p><hr>
 The value returned is always defined and never Float.NaN.
 The value assigned must also be defined; assign Float.NaN only to properties of a RawFrame instance.
 </hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShape.html#setHeight-float-">setHeight</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a></code></dd>
</dl>
</li>
</ul>
<a name="getBlackWhiteMode--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBlackWhiteMode</h4>
<pre>public final&nbsp;byte&nbsp;getBlackWhiteMode()</pre>
<div class="block"><p>
 Property specifies how a shape will render in black-and-white display mode..
 Read/write <a href="../../../com/aspose/slides/BlackWhiteMode.html" title="class in com.aspose.slides"><code>BlackWhiteMode</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShape.html#getBlackWhiteMode--">getBlackWhiteMode</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a></code></dd>
</dl>
</li>
</ul>
<a name="setBlackWhiteMode-byte-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBlackWhiteMode</h4>
<pre>public final&nbsp;void&nbsp;setBlackWhiteMode(byte&nbsp;value)</pre>
<div class="block"><p>
 Property specifies how a shape will render in black-and-white display mode..
 Read/write <a href="../../../com/aspose/slides/BlackWhiteMode.html" title="class in com.aspose.slides"><code>BlackWhiteMode</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShape.html#setBlackWhiteMode-byte-">setBlackWhiteMode</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a></code></dd>
</dl>
</li>
</ul>
<a name="getUniqueId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getUniqueId</h4>
<pre>public final&nbsp;long&nbsp;getUniqueId()</pre>
<div class="block"><p>
 Returns an internal, presentation-scoped identifier intended for use by add-ins or other code.
 Because this value can be reassigned by the user or programmatically, it must not be treated
 as a persistent unique key.
 Read-only long.
 See also <a href="../../../com/aspose/slides/Shape.html#getOfficeInteropShapeId--"><code>getOfficeInteropShapeId()</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShape.html#getUniqueId--">getUniqueId</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a></code></dd>
</dl>
</li>
</ul>
<a name="getOfficeInteropShapeId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getOfficeInteropShapeId</h4>
<pre>public final&nbsp;long&nbsp;getOfficeInteropShapeId()</pre>
<div class="block"><p>
 Returns a slide-scoped unique identifier that remains constant for the lifetime of the shape and
 lets PowerPoint or interop code reliably reference the shape from anywhere in the document.
 Read-only long.
 See also <a href="../../../com/aspose/slides/Shape.html#getUniqueId--"><code>getUniqueId()</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShape.html#getOfficeInteropShapeId--">getOfficeInteropShapeId</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a></code></dd>
</dl>
</li>
</ul>
<a name="getAlternativeText--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAlternativeText</h4>
<pre>public final&nbsp;java.lang.String&nbsp;getAlternativeText()</pre>
<div class="block"><p>
 Returns or sets the alternative text associated with a shape.
 Read/write <code>String</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShape.html#getAlternativeText--">getAlternativeText</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a></code></dd>
</dl>
</li>
</ul>
<a name="setAlternativeText-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAlternativeText</h4>
<pre>public final&nbsp;void&nbsp;setAlternativeText(java.lang.String&nbsp;value)</pre>
<div class="block"><p>
 Returns or sets the alternative text associated with a shape.
 Read/write <code>String</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShape.html#setAlternativeText-java.lang.String-">setAlternativeText</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a></code></dd>
</dl>
</li>
</ul>
<a name="getAlternativeTextTitle--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAlternativeTextTitle</h4>
<pre>public final&nbsp;java.lang.String&nbsp;getAlternativeTextTitle()</pre>
<div class="block"><p>
 Returns or sets the title of alternative text associated with a shape.
 Read/write <code>String</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShape.html#getAlternativeTextTitle--">getAlternativeTextTitle</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a></code></dd>
</dl>
</li>
</ul>
<a name="setAlternativeTextTitle-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAlternativeTextTitle</h4>
<pre>public final&nbsp;void&nbsp;setAlternativeTextTitle(java.lang.String&nbsp;value)</pre>
<div class="block"><p>
 Returns or sets the title of alternative text associated with a shape.
 Read/write <code>String</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShape.html#setAlternativeTextTitle-java.lang.String-">setAlternativeTextTitle</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a></code></dd>
</dl>
</li>
</ul>
<a name="getName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getName</h4>
<pre>public final&nbsp;java.lang.String&nbsp;getName()</pre>
<div class="block"><p>
 Returns or sets the name of a shape.
 Must be not null. Use empty string value if needed.
 Read/write <code>String</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShape.html#getName--">getName</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a></code></dd>
</dl>
</li>
</ul>
<a name="setName-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setName</h4>
<pre>public final&nbsp;void&nbsp;setName(java.lang.String&nbsp;value)</pre>
<div class="block"><p>
 Returns or sets the name of a shape.
 Must be not null. Use empty string value if needed.
 Read/write <code>String</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShape.html#setName-java.lang.String-">setName</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a></code></dd>
</dl>
</li>
</ul>
<a name="isDecorative--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isDecorative</h4>
<pre>public final&nbsp;boolean&nbsp;isDecorative()</pre>
<div class="block"><p>
 Gets or sets 'Mark as decorative' option
 Reed/write boolean.
 </p><p><hr><blockquote><pre>
 <pre>
 Presentation pres = new Presentation("sample.pptx");
 try {
    pres.getSlides().get_Item(0).getShapes().get_Item(0).setDecorative(true);
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShape.html#isDecorative--">isDecorative</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a></code></dd>
</dl>
</li>
</ul>
<a name="setDecorative-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDecorative</h4>
<pre>public final&nbsp;void&nbsp;setDecorative(boolean&nbsp;value)</pre>
<div class="block"><p>
 Gets or sets 'Mark as decorative' option
 Reed/write boolean.
 </p><p><hr><blockquote><pre>
 <pre>
 Presentation pres = new Presentation("sample.pptx");
 try {
    pres.getSlides().get_Item(0).getShapes().get_Item(0).setDecorative(true);
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShape.html#setDecorative-boolean-">setDecorative</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a></code></dd>
</dl>
</li>
</ul>
<a name="getShapeLock--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getShapeLock</h4>
<pre>public&nbsp;<a href="../../../com/aspose/slides/IBaseShapeLock.html" title="interface in com.aspose.slides">IBaseShapeLock</a>&nbsp;getShapeLock()</pre>
<div class="block"><p>
 Returns shape's locks.
 Read-only <a href="../../../com/aspose/slides/IBaseShapeLock.html" title="interface in com.aspose.slides"><code>IBaseShapeLock</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShape.html#getShapeLock--">getShapeLock</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a></code></dd>
</dl>
</li>
</ul>
<a name="isGrouped--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isGrouped</h4>
<pre>public final&nbsp;boolean&nbsp;isGrouped()</pre>
<div class="block"><p>
 Determines whether the shape is grouped.
 Read-only boolean.
 </p><p><hr>
 Property <a href="../../../com/aspose/slides/Shape.html#getParentGroup--"><code>getParentGroup()</code></a> returns parent GroupShape object if shape is grouped.
 </hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShape.html#isGrouped--">isGrouped</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a></code></dd>
</dl>
</li>
</ul>
<a name="getParentGroup--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getParentGroup</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IGroupShape.html" title="interface in com.aspose.slides">IGroupShape</a>&nbsp;getParentGroup()</pre>
<div class="block"><p>
 Returns parent GroupShape object if shape is grouped. Otherwise returns null.
 Read-only <a href="../../../com/aspose/slides/IGroupShape.html" title="interface in com.aspose.slides"><code>IGroupShape</code></a>.
 </p><p><hr>
 Property <a href="../../../com/aspose/slides/Shape.html#isGrouped--"><code>isGrouped()</code></a> determines whether the shape is grouped.
 </hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShape.html#getParentGroup--">getParentGroup</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a></code></dd>
</dl>
</li>
</ul>
<a name="getParent_Immediate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getParent_Immediate</h4>
<pre>public final&nbsp;com.aspose.slides.IDOMObject&nbsp;getParent_Immediate()</pre>
<div class="block"><p>
 Returns Parent_Immediate object.
 Read-only <code>IDOMObject</code>.
 </p></div>
</li>
</ul>
<a name="getSlide--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSlide</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IBaseSlide.html" title="interface in com.aspose.slides">IBaseSlide</a>&nbsp;getSlide()</pre>
<div class="block"><p>
 Returns the parent slide of a shape.
 Read-only <a href="../../../com/aspose/slides/IBaseSlide.html" title="interface in com.aspose.slides"><code>IBaseSlide</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISlideComponent.html#getSlide--">getSlide</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISlideComponent.html" title="interface in com.aspose.slides">ISlideComponent</a></code></dd>
</dl>
</li>
</ul>
<a name="getPresentation--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getPresentation</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IPresentation.html" title="interface in com.aspose.slides">IPresentation</a>&nbsp;getPresentation()</pre>
<div class="block"><p>
 Returns the parent presentation of a slide.
 Read-only <a href="../../../com/aspose/slides/IPresentation.html" title="interface in com.aspose.slides"><code>IPresentation</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPresentationComponent.html#getPresentation--">getPresentation</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPresentationComponent.html" title="interface in com.aspose.slides">IPresentationComponent</a></code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/SetEffect.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/ShapeAdjustmentType.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/Shape.html" target="_top">Frames</a></li>
<li><a href="Shape.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><i>Copyright &copy; 2004-2025 Aspose Pty Ltd. All Rights Reserved.</i></small></p>
</body>
</html>
