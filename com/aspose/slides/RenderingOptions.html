<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_421) on Fri Jul 11 05:32:44 CDT 2025 -->
<title>RenderingOptions (Aspose.Slides for Java (25.7) API Documentation)</title>
<meta name="date" content="2025-07-11">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="RenderingOptions (Aspose.Slides for Java (25.7) API Documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/Reflection.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/ResourceLoadingAction.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/RenderingOptions.html" target="_top">Frames</a></li>
<li><a href="RenderingOptions.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.aspose.slides</div>
<h2 title="Class RenderingOptions" class="title">Class RenderingOptions</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../com/aspose/slides/SaveOptions.html" title="class in com.aspose.slides">com.aspose.slides.SaveOptions</a></li>
<li>
<ul class="inheritance">
<li>com.aspose.slides.RenderingOptions</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../com/aspose/slides/IRenderingOptions.html" title="interface in com.aspose.slides">IRenderingOptions</a>, <a href="../../../com/aspose/slides/ISaveOptions.html" title="interface in com.aspose.slides">ISaveOptions</a></dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">RenderingOptions</span>
extends <a href="../../../com/aspose/slides/SaveOptions.html" title="class in com.aspose.slides">SaveOptions</a>
implements <a href="../../../com/aspose/slides/IRenderingOptions.html" title="interface in com.aspose.slides">IRenderingOptions</a></pre>
<div class="block"><p>
 Provides options that control how a presentation/slide is rendered.
 </p><p><hr><blockquote><pre>
 <pre>
 Presentation pres = new Presentation("pres.pptx");
 try {
     IRenderingOptions renderingOpts = new RenderingOptions();
     renderingOpts.getNotesCommentsLayouting().setNotesPosition(NotesPositions.BottomTruncated);

     ImageIO.write(pres.getSlides().get_Item(0).getThumbnail(renderingOpts), "PNG", new File("pres-Original.png"));

     renderingOpts.setDefaultRegularFont("Arial Black");
     ImageIO.write(pres.getSlides().get_Item(0).getThumbnail(renderingOpts), "PNG", new File("pres-ArialBlackDefault.png"));

     renderingOpts.setDefaultRegularFont("Arial Narrow");
     ImageIO.write(pres.getSlides().get_Item(0).getThumbnail(renderingOpts), "PNG", new File("pres-ArialNarrowDefault.png"));
 } catch (IOException e) {
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/RenderingOptions.html#RenderingOptions--">RenderingOptions</a></span>()</code>
<div class="block">
 Default constructor.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/RenderingOptions.html#getDisableFontLigatures--">getDisableFontLigatures</a></span>()</code>
<div class="block">
 Gets or sets a value indicating whether text is rendered without using ligatures.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IInkOptions.html" title="interface in com.aspose.slides">IInkOptions</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/RenderingOptions.html#getInkOptions--">getInkOptions</a></span>()</code>
<div class="block">
 Provides options that control the look of Ink objects in exported document.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/ISlidesLayoutOptions.html" title="interface in com.aspose.slides">ISlidesLayoutOptions</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/RenderingOptions.html#getSlidesLayoutOptions--">getSlidesLayoutOptions</a></span>()</code>
<div class="block">
 Gets or sets the mode in which slides are placed on the page when exporting a presentation <a href="../../../com/aspose/slides/ISlidesLayoutOptions.html" title="interface in com.aspose.slides"><code>ISlidesLayoutOptions</code></a>.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/RenderingOptions.html#setDisableFontLigatures-boolean-">setDisableFontLigatures</a></span>(boolean&nbsp;value)</code>
<div class="block">
 Gets or sets a value indicating whether text is rendered without using ligatures.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/RenderingOptions.html#setSlidesLayoutOptions-com.aspose.slides.ISlidesLayoutOptions-">setSlidesLayoutOptions</a></span>(<a href="../../../com/aspose/slides/ISlidesLayoutOptions.html" title="interface in com.aspose.slides">ISlidesLayoutOptions</a>&nbsp;value)</code>
<div class="block">
 Gets or sets the mode in which slides are placed on the page when exporting a presentation <a href="../../../com/aspose/slides/ISlidesLayoutOptions.html" title="interface in com.aspose.slides"><code>ISlidesLayoutOptions</code></a>.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.aspose.slides.SaveOptions">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;com.aspose.slides.<a href="../../../com/aspose/slides/SaveOptions.html" title="class in com.aspose.slides">SaveOptions</a></h3>
<code><a href="../../../com/aspose/slides/SaveOptions.html#getDefaultRegularFont--">getDefaultRegularFont</a>, <a href="../../../com/aspose/slides/SaveOptions.html#getGradientStyle--">getGradientStyle</a>, <a href="../../../com/aspose/slides/SaveOptions.html#getProgressCallback--">getProgressCallback</a>, <a href="../../../com/aspose/slides/SaveOptions.html#getSkipJavaScriptLinks--">getSkipJavaScriptLinks</a>, <a href="../../../com/aspose/slides/SaveOptions.html#getWarningCallback--">getWarningCallback</a>, <a href="../../../com/aspose/slides/SaveOptions.html#setDefaultRegularFont-java.lang.String-">setDefaultRegularFont</a>, <a href="../../../com/aspose/slides/SaveOptions.html#setGradientStyle-int-">setGradientStyle</a>, <a href="../../../com/aspose/slides/SaveOptions.html#setProgressCallback-com.aspose.slides.IProgressCallback-">setProgressCallback</a>, <a href="../../../com/aspose/slides/SaveOptions.html#setSkipJavaScriptLinks-boolean-">setSkipJavaScriptLinks</a>, <a href="../../../com/aspose/slides/SaveOptions.html#setWarningCallback-com.aspose.slides.IWarningCallback-">setWarningCallback</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.aspose.slides.ISaveOptions">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;com.aspose.slides.<a href="../../../com/aspose/slides/ISaveOptions.html" title="interface in com.aspose.slides">ISaveOptions</a></h3>
<code><a href="../../../com/aspose/slides/ISaveOptions.html#getDefaultRegularFont--">getDefaultRegularFont</a>, <a href="../../../com/aspose/slides/ISaveOptions.html#getGradientStyle--">getGradientStyle</a>, <a href="../../../com/aspose/slides/ISaveOptions.html#getProgressCallback--">getProgressCallback</a>, <a href="../../../com/aspose/slides/ISaveOptions.html#getSkipJavaScriptLinks--">getSkipJavaScriptLinks</a>, <a href="../../../com/aspose/slides/ISaveOptions.html#getWarningCallback--">getWarningCallback</a>, <a href="../../../com/aspose/slides/ISaveOptions.html#setDefaultRegularFont-java.lang.String-">setDefaultRegularFont</a>, <a href="../../../com/aspose/slides/ISaveOptions.html#setGradientStyle-int-">setGradientStyle</a>, <a href="../../../com/aspose/slides/ISaveOptions.html#setProgressCallback-com.aspose.slides.IProgressCallback-">setProgressCallback</a>, <a href="../../../com/aspose/slides/ISaveOptions.html#setSkipJavaScriptLinks-boolean-">setSkipJavaScriptLinks</a>, <a href="../../../com/aspose/slides/ISaveOptions.html#setWarningCallback-com.aspose.slides.IWarningCallback-">setWarningCallback</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="RenderingOptions--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>RenderingOptions</h4>
<pre>public&nbsp;RenderingOptions()</pre>
<div class="block"><p>
 Default constructor.
 </p></div>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getSlidesLayoutOptions--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSlidesLayoutOptions</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/ISlidesLayoutOptions.html" title="interface in com.aspose.slides">ISlidesLayoutOptions</a>&nbsp;getSlidesLayoutOptions()</pre>
<div class="block"><p>
 Gets or sets the mode in which slides are placed on the page when exporting a presentation <a href="../../../com/aspose/slides/ISlidesLayoutOptions.html" title="interface in com.aspose.slides"><code>ISlidesLayoutOptions</code></a>.
 </p><p><hr><blockquote><pre>Example:
 <pre>
 Presentation pres = new Presentation("pres.pptx");
 try {
     RenderingOptions options = new RenderingOptions();

     HandoutLayoutingOptions slidesLayoutOptions = new HandoutLayoutingOptions();
     slidesLayoutOptions.setHandout(HandoutType.Handouts4Horizontal);
     slidesLayoutOptions.setPrintSlideNumbers(false);
     options.setSlidesLayoutOptions(slidesLayoutOptions);

     BufferedImage[] handoutSlides = pres.getThumbnails(options);
     for (int index = 0; index &lt; handoutSlides.length; index++)
     {
         ImageIO.write(handoutSlides[index], "PNG", new java.io.File("handout-" + index + ".png"));
     }
 } catch (IOException e) {
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IRenderingOptions.html#getSlidesLayoutOptions--">getSlidesLayoutOptions</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IRenderingOptions.html" title="interface in com.aspose.slides">IRenderingOptions</a></code></dd>
</dl>
</li>
</ul>
<a name="setSlidesLayoutOptions-com.aspose.slides.ISlidesLayoutOptions-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSlidesLayoutOptions</h4>
<pre>public final&nbsp;void&nbsp;setSlidesLayoutOptions(<a href="../../../com/aspose/slides/ISlidesLayoutOptions.html" title="interface in com.aspose.slides">ISlidesLayoutOptions</a>&nbsp;value)</pre>
<div class="block"><p>
 Gets or sets the mode in which slides are placed on the page when exporting a presentation <a href="../../../com/aspose/slides/ISlidesLayoutOptions.html" title="interface in com.aspose.slides"><code>ISlidesLayoutOptions</code></a>.
 </p><p><hr><blockquote><pre>Example:
 <pre>
 Presentation pres = new Presentation("pres.pptx");
 try {
     RenderingOptions options = new RenderingOptions();

     HandoutLayoutingOptions slidesLayoutOptions = new HandoutLayoutingOptions();
     slidesLayoutOptions.setHandout(HandoutType.Handouts4Horizontal);
     slidesLayoutOptions.setPrintSlideNumbers(false);
     options.setSlidesLayoutOptions(slidesLayoutOptions);

     BufferedImage[] handoutSlides = pres.getThumbnails(options);
     for (int index = 0; index &lt; handoutSlides.length; index++)
     {
         ImageIO.write(handoutSlides[index], "PNG", new java.io.File("handout-" + index + ".png"));
     }
 } catch (IOException e) {
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IRenderingOptions.html#setSlidesLayoutOptions-com.aspose.slides.ISlidesLayoutOptions-">setSlidesLayoutOptions</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IRenderingOptions.html" title="interface in com.aspose.slides">IRenderingOptions</a></code></dd>
</dl>
</li>
</ul>
<a name="getInkOptions--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getInkOptions</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IInkOptions.html" title="interface in com.aspose.slides">IInkOptions</a>&nbsp;getInkOptions()</pre>
<div class="block"><p>
 Provides options that control the look of Ink objects in exported document.
 Read-only <a href="../../../com/aspose/slides/IInkOptions.html" title="interface in com.aspose.slides"><code>IInkOptions</code></a>
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IRenderingOptions.html#getInkOptions--">getInkOptions</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IRenderingOptions.html" title="interface in com.aspose.slides">IRenderingOptions</a></code></dd>
</dl>
</li>
</ul>
<a name="getDisableFontLigatures--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDisableFontLigatures</h4>
<pre>public final&nbsp;boolean&nbsp;getDisableFontLigatures()</pre>
<div class="block"><p>
 Gets or sets a value indicating whether text is rendered without using ligatures.
 When set to true, ligatures will be disabled in the rendered output. By default, this property is set to false.
 </p><p><hr><blockquote><pre>Example:
 <pre>
 Presentation pres = new Presentation("pres.pptx");
 try {
     RenderingOptions options = new RenderingOptions();
     options.setDisableFontLigatures(true);

     IImage[] renderedSlides = pres.getImages(options);
     for (int index = 0; index < renderedSlides.length; index++)
     {
         IImage slideImage = renderedSlides[index];
         slideImage.save("slide-" + index + ".png");
     }
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IRenderingOptions.html#getDisableFontLigatures--">getDisableFontLigatures</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IRenderingOptions.html" title="interface in com.aspose.slides">IRenderingOptions</a></code></dd>
</dl>
</li>
</ul>
<a name="setDisableFontLigatures-boolean-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setDisableFontLigatures</h4>
<pre>public final&nbsp;void&nbsp;setDisableFontLigatures(boolean&nbsp;value)</pre>
<div class="block"><p>
 Gets or sets a value indicating whether text is rendered without using ligatures.
 When set to true, ligatures will be disabled in the rendered output. By default, this property is set to false.
 </p><p><hr><blockquote><pre>Example:
 <pre>
 Presentation pres = new Presentation("pres.pptx");
 try {
     RenderingOptions options = new RenderingOptions();
     options.setDisableFontLigatures(true);

     IImage[] renderedSlides = pres.getImages(options);
     for (int index = 0; index < renderedSlides.length; index++)
     {
         IImage slideImage = renderedSlides[index];
         slideImage.save("slide-" + index + ".png");
     }
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IRenderingOptions.html#setDisableFontLigatures-boolean-">setDisableFontLigatures</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IRenderingOptions.html" title="interface in com.aspose.slides">IRenderingOptions</a></code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/Reflection.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/ResourceLoadingAction.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/RenderingOptions.html" target="_top">Frames</a></li>
<li><a href="RenderingOptions.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><i>Copyright &copy; 2004-2025 Aspose Pty Ltd. All Rights Reserved.</i></small></p>
</body>
</html>
