<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_421) on Fri Jul 11 05:32:44 CDT 2025 -->
<title>SlideShowSettings (Aspose.Slides for Java (25.7) API Documentation)</title>
<meta name="date" content="2025-07-11">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="SlideShowSettings (Aspose.Slides for Java (25.7) API Documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/SlidesAIAgentException.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/SlideShowTransition.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/SlideShowSettings.html" target="_top">Frames</a></li>
<li><a href="SlideShowSettings.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.aspose.slides</div>
<h2 title="Class SlideShowSettings" class="title">Class SlideShowSettings</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.aspose.slides.SlideShowSettings</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">SlideShowSettings</span>
extends java.lang.Object</pre>
<div class="block"><p>
 Represents the slide show settings for the presentation.
 </p></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideShowSettings.html#getLoop--">getLoop</a></span>()</code>
<div class="block">
 Loop Slide Show</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IColorFormat.html" title="interface in com.aspose.slides">IColorFormat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideShowSettings.html#getPenColor--">getPenColor</a></span>()</code>
<div class="block">
 Pen Color for Slide Show</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideShowSettings.html#getShowAnimation--">getShowAnimation</a></span>()</code>
<div class="block">
 Show Animation in Slide Show</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideShowSettings.html#getShowMediaControls--">getShowMediaControls</a></span>()</code>
<div class="block">
 Show Media Controls</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideShowSettings.html#getShowNarration--">getShowNarration</a></span>()</code>
<div class="block">
 Show Narration in Slide Show</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/SlidesRange.html" title="class in com.aspose.slides">SlidesRange</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideShowSettings.html#getSlides--">getSlides</a></span>()</code>
<div class="block">
 Slides range</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/SlideShowType.html" title="class in com.aspose.slides">SlideShowType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideShowSettings.html#getSlideShowType--">getSlideShowType</a></span>()</code>
<div class="block">
 Gets or sets the slide show type.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideShowSettings.html#getUseTimings--">getUseTimings</a></span>()</code>
<div class="block">
 Use Timings in Slide Show</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideShowSettings.html#setLoop-boolean-">setLoop</a></span>(boolean&nbsp;value)</code>
<div class="block">
 Loop Slide Show</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideShowSettings.html#setShowAnimation-boolean-">setShowAnimation</a></span>(boolean&nbsp;value)</code>
<div class="block">
 Show Animation in Slide Show</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideShowSettings.html#setShowMediaControls-boolean-">setShowMediaControls</a></span>(boolean&nbsp;value)</code>
<div class="block">
 Show Media Controls</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideShowSettings.html#setShowNarration-boolean-">setShowNarration</a></span>(boolean&nbsp;value)</code>
<div class="block">
 Show Narration in Slide Show</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideShowSettings.html#setSlides-com.aspose.slides.SlidesRange-">setSlides</a></span>(<a href="../../../com/aspose/slides/SlidesRange.html" title="class in com.aspose.slides">SlidesRange</a>&nbsp;value)</code>
<div class="block">
 Slides range</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideShowSettings.html#setSlideShowType-com.aspose.slides.SlideShowType-">setSlideShowType</a></span>(<a href="../../../com/aspose/slides/SlideShowType.html" title="class in com.aspose.slides">SlideShowType</a>&nbsp;value)</code>
<div class="block">
 Gets or sets the slide show type.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideShowSettings.html#setUseTimings-boolean-">setUseTimings</a></span>(boolean&nbsp;value)</code>
<div class="block">
 Use Timings in Slide Show</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getSlideShowType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSlideShowType</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/SlideShowType.html" title="class in com.aspose.slides">SlideShowType</a>&nbsp;getSlideShowType()</pre>
<div class="block"><p>
 Gets or sets the slide show type. Represented by the following <code>SlideShowType</code>(<a href="../../../com/aspose/slides/SlideShowSettings.html#getSlideShowType--"><code>getSlideShowType()</code></a>/<a href="../../../com/aspose/slides/SlideShowSettings.html#setSlideShowType-com.aspose.slides.SlideShowType-"><code>setSlideShowType(SlideShowType)</code></a>) ancestors: <a href="../../../com/aspose/slides/BrowsedAtKiosk.html" title="class in com.aspose.slides"><code>BrowsedAtKiosk</code></a>, <a href="../../../com/aspose/slides/PresentedBySpeaker.html" title="class in com.aspose.slides"><code>PresentedBySpeaker</code></a> and <a href="../../../com/aspose/slides/BrowsedByIndividual.html" title="class in com.aspose.slides"><code>BrowsedByIndividual</code></a> 
 </p><p><hr><blockquote><pre>
 <pre>
 Presentation pres = new Presentation();
 try {
     // to set "Browsed at a kiosk (full screen)" type
     pres.getSlideShowSettings().setSlideShowType(new BrowsedAtKiosk());

     // to set "Browsed by individual (window)" type
     pres.getSlideShowSettings().setSlideShowType(new BrowsedByIndividual());

     // to set "Presented by a speaker (full screen)" type
     pres.getSlideShowSettings().setSlideShowType(new PresentedBySpeaker());
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
</li>
</ul>
<a name="setSlideShowType-com.aspose.slides.SlideShowType-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSlideShowType</h4>
<pre>public final&nbsp;void&nbsp;setSlideShowType(<a href="../../../com/aspose/slides/SlideShowType.html" title="class in com.aspose.slides">SlideShowType</a>&nbsp;value)</pre>
<div class="block"><p>
 Gets or sets the slide show type. Represented by the following <code>SlideShowType</code>(<a href="../../../com/aspose/slides/SlideShowSettings.html#getSlideShowType--"><code>getSlideShowType()</code></a>/<a href="../../../com/aspose/slides/SlideShowSettings.html#setSlideShowType-com.aspose.slides.SlideShowType-"><code>setSlideShowType(SlideShowType)</code></a>) ancestors: <a href="../../../com/aspose/slides/BrowsedAtKiosk.html" title="class in com.aspose.slides"><code>BrowsedAtKiosk</code></a>, <a href="../../../com/aspose/slides/PresentedBySpeaker.html" title="class in com.aspose.slides"><code>PresentedBySpeaker</code></a> and <a href="../../../com/aspose/slides/BrowsedByIndividual.html" title="class in com.aspose.slides"><code>BrowsedByIndividual</code></a> 
 </p><p><hr><blockquote><pre>
 <pre>
 Presentation pres = new Presentation();
 try {
     // to set "Browsed at a kiosk (full screen)" type
     pres.getSlideShowSettings().setSlideShowType(new BrowsedAtKiosk());

     // to set "Browsed by individual (window)" type
     pres.getSlideShowSettings().setSlideShowType(new BrowsedByIndividual());

     // to set "Presented by a speaker (full screen)" type
     pres.getSlideShowSettings().setSlideShowType(new PresentedBySpeaker());
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
</li>
</ul>
<a name="getLoop--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLoop</h4>
<pre>public final&nbsp;boolean&nbsp;getLoop()</pre>
<div class="block"><p>
 Loop Slide Show
 </p></div>
</li>
</ul>
<a name="setLoop-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLoop</h4>
<pre>public final&nbsp;void&nbsp;setLoop(boolean&nbsp;value)</pre>
<div class="block"><p>
 Loop Slide Show
 </p></div>
</li>
</ul>
<a name="getShowNarration--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getShowNarration</h4>
<pre>public final&nbsp;boolean&nbsp;getShowNarration()</pre>
<div class="block"><p>
 Show Narration in Slide Show
 </p></div>
</li>
</ul>
<a name="setShowNarration-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setShowNarration</h4>
<pre>public final&nbsp;void&nbsp;setShowNarration(boolean&nbsp;value)</pre>
<div class="block"><p>
 Show Narration in Slide Show
 </p></div>
</li>
</ul>
<a name="getShowAnimation--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getShowAnimation</h4>
<pre>public final&nbsp;boolean&nbsp;getShowAnimation()</pre>
<div class="block"><p>
 Show Animation in Slide Show
 </p></div>
</li>
</ul>
<a name="setShowAnimation-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setShowAnimation</h4>
<pre>public final&nbsp;void&nbsp;setShowAnimation(boolean&nbsp;value)</pre>
<div class="block"><p>
 Show Animation in Slide Show
 </p></div>
</li>
</ul>
<a name="getPenColor--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPenColor</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IColorFormat.html" title="interface in com.aspose.slides">IColorFormat</a>&nbsp;getPenColor()</pre>
<div class="block"><p>
 Pen Color for Slide Show
 </p></div>
</li>
</ul>
<a name="getSlides--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSlides</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/SlidesRange.html" title="class in com.aspose.slides">SlidesRange</a>&nbsp;getSlides()</pre>
<div class="block"><p>
 Slides range
 </p><p><hr><blockquote><pre>
 <pre>
 Presentation pres = new Presentation();
 try {
     SlidesRange slidesRange = new SlidesRange();
     slidesRange.setStart(1);
     slidesRange.setEnd(3);
     pres.getSlideShowSettings().setSlides(slidesRange);
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
</li>
</ul>
<a name="setSlides-com.aspose.slides.SlidesRange-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSlides</h4>
<pre>public final&nbsp;void&nbsp;setSlides(<a href="../../../com/aspose/slides/SlidesRange.html" title="class in com.aspose.slides">SlidesRange</a>&nbsp;value)</pre>
<div class="block"><p>
 Slides range
 </p><p><hr><blockquote><pre>
 <pre>
 Presentation pres = new Presentation();
 try {
     SlidesRange slidesRange = new SlidesRange();
     slidesRange.setStart(1);
     slidesRange.setEnd(3);
     pres.getSlideShowSettings().setSlides(slidesRange);
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
</li>
</ul>
<a name="getUseTimings--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getUseTimings</h4>
<pre>public final&nbsp;boolean&nbsp;getUseTimings()</pre>
<div class="block"><p>
 Use Timings in Slide Show
 </p></div>
</li>
</ul>
<a name="setUseTimings-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setUseTimings</h4>
<pre>public final&nbsp;void&nbsp;setUseTimings(boolean&nbsp;value)</pre>
<div class="block"><p>
 Use Timings in Slide Show
 </p></div>
</li>
</ul>
<a name="getShowMediaControls--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getShowMediaControls</h4>
<pre>public final&nbsp;boolean&nbsp;getShowMediaControls()</pre>
<div class="block"><p>
 Show Media Controls
 </p><p><hr><blockquote><pre>
 <pre>
 Presentation pres = new Presentation();
 try {
    pres.getSlideShowSettings().setShowMediaControls(true);
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
</li>
</ul>
<a name="setShowMediaControls-boolean-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setShowMediaControls</h4>
<pre>public final&nbsp;void&nbsp;setShowMediaControls(boolean&nbsp;value)</pre>
<div class="block"><p>
 Show Media Controls
 </p><p><hr><blockquote><pre>
 <pre>
 Presentation pres = new Presentation();
 try {
    pres.getSlideShowSettings().setShowMediaControls(true);
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/SlidesAIAgentException.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/SlideShowTransition.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/SlideShowSettings.html" target="_top">Frames</a></li>
<li><a href="SlideShowSettings.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><i>Copyright &copy; 2004-2025 Aspose Pty Ltd. All Rights Reserved.</i></small></p>
</body>
</html>
