<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_421) on Fri Jul 11 05:32:44 CDT 2025 -->
<title>ShapeStyle (Aspose.Slides for Java (25.7) API Documentation)</title>
<meta name="date" content="2025-07-11">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ShapeStyle (Aspose.Slides for Java (25.7) API Documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/ShapesAlignmentType.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/ShapeThumbnailBounds.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/ShapeStyle.html" target="_top">Frames</a></li>
<li><a href="ShapeStyle.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.aspose.slides</div>
<h2 title="Class ShapeStyle" class="title">Class ShapeStyle</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../com/aspose/slides/DomObject.html" title="class in com.aspose.slides">com.aspose.slides.DomObject</a>&lt;<a href="../../../com/aspose/slides/Shape.html" title="class in com.aspose.slides">Shape</a>&gt;</li>
<li>
<ul class="inheritance">
<li>com.aspose.slides.ShapeStyle</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../com/aspose/slides/IShapeStyle.html" title="interface in com.aspose.slides">IShapeStyle</a></dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">ShapeStyle</span>
extends <a href="../../../com/aspose/slides/DomObject.html" title="class in com.aspose.slides">DomObject</a>&lt;<a href="../../../com/aspose/slides/Shape.html" title="class in com.aspose.slides">Shape</a>&gt;
implements <a href="../../../com/aspose/slides/IShapeStyle.html" title="interface in com.aspose.slides">IShapeStyle</a></pre>
<div class="block"><p>
 Represent shape's style reference.
 </p></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IColorFormat.html" title="interface in com.aspose.slides">IColorFormat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeStyle.html#getEffectColor--">getEffectColor</a></span>()</code>
<div class="block">
 Returns a shape's effect color.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeStyle.html#getEffectStyleIndex--">getEffectStyleIndex</a></span>()</code>
<div class="block">
 Returns or sets shape's effect column index in a style matrix.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IColorFormat.html" title="interface in com.aspose.slides">IColorFormat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeStyle.html#getFillColor--">getFillColor</a></span>()</code>
<div class="block">
 Returns a shape's fill color.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>short</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeStyle.html#getFillStyleIndex--">getFillStyleIndex</a></span>()</code>
<div class="block">
 Returns or sets shape's fill column index in style matrices.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>byte</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeStyle.html#getFontCollectionIndex--">getFontCollectionIndex</a></span>()</code>
<div class="block">
 Returns or sets shape's font index in a font collection.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IColorFormat.html" title="interface in com.aspose.slides">IColorFormat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeStyle.html#getFontColor--">getFontColor</a></span>()</code>
<div class="block">
 Returns a shape's font color.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IColorFormat.html" title="interface in com.aspose.slides">IColorFormat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeStyle.html#getLineColor--">getLineColor</a></span>()</code>
<div class="block">
 Returns a shape's outline color.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeStyle.html#getLineStyleIndex--">getLineStyleIndex</a></span>()</code>
<div class="block">
 Returns or sets line's column index in a style matrix.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeStyle.html#setEffectStyleIndex-long-">setEffectStyleIndex</a></span>(long&nbsp;value)</code>
<div class="block">
 Returns or sets shape's effect column index in a style matrix.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeStyle.html#setFillStyleIndex-short-">setFillStyleIndex</a></span>(short&nbsp;value)</code>
<div class="block">
 Returns or sets shape's fill column index in style matrices.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeStyle.html#setFontCollectionIndex-byte-">setFontCollectionIndex</a></span>(byte&nbsp;value)</code>
<div class="block">
 Returns or sets shape's font index in a font collection.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeStyle.html#setLineStyleIndex-int-">setLineStyleIndex</a></span>(int&nbsp;value)</code>
<div class="block">
 Returns or sets line's column index in a style matrix.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.aspose.slides.DomObject">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;com.aspose.slides.<a href="../../../com/aspose/slides/DomObject.html" title="class in com.aspose.slides">DomObject</a></h3>
<code><a href="../../../com/aspose/slides/DomObject.html#getParent_Immediate--">getParent_Immediate</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getLineColor--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLineColor</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IColorFormat.html" title="interface in com.aspose.slides">IColorFormat</a>&nbsp;getLineColor()</pre>
<div class="block"><p>
 Returns a shape's outline color.
 Read-only <a href="../../../com/aspose/slides/IColorFormat.html" title="interface in com.aspose.slides"><code>IColorFormat</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShapeStyle.html#getLineColor--">getLineColor</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShapeStyle.html" title="interface in com.aspose.slides">IShapeStyle</a></code></dd>
</dl>
</li>
</ul>
<a name="getLineStyleIndex--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLineStyleIndex</h4>
<pre>public final&nbsp;int&nbsp;getLineStyleIndex()</pre>
<div class="block"><p>
 Returns or sets line's column index in a style matrix.
 Read/write <code>int</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShapeStyle.html#getLineStyleIndex--">getLineStyleIndex</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShapeStyle.html" title="interface in com.aspose.slides">IShapeStyle</a></code></dd>
</dl>
</li>
</ul>
<a name="setLineStyleIndex-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLineStyleIndex</h4>
<pre>public final&nbsp;void&nbsp;setLineStyleIndex(int&nbsp;value)</pre>
<div class="block"><p>
 Returns or sets line's column index in a style matrix.
 Read/write <code>int</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShapeStyle.html#setLineStyleIndex-int-">setLineStyleIndex</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShapeStyle.html" title="interface in com.aspose.slides">IShapeStyle</a></code></dd>
</dl>
</li>
</ul>
<a name="getFillColor--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFillColor</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IColorFormat.html" title="interface in com.aspose.slides">IColorFormat</a>&nbsp;getFillColor()</pre>
<div class="block"><p>
 Returns a shape's fill color.
 Read-only <a href="../../../com/aspose/slides/IColorFormat.html" title="interface in com.aspose.slides"><code>IColorFormat</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShapeStyle.html#getFillColor--">getFillColor</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShapeStyle.html" title="interface in com.aspose.slides">IShapeStyle</a></code></dd>
</dl>
</li>
</ul>
<a name="getFillStyleIndex--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFillStyleIndex</h4>
<pre>public final&nbsp;short&nbsp;getFillStyleIndex()</pre>
<div class="block"><p>
 Returns or sets shape's fill column index in style matrices.
 0 means no fill,
 positive value - index in theme's fill styles,
 negative value - index in theme's background styles.
 Read/write <code>short</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShapeStyle.html#getFillStyleIndex--">getFillStyleIndex</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShapeStyle.html" title="interface in com.aspose.slides">IShapeStyle</a></code></dd>
</dl>
</li>
</ul>
<a name="setFillStyleIndex-short-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFillStyleIndex</h4>
<pre>public final&nbsp;void&nbsp;setFillStyleIndex(short&nbsp;value)</pre>
<div class="block"><p>
 Returns or sets shape's fill column index in style matrices.
 0 means no fill,
 positive value - index in theme's fill styles,
 negative value - index in theme's background styles.
 Read/write <code>short</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShapeStyle.html#setFillStyleIndex-short-">setFillStyleIndex</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShapeStyle.html" title="interface in com.aspose.slides">IShapeStyle</a></code></dd>
</dl>
</li>
</ul>
<a name="getEffectColor--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEffectColor</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IColorFormat.html" title="interface in com.aspose.slides">IColorFormat</a>&nbsp;getEffectColor()</pre>
<div class="block"><p>
 Returns a shape's effect color.
 Read-only <a href="../../../com/aspose/slides/IColorFormat.html" title="interface in com.aspose.slides"><code>IColorFormat</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShapeStyle.html#getEffectColor--">getEffectColor</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShapeStyle.html" title="interface in com.aspose.slides">IShapeStyle</a></code></dd>
</dl>
</li>
</ul>
<a name="getEffectStyleIndex--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEffectStyleIndex</h4>
<pre>public final&nbsp;long&nbsp;getEffectStyleIndex()</pre>
<div class="block"><p>
 Returns or sets shape's effect column index in a style matrix.
 Read/write <code>long</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShapeStyle.html#getEffectStyleIndex--">getEffectStyleIndex</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShapeStyle.html" title="interface in com.aspose.slides">IShapeStyle</a></code></dd>
</dl>
</li>
</ul>
<a name="setEffectStyleIndex-long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEffectStyleIndex</h4>
<pre>public final&nbsp;void&nbsp;setEffectStyleIndex(long&nbsp;value)</pre>
<div class="block"><p>
 Returns or sets shape's effect column index in a style matrix.
 Read/write <code>long</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShapeStyle.html#setEffectStyleIndex-long-">setEffectStyleIndex</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShapeStyle.html" title="interface in com.aspose.slides">IShapeStyle</a></code></dd>
</dl>
</li>
</ul>
<a name="getFontColor--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFontColor</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IColorFormat.html" title="interface in com.aspose.slides">IColorFormat</a>&nbsp;getFontColor()</pre>
<div class="block"><p>
 Returns a shape's font color.
 Read-only <a href="../../../com/aspose/slides/IColorFormat.html" title="interface in com.aspose.slides"><code>IColorFormat</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShapeStyle.html#getFontColor--">getFontColor</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShapeStyle.html" title="interface in com.aspose.slides">IShapeStyle</a></code></dd>
</dl>
</li>
</ul>
<a name="getFontCollectionIndex--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFontCollectionIndex</h4>
<pre>public final&nbsp;byte&nbsp;getFontCollectionIndex()</pre>
<div class="block"><p>
 Returns or sets shape's font index in a font collection.
 Read/write <a href="../../../com/aspose/slides/FontCollectionIndex.html" title="class in com.aspose.slides"><code>FontCollectionIndex</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShapeStyle.html#getFontCollectionIndex--">getFontCollectionIndex</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShapeStyle.html" title="interface in com.aspose.slides">IShapeStyle</a></code></dd>
</dl>
</li>
</ul>
<a name="setFontCollectionIndex-byte-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setFontCollectionIndex</h4>
<pre>public final&nbsp;void&nbsp;setFontCollectionIndex(byte&nbsp;value)</pre>
<div class="block"><p>
 Returns or sets shape's font index in a font collection.
 Read/write <a href="../../../com/aspose/slides/FontCollectionIndex.html" title="class in com.aspose.slides"><code>FontCollectionIndex</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShapeStyle.html#setFontCollectionIndex-byte-">setFontCollectionIndex</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShapeStyle.html" title="interface in com.aspose.slides">IShapeStyle</a></code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/ShapesAlignmentType.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/ShapeThumbnailBounds.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/ShapeStyle.html" target="_top">Frames</a></li>
<li><a href="ShapeStyle.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><i>Copyright &copy; 2004-2025 Aspose Pty Ltd. All Rights Reserved.</i></small></p>
</body>
</html>
