<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_421) on Fri Jul 11 05:32:44 CDT 2025 -->
<title>ThreeDFormat (Aspose.Slides for Java (25.7) API Documentation)</title>
<meta name="date" content="2025-07-11">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ThreeDFormat (Aspose.Slides for Java (25.7) API Documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/Theme.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/TickLabelPositionType.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/ThreeDFormat.html" target="_top">Frames</a></li>
<li><a href="ThreeDFormat.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.aspose.slides</div>
<h2 title="Class ThreeDFormat" class="title">Class ThreeDFormat</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../com/aspose/slides/PVIObject.html" title="class in com.aspose.slides">com.aspose.slides.PVIObject</a></li>
<li>
<ul class="inheritance">
<li>com.aspose.slides.ThreeDFormat</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../com/aspose/slides/IPresentationComponent.html" title="interface in com.aspose.slides">IPresentationComponent</a>, <a href="../../../com/aspose/slides/ISlideComponent.html" title="interface in com.aspose.slides">ISlideComponent</a>, <a href="../../../com/aspose/slides/IThreeDFormat.html" title="interface in com.aspose.slides">IThreeDFormat</a>, <a href="../../../com/aspose/slides/IThreeDParamSource.html" title="interface in com.aspose.slides">IThreeDParamSource</a></dd>
</dl>
<hr>
<br>
<pre>public final class <span class="typeNameLabel">ThreeDFormat</span>
extends <a href="../../../com/aspose/slides/PVIObject.html" title="class in com.aspose.slides">PVIObject</a>
implements <a href="../../../com/aspose/slides/IThreeDFormat.html" title="interface in com.aspose.slides">IThreeDFormat</a>, <a href="../../../com/aspose/slides/IThreeDParamSource.html" title="interface in com.aspose.slides">IThreeDParamSource</a></pre>
<div class="block"><p>
 Represents 3-D properties.
 </p><p><hr><blockquote><pre>
 The following example shows how to add 3D shape in PowerPoint Presentation.
 <pre>
 // Create an instance of Presentation class.
 Presentation pres = new Presentation();
 try {
     // Add a shape using AddAutoShape method
     IAutoShape shape = pres.getSlides().get_Item(0).getShapes().addAutoShape(ShapeType.Rectangle, 200, 150, 200, 200);
     // Define TextFrame and its properties
     shape.getTextFrame().setText("3D");
     shape.getTextFrame().getParagraphs().get_Item(0).getParagraphFormat().getDefaultPortionFormat().setFontHeight(64);
     // Define ThreeDFormat Properties
     shape.getThreeDFormat().getCamera().setCameraType(CameraPresetType.OrthographicFront);
     shape.getThreeDFormat().getCamera().setRotation(20, 30, 40);
     shape.getThreeDFormat().getLightRig().setLightType(LightRigPresetType.Flat);
     shape.getThreeDFormat().getLightRig().setDirection(LightingDirection.Top);
     shape.getThreeDFormat().setMaterial(MaterialPresetType.Flat);
     shape.getThreeDFormat().setExtrusionHeight(100);
     shape.getThreeDFormat().getExtrusionColor().setColor(Color.BLUE);
     // Save the Presentation file
     pres.save("sandbox_3d.pptx", SaveFormat.Pptx);
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 The following example shows how to apply Gradient affect to 3D shape in PowerPoint Presentation.
 <pre>
 // Create an instance of Presentation class.
 Presentation pres = new Presentation();
 try {
     // Add a shape using AddAutoShape method
     IAutoShape shape = pres.getSlides().get_Item(0).getShapes().addAutoShape(ShapeType.Rectangle, 200, 150, 250, 250);
     // Define TextFrame and its properties
     shape.getTextFrame().setText("3D Gradient");
     shape.getTextFrame().getParagraphs().get_Item(0).getParagraphFormat().getDefaultPortionFormat().setFontHeight(64);
     // Configure FillFormat.FillType as FillType.Gradient and define gradient properties
     shape.getFillFormat().setFillType(FillType.Gradient);
     shape.getFillFormat().getGradientFormat().getGradientStops().add(0, Color.BLUE);
     shape.getFillFormat().getGradientFormat().getGradientStops().add(100, Color.ORANGE);
     // Define ThreeDFormat Properties
     shape.getThreeDFormat().getCamera().setCameraType(CameraPresetType.OrthographicFront);
     shape.getThreeDFormat().getCamera().setRotation(20, 30, 40);
     shape.getThreeDFormat().getLightRig().setLightType(LightRigPresetType.Flat);
     shape.getThreeDFormat().getLightRig().setDirection(LightingDirection.Top);
     shape.getThreeDFormat().setMaterial(MaterialPresetType.Flat);
     shape.getThreeDFormat().setExtrusionHeight(100);
     shape.getThreeDFormat().getExtrusionColor().setColor(Color.BLUE);
     // Save the Presentation file
     pres.save("sandbox_3d.pptx", SaveFormat.Pptx);
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 The following example shows how to apply 3D effect on text. For creating a 3D text its possible to use WordArt transform effect.
 <pre>
 // Create an instance of Presentation class.
 Presentation pres = new Presentation();
 try {
     // Add a shape using AddAutoShape method
      IAutoShape shape = pres.getSlides().get_Item(0).getShapes().addAutoShape(ShapeType.Rectangle, 200, 150, 250, 250);
     // Define TextFrame and its properties
     shape.getTextFrame().setText("3D Text");
     // Configure FillFormat.FillType as FillType.NoFill
     shape.getFillFormat().setFillType(FillType.NoFill);
     shape.getLineFormat().getFillFormat().setFillType(FillType.NoFill);
     // Configure Portion of TextFrame and configure properties of PortionFormat
     Portion portion = (Portion)shape.getTextFrame().getParagraphs().get_Item(0).getPortions().get_Item(0);
     portion.getPortionFormat().getFillFormat().setFillType(FillType.Pattern);
     portion.getPortionFormat().getFillFormat().getPatternFormat().getForeColor().setColor(Color.ORANGE);
     portion.getPortionFormat().getFillFormat().getPatternFormat().getBackColor().setColor(Color.WHITE);
     portion.getPortionFormat().getFillFormat().getPatternFormat().setPatternStyle(PatternStyle.LargeGrid);
     shape.getTextFrame().getParagraphs().get_Item(0).getParagraphFormat().getDefaultPortionFormat().setFontHeight(128);
     ITextFrame textFrame = shape.getTextFrame();
     // setup "Arch Up" WordArt transform effect
     textFrame.getTextFrameFormat().setTransform(TextShapeType.ArchUp);
     // Define ThreeDFormat Properties of ITextFrame
     textFrame.getTextFrameFormat().getThreeDFormat().setExtrusionHeight(3.5f);
     textFrame.getTextFrameFormat().getThreeDFormat().setDepth(3);
     textFrame.getTextFrameFormat().getThreeDFormat().setMaterial(MaterialPresetType.Plastic);
     textFrame.getTextFrameFormat().getThreeDFormat().getLightRig().setDirection(LightingDirection.Top);
     textFrame.getTextFrameFormat().getThreeDFormat().getLightRig().setLightType(LightRigPresetType.Balanced);
     textFrame.getTextFrameFormat().getThreeDFormat().getLightRig().setRotation(0, 0, 40);
     textFrame.getTextFrameFormat().getThreeDFormat().getCamera().setCameraType(CameraPresetType.PerspectiveContrastingRightFacing);
     // Save the Presentation file
     pres.save("text3d.pptx", SaveFormat.Pptx);
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IShapeBevel.html" title="interface in com.aspose.slides">IShapeBevel</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ThreeDFormat.html#getBevelBottom--">getBevelBottom</a></span>()</code>
<div class="block">
 Returns or sets the type of a bottom 3D bevel.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IShapeBevel.html" title="interface in com.aspose.slides">IShapeBevel</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ThreeDFormat.html#getBevelTop--">getBevelTop</a></span>()</code>
<div class="block">
 Returns or sets the type of a top 3D bevel.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/ICamera.html" title="interface in com.aspose.slides">ICamera</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ThreeDFormat.html#getCamera--">getCamera</a></span>()</code>
<div class="block">
 Returns or sets the settings of a camera.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IColorFormat.html" title="interface in com.aspose.slides">IColorFormat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ThreeDFormat.html#getContourColor--">getContourColor</a></span>()</code>
<div class="block">
 Returns or sets the color of a contour.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ThreeDFormat.html#getContourWidth--">getContourWidth</a></span>()</code>
<div class="block">
 Returns or sets the width of a 3D contour.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ThreeDFormat.html#getDepth--">getDepth</a></span>()</code>
<div class="block">
 Returns or sets the depth of a 3D shape.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IThreeDFormatEffectiveData.html" title="interface in com.aspose.slides">IThreeDFormatEffectiveData</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ThreeDFormat.html#getEffective--">getEffective</a></span>()</code>
<div class="block">
 Gets effective 3-D formatting data with the inheritance applied.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IColorFormat.html" title="interface in com.aspose.slides">IColorFormat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ThreeDFormat.html#getExtrusionColor--">getExtrusionColor</a></span>()</code>
<div class="block">
 Returns or sets the color of an extrusion.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ThreeDFormat.html#getExtrusionHeight--">getExtrusionHeight</a></span>()</code>
<div class="block">
 Returns or sets the height of an extrusion effect.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/ILightRig.html" title="interface in com.aspose.slides">ILightRig</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ThreeDFormat.html#getLightRig--">getLightRig</a></span>()</code>
<div class="block">
 Returns or sets the type of a light.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ThreeDFormat.html#getMaterial--">getMaterial</a></span>()</code>
<div class="block">
 Returns or sets the type of a material.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ThreeDFormat.html#getVersion--">getVersion</a></span>()</code>
<div class="block">
 Version.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ThreeDFormat.html#setContourWidth-double-">setContourWidth</a></span>(double&nbsp;value)</code>
<div class="block">
 Returns or sets the width of a 3D contour.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ThreeDFormat.html#setDepth-double-">setDepth</a></span>(double&nbsp;value)</code>
<div class="block">
 Returns or sets the depth of a 3D shape.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ThreeDFormat.html#setExtrusionHeight-double-">setExtrusionHeight</a></span>(double&nbsp;value)</code>
<div class="block">
 Returns or sets the height of an extrusion effect.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ThreeDFormat.html#setMaterial-int-">setMaterial</a></span>(int&nbsp;value)</code>
<div class="block">
 Returns or sets the type of a material.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.aspose.slides.PVIObject">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;com.aspose.slides.<a href="../../../com/aspose/slides/PVIObject.html" title="class in com.aspose.slides">PVIObject</a></h3>
<code><a href="../../../com/aspose/slides/PVIObject.html#equals-java.lang.Object-">equals</a>, <a href="../../../com/aspose/slides/PVIObject.html#getParent_Immediate--">getParent_Immediate</a>, <a href="../../../com/aspose/slides/PVIObject.html#getParent_IPresentationComponent--">getParent_IPresentationComponent</a>, <a href="../../../com/aspose/slides/PVIObject.html#getParent_ISlideComponent--">getParent_ISlideComponent</a>, <a href="../../../com/aspose/slides/PVIObject.html#getPresentation--">getPresentation</a>, <a href="../../../com/aspose/slides/PVIObject.html#getSlide--">getSlide</a>, <a href="../../../com/aspose/slides/PVIObject.html#hashCode--">hashCode</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>getClass, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getVersion--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVersion</h4>
<pre>public&nbsp;long&nbsp;getVersion()</pre>
<div class="block"><p>
 Version.
 Read-only <code>long</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../com/aspose/slides/PVIObject.html#getVersion--">getVersion</a></code>&nbsp;in class&nbsp;<code><a href="../../../com/aspose/slides/PVIObject.html" title="class in com.aspose.slides">PVIObject</a></code></dd>
</dl>
</li>
</ul>
<a name="getContourWidth--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getContourWidth</h4>
<pre>public final&nbsp;double&nbsp;getContourWidth()</pre>
<div class="block"><p>
 Returns or sets the width of a 3D contour.
 Read/write <code>double</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IThreeDFormat.html#getContourWidth--">getContourWidth</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IThreeDFormat.html" title="interface in com.aspose.slides">IThreeDFormat</a></code></dd>
</dl>
</li>
</ul>
<a name="setContourWidth-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setContourWidth</h4>
<pre>public final&nbsp;void&nbsp;setContourWidth(double&nbsp;value)</pre>
<div class="block"><p>
 Returns or sets the width of a 3D contour.
 Read/write <code>double</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IThreeDFormat.html#setContourWidth-double-">setContourWidth</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IThreeDFormat.html" title="interface in com.aspose.slides">IThreeDFormat</a></code></dd>
</dl>
</li>
</ul>
<a name="getExtrusionHeight--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getExtrusionHeight</h4>
<pre>public final&nbsp;double&nbsp;getExtrusionHeight()</pre>
<div class="block"><p>
 Returns or sets the height of an extrusion effect.
 Read/write <code>double</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IThreeDFormat.html#getExtrusionHeight--">getExtrusionHeight</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IThreeDFormat.html" title="interface in com.aspose.slides">IThreeDFormat</a></code></dd>
</dl>
</li>
</ul>
<a name="setExtrusionHeight-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setExtrusionHeight</h4>
<pre>public final&nbsp;void&nbsp;setExtrusionHeight(double&nbsp;value)</pre>
<div class="block"><p>
 Returns or sets the height of an extrusion effect.
 Read/write <code>double</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IThreeDFormat.html#setExtrusionHeight-double-">setExtrusionHeight</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IThreeDFormat.html" title="interface in com.aspose.slides">IThreeDFormat</a></code></dd>
</dl>
</li>
</ul>
<a name="getDepth--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDepth</h4>
<pre>public final&nbsp;double&nbsp;getDepth()</pre>
<div class="block"><p>
 Returns or sets the depth of a 3D shape.
 Read/write <code>double</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IThreeDFormat.html#getDepth--">getDepth</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IThreeDFormat.html" title="interface in com.aspose.slides">IThreeDFormat</a></code></dd>
</dl>
</li>
</ul>
<a name="setDepth-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDepth</h4>
<pre>public final&nbsp;void&nbsp;setDepth(double&nbsp;value)</pre>
<div class="block"><p>
 Returns or sets the depth of a 3D shape.
 Read/write <code>double</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IThreeDFormat.html#setDepth-double-">setDepth</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IThreeDFormat.html" title="interface in com.aspose.slides">IThreeDFormat</a></code></dd>
</dl>
</li>
</ul>
<a name="getBevelTop--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBevelTop</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IShapeBevel.html" title="interface in com.aspose.slides">IShapeBevel</a>&nbsp;getBevelTop()</pre>
<div class="block"><p>
 Returns or sets the type of a top 3D bevel.
 Read-only <a href="../../../com/aspose/slides/IShapeBevel.html" title="interface in com.aspose.slides"><code>IShapeBevel</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IThreeDFormat.html#getBevelTop--">getBevelTop</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IThreeDFormat.html" title="interface in com.aspose.slides">IThreeDFormat</a></code></dd>
</dl>
</li>
</ul>
<a name="getBevelBottom--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBevelBottom</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IShapeBevel.html" title="interface in com.aspose.slides">IShapeBevel</a>&nbsp;getBevelBottom()</pre>
<div class="block"><p>
 Returns or sets the type of a bottom 3D bevel.
 Read-only <a href="../../../com/aspose/slides/IShapeBevel.html" title="interface in com.aspose.slides"><code>IShapeBevel</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IThreeDFormat.html#getBevelBottom--">getBevelBottom</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IThreeDFormat.html" title="interface in com.aspose.slides">IThreeDFormat</a></code></dd>
</dl>
</li>
</ul>
<a name="getContourColor--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getContourColor</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IColorFormat.html" title="interface in com.aspose.slides">IColorFormat</a>&nbsp;getContourColor()</pre>
<div class="block"><p>
 Returns or sets the color of a contour.
 Read-only <a href="../../../com/aspose/slides/IColorFormat.html" title="interface in com.aspose.slides"><code>IColorFormat</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IThreeDFormat.html#getContourColor--">getContourColor</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IThreeDFormat.html" title="interface in com.aspose.slides">IThreeDFormat</a></code></dd>
</dl>
</li>
</ul>
<a name="getExtrusionColor--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getExtrusionColor</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IColorFormat.html" title="interface in com.aspose.slides">IColorFormat</a>&nbsp;getExtrusionColor()</pre>
<div class="block"><p>
 Returns or sets the color of an extrusion.
 Read-only <a href="../../../com/aspose/slides/IColorFormat.html" title="interface in com.aspose.slides"><code>IColorFormat</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IThreeDFormat.html#getExtrusionColor--">getExtrusionColor</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IThreeDFormat.html" title="interface in com.aspose.slides">IThreeDFormat</a></code></dd>
</dl>
</li>
</ul>
<a name="getCamera--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCamera</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/ICamera.html" title="interface in com.aspose.slides">ICamera</a>&nbsp;getCamera()</pre>
<div class="block"><p>
 Returns or sets the settings of a camera.
 Read-only <a href="../../../com/aspose/slides/ICamera.html" title="interface in com.aspose.slides"><code>ICamera</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IThreeDFormat.html#getCamera--">getCamera</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IThreeDFormat.html" title="interface in com.aspose.slides">IThreeDFormat</a></code></dd>
</dl>
</li>
</ul>
<a name="getLightRig--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLightRig</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/ILightRig.html" title="interface in com.aspose.slides">ILightRig</a>&nbsp;getLightRig()</pre>
<div class="block"><p>
 Returns or sets the type of a light.
 Read-only <a href="../../../com/aspose/slides/ILightRig.html" title="interface in com.aspose.slides"><code>ILightRig</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IThreeDFormat.html#getLightRig--">getLightRig</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IThreeDFormat.html" title="interface in com.aspose.slides">IThreeDFormat</a></code></dd>
</dl>
</li>
</ul>
<a name="getMaterial--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMaterial</h4>
<pre>public final&nbsp;int&nbsp;getMaterial()</pre>
<div class="block"><p>
 Returns or sets the type of a material.
 Read/write <a href="../../../com/aspose/slides/MaterialPresetType.html" title="class in com.aspose.slides"><code>MaterialPresetType</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IThreeDFormat.html#getMaterial--">getMaterial</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IThreeDFormat.html" title="interface in com.aspose.slides">IThreeDFormat</a></code></dd>
</dl>
</li>
</ul>
<a name="setMaterial-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMaterial</h4>
<pre>public final&nbsp;void&nbsp;setMaterial(int&nbsp;value)</pre>
<div class="block"><p>
 Returns or sets the type of a material.
 Read/write <a href="../../../com/aspose/slides/MaterialPresetType.html" title="class in com.aspose.slides"><code>MaterialPresetType</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IThreeDFormat.html#setMaterial-int-">setMaterial</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IThreeDFormat.html" title="interface in com.aspose.slides">IThreeDFormat</a></code></dd>
</dl>
</li>
</ul>
<a name="getEffective--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getEffective</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IThreeDFormatEffectiveData.html" title="interface in com.aspose.slides">IThreeDFormatEffectiveData</a>&nbsp;getEffective()</pre>
<div class="block"><p>
 Gets effective 3-D formatting data with the inheritance applied.
 </p><p><hr><blockquote><pre>
 This example demonstrates how to get effective properties for camera, light rig and shape's top bevel.
 <pre>
 Presentation pres = new Presentation("MyPresentation.pptx");
 try 
 {
     IThreeDFormatEffectiveData threeDEffectiveData = pres.getSlides().get_Item(0).getShapes().get_Item(0).getThreeDFormat().getEffective();
     System.out.println("= Effective camera properties =");
     System.out.println("Type: " + threeDEffectiveData.getCamera().getCameraType());
     System.out.println("Field of view: " + threeDEffectiveData.getCamera().getFieldOfViewAngle());
     System.out.println("Zoom: " + threeDEffectiveData.getCamera().getZoom());
     System.out.println("= Effective light rig properties =");
     System.out.println("Type: " + threeDEffectiveData.getLightRig().getLightType());
     System.out.println("Direction: " + threeDEffectiveData.getLightRig().getDirection());
     System.out.println("= Effective shape's top face relief properties =");
     System.out.println("Type: " + threeDEffectiveData.getBevelTop().getBevelType());
     System.out.println("Width: " + threeDEffectiveData.getBevelTop().getWidth());
     System.out.println("Height: " + threeDEffectiveData.getBevelTop().getHeight());
 } finally {
  if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IThreeDFormat.html#getEffective--">getEffective</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IThreeDFormat.html" title="interface in com.aspose.slides">IThreeDFormat</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>A <a href="../../../com/aspose/slides/IThreeDFormatEffectiveData.html" title="interface in com.aspose.slides"><code>IThreeDFormatEffectiveData</code></a>.</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/Theme.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/TickLabelPositionType.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/ThreeDFormat.html" target="_top">Frames</a></li>
<li><a href="ThreeDFormat.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><i>Copyright &copy; 2004-2025 Aspose Pty Ltd. All Rights Reserved.</i></small></p>
</body>
</html>
