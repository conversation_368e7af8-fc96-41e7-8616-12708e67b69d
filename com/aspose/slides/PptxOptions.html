<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_421) on Fri Jul 11 05:32:43 CDT 2025 -->
<title>PptxOptions (Aspose.Slides for Java (25.7) API Documentation)</title>
<meta name="date" content="2025-07-11">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="PptxOptions (Aspose.Slides for Java (25.7) API Documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/PptxException.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/PptxReadException.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/PptxOptions.html" target="_top">Frames</a></li>
<li><a href="PptxOptions.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.aspose.slides</div>
<h2 title="Class PptxOptions" class="title">Class PptxOptions</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../com/aspose/slides/SaveOptions.html" title="class in com.aspose.slides">com.aspose.slides.SaveOptions</a></li>
<li>
<ul class="inheritance">
<li>com.aspose.slides.PptxOptions</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../com/aspose/slides/IPptxOptions.html" title="interface in com.aspose.slides">IPptxOptions</a>, <a href="../../../com/aspose/slides/ISaveOptions.html" title="interface in com.aspose.slides">ISaveOptions</a>, java.lang.Cloneable</dd>
</dl>
<hr>
<br>
<pre>public final class <span class="typeNameLabel">PptxOptions</span>
extends <a href="../../../com/aspose/slides/SaveOptions.html" title="class in com.aspose.slides">SaveOptions</a>
implements <a href="../../../com/aspose/slides/IPptxOptions.html" title="interface in com.aspose.slides">IPptxOptions</a>, java.lang.Cloneable</pre>
<div class="block"><p>
 Represents options for saving OpenXml presentations (PPTX, PPSX, POTX, PPTM, PPSM, POTM).
 </p></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PptxOptions.html#PptxOptions--">PptxOptions</a></span>()</code>
<div class="block">
 Creates new instance of PptxOptions</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PptxOptions.html#getConformance--">getConformance</a></span>()</code>
<div class="block">
 Specifies the conformance class to which the Presentation document conforms.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PptxOptions.html#getRefreshThumbnail--">getRefreshThumbnail</a></span>()</code>
<div class="block">
 Specifies whether the presentation thumbnail will be refreshed.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PptxOptions.html#getZip64Mode--">getZip64Mode</a></span>()</code>
<div class="block">
 Specifies whether the ZIP64 format is used for the Presentation document.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PptxOptions.html#setConformance-int-">setConformance</a></span>(int&nbsp;value)</code>
<div class="block">
 Specifies the conformance class to which the Presentation document conforms.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PptxOptions.html#setRefreshThumbnail-boolean-">setRefreshThumbnail</a></span>(boolean&nbsp;value)</code>
<div class="block">
 Specifies whether the presentation thumbnail will be refreshed.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PptxOptions.html#setZip64Mode-int-">setZip64Mode</a></span>(int&nbsp;value)</code>
<div class="block">
 Specifies whether the ZIP64 format is used for the Presentation document.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.aspose.slides.SaveOptions">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;com.aspose.slides.<a href="../../../com/aspose/slides/SaveOptions.html" title="class in com.aspose.slides">SaveOptions</a></h3>
<code><a href="../../../com/aspose/slides/SaveOptions.html#getDefaultRegularFont--">getDefaultRegularFont</a>, <a href="../../../com/aspose/slides/SaveOptions.html#getGradientStyle--">getGradientStyle</a>, <a href="../../../com/aspose/slides/SaveOptions.html#getProgressCallback--">getProgressCallback</a>, <a href="../../../com/aspose/slides/SaveOptions.html#getSkipJavaScriptLinks--">getSkipJavaScriptLinks</a>, <a href="../../../com/aspose/slides/SaveOptions.html#getWarningCallback--">getWarningCallback</a>, <a href="../../../com/aspose/slides/SaveOptions.html#setDefaultRegularFont-java.lang.String-">setDefaultRegularFont</a>, <a href="../../../com/aspose/slides/SaveOptions.html#setGradientStyle-int-">setGradientStyle</a>, <a href="../../../com/aspose/slides/SaveOptions.html#setProgressCallback-com.aspose.slides.IProgressCallback-">setProgressCallback</a>, <a href="../../../com/aspose/slides/SaveOptions.html#setSkipJavaScriptLinks-boolean-">setSkipJavaScriptLinks</a>, <a href="../../../com/aspose/slides/SaveOptions.html#setWarningCallback-com.aspose.slides.IWarningCallback-">setWarningCallback</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.aspose.slides.ISaveOptions">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;com.aspose.slides.<a href="../../../com/aspose/slides/ISaveOptions.html" title="interface in com.aspose.slides">ISaveOptions</a></h3>
<code><a href="../../../com/aspose/slides/ISaveOptions.html#getDefaultRegularFont--">getDefaultRegularFont</a>, <a href="../../../com/aspose/slides/ISaveOptions.html#getGradientStyle--">getGradientStyle</a>, <a href="../../../com/aspose/slides/ISaveOptions.html#getProgressCallback--">getProgressCallback</a>, <a href="../../../com/aspose/slides/ISaveOptions.html#getSkipJavaScriptLinks--">getSkipJavaScriptLinks</a>, <a href="../../../com/aspose/slides/ISaveOptions.html#getWarningCallback--">getWarningCallback</a>, <a href="../../../com/aspose/slides/ISaveOptions.html#setDefaultRegularFont-java.lang.String-">setDefaultRegularFont</a>, <a href="../../../com/aspose/slides/ISaveOptions.html#setGradientStyle-int-">setGradientStyle</a>, <a href="../../../com/aspose/slides/ISaveOptions.html#setProgressCallback-com.aspose.slides.IProgressCallback-">setProgressCallback</a>, <a href="../../../com/aspose/slides/ISaveOptions.html#setSkipJavaScriptLinks-boolean-">setSkipJavaScriptLinks</a>, <a href="../../../com/aspose/slides/ISaveOptions.html#setWarningCallback-com.aspose.slides.IWarningCallback-">setWarningCallback</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="PptxOptions--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>PptxOptions</h4>
<pre>public&nbsp;PptxOptions()</pre>
<div class="block"><p>
 Creates new instance of PptxOptions
 </p></div>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getConformance--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getConformance</h4>
<pre>public final&nbsp;int&nbsp;getConformance()</pre>
<div class="block"><p>
 Specifies the conformance class to which the Presentation document conforms.
 Default value is <a href="../../../com/aspose/slides/Conformance.html#Ecma376_2006"><code>Conformance.Ecma376_2006</code></a>
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPptxOptions.html#getConformance--">getConformance</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPptxOptions.html" title="interface in com.aspose.slides">IPptxOptions</a></code></dd>
</dl>
</li>
</ul>
<a name="setConformance-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setConformance</h4>
<pre>public final&nbsp;void&nbsp;setConformance(int&nbsp;value)</pre>
<div class="block"><p>
 Specifies the conformance class to which the Presentation document conforms.
 Default value is <a href="../../../com/aspose/slides/Conformance.html#Ecma376_2006"><code>Conformance.Ecma376_2006</code></a>
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPptxOptions.html#setConformance-int-">setConformance</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPptxOptions.html" title="interface in com.aspose.slides">IPptxOptions</a></code></dd>
</dl>
</li>
</ul>
<a name="getZip64Mode--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getZip64Mode</h4>
<pre>public final&nbsp;int&nbsp;getZip64Mode()</pre>
<div class="block"><p>
 Specifies whether the ZIP64 format is used for the Presentation document. 
 The default value is <a href="../../../com/aspose/slides/Zip64Mode.html#IfNecessary"><code>Zip64Mode.IfNecessary</code></a>
 </p><p><hr><blockquote><pre> Example:
 <pre>
 Presentation pres = new Presentation("demo.pptx");
 try {
     PptxOptions pptxOptions = new PptxOptions();
     pptxOptions.setZip64Mode(Zip64Mode.Always);
     pres.save("demo-zip64.pptx", SaveFormat.Pptx, pptxOptions);
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPptxOptions.html#getZip64Mode--">getZip64Mode</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPptxOptions.html" title="interface in com.aspose.slides">IPptxOptions</a></code></dd>
</dl>
</li>
</ul>
<a name="setZip64Mode-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setZip64Mode</h4>
<pre>public final&nbsp;void&nbsp;setZip64Mode(int&nbsp;value)</pre>
<div class="block"><p>
 Specifies whether the ZIP64 format is used for the Presentation document. 
 The default value is <a href="../../../com/aspose/slides/Zip64Mode.html#IfNecessary"><code>Zip64Mode.IfNecessary</code></a>
 </p><p><hr><blockquote><pre> Example:
 <pre>
 Presentation pres = new Presentation("demo.pptx");
 try {
     PptxOptions pptxOptions = new PptxOptions();
     pptxOptions.setZip64Mode(Zip64Mode.Always);
     pres.save("demo-zip64.pptx", SaveFormat.Pptx, pptxOptions);
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPptxOptions.html#setZip64Mode-int-">setZip64Mode</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPptxOptions.html" title="interface in com.aspose.slides">IPptxOptions</a></code></dd>
</dl>
</li>
</ul>
<a name="getRefreshThumbnail--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRefreshThumbnail</h4>
<pre>public final&nbsp;boolean&nbsp;getRefreshThumbnail()</pre>
<div class="block"><p>
 Specifies whether the presentation thumbnail will be refreshed. 
 Read/write boolean.
 Default value is <b>true</b>. 
 </p><p><hr><blockquote><pre> Example:
 <pre>
 Presentation pres = new Presentation("demo.pptx");
 try {
     PptxOptions pptxOptions = new PptxOptions();
     pptxOptions.setRefreshThumbnail(false);
     pres.save("result_with_old_thumbnail.pptx", SaveFormat.Pptx, pptxOptions);
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p><p><hr>
 <p>When the option value is <b>true</b>, the new thumbnail will be generated.</p>
 <p>When the option value is <b>false</b>, the current thumbnail will be saved as is.</p> 
 </hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPptxOptions.html#getRefreshThumbnail--">getRefreshThumbnail</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPptxOptions.html" title="interface in com.aspose.slides">IPptxOptions</a></code></dd>
</dl>
</li>
</ul>
<a name="setRefreshThumbnail-boolean-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setRefreshThumbnail</h4>
<pre>public final&nbsp;void&nbsp;setRefreshThumbnail(boolean&nbsp;value)</pre>
<div class="block"><p>
 Specifies whether the presentation thumbnail will be refreshed. 
 Read/write boolean.
 Default value is <b>true</b>. 
 </p><p><hr><blockquote><pre> Example:
 <pre>
 Presentation pres = new Presentation("demo.pptx");
 try {
     PptxOptions pptxOptions = new PptxOptions();
     pptxOptions.setRefreshThumbnail(false);
     pres.save("result_with_old_thumbnail.pptx", SaveFormat.Pptx, pptxOptions);
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p><p><hr>
 <p>When the option value is <b>true</b>, the new thumbnail will be generated.</p>
 <p>When the option value is <b>false</b>, the current thumbnail will be saved as is.</p> 
 </hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPptxOptions.html#setRefreshThumbnail-boolean-">setRefreshThumbnail</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPptxOptions.html" title="interface in com.aspose.slides">IPptxOptions</a></code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/PptxException.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/PptxReadException.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/PptxOptions.html" target="_top">Frames</a></li>
<li><a href="PptxOptions.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><i>Copyright &copy; 2004-2025 Aspose Pty Ltd. All Rights Reserved.</i></small></p>
</body>
</html>
