<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_421) on Fri Jul 11 05:32:45 CDT 2025 -->
<title>XpsOptions (Aspose.Slides for Java (25.7) API Documentation)</title>
<meta name="date" content="2025-07-11">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="XpsOptions (Aspose.Slides for Java (25.7) API Documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/XamlOptions.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/Zip64Mode.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/XpsOptions.html" target="_top">Frames</a></li>
<li><a href="XpsOptions.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.aspose.slides</div>
<h2 title="Class XpsOptions" class="title">Class XpsOptions</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../com/aspose/slides/SaveOptions.html" title="class in com.aspose.slides">com.aspose.slides.SaveOptions</a></li>
<li>
<ul class="inheritance">
<li>com.aspose.slides.XpsOptions</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../com/aspose/slides/ISaveOptions.html" title="interface in com.aspose.slides">ISaveOptions</a>, <a href="../../../com/aspose/slides/IXpsOptions.html" title="interface in com.aspose.slides">IXpsOptions</a></dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">XpsOptions</span>
extends <a href="../../../com/aspose/slides/SaveOptions.html" title="class in com.aspose.slides">SaveOptions</a>
implements <a href="../../../com/aspose/slides/IXpsOptions.html" title="interface in com.aspose.slides">IXpsOptions</a></pre>
<div class="block"><p>
 Provides options that control how a presentation is saved in XPS format.
 </p><p><hr><blockquote><pre>
 The following example shows how to converting presentations to XPS using default settings.
 <pre>
 // Instantiate a Presentation object that represents a presentation file
 Presentation pres = new Presentation("Convert_XPS.pptx");
 try {
     // Saving the presentation to XPS document
     pres.save("XPS_Output_Without_XPSOption_out.xps", SaveFormat.Xps);
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 The following example shows how to converting presentations to XPS using custom settings.
 <pre>
 // Instantiate a Presentation object that represents a presentation file
 Presentation pres = new Presentation("Convert_XPS_Options.pptx");
 try {
     // Instantiate the TiffOptions class
     XpsOptions options = new XpsOptions();
     // Save MetaFiles as PNG
     options.setSaveMetafilesAsPng(true);
     // Save the presentation to XPS document
     pres.save("XPS_With_Options_out.xps", SaveFormat.Xps, options);
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/XpsOptions.html#XpsOptions--">XpsOptions</a></span>()</code>
<div class="block">
 Default constructor.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/XpsOptions.html#getDrawSlidesFrame--">getDrawSlidesFrame</a></span>()</code>
<div class="block">
  True to draw black frame around each slide.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/XpsOptions.html#getSaveMetafilesAsPng--">getSaveMetafilesAsPng</a></span>()</code>
<div class="block">
 True to convert all metafiles used in a presentation to the PNG images.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/XpsOptions.html#getShowHiddenSlides--">getShowHiddenSlides</a></span>()</code>
<div class="block">
 Specifies whether the generated document should include hidden slides or not.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/XpsOptions.html#setDrawSlidesFrame-boolean-">setDrawSlidesFrame</a></span>(boolean&nbsp;value)</code>
<div class="block">
  True to draw black frame around each slide.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/XpsOptions.html#setSaveMetafilesAsPng-boolean-">setSaveMetafilesAsPng</a></span>(boolean&nbsp;value)</code>
<div class="block">
 True to convert all metafiles used in a presentation to the PNG images.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/XpsOptions.html#setShowHiddenSlides-boolean-">setShowHiddenSlides</a></span>(boolean&nbsp;value)</code>
<div class="block">
 Specifies whether the generated document should include hidden slides or not.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.aspose.slides.SaveOptions">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;com.aspose.slides.<a href="../../../com/aspose/slides/SaveOptions.html" title="class in com.aspose.slides">SaveOptions</a></h3>
<code><a href="../../../com/aspose/slides/SaveOptions.html#getDefaultRegularFont--">getDefaultRegularFont</a>, <a href="../../../com/aspose/slides/SaveOptions.html#getGradientStyle--">getGradientStyle</a>, <a href="../../../com/aspose/slides/SaveOptions.html#getProgressCallback--">getProgressCallback</a>, <a href="../../../com/aspose/slides/SaveOptions.html#getSkipJavaScriptLinks--">getSkipJavaScriptLinks</a>, <a href="../../../com/aspose/slides/SaveOptions.html#getWarningCallback--">getWarningCallback</a>, <a href="../../../com/aspose/slides/SaveOptions.html#setDefaultRegularFont-java.lang.String-">setDefaultRegularFont</a>, <a href="../../../com/aspose/slides/SaveOptions.html#setGradientStyle-int-">setGradientStyle</a>, <a href="../../../com/aspose/slides/SaveOptions.html#setProgressCallback-com.aspose.slides.IProgressCallback-">setProgressCallback</a>, <a href="../../../com/aspose/slides/SaveOptions.html#setSkipJavaScriptLinks-boolean-">setSkipJavaScriptLinks</a>, <a href="../../../com/aspose/slides/SaveOptions.html#setWarningCallback-com.aspose.slides.IWarningCallback-">setWarningCallback</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.aspose.slides.ISaveOptions">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;com.aspose.slides.<a href="../../../com/aspose/slides/ISaveOptions.html" title="interface in com.aspose.slides">ISaveOptions</a></h3>
<code><a href="../../../com/aspose/slides/ISaveOptions.html#getDefaultRegularFont--">getDefaultRegularFont</a>, <a href="../../../com/aspose/slides/ISaveOptions.html#getGradientStyle--">getGradientStyle</a>, <a href="../../../com/aspose/slides/ISaveOptions.html#getProgressCallback--">getProgressCallback</a>, <a href="../../../com/aspose/slides/ISaveOptions.html#getSkipJavaScriptLinks--">getSkipJavaScriptLinks</a>, <a href="../../../com/aspose/slides/ISaveOptions.html#getWarningCallback--">getWarningCallback</a>, <a href="../../../com/aspose/slides/ISaveOptions.html#setDefaultRegularFont-java.lang.String-">setDefaultRegularFont</a>, <a href="../../../com/aspose/slides/ISaveOptions.html#setGradientStyle-int-">setGradientStyle</a>, <a href="../../../com/aspose/slides/ISaveOptions.html#setProgressCallback-com.aspose.slides.IProgressCallback-">setProgressCallback</a>, <a href="../../../com/aspose/slides/ISaveOptions.html#setSkipJavaScriptLinks-boolean-">setSkipJavaScriptLinks</a>, <a href="../../../com/aspose/slides/ISaveOptions.html#setWarningCallback-com.aspose.slides.IWarningCallback-">setWarningCallback</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="XpsOptions--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>XpsOptions</h4>
<pre>public&nbsp;XpsOptions()</pre>
<div class="block"><p>
 Default constructor.
 </p></div>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getShowHiddenSlides--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getShowHiddenSlides</h4>
<pre>public final&nbsp;boolean&nbsp;getShowHiddenSlides()</pre>
<div class="block"><p>
 Specifies whether the generated document should include hidden slides or not.
 Default is false.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IXpsOptions.html#getShowHiddenSlides--">getShowHiddenSlides</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IXpsOptions.html" title="interface in com.aspose.slides">IXpsOptions</a></code></dd>
</dl>
</li>
</ul>
<a name="setShowHiddenSlides-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setShowHiddenSlides</h4>
<pre>public final&nbsp;void&nbsp;setShowHiddenSlides(boolean&nbsp;value)</pre>
<div class="block"><p>
 Specifies whether the generated document should include hidden slides or not.
 Default is false.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IXpsOptions.html#setShowHiddenSlides-boolean-">setShowHiddenSlides</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IXpsOptions.html" title="interface in com.aspose.slides">IXpsOptions</a></code></dd>
</dl>
</li>
</ul>
<a name="getSaveMetafilesAsPng--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSaveMetafilesAsPng</h4>
<pre>public final&nbsp;boolean&nbsp;getSaveMetafilesAsPng()</pre>
<div class="block"><p>
 True to convert all metafiles used in a presentation to the PNG images.
 Read/write <code>boolean</code>.
 </p><p><hr>
 Default is <b>true</b>.
 </hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IXpsOptions.html#getSaveMetafilesAsPng--">getSaveMetafilesAsPng</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IXpsOptions.html" title="interface in com.aspose.slides">IXpsOptions</a></code></dd>
</dl>
</li>
</ul>
<a name="setSaveMetafilesAsPng-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSaveMetafilesAsPng</h4>
<pre>public final&nbsp;void&nbsp;setSaveMetafilesAsPng(boolean&nbsp;value)</pre>
<div class="block"><p>
 True to convert all metafiles used in a presentation to the PNG images.
 Read/write <code>boolean</code>.
 </p><p><hr>
 Default is <b>true</b>.
 </hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IXpsOptions.html#setSaveMetafilesAsPng-boolean-">setSaveMetafilesAsPng</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IXpsOptions.html" title="interface in com.aspose.slides">IXpsOptions</a></code></dd>
</dl>
</li>
</ul>
<a name="getDrawSlidesFrame--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDrawSlidesFrame</h4>
<pre>public final&nbsp;boolean&nbsp;getDrawSlidesFrame()</pre>
<div class="block"><p>
  True to draw black frame around each slide.
  Read/write <code>boolean</code>.
  </p><p><hr>
 Default is <b>false</b>.
 </hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IXpsOptions.html#getDrawSlidesFrame--">getDrawSlidesFrame</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IXpsOptions.html" title="interface in com.aspose.slides">IXpsOptions</a></code></dd>
</dl>
</li>
</ul>
<a name="setDrawSlidesFrame-boolean-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setDrawSlidesFrame</h4>
<pre>public final&nbsp;void&nbsp;setDrawSlidesFrame(boolean&nbsp;value)</pre>
<div class="block"><p>
  True to draw black frame around each slide.
  Read/write <code>boolean</code>.
  </p><p><hr>
 Default is <b>false</b>.
 </hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IXpsOptions.html#setDrawSlidesFrame-boolean-">setDrawSlidesFrame</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IXpsOptions.html" title="interface in com.aspose.slides">IXpsOptions</a></code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/XamlOptions.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/Zip64Mode.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/XpsOptions.html" target="_top">Frames</a></li>
<li><a href="XpsOptions.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><i>Copyright &copy; 2004-2025 Aspose Pty Ltd. All Rights Reserved.</i></small></p>
</body>
</html>
