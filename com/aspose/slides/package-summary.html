<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_421) on Fri Jul 11 05:32:45 CDT 2025 -->
<title>com.aspose.slides (Aspose.Slides for Java (25.7) API Documentation)</title>
<meta name="date" content="2025-07-11">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="com.aspose.slides (Aspose.Slides for Java (25.7) API Documentation)";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li>Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev&nbsp;Package</li>
<li>Next&nbsp;Package</li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/package-summary.html" target="_top">Frames</a></li>
<li><a href="package-summary.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 title="Package" class="title">Package&nbsp;com.aspose.slides</h1>
</div>
<div class="contentContainer">
<ul class="blockList">
<li class="blockList">
<table class="typeSummary" border="0" cellpadding="3" cellspacing="0" summary="Interface Summary table, listing interfaces, and an explanation">
<caption><span>Interface Summary</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Interface</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/Convert.GetOutPathCallback.html" title="interface in com.aspose.slides">Convert.GetOutPathCallback</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ForEach.ForEachLayoutSlideCallback.html" title="interface in com.aspose.slides">ForEach.ForEachLayoutSlideCallback</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ForEach.ForEachMasterSlideCallback.html" title="interface in com.aspose.slides">ForEach.ForEachMasterSlideCallback</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ForEach.ForEachParagraphCallback.html" title="interface in com.aspose.slides">ForEach.ForEachParagraphCallback</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ForEach.ForEachPortionCallback.html" title="interface in com.aspose.slides">ForEach.ForEachPortionCallback</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ForEach.ForEachShapeCallback.html" title="interface in com.aspose.slides">ForEach.ForEachShapeCallback</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ForEach.ForEachSlideCallback.html" title="interface in com.aspose.slides">ForEach.ForEachSlideCallback</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IAccessiblePVIObject.html" title="interface in com.aspose.slides">IAccessiblePVIObject</a>&lt;T&gt;</td>
<td class="colLast">
<div class="block">
 Represents a type that can return corresponding effective data with the inheritance applied.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IActualLayout.html" title="interface in com.aspose.slides">IActualLayout</a></td>
<td class="colLast">
<div class="block">
 Specifies actual position of a chart element.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IAdjustValue.html" title="interface in com.aspose.slides">IAdjustValue</a></td>
<td class="colLast">
<div class="block">
 Represents a geometry shape's adjustment value.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IAdjustValueCollection.html" title="interface in com.aspose.slides">IAdjustValueCollection</a></td>
<td class="colLast">
<div class="block">
 Reprasents a collection of shape's adjustments.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IAIConversation.html" title="interface in com.aspose.slides">IAIConversation</a></td>
<td class="colLast">
<div class="block">
 Represents a conversation instance.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IAIWebClient.html" title="interface in com.aspose.slides">IAIWebClient</a></td>
<td class="colLast">
<div class="block">
 AI Web client interface.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IAlphaBiLevel.html" title="interface in com.aspose.slides">IAlphaBiLevel</a></td>
<td class="colLast">
<div class="block">
 Represents an Alpha Bi-Level effect.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IAlphaBiLevelEffectiveData.html" title="interface in com.aspose.slides">IAlphaBiLevelEffectiveData</a></td>
<td class="colLast">
<div class="block">
 Immutable object which represents an Alpha Bi-Level effect.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IAlphaCeiling.html" title="interface in com.aspose.slides">IAlphaCeiling</a></td>
<td class="colLast">
<div class="block">
 Represents an Alpha Ceiling effect.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IAlphaCeilingEffectiveData.html" title="interface in com.aspose.slides">IAlphaCeilingEffectiveData</a></td>
<td class="colLast">
<div class="block">
 Immutable object which represents an Alpha Ceiling effect.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IAlphaFloor.html" title="interface in com.aspose.slides">IAlphaFloor</a></td>
<td class="colLast">
<div class="block">
 Represents an Alpha Floor effect.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IAlphaFloorEffectiveData.html" title="interface in com.aspose.slides">IAlphaFloorEffectiveData</a></td>
<td class="colLast">
<div class="block">
 Immutable object which represents an Alpha Floor effect.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IAlphaInverse.html" title="interface in com.aspose.slides">IAlphaInverse</a></td>
<td class="colLast">
<div class="block">
 Represents an Alpha Inverse effect.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IAlphaInverseEffectiveData.html" title="interface in com.aspose.slides">IAlphaInverseEffectiveData</a></td>
<td class="colLast">
<div class="block">
 Immutable object which represents an Alpha Inverse effect.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IAlphaModulate.html" title="interface in com.aspose.slides">IAlphaModulate</a></td>
<td class="colLast">
<div class="block">
 Represents an Alpha Modulate effect.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IAlphaModulateEffectiveData.html" title="interface in com.aspose.slides">IAlphaModulateEffectiveData</a></td>
<td class="colLast">
<div class="block">
 Immutable object which represents an Alpha Modulate effect.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IAlphaModulateFixed.html" title="interface in com.aspose.slides">IAlphaModulateFixed</a></td>
<td class="colLast">
<div class="block">
 Represents an Alpha Modulate Fixed effect.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IAlphaModulateFixedEffectiveData.html" title="interface in com.aspose.slides">IAlphaModulateFixedEffectiveData</a></td>
<td class="colLast">
<div class="block">
 Immutable object which represents an Alpha Modulate Fixed effect.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IAlphaReplace.html" title="interface in com.aspose.slides">IAlphaReplace</a></td>
<td class="colLast">
<div class="block">
 Represents base IImageTransformOperation interface.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IAlphaReplaceEffectiveData.html" title="interface in com.aspose.slides">IAlphaReplaceEffectiveData</a></td>
<td class="colLast">
<div class="block">
 Immutable object which represents and Alpha Replace effect.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IAnimationTimeLine.html" title="interface in com.aspose.slides">IAnimationTimeLine</a></td>
<td class="colLast">
<div class="block">
 Represents timeline of animation.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IAudio.html" title="interface in com.aspose.slides">IAudio</a></td>
<td class="colLast">
<div class="block">
 Represents an embedded audio file.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IAudioCollection.html" title="interface in com.aspose.slides">IAudioCollection</a></td>
<td class="colLast">
<div class="block">
 Represents a collection of embedded audio files.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IAudioFrame.html" title="interface in com.aspose.slides">IAudioFrame</a></td>
<td class="colLast">
<div class="block">
 Represents an audio clip on a slide.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IAutoShape.html" title="interface in com.aspose.slides">IAutoShape</a></td>
<td class="colLast">
<div class="block">
 Represents an AutoShape.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IAutoShapeLock.html" title="interface in com.aspose.slides">IAutoShapeLock</a></td>
<td class="colLast">
<div class="block">
 Determines which operations are disabled on the parent AutoshapeEx.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IAxesManager.html" title="interface in com.aspose.slides">IAxesManager</a></td>
<td class="colLast">
<div class="block">
 Provides access to chart axes.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IAxis.html" title="interface in com.aspose.slides">IAxis</a></td>
<td class="colLast">
<div class="block">
 Encapsulates the object that represents a chart's axis.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IAxisFormat.html" title="interface in com.aspose.slides">IAxisFormat</a></td>
<td class="colLast">
<div class="block">
 Represents chart format properties.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IBackdrop3DScene.html" title="interface in com.aspose.slides">IBackdrop3DScene</a></td>
<td class="colLast">
<div class="block">
 Defines a plane in which effects, such as glow and shadow, are applied in relation to the shape they are being applied to.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IBackground.html" title="interface in com.aspose.slides">IBackground</a></td>
<td class="colLast">
<div class="block">
 Represents background of a slide.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IBackgroundEffectiveData.html" title="interface in com.aspose.slides">IBackgroundEffectiveData</a></td>
<td class="colLast">
<div class="block">
 Immutable object which contains effective background properties.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IBaseChartValue.html" title="interface in com.aspose.slides">IBaseChartValue</a></td>
<td class="colLast">
<div class="block">
 Represents a value of a chart.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IBaseHandoutNotesSlideHeaderFooterManag.html" title="interface in com.aspose.slides">IBaseHandoutNotesSlideHeaderFooterManag</a></td>
<td class="colLast">
<div class="block">
 Represents manager which holds behavior of the placeholders, including header placeholder for all types handout and notes slides.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IBaseHeaderFooterManager.html" title="interface in com.aspose.slides">IBaseHeaderFooterManager</a></td>
<td class="colLast">
<div class="block">
 Represents manager which holds behavior of the footer, date-time, page number placeholders for all slide types.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IBasePortionFormat.html" title="interface in com.aspose.slides">IBasePortionFormat</a></td>
<td class="colLast">
<div class="block">
 This class contains the text portion formatting properties.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IBasePortionFormatEffectiveData.html" title="interface in com.aspose.slides">IBasePortionFormatEffectiveData</a></td>
<td class="colLast">
<div class="block">
 Base interface for immutable objects which contain effective text portion formatting properties.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IBaseShapeLock.html" title="interface in com.aspose.slides">IBaseShapeLock</a></td>
<td class="colLast">
<div class="block">
 Represents Shape lock (disabled operation).</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IBaseSlide.html" title="interface in com.aspose.slides">IBaseSlide</a></td>
<td class="colLast">
<div class="block">
 Represents common data for all slide types.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IBaseSlideHeaderFooterManager.html" title="interface in com.aspose.slides">IBaseSlideHeaderFooterManager</a></td>
<td class="colLast">
<div class="block">
 Represents manager which holds behavior of the footer, date-time, page number placeholders for all slide types.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IBaseTableFormatEffectiveData.html" title="interface in com.aspose.slides">IBaseTableFormatEffectiveData</a></td>
<td class="colLast">
<div class="block">
 Base interface for immutable objects which contain effective table formatting properties.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IBehavior.html" title="interface in com.aspose.slides">IBehavior</a></td>
<td class="colLast">
<div class="block">
 Represent base class behavior of effect.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IBehaviorCollection.html" title="interface in com.aspose.slides">IBehaviorCollection</a></td>
<td class="colLast">
<div class="block">
 Represents collection of behavior effects.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IBehaviorFactory.html" title="interface in com.aspose.slides">IBehaviorFactory</a></td>
<td class="colLast">
<div class="block">
 Allows to create animation effects</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IBehaviorProperty.html" title="interface in com.aspose.slides">IBehaviorProperty</a></td>
<td class="colLast">
<div class="block">
 Represent property types for animation behavior.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IBehaviorPropertyCollection.html" title="interface in com.aspose.slides">IBehaviorPropertyCollection</a></td>
<td class="colLast">
<div class="block">
 Represents timing properties for the effect behavior.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IBiLevel.html" title="interface in com.aspose.slides">IBiLevel</a></td>
<td class="colLast">
<div class="block">
 Represents base IImageTransformOperation interface.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IBiLevelEffectiveData.html" title="interface in com.aspose.slides">IBiLevelEffectiveData</a></td>
<td class="colLast">
<div class="block">
 Immutable object which represents a Bi-Level (black/white) effect.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IBlobManagementOptions.html" title="interface in com.aspose.slides">IBlobManagementOptions</a></td>
<td class="colLast">
<div class="block">
 A Binary Large Object (BLOB) is a binary data stored as a single entity - i.e.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IBlur.html" title="interface in com.aspose.slides">IBlur</a></td>
<td class="colLast">
<div class="block">
 Represents a Blur effect that is applied to the entire shape, including its fill.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IBlurEffectiveData.html" title="interface in com.aspose.slides">IBlurEffectiveData</a></td>
<td class="colLast">
<div class="block">
 Immutable object which represents a Blur effect that is applied to the entire shape, including its fill.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IBulkTextFormattable.html" title="interface in com.aspose.slides">IBulkTextFormattable</a></td>
<td class="colLast">
<div class="block">
 Represents an object with possibility of bulk setting child text elements' formats.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IBulletFormat.html" title="interface in com.aspose.slides">IBulletFormat</a></td>
<td class="colLast">
<div class="block">
 Represents paragraph bullet formatting properties.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IBulletFormatEffectiveData.html" title="interface in com.aspose.slides">IBulletFormatEffectiveData</a></td>
<td class="colLast">
<div class="block">
 Immutable object which contains effective paragraph bullet formatting properties.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ICamera.html" title="interface in com.aspose.slides">ICamera</a></td>
<td class="colLast">
<div class="block">
 Represents Camera.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ICameraEffectiveData.html" title="interface in com.aspose.slides">ICameraEffectiveData</a></td>
<td class="colLast">
<div class="block">
 Immutable object which contains effective camera properties.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ICaptions.html" title="interface in com.aspose.slides">ICaptions</a></td>
<td class="colLast">
<div class="block">
 Represents the WebVTT closed captions.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ICaptionsCollection.html" title="interface in com.aspose.slides">ICaptionsCollection</a></td>
<td class="colLast">
<div class="block">
 Represents a collection of the closed captions.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ICell.html" title="interface in com.aspose.slides">ICell</a></td>
<td class="colLast">
<div class="block">
 Represents a cell in a table.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ICellCollection.html" title="interface in com.aspose.slides">ICellCollection</a></td>
<td class="colLast">
<div class="block">
 Represents a collection of cells.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ICellFormat.html" title="interface in com.aspose.slides">ICellFormat</a></td>
<td class="colLast">
<div class="block">
 Represents format of a table cell.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ICellFormatEffectiveData.html" title="interface in com.aspose.slides">ICellFormatEffectiveData</a></td>
<td class="colLast">
<div class="block">
 Immutable object which contains effective table cell formatting properties.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IChart.html" title="interface in com.aspose.slides">IChart</a></td>
<td class="colLast">
<div class="block">
 Represents an graphic chart on a slide.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IChartCategory.html" title="interface in com.aspose.slides">IChartCategory</a></td>
<td class="colLast">
<div class="block">
 Represents chart categories.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IChartCategoryCollection.html" title="interface in com.aspose.slides">IChartCategoryCollection</a></td>
<td class="colLast">
<div class="block">
 Represents collection of <a href="../../../com/aspose/slides/IChartCategory.html" title="interface in com.aspose.slides"><code>IChartCategory</code></a></div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IChartCategoryLevelsManager.html" title="interface in com.aspose.slides">IChartCategoryLevelsManager</a></td>
<td class="colLast">
<div class="block">
 Managed container of the values of the chart category levels.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IChartCellCollection.html" title="interface in com.aspose.slides">IChartCellCollection</a></td>
<td class="colLast">
<div class="block">
 Represents collection of a cells with data.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IChartComponent.html" title="interface in com.aspose.slides">IChartComponent</a></td>
<td class="colLast">
<div class="block">
 Represents a component of a chart.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IChartData.html" title="interface in com.aspose.slides">IChartData</a></td>
<td class="colLast">
<div class="block">
 Represents data used for a chart plotting.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IChartDataCell.html" title="interface in com.aspose.slides">IChartDataCell</a></td>
<td class="colLast">
<div class="block">
 Represents cell for chart data.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IChartDataPoint.html" title="interface in com.aspose.slides">IChartDataPoint</a></td>
<td class="colLast">
<div class="block">
 Represents series data point.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IChartDataPointCollection.html" title="interface in com.aspose.slides">IChartDataPointCollection</a></td>
<td class="colLast">
<div class="block">
 Represents collection of a series data point.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IChartDataPointLevel.html" title="interface in com.aspose.slides">IChartDataPointLevel</a></td>
<td class="colLast">
<div class="block">
 Represents data point level.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IChartDataPointLevelsManager.html" title="interface in com.aspose.slides">IChartDataPointLevelsManager</a></td>
<td class="colLast">
<div class="block">
 Container of data point levels.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IChartDataWorkbook.html" title="interface in com.aspose.slides">IChartDataWorkbook</a></td>
<td class="colLast">
<div class="block">
 Provides access to embedded Excel workbook</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IChartDataWorksheet.html" title="interface in com.aspose.slides">IChartDataWorksheet</a></td>
<td class="colLast">
<div class="block">
 Represents worksheet associated with <a href="../../../com/aspose/slides/IChartDataCell.html" title="interface in com.aspose.slides"><code>IChartDataCell</code></a></div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IChartDataWorksheetCollection.html" title="interface in com.aspose.slides">IChartDataWorksheetCollection</a></td>
<td class="colLast">
<div class="block">
 Represents the collection of worksheets of chart data workbook.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IChartLinesFormat.html" title="interface in com.aspose.slides">IChartLinesFormat</a></td>
<td class="colLast">
<div class="block">
 Represents gridlines format properties.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IChartParagraphFormat.html" title="interface in com.aspose.slides">IChartParagraphFormat</a></td>
<td class="colLast">
<div class="block">
 Represents a paragraph formatting properties of a chart.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IChartPlotArea.html" title="interface in com.aspose.slides">IChartPlotArea</a></td>
<td class="colLast">
<div class="block">
 Represents chart title properties.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IChartPortionFormat.html" title="interface in com.aspose.slides">IChartPortionFormat</a></td>
<td class="colLast">
<div class="block">
 Represents the chart portion formatting properties used in charts.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IChartSeries.html" title="interface in com.aspose.slides">IChartSeries</a></td>
<td class="colLast">
<div class="block">
 Represents a chart series.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IChartSeriesCollection.html" title="interface in com.aspose.slides">IChartSeriesCollection</a></td>
<td class="colLast">
<div class="block">
 Represents collection of  <a href="../../../com/aspose/slides/IChartSeries.html" title="interface in com.aspose.slides"><code>IChartSeries</code></a></div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IChartSeriesGroup.html" title="interface in com.aspose.slides">IChartSeriesGroup</a></td>
<td class="colLast">
<div class="block">
 Represents group of series.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IChartSeriesGroupCollection.html" title="interface in com.aspose.slides">IChartSeriesGroupCollection</a></td>
<td class="colLast">
<div class="block">
 Represents the collection of groups of combinable series.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IChartSeriesReadonlyCollection.html" title="interface in com.aspose.slides">IChartSeriesReadonlyCollection</a></td>
<td class="colLast">
<div class="block">
 Represents a readonly collection of  <a href="../../../com/aspose/slides/IChartSeries.html" title="interface in com.aspose.slides"><code>IChartSeries</code></a></div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IChartTextBlockFormat.html" title="interface in com.aspose.slides">IChartTextBlockFormat</a></td>
<td class="colLast">
<div class="block">
 Represents formatting properties for chart text elements.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IChartTextFormat.html" title="interface in com.aspose.slides">IChartTextFormat</a></td>
<td class="colLast">
<div class="block">
 Chart operate with restricted set of text format properties.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IChartTitle.html" title="interface in com.aspose.slides">IChartTitle</a></td>
<td class="colLast">
<div class="block">
 Represents chart title properties.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IChartWall.html" title="interface in com.aspose.slides">IChartWall</a></td>
<td class="colLast">
<div class="block">
 Represents walls on 3d charts.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IColorChange.html" title="interface in com.aspose.slides">IColorChange</a></td>
<td class="colLast">
<div class="block">
 Represents a Color Change effect.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IColorChangeEffectiveData.html" title="interface in com.aspose.slides">IColorChangeEffectiveData</a></td>
<td class="colLast">
<div class="block">
 Immutable object which represents a Color Change effect.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IColorEffect.html" title="interface in com.aspose.slides">IColorEffect</a></td>
<td class="colLast">
<div class="block">
 Represents a color effect for an animation behavior.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IColorFormat.html" title="interface in com.aspose.slides">IColorFormat</a></td>
<td class="colLast">
<div class="block">
 Represents a color used in a presentation.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IColorOffset.html" title="interface in com.aspose.slides">IColorOffset</a></td>
<td class="colLast">
<div class="block">
 Represent color offset.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IColorOperation.html" title="interface in com.aspose.slides">IColorOperation</a></td>
<td class="colLast">
<div class="block">
 Represents different color operations used for color transformations.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IColorOperationCollection.html" title="interface in com.aspose.slides">IColorOperationCollection</a></td>
<td class="colLast">
<div class="block">
 Represents a collection of color transform operations.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IColorReplace.html" title="interface in com.aspose.slides">IColorReplace</a></td>
<td class="colLast">
<div class="block">
 Represents a Color Replacement effect.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IColorReplaceEffectiveData.html" title="interface in com.aspose.slides">IColorReplaceEffectiveData</a></td>
<td class="colLast">
<div class="block">
 Immutable object which represents a Color Replacement effect.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IColorScheme.html" title="interface in com.aspose.slides">IColorScheme</a></td>
<td class="colLast">
<div class="block">
 Stores theme-defined colors.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IColorSchemeEffectiveData.html" title="interface in com.aspose.slides">IColorSchemeEffectiveData</a></td>
<td class="colLast">
<div class="block">
 Immutable object which contains effective color scheme properties.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IColumn.html" title="interface in com.aspose.slides">IColumn</a></td>
<td class="colLast">
<div class="block">
 Represents a column in a table.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IColumnCollection.html" title="interface in com.aspose.slides">IColumnCollection</a></td>
<td class="colLast">
<div class="block">
 Represents collection of columns in a table.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IColumnFormat.html" title="interface in com.aspose.slides">IColumnFormat</a></td>
<td class="colLast">
<div class="block">
 Represents format of a table column.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IColumnFormatEffectiveData.html" title="interface in com.aspose.slides">IColumnFormatEffectiveData</a></td>
<td class="colLast">
<div class="block">
 Immutable object which contains effective table column formatting properties.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ICommandEffect.html" title="interface in com.aspose.slides">ICommandEffect</a></td>
<td class="colLast">
<div class="block">
 Represents a command effect for an animation behavior.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IComment.html" title="interface in com.aspose.slides">IComment</a></td>
<td class="colLast">
<div class="block">
 Represents a comment on a slide.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ICommentAuthor.html" title="interface in com.aspose.slides">ICommentAuthor</a></td>
<td class="colLast">
<div class="block">
 Represents an author of comments.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ICommentAuthorCollection.html" title="interface in com.aspose.slides">ICommentAuthorCollection</a></td>
<td class="colLast">
<div class="block">
 Represents a collection of comment authors.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ICommentCollection.html" title="interface in com.aspose.slides">ICommentCollection</a></td>
<td class="colLast">
<div class="block">
 Represents a collection of comments of one author.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ICommonSlideViewProperties.html" title="interface in com.aspose.slides">ICommonSlideViewProperties</a></td>
<td class="colLast">
<div class="block">
 Represents common slide view properties.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IConnector.html" title="interface in com.aspose.slides">IConnector</a></td>
<td class="colLast">
<div class="block">
 Represents a connector.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IConnectorLock.html" title="interface in com.aspose.slides">IConnectorLock</a></td>
<td class="colLast">
<div class="block">
 Determines which operations are disabled on the parent Connector.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IControl.html" title="interface in com.aspose.slides">IControl</a></td>
<td class="colLast">
<div class="block">
 Represents an ActiveX control.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IControlCollection.html" title="interface in com.aspose.slides">IControlCollection</a></td>
<td class="colLast">
<div class="block">
 A collection of ActiveX controls.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IControlPropertiesCollection.html" title="interface in com.aspose.slides">IControlPropertiesCollection</a></td>
<td class="colLast">
<div class="block">
 A collection of ActiveX controls.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ICornerDirectionTransition.html" title="interface in com.aspose.slides">ICornerDirectionTransition</a></td>
<td class="colLast">
<div class="block">
 Corner direction slide transition effect.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ICustomData.html" title="interface in com.aspose.slides">ICustomData</a></td>
<td class="colLast">
<div class="block">
 Represents container for custom data.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ICustomXmlPart.html" title="interface in com.aspose.slides">ICustomXmlPart</a></td>
<td class="colLast">
<div class="block">
 Represents custom xml part.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ICustomXmlPartCollection.html" title="interface in com.aspose.slides">ICustomXmlPartCollection</a></td>
<td class="colLast">
<div class="block">
 Represents collection of custom xml parts.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IDataLabel.html" title="interface in com.aspose.slides">IDataLabel</a></td>
<td class="colLast">
<div class="block">
 Represents a series labels.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IDataLabelCollection.html" title="interface in com.aspose.slides">IDataLabelCollection</a></td>
<td class="colLast">
<div class="block">
 Represents a series labels.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IDataLabelFormat.html" title="interface in com.aspose.slides">IDataLabelFormat</a></td>
<td class="colLast">
<div class="block">
 Represents formatting options for DataLabel.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IDataSourceTypeForErrorBarsCustomValues.html" title="interface in com.aspose.slides">IDataSourceTypeForErrorBarsCustomValues</a></td>
<td class="colLast">
<div class="block">
  Specifies types of values in ChartDataPoint.ErrorBarsCustomValues properties list</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IDataTable.html" title="interface in com.aspose.slides">IDataTable</a></td>
<td class="colLast">
<div class="block">
 Represents data table properties.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IDigitalSignature.html" title="interface in com.aspose.slides">IDigitalSignature</a></td>
<td class="colLast">
<div class="block">
 Digital signature in signed file.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IDigitalSignatureCollection.html" title="interface in com.aspose.slides">IDigitalSignatureCollection</a></td>
<td class="colLast">
<div class="block">
 Represents a collection of digital signatures attached to a document.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IDocumentProperties.html" title="interface in com.aspose.slides">IDocumentProperties</a></td>
<td class="colLast">
<div class="block">
 Represents properties of a presentation.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IDoubleChartValue.html" title="interface in com.aspose.slides">IDoubleChartValue</a></td>
<td class="colLast">
<div class="block">
 Represent double value which can be stored in pptx presentation document in two ways:
 1) in cell/cells of workbook related to chart;
 2) as literal value.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IDrawingGuide.html" title="interface in com.aspose.slides">IDrawingGuide</a></td>
<td class="colLast">
<div class="block">
 Represents an adjustable drawing guide.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IDrawingGuidesCollection.html" title="interface in com.aspose.slides">IDrawingGuidesCollection</a></td>
<td class="colLast">
<div class="block">
 Represents a collection of the adjustable drawing guides.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IDuotone.html" title="interface in com.aspose.slides">IDuotone</a></td>
<td class="colLast">
<div class="block">
 Represents a Duotone effect.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IDuotoneEffectiveData.html" title="interface in com.aspose.slides">IDuotoneEffectiveData</a></td>
<td class="colLast">
<div class="block">
 Immutable object which represents a Duotone effect.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IEffect.html" title="interface in com.aspose.slides">IEffect</a></td>
<td class="colLast">
<div class="block">
 Represents animation effect.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IEffectEffectiveData.html" title="interface in com.aspose.slides">IEffectEffectiveData</a></td>
<td class="colLast">
<div class="block">
 Base class for immutable objects, which represent effect.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IEffectFactory.html" title="interface in com.aspose.slides">IEffectFactory</a></td>
<td class="colLast">
<div class="block">
 Allows to create effects' instances</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IEffectFormat.html" title="interface in com.aspose.slides">IEffectFormat</a></td>
<td class="colLast">
<div class="block">
 Represents effect properties of shape.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IEffectFormatEffectiveData.html" title="interface in com.aspose.slides">IEffectFormatEffectiveData</a></td>
<td class="colLast">
<div class="block">
 Imutable object which contains effective effect formatting properties.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IEffectParamSource.html" title="interface in com.aspose.slides">IEffectParamSource</a></td>
<td class="colLast">
<div class="block">
 Auxiliary effect parameters interface.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IEffectStyle.html" title="interface in com.aspose.slides">IEffectStyle</a></td>
<td class="colLast">
<div class="block">
 Represents an effect style.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IEffectStyleCollection.html" title="interface in com.aspose.slides">IEffectStyleCollection</a></td>
<td class="colLast">
<div class="block">
 Represents a collection of effect styles.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IEffectStyleCollectionEffectiveData.html" title="interface in com.aspose.slides">IEffectStyleCollectionEffectiveData</a></td>
<td class="colLast">
<div class="block">
 Immutable object that represents a readonly collection of effective effect styles.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IEffectStyleEffectiveData.html" title="interface in com.aspose.slides">IEffectStyleEffectiveData</a></td>
<td class="colLast">
<div class="block">
 Immutable object which contains effective effect style properties.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IEightDirectionTransition.html" title="interface in com.aspose.slides">IEightDirectionTransition</a></td>
<td class="colLast">
<div class="block">
 Eight direction slide transition effect.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IEmbeddedEotFontsHtmlController.html" title="interface in com.aspose.slides">IEmbeddedEotFontsHtmlController</a></td>
<td class="colLast">
<div class="block">
 Embedded Eot fonts HTML controller.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IEmbeddedWoffFontsHtmlController.html" title="interface in com.aspose.slides">IEmbeddedWoffFontsHtmlController</a></td>
<td class="colLast">
<div class="block">
 Embedded woff fonts html controller.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IEmptyTransition.html" title="interface in com.aspose.slides">IEmptyTransition</a></td>
<td class="colLast">
<div class="block">
 Empty slide transition effect.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IErrorBarsCustomValues.html" title="interface in com.aspose.slides">IErrorBarsCustomValues</a></td>
<td class="colLast">
<div class="block">
 Specifies the errors bar values.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IErrorBarsFormat.html" title="interface in com.aspose.slides">IErrorBarsFormat</a></td>
<td class="colLast">
<div class="block">
 Represents error bars of chart series.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IExternalResourceResolver.html" title="interface in com.aspose.slides">IExternalResourceResolver</a></td>
<td class="colLast">
<div class="block">
 Callback interface used to resolve external resources during Html, Svg documents import.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IExtraColorScheme.html" title="interface in com.aspose.slides">IExtraColorScheme</a></td>
<td class="colLast">
<div class="block">
 Represents an additional color scheme which can be assigned to a slide.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IExtraColorSchemeCollection.html" title="interface in com.aspose.slides">IExtraColorSchemeCollection</a></td>
<td class="colLast">
<div class="block">
 Represents a collection of additional color schemes.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IField.html" title="interface in com.aspose.slides">IField</a></td>
<td class="colLast">
<div class="block">
 Represents a field.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IFieldType.html" title="interface in com.aspose.slides">IFieldType</a></td>
<td class="colLast">
<div class="block">
 Represents a type of field.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IFillFormat.html" title="interface in com.aspose.slides">IFillFormat</a></td>
<td class="colLast">
<div class="block">
 Represents a fill formatting options.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IFillFormatCollection.html" title="interface in com.aspose.slides">IFillFormatCollection</a></td>
<td class="colLast">
<div class="block">
 Represents the collection of fill styles.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IFillFormatCollectionEffectiveData.html" title="interface in com.aspose.slides">IFillFormatCollectionEffectiveData</a></td>
<td class="colLast">
<div class="block">
 Immutable object that represents a readonly collection of effective fill formats.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IFillFormatEffectiveData.html" title="interface in com.aspose.slides">IFillFormatEffectiveData</a></td>
<td class="colLast">
<div class="block">
 Immutable object which contains effective fill formatting properties.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IFillOverlay.html" title="interface in com.aspose.slides">IFillOverlay</a></td>
<td class="colLast">
<div class="block">
 Represents a Fill Overlay effect.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IFillOverlayEffectiveData.html" title="interface in com.aspose.slides">IFillOverlayEffectiveData</a></td>
<td class="colLast">
<div class="block">
 Immutable object which represents a Fill Overlay effect.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IFillParamSource.html" title="interface in com.aspose.slides">IFillParamSource</a></td>
<td class="colLast">
<div class="block">
 Auxiliary fill parameters interface.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IFilterEffect.html" title="interface in com.aspose.slides">IFilterEffect</a></td>
<td class="colLast">
<div class="block">
 Represent filter effect of behavior.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IFindResultCallback.html" title="interface in com.aspose.slides">IFindResultCallback</a></td>
<td class="colLast">
<div class="block">
 Callback interface used to getting search text result.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IFlyThroughTransition.html" title="interface in com.aspose.slides">IFlyThroughTransition</a></td>
<td class="colLast">
<div class="block">
 Fly-through slide transition effect.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IFontData.html" title="interface in com.aspose.slides">IFontData</a></td>
<td class="colLast">
<div class="block">
 Represents a font definition.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IFontDataFactory.html" title="interface in com.aspose.slides">IFontDataFactory</a></td>
<td class="colLast">
<div class="block">
 FontData factory interface</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IFontFallBackRule.html" title="interface in com.aspose.slides">IFontFallBackRule</a></td>
<td class="colLast">
<div class="block">
 Represents font fallback rule</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IFontFallBackRulesCollection.html" title="interface in com.aspose.slides">IFontFallBackRulesCollection</a></td>
<td class="colLast">
<div class="block">
 Represents a collection of FontFallBack rules, defined by user</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IFonts.html" title="interface in com.aspose.slides">IFonts</a></td>
<td class="colLast">
<div class="block">
 Represents fonts collection.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IFontScheme.html" title="interface in com.aspose.slides">IFontScheme</a></td>
<td class="colLast">
<div class="block">
 Stores theme-defined fonts.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IFontSchemeEffectiveData.html" title="interface in com.aspose.slides">IFontSchemeEffectiveData</a></td>
<td class="colLast">
<div class="block">
 Immutable object which contains effective font scheme properties.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IFontsEffectiveData.html" title="interface in com.aspose.slides">IFontsEffectiveData</a></td>
<td class="colLast">
<div class="block">
 Immutable object which contains effective fonts set.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IFontsLoader.html" title="interface in com.aspose.slides">IFontsLoader</a></td>
<td class="colLast">
<div class="block">
 Class for loading custom fonts defined by user.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IFontsManager.html" title="interface in com.aspose.slides">IFontsManager</a></td>
<td class="colLast">
<div class="block">
 Manages fonts across the presentation.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IFontSources.html" title="interface in com.aspose.slides">IFontSources</a></td>
<td class="colLast">
<div class="block">
 Provides file and memory sources for external fonts.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IFontSubstRule.html" title="interface in com.aspose.slides">IFontSubstRule</a></td>
<td class="colLast">
<div class="block">
 Represents font subtituition information</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IFontSubstRuleCollection.html" title="interface in com.aspose.slides">IFontSubstRuleCollection</a></td>
<td class="colLast">
<div class="block">
 Represents collection of fonts substitution.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IFormat.html" title="interface in com.aspose.slides">IFormat</a></td>
<td class="colLast">
<div class="block">
 Represents chart format properties.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IFormatFactory.html" title="interface in com.aspose.slides">IFormatFactory</a></td>
<td class="colLast">
<div class="block">
 Allows to create formats via COM interface.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IFormatScheme.html" title="interface in com.aspose.slides">IFormatScheme</a></td>
<td class="colLast">
<div class="block">
 Stores theme-defined formats for the shapes.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IFormatSchemeEffectiveData.html" title="interface in com.aspose.slides">IFormatSchemeEffectiveData</a></td>
<td class="colLast">
<div class="block">
 Immutable object which contains effective format scheme properties.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IFormattedTextContainer.html" title="interface in com.aspose.slides">IFormattedTextContainer</a></td>
<td class="colLast">
<div class="block">
 Represents chart text format.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IGenericCloneable.html" title="interface in com.aspose.slides">IGenericCloneable</a>&lt;T&gt;</td>
<td class="colLast">
<div class="block">
 Represents generic version of ICloneable</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IGenericCollection.html" title="interface in com.aspose.slides">IGenericCollection</a>&lt;T&gt;</td>
<td class="colLast">
<div class="block">
 Auxiliary generic collection interface.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IGeometryPath.html" title="interface in com.aspose.slides">IGeometryPath</a></td>
<td class="colLast">
<div class="block">
 Represents geometry path of GeometryShape</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IGeometryShape.html" title="interface in com.aspose.slides">IGeometryShape</a></td>
<td class="colLast">
<div class="block">
 Represents the parent class for all geometric shapes.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IGifOptions.html" title="interface in com.aspose.slides">IGifOptions</a></td>
<td class="colLast">
<div class="block">
 Represents GIF exporting options.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IGlitterTransition.html" title="interface in com.aspose.slides">IGlitterTransition</a></td>
<td class="colLast">
<div class="block">
 Glitter slide transition effect.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IGlobalLayoutSlideCollection.html" title="interface in com.aspose.slides">IGlobalLayoutSlideCollection</a></td>
<td class="colLast">
<div class="block">
 Represents a collection of all layout slides in presentation.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IGlow.html" title="interface in com.aspose.slides">IGlow</a></td>
<td class="colLast">
<div class="block">
 Represents a Glow effect, in which a color blurred outline 
 is added outside the edges of the object.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IGlowEffectiveData.html" title="interface in com.aspose.slides">IGlowEffectiveData</a></td>
<td class="colLast">
<div class="block">
 Immutable object which represents a Glow effect, in which a color blurred outline 
 is added outside the edges of the object.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IGradientFormat.html" title="interface in com.aspose.slides">IGradientFormat</a></td>
<td class="colLast">
<div class="block">
 Represent a gradient format.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IGradientFormatEffectiveData.html" title="interface in com.aspose.slides">IGradientFormatEffectiveData</a></td>
<td class="colLast">
<div class="block">
 Imutable object which contains effective gradient filling properties.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IGradientStop.html" title="interface in com.aspose.slides">IGradientStop</a></td>
<td class="colLast">
<div class="block">
 Represents a gradient format.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IGradientStopCollection.html" title="interface in com.aspose.slides">IGradientStopCollection</a></td>
<td class="colLast">
<div class="block">
 Represnts a collection of gradient stops.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IGradientStopCollectionEffectiveData.html" title="interface in com.aspose.slides">IGradientStopCollectionEffectiveData</a></td>
<td class="colLast">
<div class="block">
 Represents a collection of GradientStopData objects.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IGradientStopEffectiveData.html" title="interface in com.aspose.slides">IGradientStopEffectiveData</a></td>
<td class="colLast">
<div class="block">
 immutable object which represents a gradient stop.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IGraphicalObject.html" title="interface in com.aspose.slides">IGraphicalObject</a></td>
<td class="colLast">
<div class="block">
 Represents abstract graphical object.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IGraphicalObjectLock.html" title="interface in com.aspose.slides">IGraphicalObjectLock</a></td>
<td class="colLast">
<div class="block">
 Determines which operations are disabled on the parent GraphicalObject.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IGrayScale.html" title="interface in com.aspose.slides">IGrayScale</a></td>
<td class="colLast">
<div class="block">
 Represents IImageTransformOperation interface.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IGrayScaleEffectiveData.html" title="interface in com.aspose.slides">IGrayScaleEffectiveData</a></td>
<td class="colLast">
<div class="block">
 Immutable object which representsepresents a Gray Scale effect.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IGroupShape.html" title="interface in com.aspose.slides">IGroupShape</a></td>
<td class="colLast">
<div class="block">
 Represents a group of shapes on a slide.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IGroupShapeLock.html" title="interface in com.aspose.slides">IGroupShapeLock</a></td>
<td class="colLast">
<div class="block">
 Determines which operations are disabled on the parent GroupShape.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IHeadingPair.html" title="interface in com.aspose.slides">IHeadingPair</a></td>
<td class="colLast">
<div class="block">
 Represents a 'Heading pair' property of the document.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IHSL.html" title="interface in com.aspose.slides">IHSL</a></td>
<td class="colLast">
<div class="block">
 Represents a Hue/Saturation/Luminance effect.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IHSLEffectiveData.html" title="interface in com.aspose.slides">IHSLEffectiveData</a></td>
<td class="colLast">
<div class="block">
 Represents a Hue/Saturation/Luminance effect.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IHtml5Options.html" title="interface in com.aspose.slides">IHtml5Options</a></td>
<td class="colLast">
<div class="block">
 Represents a HTML5 exporting options.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IHtmlExternalResolver.html" title="interface in com.aspose.slides">IHtmlExternalResolver</a></td>
<td class="colLast">Deprecated
<div class="block"><span class="deprecationComment">Obsolete.</span></div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IHtmlFormatter.html" title="interface in com.aspose.slides">IHtmlFormatter</a></td>
<td class="colLast">
<div class="block">
 Represents HTML file template.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IHtmlFormattingController.html" title="interface in com.aspose.slides">IHtmlFormattingController</a></td>
<td class="colLast">
<div class="block">
 Controls a html file generation.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IHtmlGenerator.html" title="interface in com.aspose.slides">IHtmlGenerator</a></td>
<td class="colLast">
<div class="block">
 Html generator.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IHtmlOptions.html" title="interface in com.aspose.slides">IHtmlOptions</a></td>
<td class="colLast">
<div class="block">
 Represents a HTML exporting options.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IHyperlink.html" title="interface in com.aspose.slides">IHyperlink</a></td>
<td class="colLast">
<div class="block">
 Represents a hyperlink.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IHyperlinkContainer.html" title="interface in com.aspose.slides">IHyperlinkContainer</a></td>
<td class="colLast">
<div class="block">
 Represents hyperlink object.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IHyperlinkManager.html" title="interface in com.aspose.slides">IHyperlinkManager</a></td>
<td class="colLast">
<div class="block">
 Provide hyperlinks management (adding, removing).</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IHyperlinkQueries.html" title="interface in com.aspose.slides">IHyperlinkQueries</a></td>
<td class="colLast">
<div class="block">
 Provide easy access to contained hyperlinks.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IImage.html" title="interface in com.aspose.slides">IImage</a></td>
<td class="colLast">
<div class="block">
 Represents a raster or vector image.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IImageCollection.html" title="interface in com.aspose.slides">IImageCollection</a></td>
<td class="colLast">
<div class="block">
 Represents collection of PPImage.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IImageTransformOCollectionEffectiveData.html" title="interface in com.aspose.slides">IImageTransformOCollectionEffectiveData</a></td>
<td class="colLast">
<div class="block">
 Immutable object that represents a readonly collection of effective image transform effects.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IImageTransformOperation.html" title="interface in com.aspose.slides">IImageTransformOperation</a></td>
<td class="colLast">
<div class="block">
 Represents abstract image transformation effect.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IImageTransformOperationCollection.html" title="interface in com.aspose.slides">IImageTransformOperationCollection</a></td>
<td class="colLast">
<div class="block">
 Represents a collection of effects apllied to an image.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IImageTransformOperationFactory.html" title="interface in com.aspose.slides">IImageTransformOperationFactory</a></td>
<td class="colLast">
<div class="block">
 Allows to create image effects' instances</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IInk.html" title="interface in com.aspose.slides">IInk</a></td>
<td class="colLast">
<div class="block">
 Represents an ink object on a slide.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IInkActions.html" title="interface in com.aspose.slides">IInkActions</a></td>
<td class="colLast">
<div class="block">
 Represents an ink object on a slide.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IInkBrush.html" title="interface in com.aspose.slides">IInkBrush</a></td>
<td class="colLast">
<div class="block">
 Represents trace brush.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IInkOptions.html" title="interface in com.aspose.slides">IInkOptions</a></td>
<td class="colLast">
<div class="block">
 Provides options that control the look of Ink objects in exported document.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IInkTrace.html" title="interface in com.aspose.slides">IInkTrace</a></td>
<td class="colLast">
<div class="block">
 Represents handwritten line in an Ink object.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IInnerShadow.html" title="interface in com.aspose.slides">IInnerShadow</a></td>
<td class="colLast">
<div class="block">
 Represents a inner shadow effect.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IInnerShadowEffectiveData.html" title="interface in com.aspose.slides">IInnerShadowEffectiveData</a></td>
<td class="colLast">
<div class="block">
 Immutable object which represents a inner shadow effect.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IInOutTransition.html" title="interface in com.aspose.slides">IInOutTransition</a></td>
<td class="colLast">
<div class="block">
 In-Out slide transition effect.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IInterruptionToken.html" title="interface in com.aspose.slides">IInterruptionToken</a></td>
<td class="colLast">
<div class="block">
 This class represents the token to use for signaling long running tasks whether the interruption was requested.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IInterruptionTokenSource.html" title="interface in com.aspose.slides">IInterruptionTokenSource</a></td>
<td class="colLast">
<div class="block">
 Represents the source of <a href="../../../com/aspose/slides/IInterruptionToken.html" title="interface in com.aspose.slides"><code>IInterruptionToken</code></a>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IKnownIssueWarningInfo.html" title="interface in com.aspose.slides">IKnownIssueWarningInfo</a></td>
<td class="colLast">
<div class="block">
 Represents a warning about known issue which won't be fixed in the near future.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ILayoutable.html" title="interface in com.aspose.slides">ILayoutable</a></td>
<td class="colLast">
<div class="block">
 Specifies the exact position of a chart element.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ILayoutPlaceholderManager.html" title="interface in com.aspose.slides">ILayoutPlaceholderManager</a></td>
<td class="colLast">
<div class="block">
 Represents manager that allows you to add placeholders to the layout slide.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ILayoutSlide.html" title="interface in com.aspose.slides">ILayoutSlide</a></td>
<td class="colLast">
<div class="block">
 Represents a layout slide.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ILayoutSlideCollection.html" title="interface in com.aspose.slides">ILayoutSlideCollection</a></td>
<td class="colLast">
<div class="block">
 Represents a base class for collection of a layout slides.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ILayoutSlideHeaderFooterManager.html" title="interface in com.aspose.slides">ILayoutSlideHeaderFooterManager</a></td>
<td class="colLast">
<div class="block">
 Represents manager which holds behavior of the layout slide footer, date-time, page number placeholders and all child placeholders.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ILeftRightDirectionTransition.html" title="interface in com.aspose.slides">ILeftRightDirectionTransition</a></td>
<td class="colLast">
<div class="block">
 Left-right direction slide transition effect.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ILegacyDiagram.html" title="interface in com.aspose.slides">ILegacyDiagram</a></td>
<td class="colLast">
<div class="block">
 Represents a legacy diagram object</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ILegend.html" title="interface in com.aspose.slides">ILegend</a></td>
<td class="colLast">
<div class="block">
 Represents chart's legend properties.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ILegendEntryCollection.html" title="interface in com.aspose.slides">ILegendEntryCollection</a></td>
<td class="colLast">
<div class="block">
 Represents legends collection.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ILegendEntryProperties.html" title="interface in com.aspose.slides">ILegendEntryProperties</a></td>
<td class="colLast">
<div class="block">
 Represents legend properties of a chart.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ILicense.html" title="interface in com.aspose.slides">ILicense</a></td>
<td class="colLast">
<div class="block">
 Provides methods to license the component.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ILightRig.html" title="interface in com.aspose.slides">ILightRig</a></td>
<td class="colLast">
<div class="block">
 Represents LightRig.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ILightRigEffectiveData.html" title="interface in com.aspose.slides">ILightRigEffectiveData</a></td>
<td class="colLast">
<div class="block">
 Immutable object which contains effective light rig properties.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ILineFillFormat.html" title="interface in com.aspose.slides">ILineFillFormat</a></td>
<td class="colLast">
<div class="block">
 Represents properties for lines filling.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ILineFillFormatEffectiveData.html" title="interface in com.aspose.slides">ILineFillFormatEffectiveData</a></td>
<td class="colLast">
<div class="block">
 Immutable object which contains effective line filling properties.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ILineFormat.html" title="interface in com.aspose.slides">ILineFormat</a></td>
<td class="colLast">
<div class="block">
 Represents format of a line.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ILineFormatCollection.html" title="interface in com.aspose.slides">ILineFormatCollection</a></td>
<td class="colLast">
<div class="block">
 Represents the collection of line styles.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ILineFormatCollectionEffectiveData.html" title="interface in com.aspose.slides">ILineFormatCollectionEffectiveData</a></td>
<td class="colLast">
<div class="block">
 Immutable object that represents a readonly collection of effective line formats.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ILineFormatEffectiveData.html" title="interface in com.aspose.slides">ILineFormatEffectiveData</a></td>
<td class="colLast">
<div class="block">
 Immutable object which contains effective line formatting properties.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ILineParamSource.html" title="interface in com.aspose.slides">ILineParamSource</a></td>
<td class="colLast">
<div class="block">
 Auxiliary line parameters interface.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ILinkEmbedController.html" title="interface in com.aspose.slides">ILinkEmbedController</a></td>
<td class="colLast">
<div class="block">
 Callback interface used to determine how object should be processed during saving.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ILoadOptions.html" title="interface in com.aspose.slides">ILoadOptions</a></td>
<td class="colLast">
<div class="block">
 Allows to specify additional options (such as format or default font) when loading a presentation.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ILuminance.html" title="interface in com.aspose.slides">ILuminance</a></td>
<td class="colLast">
<div class="block">
 Represents a Luminance effect.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ILuminanceEffectiveData.html" title="interface in com.aspose.slides">ILuminanceEffectiveData</a></td>
<td class="colLast">
<div class="block">
 Represents a Luminance effect.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IMarker.html" title="interface in com.aspose.slides">IMarker</a></td>
<td class="colLast">
<div class="block">
 Represents marker of a chert.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IMasterHandoutSlide.html" title="interface in com.aspose.slides">IMasterHandoutSlide</a></td>
<td class="colLast">
<div class="block">
 Represents master slide for handouts.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IMasterHandoutSlideHeaderFooterManager.html" title="interface in com.aspose.slides">IMasterHandoutSlideHeaderFooterManager</a></td>
<td class="colLast">
<div class="block">
 Represents manager which holds behavior of the master handout slide placeholders, including header placeholder.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IMasterHandoutSlideManager.html" title="interface in com.aspose.slides">IMasterHandoutSlideManager</a></td>
<td class="colLast">
<div class="block">
 Master handout slide manager.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IMasterLayoutSlideCollection.html" title="interface in com.aspose.slides">IMasterLayoutSlideCollection</a></td>
<td class="colLast">
<div class="block">
 Represents a collections of all layout slides of defined master slide.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IMasterNotesSlide.html" title="interface in com.aspose.slides">IMasterNotesSlide</a></td>
<td class="colLast">
<div class="block">
 Represents master slide for notes.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IMasterNotesSlideHeaderFooterManager.html" title="interface in com.aspose.slides">IMasterNotesSlideHeaderFooterManager</a></td>
<td class="colLast">
<div class="block">
 Represents manager which holds behavior of the master notes slide footer, date-time, page number placeholders and all child placeholders.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IMasterNotesSlideManager.html" title="interface in com.aspose.slides">IMasterNotesSlideManager</a></td>
<td class="colLast">
<div class="block">
 Master notes slide manager.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IMasterSlide.html" title="interface in com.aspose.slides">IMasterSlide</a></td>
<td class="colLast">
<div class="block">
 Represents a master slide in a presentation.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IMasterSlideCollection.html" title="interface in com.aspose.slides">IMasterSlideCollection</a></td>
<td class="colLast">
<div class="block">
 Represents a collection of master slides.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IMasterSlideHeaderFooterManager.html" title="interface in com.aspose.slides">IMasterSlideHeaderFooterManager</a></td>
<td class="colLast">
<div class="block">
 Represents manager which holds behavior of the master slide footer, date-time, page number placeholders and all child placeholders.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IMasterTheme.html" title="interface in com.aspose.slides">IMasterTheme</a></td>
<td class="colLast">
<div class="block">
 Represents a master theme.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IMasterThemeable.html" title="interface in com.aspose.slides">IMasterThemeable</a></td>
<td class="colLast">
<div class="block">
 Represent master theme manager.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IMasterThemeManager.html" title="interface in com.aspose.slides">IMasterThemeManager</a></td>
<td class="colLast">
<div class="block">
 Provides access to presentation master theme.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IMathAccent.html" title="interface in com.aspose.slides">IMathAccent</a></td>
<td class="colLast">
<div class="block">
 Specifies the accent function, consisting of a base and a combining diacritical mark
 Example: 𝑎́</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IMathAccentFactory.html" title="interface in com.aspose.slides">IMathAccentFactory</a></td>
<td class="colLast">
<div class="block">
 Allows to create a math accent</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IMathArray.html" title="interface in com.aspose.slides">IMathArray</a></td>
<td class="colLast">
<div class="block">
 Specifies a vertical array of equations or any mathematical objects</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IMathArrayFactory.html" title="interface in com.aspose.slides">IMathArrayFactory</a></td>
<td class="colLast">
<div class="block">
 Allows to create a math array</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IMathBar.html" title="interface in com.aspose.slides">IMathBar</a></td>
<td class="colLast">
<div class="block">
 Specifies the bar function, consisting of a base argument and an overbar or underbar</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IMathBarFactory.html" title="interface in com.aspose.slides">IMathBarFactory</a></td>
<td class="colLast">
<div class="block">
 Allows to create a math bar</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IMathBlock.html" title="interface in com.aspose.slides">IMathBlock</a></td>
<td class="colLast">
<div class="block">
 Specifies an instance of mathematical text that contained within a MathParagraph and starts on its own line.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IMathBlockCollection.html" title="interface in com.aspose.slides">IMathBlockCollection</a></td>
<td class="colLast">
<div class="block">
 Collection of math blocks (IMathBlock)</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IMathBlockFactory.html" title="interface in com.aspose.slides">IMathBlockFactory</a></td>
<td class="colLast">
<div class="block">
 Allows to create a math block</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IMathBorderBox.html" title="interface in com.aspose.slides">IMathBorderBox</a></td>
<td class="colLast">
<div class="block">
 Draws a rectangular or some other border around the IMathElement.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IMathBorderBoxFactory.html" title="interface in com.aspose.slides">IMathBorderBoxFactory</a></td>
<td class="colLast">
<div class="block">
 Allows to create a math border box</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IMathBox.html" title="interface in com.aspose.slides">IMathBox</a></td>
<td class="colLast">
<div class="block">
 Specifies the logical boxing (packaging) of mathematical element.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IMathBoxFactory.html" title="interface in com.aspose.slides">IMathBoxFactory</a></td>
<td class="colLast">
<div class="block">
 Allows to create a math box</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IMathDelimiter.html" title="interface in com.aspose.slides">IMathDelimiter</a></td>
<td class="colLast">
<div class="block">
 Specifies the delimiter object, consisting of opening and closing characters (such as parentheses, 
 braces, brackets, and vertical bars), and one or more mathematical elements inside, separated by a specified character.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IMathDelimiterFactory.html" title="interface in com.aspose.slides">IMathDelimiterFactory</a></td>
<td class="colLast">
<div class="block">
 Allows to create a math delimiter</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IMathElement.html" title="interface in com.aspose.slides">IMathElement</a></td>
<td class="colLast">
<div class="block">
 Base interface of any mathematical element: 
 fraction, mathmatical text, function, expression with multiple elements etc</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IMathElementCollection.html" title="interface in com.aspose.slides">IMathElementCollection</a></td>
<td class="colLast">
<div class="block">
 Represents a collection of mathematical elements (MathElement).</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IMathematicalText.html" title="interface in com.aspose.slides">IMathematicalText</a></td>
<td class="colLast">
<div class="block">
 Mathematical text</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IMathematicalTextFactory.html" title="interface in com.aspose.slides">IMathematicalTextFactory</a></td>
<td class="colLast">
<div class="block">
 Allows to create a MathematicalText element</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IMathFraction.html" title="interface in com.aspose.slides">IMathFraction</a></td>
<td class="colLast">
<div class="block">
 Specifies the fraction object, consisting of a numerator and denominator separated by a fraction bar.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IMathFractionFactory.html" title="interface in com.aspose.slides">IMathFractionFactory</a></td>
<td class="colLast">
<div class="block">
 Allows to create a math fraction</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IMathFunction.html" title="interface in com.aspose.slides">IMathFunction</a></td>
<td class="colLast">
<div class="block">
 Specifies a function of an argument.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IMathFunctionFactory.html" title="interface in com.aspose.slides">IMathFunctionFactory</a></td>
<td class="colLast">
<div class="block">
 Allows to create a math function</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IMathGroupingCharacter.html" title="interface in com.aspose.slides">IMathGroupingCharacter</a></td>
<td class="colLast">
<div class="block">
 Specifies a grouping symbol above or below an expression, usually to highlight the relationship between elements</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IMathGroupingCharacterFactory.html" title="interface in com.aspose.slides">IMathGroupingCharacterFactory</a></td>
<td class="colLast">
<div class="block">
 Allows to create a math grouping character</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IMathLeftSubSuperscriptElement.html" title="interface in com.aspose.slides">IMathLeftSubSuperscriptElement</a></td>
<td class="colLast">
<div class="block">
 Specifies the Sub-Superscript object, which consists of a base 
 and a subscript and superscript placed to the left of the base.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IMathLimit.html" title="interface in com.aspose.slides">IMathLimit</a></td>
<td class="colLast">
<div class="block">
 Specifies the Limit object, consisting of text on the baseline and reduced-size text immediately above or below it.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IMathLimitFactory.html" title="interface in com.aspose.slides">IMathLimitFactory</a></td>
<td class="colLast">
<div class="block">
 Allows to create IMathLimit</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IMathMatrix.html" title="interface in com.aspose.slides">IMathMatrix</a></td>
<td class="colLast">
<div class="block">
 Specifies the Matrix object, consisting of child elements laid out in one or more rows and columns.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IMathMatrixFactory.html" title="interface in com.aspose.slides">IMathMatrixFactory</a></td>
<td class="colLast">
<div class="block">
 Allows to create a math matrix</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IMathNaryOperator.html" title="interface in com.aspose.slides">IMathNaryOperator</a></td>
<td class="colLast">
<div class="block">
 Specifies an N-ary mathematical object, such as Summation and Integral.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IMathNaryOperatorFactory.html" title="interface in com.aspose.slides">IMathNaryOperatorFactory</a></td>
<td class="colLast">
<div class="block">
 Allows to create IMathNaryOperator</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IMathNaryOperatorProperties.html" title="interface in com.aspose.slides">IMathNaryOperatorProperties</a></td>
<td class="colLast">
<div class="block">
 Specifies properties of IMathNaryOperator</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IMathParagraph.html" title="interface in com.aspose.slides">IMathParagraph</a></td>
<td class="colLast">
<div class="block">
 Mathematical paragraph that is a container for mathematical blocks (IMathBlock)</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IMathParagraphFactory.html" title="interface in com.aspose.slides">IMathParagraphFactory</a></td>
<td class="colLast">
<div class="block">
 Allows to create a math paragraph</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IMathPortion.html" title="interface in com.aspose.slides">IMathPortion</a></td>
<td class="colLast">
<div class="block">
 Represents a portion with mathematical context inside.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IMathRadical.html" title="interface in com.aspose.slides">IMathRadical</a></td>
<td class="colLast">
<div class="block">
 Specifies the radical function, consisting of a base, and an optional degree.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IMathRadicalFactory.html" title="interface in com.aspose.slides">IMathRadicalFactory</a></td>
<td class="colLast">
<div class="block">
 Allows to create math radical</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IMathRightSubSuperscriptElement.html" title="interface in com.aspose.slides">IMathRightSubSuperscriptElement</a></td>
<td class="colLast">
<div class="block">
 Specifies the Sub-Superscript object, which consists of a base 
 and a subscript and superscript placed to the right of the base.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IMathRightSubSuperscriptElementFactory.html" title="interface in com.aspose.slides">IMathRightSubSuperscriptElementFactory</a></td>
<td class="colLast">
<div class="block">
 Allows to create IMathRightSubSuperscriptElementFactory</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IMathSubscriptElement.html" title="interface in com.aspose.slides">IMathSubscriptElement</a></td>
<td class="colLast">
<div class="block">
 Specifies the subscript object, which consists of a base 
 and a reduced-size subscript placed below and to the right.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IMathSubscriptElementFactory.html" title="interface in com.aspose.slides">IMathSubscriptElementFactory</a></td>
<td class="colLast">
<div class="block">
 Allows to create IMathSubscriptElement</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IMathSuperscriptElement.html" title="interface in com.aspose.slides">IMathSuperscriptElement</a></td>
<td class="colLast">
<div class="block">
 Specifies the superscript object, which consists of a base 
 and a reduced-size superscript placed above and to the right</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IMathSuperscriptElementFactory.html" title="interface in com.aspose.slides">IMathSuperscriptElementFactory</a></td>
<td class="colLast">
<div class="block">
 Allows to create IMathSuperscriptElement</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IMetered.html" title="interface in com.aspose.slides">IMetered</a></td>
<td class="colLast">
<div class="block">Provides methods to set metered key.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IModernComment.html" title="interface in com.aspose.slides">IModernComment</a></td>
<td class="colLast">
<div class="block">
 Represents a comment on a slide.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IMorphTransition.html" title="interface in com.aspose.slides">IMorphTransition</a></td>
<td class="colLast">
<div class="block">
 Ripple slide transition effect.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IMotionCmdPath.html" title="interface in com.aspose.slides">IMotionCmdPath</a></td>
<td class="colLast">
<div class="block">
 Represent one command of a path.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IMotionEffect.html" title="interface in com.aspose.slides">IMotionEffect</a></td>
<td class="colLast">
<div class="block">
 Represent motion effect behavior of effect.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IMotionPath.html" title="interface in com.aspose.slides">IMotionPath</a></td>
<td class="colLast">
<div class="block">
 Represent motion path.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IMultipleCellChartValue.html" title="interface in com.aspose.slides">IMultipleCellChartValue</a></td>
<td class="colLast">
<div class="block">
 Represents a collection of a chart cells.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/INormalViewProperties.html" title="interface in com.aspose.slides">INormalViewProperties</a></td>
<td class="colLast">
<div class="block">
 Represents normal view properties.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/INormalViewRestoredProperties.html" title="interface in com.aspose.slides">INormalViewRestoredProperties</a></td>
<td class="colLast">
<div class="block">
 Specifies the sizing of the slide region ((width when a child of restoredTop, height when a
 child of restoredLeft) of the normal view, when the region is of a variable restored size(neither minimized nor maximized).</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/INotesCommentsLayoutingOptions.html" title="interface in com.aspose.slides">INotesCommentsLayoutingOptions</a></td>
<td class="colLast">Deprecated
<div class="block"><span class="deprecationComment">Use the interface ISlidesLayoutOptions.</span></div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/INotesSize.html" title="interface in com.aspose.slides">INotesSize</a></td>
<td class="colLast">
<div class="block">
 Represents a size of notes slide.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/INotesSlide.html" title="interface in com.aspose.slides">INotesSlide</a></td>
<td class="colLast">
<div class="block">
 Represents a notes slide in a presentation.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/INotesSlideHeaderFooterManager.html" title="interface in com.aspose.slides">INotesSlideHeaderFooterManager</a></td>
<td class="colLast">
<div class="block">
 Represents manager which holds behavior of the notes slide placeholders, including header placeholder.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/INotesSlideManager.html" title="interface in com.aspose.slides">INotesSlideManager</a></td>
<td class="colLast">
<div class="block">
 Notes slide manager.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/INotImplementedWarningInfo.html" title="interface in com.aspose.slides">INotImplementedWarningInfo</a></td>
<td class="colLast">
<div class="block">
 Represents a warning about known not implemented feature which won't be implemented in the near future.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IObsoletePresLockingBehaviorWarningInfo.html" title="interface in com.aspose.slides">IObsoletePresLockingBehaviorWarningInfo</a></td>
<td class="colLast">
<div class="block">
 This warning indicates that an obsolete presentation locking behavior is used.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IOleEmbeddedDataInfo.html" title="interface in com.aspose.slides">IOleEmbeddedDataInfo</a></td>
<td class="colLast">
<div class="block">
 Represents embedded data info for OLE object.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IOleObjectFrame.html" title="interface in com.aspose.slides">IOleObjectFrame</a></td>
<td class="colLast">
<div class="block">
 Represents an OLE object on a slide.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IOptionalBlackTransition.html" title="interface in com.aspose.slides">IOptionalBlackTransition</a></td>
<td class="colLast">
<div class="block">
 Optional black slide transition effect.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IOrientationTransition.html" title="interface in com.aspose.slides">IOrientationTransition</a></td>
<td class="colLast">
<div class="block">
 Orientation slide transition effect.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IOuterShadow.html" title="interface in com.aspose.slides">IOuterShadow</a></td>
<td class="colLast">
<div class="block">
 Represents an Outer Shadow effect.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IOuterShadowEffectiveData.html" title="interface in com.aspose.slides">IOuterShadowEffectiveData</a></td>
<td class="colLast">
<div class="block">
 Immutable object which represents an Outer Shadow effect.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IOutputFile.html" title="interface in com.aspose.slides">IOutputFile</a></td>
<td class="colLast">
<div class="block">
 Represents an output file.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IOutputSaver.html" title="interface in com.aspose.slides">IOutputSaver</a></td>
<td class="colLast">
<div class="block">
 Represents an output saving service.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IOverridableText.html" title="interface in com.aspose.slides">IOverridableText</a></td>
<td class="colLast">
<div class="block">
 Represents overridable text for a chart.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IOverrideTheme.html" title="interface in com.aspose.slides">IOverrideTheme</a></td>
<td class="colLast">
<div class="block">
 Represents a overriding theme.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IOverrideThemeable.html" title="interface in com.aspose.slides">IOverrideThemeable</a></td>
<td class="colLast">
<div class="block">
 Represents override theme manager.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IOverrideThemeManager.html" title="interface in com.aspose.slides">IOverrideThemeManager</a></td>
<td class="colLast">
<div class="block">
 Provides access to different types of overriden themes.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IParagraph.html" title="interface in com.aspose.slides">IParagraph</a></td>
<td class="colLast">
<div class="block">
 Represents a paragraph of a text.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IParagraphCollection.html" title="interface in com.aspose.slides">IParagraphCollection</a></td>
<td class="colLast">
<div class="block">
 Represents a collection of a paragraphs.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IParagraphFactory.html" title="interface in com.aspose.slides">IParagraphFactory</a></td>
<td class="colLast">
<div class="block">
 Allows to create paragraphs</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IParagraphFormat.html" title="interface in com.aspose.slides">IParagraphFormat</a></td>
<td class="colLast">
<div class="block">
 This class contains the paragraph formatting properties.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IParagraphFormatEffectiveData.html" title="interface in com.aspose.slides">IParagraphFormatEffectiveData</a></td>
<td class="colLast">
<div class="block">
 Immutable object which contains effective paragraph formatting properties.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IPathSegment.html" title="interface in com.aspose.slides">IPathSegment</a></td>
<td class="colLast">
<div class="block">
 Represents segment of graphics path of GeometryShape</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IPatternFormat.html" title="interface in com.aspose.slides">IPatternFormat</a></td>
<td class="colLast">
<div class="block">
 Represents a pattern to fill a shape.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IPatternFormatEffectiveData.html" title="interface in com.aspose.slides">IPatternFormatEffectiveData</a></td>
<td class="colLast">
<div class="block">
 Immutable object which contains effective pattern filling properties.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IPdfOptions.html" title="interface in com.aspose.slides">IPdfOptions</a></td>
<td class="colLast">
<div class="block">
 Provides options that control how a presentation is saved in Pdf format.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IPictureEffectiveData.html" title="interface in com.aspose.slides">IPictureEffectiveData</a></td>
<td class="colLast">
<div class="block">
 Immutable object which contains effective picture properties.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IPictureFillFormat.html" title="interface in com.aspose.slides">IPictureFillFormat</a></td>
<td class="colLast">
<div class="block">
 Represents a picture fill style.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IPictureFillFormatEffectiveData.html" title="interface in com.aspose.slides">IPictureFillFormatEffectiveData</a></td>
<td class="colLast">
<div class="block">
 Immutable object which contains the properties of picture fill.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IPictureFrame.html" title="interface in com.aspose.slides">IPictureFrame</a></td>
<td class="colLast">
<div class="block">
 Represents a frame with a picture inside.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IPictureFrameLock.html" title="interface in com.aspose.slides">IPictureFrameLock</a></td>
<td class="colLast">
<div class="block">
 Determines which operations are disabled on the parent PictureFrameEx.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IPieSplitCustomPointCollection.html" title="interface in com.aspose.slides">IPieSplitCustomPointCollection</a></td>
<td class="colLast">
<div class="block">
 Represents a collection of points that shall be drawn in the second pie or bar on a bar-of-pie or pie-of-pie chart with a custom split.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IPlaceholder.html" title="interface in com.aspose.slides">IPlaceholder</a></td>
<td class="colLast">
<div class="block">
 Represents a placeholder on a slide.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IPoint.html" title="interface in com.aspose.slides">IPoint</a></td>
<td class="colLast">
<div class="block">
 Represent animation point.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IPointCollection.html" title="interface in com.aspose.slides">IPointCollection</a></td>
<td class="colLast">
<div class="block">
 Represents a collection of portions.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IPortion.html" title="interface in com.aspose.slides">IPortion</a></td>
<td class="colLast">
<div class="block">
 Represents a portion of text inside a text paragraph.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IPortionCollection.html" title="interface in com.aspose.slides">IPortionCollection</a></td>
<td class="colLast">
<div class="block">
 Represents a collection of a portions.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IPortionFactory.html" title="interface in com.aspose.slides">IPortionFactory</a></td>
<td class="colLast">
<div class="block">
 Allows to create test portions</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IPortionFormat.html" title="interface in com.aspose.slides">IPortionFormat</a></td>
<td class="colLast">
<div class="block">
 This class contains the text portion formatting properties.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IPortionFormatEffectiveData.html" title="interface in com.aspose.slides">IPortionFormatEffectiveData</a></td>
<td class="colLast">
<div class="block">
 Immutable object which contains effective text portion formatting properties.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IPPImage.html" title="interface in com.aspose.slides">IPPImage</a></td>
<td class="colLast">
<div class="block">
 Represents an image in a presentation.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IPptOptions.html" title="interface in com.aspose.slides">IPptOptions</a></td>
<td class="colLast">
<div class="block">
 Provides options that control how a presentation is saved in PPT format.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IPptxOptions.html" title="interface in com.aspose.slides">IPptxOptions</a></td>
<td class="colLast">
<div class="block">
 Represents options for saving OpenXml presentations (PPTX, PPSX, POTX, PPTM, PPSM, POTM).</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IPresentation.html" title="interface in com.aspose.slides">IPresentation</a></td>
<td class="colLast">
<div class="block">
 Presentation document</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IPresentationAnimationPlayer.html" title="interface in com.aspose.slides">IPresentationAnimationPlayer</a></td>
<td class="colLast">
<div class="block">
 Represents a player of the animation.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IPresentationComponent.html" title="interface in com.aspose.slides">IPresentationComponent</a></td>
<td class="colLast">
<div class="block">
 Represents a component of a presentation.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IPresentationFactory.html" title="interface in com.aspose.slides">IPresentationFactory</a></td>
<td class="colLast">
<div class="block">
 Allows to create presentation via COM interface</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IPresentationHeaderFooterManager.html" title="interface in com.aspose.slides">IPresentationHeaderFooterManager</a></td>
<td class="colLast">
<div class="block">
 Represents manager which holds behavior of all footer, date-time and page number placeholders of presentation.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IPresentationInfo.html" title="interface in com.aspose.slides">IPresentationInfo</a></td>
<td class="colLast">
<div class="block">
 Information about presentation file</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IPresentationSignedWarningInfo.html" title="interface in com.aspose.slides">IPresentationSignedWarningInfo</a></td>
<td class="colLast">
<div class="block">
 This warning indicates that the presentation being read has the signature 
 and this signature will be removed during processing.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IPresentationText.html" title="interface in com.aspose.slides">IPresentationText</a></td>
<td class="colLast">
<div class="block">
 Represents the text extracted from the slide</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IPresetShadow.html" title="interface in com.aspose.slides">IPresetShadow</a></td>
<td class="colLast">
<div class="block">
 Represents a Preset Shadow effect.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IPresetShadowEffectiveData.html" title="interface in com.aspose.slides">IPresetShadowEffectiveData</a></td>
<td class="colLast">
<div class="block">
 Immutable object which represents a Preset Shadow effect.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IProgressCallback.html" title="interface in com.aspose.slides">IProgressCallback</a></td>
<td class="colLast">
<div class="block">
 Represents a callback object for saving progress updates in percentage.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IPropertyEffect.html" title="interface in com.aspose.slides">IPropertyEffect</a></td>
<td class="colLast">
<div class="block">
 Represent property effect behavior.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IProtectionManager.html" title="interface in com.aspose.slides">IProtectionManager</a></td>
<td class="colLast">
<div class="block">
 Presentation password protection management.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IReflection.html" title="interface in com.aspose.slides">IReflection</a></td>
<td class="colLast">
<div class="block">
 Represents a reflection effect.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IReflectionEffectiveData.html" title="interface in com.aspose.slides">IReflectionEffectiveData</a></td>
<td class="colLast">
<div class="block">
 Immutable object which represents a Reflection effect.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IRenderingOptions.html" title="interface in com.aspose.slides">IRenderingOptions</a></td>
<td class="colLast">
<div class="block">
 Provides options that control how a presentation/slide is rendered.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IResourceLoadingArgs.html" title="interface in com.aspose.slides">IResourceLoadingArgs</a></td>
<td class="colLast">
<div class="block">
 Interface for external resource loading arguments.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IResourceLoadingCallback.html" title="interface in com.aspose.slides">IResourceLoadingCallback</a></td>
<td class="colLast">
<div class="block">
 Callback interface used to manage external resources loading.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IResponsiveHtmlController.html" title="interface in com.aspose.slides">IResponsiveHtmlController</a></td>
<td class="colLast">
<div class="block">
 Responsive HTML Controller</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IRevealTransition.html" title="interface in com.aspose.slides">IRevealTransition</a></td>
<td class="colLast">
<div class="block">
 Reveal slide transition effect.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IRippleTransition.html" title="interface in com.aspose.slides">IRippleTransition</a></td>
<td class="colLast">
<div class="block">
 Ripple slide transition effect.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IRotation3D.html" title="interface in com.aspose.slides">IRotation3D</a></td>
<td class="colLast">
<div class="block">
 Represents 3D rotation of a chart.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IRotationEffect.html" title="interface in com.aspose.slides">IRotationEffect</a></td>
<td class="colLast">
<div class="block">
 Represent rotation behavior of effect.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IRow.html" title="interface in com.aspose.slides">IRow</a></td>
<td class="colLast">
<div class="block">
 Represents a row in a table.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IRowCollection.html" title="interface in com.aspose.slides">IRowCollection</a></td>
<td class="colLast">
<div class="block">
 Represents table row collection.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IRowFormat.html" title="interface in com.aspose.slides">IRowFormat</a></td>
<td class="colLast">
<div class="block">
 Represents format of a table row.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IRowFormatEffectiveData.html" title="interface in com.aspose.slides">IRowFormatEffectiveData</a></td>
<td class="colLast">
<div class="block">
 Immutable object which contains effective table row formatting properties.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ISaveOptions.html" title="interface in com.aspose.slides">ISaveOptions</a></td>
<td class="colLast">
<div class="block">
 Options that control how a presentation is saved.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ISaveOptionsFactory.html" title="interface in com.aspose.slides">ISaveOptionsFactory</a></td>
<td class="colLast">
<div class="block">
 Allows to create save options' instances</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IScaleEffect.html" title="interface in com.aspose.slides">IScaleEffect</a></td>
<td class="colLast">
<div class="block">
 Represents animation scale effect.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ISection.html" title="interface in com.aspose.slides">ISection</a></td>
<td class="colLast">
<div class="block">
 Represents section of slides.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ISectionCollection.html" title="interface in com.aspose.slides">ISectionCollection</a></td>
<td class="colLast">
<div class="block">
 Represents a collection of sections.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ISectionSlideCollection.html" title="interface in com.aspose.slides">ISectionSlideCollection</a></td>
<td class="colLast">
<div class="block">
 Represents a collection of a slides in the section.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ISectionZoomFrame.html" title="interface in com.aspose.slides">ISectionZoomFrame</a></td>
<td class="colLast">
<div class="block">
 Represents a Section Zoom object in a slide.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ISequence.html" title="interface in com.aspose.slides">ISequence</a></td>
<td class="colLast">
<div class="block">
 Represents sequence (collection of effects).</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ISequenceCollection.html" title="interface in com.aspose.slides">ISequenceCollection</a></td>
<td class="colLast">
<div class="block">
 Represents collection of interactive sequences.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ISetEffect.html" title="interface in com.aspose.slides">ISetEffect</a></td>
<td class="colLast">
<div class="block">
 Represents a set effect for an animation behavior.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a></td>
<td class="colLast">
<div class="block">
 Represents a shape on a slide.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IShapeBevel.html" title="interface in com.aspose.slides">IShapeBevel</a></td>
<td class="colLast">
<div class="block">
 Represents properties of shape's main face relief.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IShapeBevelEffectiveData.html" title="interface in com.aspose.slides">IShapeBevelEffectiveData</a></td>
<td class="colLast">
<div class="block">
 Immutable object which contains effective shape's face relief properties.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IShapeCollection.html" title="interface in com.aspose.slides">IShapeCollection</a></td>
<td class="colLast">
<div class="block">
 Represents a collection of shapes.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IShapeElement.html" title="interface in com.aspose.slides">IShapeElement</a></td>
<td class="colLast">
<div class="block">
 Represents a part of shape with same outline and fill properties.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IShapeFrame.html" title="interface in com.aspose.slides">IShapeFrame</a></td>
<td class="colLast">
<div class="block">
 Represents shape frame's properties.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IShapeStyle.html" title="interface in com.aspose.slides">IShapeStyle</a></td>
<td class="colLast">
<div class="block">
 Represent shape's style reference.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IShredTransition.html" title="interface in com.aspose.slides">IShredTransition</a></td>
<td class="colLast">
<div class="block">
 Shred slide transition effect.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ISideDirectionTransition.html" title="interface in com.aspose.slides">ISideDirectionTransition</a></td>
<td class="colLast">
<div class="block">
 Side direction slide transition effect.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ISingleCellChartValue.html" title="interface in com.aspose.slides">ISingleCellChartValue</a></td>
<td class="colLast">
<div class="block">
 Represents a chart data cell.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ISketchFormat.html" title="interface in com.aspose.slides">ISketchFormat</a></td>
<td class="colLast">
<div class="block">
 Represents properties for lines sketch format.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ISketchFormatEffectiveData.html" title="interface in com.aspose.slides">ISketchFormatEffectiveData</a></td>
<td class="colLast">
<div class="block">
 Immutable object which contains effective line sketch properties.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a></td>
<td class="colLast">
<div class="block">
 Represents a slide in a presentation.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ISlideCollection.html" title="interface in com.aspose.slides">ISlideCollection</a></td>
<td class="colLast">
<div class="block">
 Represents a collection of a slides.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ISlideComponent.html" title="interface in com.aspose.slides">ISlideComponent</a></td>
<td class="colLast">
<div class="block">
 Represents a component of a slide.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ISlideHeaderFooterManager.html" title="interface in com.aspose.slides">ISlideHeaderFooterManager</a></td>
<td class="colLast">
<div class="block">
 Represents manager which holds behavior of the slide footer, date-time, page number placeholders.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ISlideImageFormat.html" title="interface in com.aspose.slides">ISlideImageFormat</a></td>
<td class="colLast">
<div class="block">
 Determines format in which slide image will be saved for presentation to HTML export.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ISlideShowTransition.html" title="interface in com.aspose.slides">ISlideShowTransition</a></td>
<td class="colLast">
<div class="block">
 Represents slide show transition.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ISlideSize.html" title="interface in com.aspose.slides">ISlideSize</a></td>
<td class="colLast">
<div class="block">
 Represents the size and orientation of a slide.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ISlidesLayoutOptions.html" title="interface in com.aspose.slides">ISlidesLayoutOptions</a></td>
<td class="colLast">
<div class="block">
 Represents the presentation layout mode for export.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ISlidesPicture.html" title="interface in com.aspose.slides">ISlidesPicture</a></td>
<td class="colLast">
<div class="block">
 Represents a picture in a presentation.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ISlideText.html" title="interface in com.aspose.slides">ISlideText</a></td>
<td class="colLast">
<div class="block">
 Represents the text extracted from the slide</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ISmartArt.html" title="interface in com.aspose.slides">ISmartArt</a></td>
<td class="colLast">
<div class="block">
 Represents a SmartArt diagram.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ISmartArtNode.html" title="interface in com.aspose.slides">ISmartArtNode</a></td>
<td class="colLast">
<div class="block">
 Represents node of a SmartArt diagram.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ISmartArtNodeCollection.html" title="interface in com.aspose.slides">ISmartArtNodeCollection</a></td>
<td class="colLast">
<div class="block">
 Represents a collection of SmartArt nodes.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ISmartArtShape.html" title="interface in com.aspose.slides">ISmartArtShape</a></td>
<td class="colLast">
<div class="block">
 Represents a shape inside SmartArt diagram</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ISmartArtShapeCollection.html" title="interface in com.aspose.slides">ISmartArtShapeCollection</a></td>
<td class="colLast">
<div class="block">
 Represents a collection of SmartArt shapes</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ISoftEdge.html" title="interface in com.aspose.slides">ISoftEdge</a></td>
<td class="colLast">
<div class="block">
 Represents a Soft Edge effect.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ISoftEdgeEffectiveData.html" title="interface in com.aspose.slides">ISoftEdgeEffectiveData</a></td>
<td class="colLast">
<div class="block">
 Immutable object which represents a soft edge effect.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ISplitTransition.html" title="interface in com.aspose.slides">ISplitTransition</a></td>
<td class="colLast">
<div class="block">
 Split slide transition effect.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ISpreadsheetOptions.html" title="interface in com.aspose.slides">ISpreadsheetOptions</a></td>
<td class="colLast">
<div class="block">
 Represents options which can be used to specify additional spreadsheets behavior.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IStringChartValue.html" title="interface in com.aspose.slides">IStringChartValue</a></td>
<td class="colLast">
<div class="block">
 Represent string value which can be stored in pptx presentation document in two ways:
 1) in cell/cells of workbook related to chart;
 2) as literal value.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IStringOrDoubleChartValue.html" title="interface in com.aspose.slides">IStringOrDoubleChartValue</a></td>
<td class="colLast">
<div class="block">
 Represent string or double value which can be stored in pptx presentation document in two ways:
 1) in cell/cells of workbook related to chart;
 2) as literal value.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ISummaryZoomFrame.html" title="interface in com.aspose.slides">ISummaryZoomFrame</a></td>
<td class="colLast">
<div class="block">
 Represents a Summary Zoom frame in a slide.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ISummaryZoomSection.html" title="interface in com.aspose.slides">ISummaryZoomSection</a></td>
<td class="colLast">
<div class="block">
 Represents a Summary Zoom Section object in a Summary Zoom frame.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ISummaryZoomSectionCollection.html" title="interface in com.aspose.slides">ISummaryZoomSectionCollection</a></td>
<td class="colLast">
<div class="block">
 Represents a collection of Summary Zoom Section objects.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ISvgImage.html" title="interface in com.aspose.slides">ISvgImage</a></td>
<td class="colLast">
<div class="block">
 Represents an SVG image.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ISVGOptions.html" title="interface in com.aspose.slides">ISVGOptions</a></td>
<td class="colLast">
<div class="block">
 Represents an SVG options.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ISvgShape.html" title="interface in com.aspose.slides">ISvgShape</a></td>
<td class="colLast">
<div class="block">
 Represents options for SVG shape.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ISvgShapeAndTextFormattingController.html" title="interface in com.aspose.slides">ISvgShapeAndTextFormattingController</a></td>
<td class="colLast">
<div class="block">
 Controls SVG shape and text generation.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ISvgShapeFormattingController.html" title="interface in com.aspose.slides">ISvgShapeFormattingController</a></td>
<td class="colLast">
<div class="block">
 Controls SVG shape generation.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ISvgTSpan.html" title="interface in com.aspose.slides">ISvgTSpan</a></td>
<td class="colLast">
<div class="block">
 Represents options for SVG text portion ("tspan").</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ISwfOptions.html" title="interface in com.aspose.slides">ISwfOptions</a></td>
<td class="colLast">
<div class="block">
 Provides options that control how a presentation is saved in SWF format.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ITab.html" title="interface in com.aspose.slides">ITab</a></td>
<td class="colLast">
<div class="block">
 Represents a tabulation for a text.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ITabCollection.html" title="interface in com.aspose.slides">ITabCollection</a></td>
<td class="colLast">
<div class="block">
 Represents a collection of tabs.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ITabEffectiveData.html" title="interface in com.aspose.slides">ITabEffectiveData</a></td>
<td class="colLast">
<div class="block">
 Immutable object which contains effective text's tabulation stop properties.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ITabFactory.html" title="interface in com.aspose.slides">ITabFactory</a></td>
<td class="colLast">
<div class="block">
 Allow to create ITab instances</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ITable.html" title="interface in com.aspose.slides">ITable</a></td>
<td class="colLast">
<div class="block">
 Represents a table on a slide.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ITableFormat.html" title="interface in com.aspose.slides">ITableFormat</a></td>
<td class="colLast">
<div class="block">
 Represents format of a table.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ITableFormatEffectiveData.html" title="interface in com.aspose.slides">ITableFormatEffectiveData</a></td>
<td class="colLast">
<div class="block">
 Immutable object which contains effective table formatting properties.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ITagCollection.html" title="interface in com.aspose.slides">ITagCollection</a></td>
<td class="colLast">
<div class="block">
 Represents the collection of tags (user defined pairs of strings)</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ITemplateEngine.html" title="interface in com.aspose.slides">ITemplateEngine</a></td>
<td class="colLast">
<div class="block">
 Represents a template engine that transforms template and data pair into resulting output (usually HTML).</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ITextAnimation.html" title="interface in com.aspose.slides">ITextAnimation</a></td>
<td class="colLast">
<div class="block">
 Represent text animation.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ITextAnimationCollection.html" title="interface in com.aspose.slides">ITextAnimationCollection</a></td>
<td class="colLast">
<div class="block">
 Represents collection of text animations.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ITextFrame.html" title="interface in com.aspose.slides">ITextFrame</a></td>
<td class="colLast">
<div class="block">
 Represents a TextFrame.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ITextFrameFormat.html" title="interface in com.aspose.slides">ITextFrameFormat</a></td>
<td class="colLast">
<div class="block">
 Contains the TextFrame's formatting properties.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ITextFrameFormatEffectiveData.html" title="interface in com.aspose.slides">ITextFrameFormatEffectiveData</a></td>
<td class="colLast">
<div class="block">
 Immutable object which contains effective text frame formatting properties.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ITextHighlightingOptions.html" title="interface in com.aspose.slides">ITextHighlightingOptions</a></td>
<td class="colLast">Deprecated
<div class="block"><span class="deprecationComment">The interface ITextHighlightingOptions will be removed after release of version 24.10.</span></div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ITextSearchOptions.html" title="interface in com.aspose.slides">ITextSearchOptions</a></td>
<td class="colLast">
<div class="block">
 Represents options that can be used to search for text in a Presentation, Slide or TextFrame.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ITextStyle.html" title="interface in com.aspose.slides">ITextStyle</a></td>
<td class="colLast">
<div class="block">
 Text style formatting properties.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ITextStyleEffectiveData.html" title="interface in com.aspose.slides">ITextStyleEffectiveData</a></td>
<td class="colLast">
<div class="block">
 Immutable object which contains effective text style properties.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ITextToHtmlConversionOptions.html" title="interface in com.aspose.slides">ITextToHtmlConversionOptions</a></td>
<td class="colLast">
<div class="block">
 Options for extracting HTML from the Pptx text.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ITheme.html" title="interface in com.aspose.slides">ITheme</a></td>
<td class="colLast">
<div class="block">
 Represents a theme.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IThemeable.html" title="interface in com.aspose.slides">IThemeable</a></td>
<td class="colLast">
<div class="block">
 Represents objects that can be themed with <a href="../../../com/aspose/slides/ITheme.html" title="interface in com.aspose.slides"><code>ITheme</code></a>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IThemeEffectiveData.html" title="interface in com.aspose.slides">IThemeEffectiveData</a></td>
<td class="colLast">
<div class="block">
 Immutable object which contains effective theme properties.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IThemeManager.html" title="interface in com.aspose.slides">IThemeManager</a></td>
<td class="colLast">
<div class="block">
 Represent theme properties.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IThreeDFormat.html" title="interface in com.aspose.slides">IThreeDFormat</a></td>
<td class="colLast">
<div class="block">
 Represents 3-D properties.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IThreeDFormatEffectiveData.html" title="interface in com.aspose.slides">IThreeDFormatEffectiveData</a></td>
<td class="colLast">
<div class="block">
 Immutable object which represents effective 3-D formatting properties.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IThreeDParamSource.html" title="interface in com.aspose.slides">IThreeDParamSource</a></td>
<td class="colLast">
<div class="block">
 3D properties source auxiliary interface</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ITiffOptions.html" title="interface in com.aspose.slides">ITiffOptions</a></td>
<td class="colLast">
<div class="block">
 Provides options that control how a presentation is saved in TIFF format.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ITiming.html" title="interface in com.aspose.slides">ITiming</a></td>
<td class="colLast">
<div class="block">
 Represents animation timing.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ITint.html" title="interface in com.aspose.slides">ITint</a></td>
<td class="colLast">
<div class="block">
 Represents a Tint effect.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ITintEffectiveData.html" title="interface in com.aspose.slides">ITintEffectiveData</a></td>
<td class="colLast">
<div class="block">
 Immutable object which represents a Tint effect.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ITransitionValueBase.html" title="interface in com.aspose.slides">ITransitionValueBase</a></td>
<td class="colLast">
<div class="block">
 Represents base class for slide transition effects.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ITrendline.html" title="interface in com.aspose.slides">ITrendline</a></td>
<td class="colLast">
<div class="block">
 Class represents trend line of chart series</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ITrendlineCollection.html" title="interface in com.aspose.slides">ITrendlineCollection</a></td>
<td class="colLast">
<div class="block">
 Represents a collection of TrendlineEx</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IUpDownBarsManager.html" title="interface in com.aspose.slides">IUpDownBarsManager</a></td>
<td class="colLast">
<div class="block">
 Provide access to up/down bars of Line- or Stock-chart.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IVbaModule.html" title="interface in com.aspose.slides">IVbaModule</a></td>
<td class="colLast">
<div class="block">
 Represents module that is contained in VBA project.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IVbaModuleCollection.html" title="interface in com.aspose.slides">IVbaModuleCollection</a></td>
<td class="colLast">
<div class="block">
 Represents a collection of a VBA Project modules.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IVbaProject.html" title="interface in com.aspose.slides">IVbaProject</a></td>
<td class="colLast">
<div class="block">
 Represents VBA project with presentation macros.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IVbaProjectFactory.html" title="interface in com.aspose.slides">IVbaProjectFactory</a></td>
<td class="colLast">
<div class="block">
 Allows to create VBA project via COM interface</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IVbaReference.html" title="interface in com.aspose.slides">IVbaReference</a></td>
<td class="colLast">
<div class="block">
 Represents the name of the VBA project reference.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IVbaReferenceCollection.html" title="interface in com.aspose.slides">IVbaReferenceCollection</a></td>
<td class="colLast">
<div class="block">
 Represents a collection of a VBA Project references.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IVbaReferenceFactory.html" title="interface in com.aspose.slides">IVbaReferenceFactory</a></td>
<td class="colLast">
<div class="block">
 Allows to create VBA project references via COM interface</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IVbaReferenceOleTwiddledTypeLib.html" title="interface in com.aspose.slides">IVbaReferenceOleTwiddledTypeLib</a></td>
<td class="colLast">
<div class="block">
 Represents modified OLE Automation type library reference in which 
 all controls are marked as extensible.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IVbaReferenceOleTypeLib.html" title="interface in com.aspose.slides">IVbaReferenceOleTypeLib</a></td>
<td class="colLast">
<div class="block">
 Represents OLE Automation type library reference.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IVbaReferenceProject.html" title="interface in com.aspose.slides">IVbaReferenceProject</a></td>
<td class="colLast">
<div class="block">
 Represents reference to an external VBA project.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IVideo.html" title="interface in com.aspose.slides">IVideo</a></td>
<td class="colLast">
<div class="block">
 Represents a video embedded into a presentation.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IVideoCollection.html" title="interface in com.aspose.slides">IVideoCollection</a></td>
<td class="colLast">
<div class="block">
 Represents a collection of Video objects.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IVideoFrame.html" title="interface in com.aspose.slides">IVideoFrame</a></td>
<td class="colLast">
<div class="block">
 Represents a video clip on a slide.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IVideoPlayerHtmlController.html" title="interface in com.aspose.slides">IVideoPlayerHtmlController</a></td>
<td class="colLast">
<div class="block">
 This class allows export of video and audio files into a HTML</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IVideoPlayerHtmlControllerFactory.html" title="interface in com.aspose.slides">IVideoPlayerHtmlControllerFactory</a></td>
<td class="colLast">
<div class="block">
 Allows to create VideoPlayerHtmlController.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IViewProperties.html" title="interface in com.aspose.slides">IViewProperties</a></td>
<td class="colLast">
<div class="block">
 Presentation wide view properties.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IWarningCallback.html" title="interface in com.aspose.slides">IWarningCallback</a></td>
<td class="colLast">
<div class="block">
 Interface for classes which receive warning</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IWarningInfo.html" title="interface in com.aspose.slides">IWarningInfo</a></td>
<td class="colLast">
<div class="block">
 Represents a base interface for all warnings.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IWheelTransition.html" title="interface in com.aspose.slides">IWheelTransition</a></td>
<td class="colLast">
<div class="block">
 Wheel slide transition effect.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IXamlOptions.html" title="interface in com.aspose.slides">IXamlOptions</a></td>
<td class="colLast">
<div class="block">
 Options that control how a XAML document is saved.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IXamlOutputSaver.html" title="interface in com.aspose.slides">IXamlOutputSaver</a></td>
<td class="colLast">
<div class="block">
 Represents an output saver implementation for transfer data to the external storage.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IXpsOptions.html" title="interface in com.aspose.slides">IXpsOptions</a></td>
<td class="colLast">
<div class="block">
 Provides options that control how a presentation is saved in XPS format.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IZoomFrame.html" title="interface in com.aspose.slides">IZoomFrame</a></td>
<td class="colLast">
<div class="block">
 Represents a Slide Zoom object in a slide.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/IZoomObject.html" title="interface in com.aspose.slides">IZoomObject</a></td>
<td class="colLast">
<div class="block">
 Represents a Zoom object in a slide.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/PresentationAnimationsGenerator.NewAnimation.html" title="interface in com.aspose.slides">PresentationAnimationsGenerator.NewAnimation</a></td>
<td class="colLast">
<div class="block">
 An event represents that a new animation was generated.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/PresentationPlayer.FrameTick.html" title="interface in com.aspose.slides">PresentationPlayer.FrameTick</a></td>
<td class="colLast">
<div class="block">
 Represents the frame tick handler of <a href="../../../com/aspose/slides/PresentationPlayer.html#FrameTickDelegate"><code>PresentationPlayer.FrameTickDelegate</code></a> event.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="typeSummary" border="0" cellpadding="3" cellspacing="0" summary="Class Summary table, listing classes, and an explanation">
<caption><span>Class Summary</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Class</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/AdjustValue.html" title="class in com.aspose.slides">AdjustValue</a></td>
<td class="colLast">
<div class="block">
 Represents a geometry shape's adjustment value.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/AdjustValueCollection.html" title="class in com.aspose.slides">AdjustValueCollection</a></td>
<td class="colLast">
<div class="block">
 Reprasents a collection of shape's adjustments.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/AfterAnimationType.html" title="class in com.aspose.slides">AfterAnimationType</a></td>
<td class="colLast">
<div class="block">
 Represents the after animation type of an animation effect.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/AlphaBiLevel.html" title="class in com.aspose.slides">AlphaBiLevel</a></td>
<td class="colLast">
<div class="block">
 Represents an Alpha Bi-Level effect.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/AlphaCeiling.html" title="class in com.aspose.slides">AlphaCeiling</a></td>
<td class="colLast">
<div class="block">
 Represents an Alpha Ceiling effect.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/AlphaFloor.html" title="class in com.aspose.slides">AlphaFloor</a></td>
<td class="colLast">
<div class="block">
 Represents an Alpha Floor effect.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/AlphaInverse.html" title="class in com.aspose.slides">AlphaInverse</a></td>
<td class="colLast">
<div class="block">
 Represents an Alpha Inverse effect.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/AlphaModulate.html" title="class in com.aspose.slides">AlphaModulate</a></td>
<td class="colLast">
<div class="block">
 Represents an Alpha Modulate effect.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/AlphaModulateFixed.html" title="class in com.aspose.slides">AlphaModulateFixed</a></td>
<td class="colLast">
<div class="block">
 Represents an Alpha Modulate Fixed effect.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/AlphaReplace.html" title="class in com.aspose.slides">AlphaReplace</a></td>
<td class="colLast">
<div class="block">
 Represents and Alpha Replace effect.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/AnimateTextType.html" title="class in com.aspose.slides">AnimateTextType</a></td>
<td class="colLast">
<div class="block">
 Represents the animate text type of an animation effect.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/AnimationTimeLine.html" title="class in com.aspose.slides">AnimationTimeLine</a></td>
<td class="colLast">
<div class="block">
 Represents timeline of animation.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/Audio.html" title="class in com.aspose.slides">Audio</a></td>
<td class="colLast">
<div class="block">
 Represents an embedded audio file.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/AudioCollection.html" title="class in com.aspose.slides">AudioCollection</a></td>
<td class="colLast">
<div class="block">
 Represents a collection of embedded audio files.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/AudioFrame.html" title="class in com.aspose.slides">AudioFrame</a></td>
<td class="colLast">
<div class="block">
  Represents an audio clip on a slide.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/AudioPlayModePreset.html" title="class in com.aspose.slides">AudioPlayModePreset</a></td>
<td class="colLast">
<div class="block">
 Constants which define how a sound is played.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/AudioVolumeMode.html" title="class in com.aspose.slides">AudioVolumeMode</a></td>
<td class="colLast">
<div class="block">
 Constants which define audio volume.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/AutoShape.html" title="class in com.aspose.slides">AutoShape</a></td>
<td class="colLast">
<div class="block">
  Represents an AutoShape.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/AutoShapeLock.html" title="class in com.aspose.slides">AutoShapeLock</a></td>
<td class="colLast">
<div class="block">
 Determines which operations are disabled on the parent AutoshapeEx.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/AxesManager.html" title="class in com.aspose.slides">AxesManager</a></td>
<td class="colLast">
<div class="block">
 Provides access to chart axes.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/Axis.html" title="class in com.aspose.slides">Axis</a></td>
<td class="colLast">
<div class="block">
 Encapsulates the object that represents a chart's axis.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/AxisAggregationType.html" title="class in com.aspose.slides">AxisAggregationType</a></td>
<td class="colLast">
<div class="block">
 Represents aggregation type of category axis.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/AxisFormat.html" title="class in com.aspose.slides">AxisFormat</a></td>
<td class="colLast">
<div class="block">
 Represents chart format properties.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/AxisPositionType.html" title="class in com.aspose.slides">AxisPositionType</a></td>
<td class="colLast">
<div class="block">
 Determines a position of axis.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/Backdrop3DScene.html" title="class in com.aspose.slides">Backdrop3DScene</a></td>
<td class="colLast">
<div class="block">
 Defines a plane in which effects, such as glow and shadow, are applied in relation to the shape they are being applied to.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/Background.html" title="class in com.aspose.slides">Background</a></td>
<td class="colLast">
<div class="block">
 Represents background of a slide.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/BackgroundType.html" title="class in com.aspose.slides">BackgroundType</a></td>
<td class="colLast">
<div class="block">
 Defines the slide background fill source.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/BaseChartValue.html" title="class in com.aspose.slides">BaseChartValue</a></td>
<td class="colLast">
<div class="block">
 Represents a value of a chart.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/BaseHandoutNotesSlideHeaderFooterManager.html" title="class in com.aspose.slides">BaseHandoutNotesSlideHeaderFooterManager</a></td>
<td class="colLast">
<div class="block">
 Represents manager which holds behavior of the placeholders, including header placeholder for all types handout and notes slides.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/BaseHeaderFooterManager.html" title="class in com.aspose.slides">BaseHeaderFooterManager</a></td>
<td class="colLast">
<div class="block">
 Represents manager which holds behavior of the footer, date-time, page number placeholders for all slide types.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/BaseOverrideThemeManager.html" title="class in com.aspose.slides">BaseOverrideThemeManager</a></td>
<td class="colLast">
<div class="block">
 Base class for classes that provide access to different types of overriden themes.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/BasePortionFormat.html" title="class in com.aspose.slides">BasePortionFormat</a></td>
<td class="colLast">
<div class="block">
 Common text portion formatting properties.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/BaseScript.html" title="class in com.aspose.slides">BaseScript</a></td>
<td class="colLast">
<div class="block">
 Math script</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/BaseShapeLock.html" title="class in com.aspose.slides">BaseShapeLock</a></td>
<td class="colLast">
<div class="block">
 Represents a base class for locks (disabled operation).</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/BaseSlide.html" title="class in com.aspose.slides">BaseSlide</a></td>
<td class="colLast">
<div class="block">
  Represents common data for all slide types.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/BaseSlideHeaderFooterManager.html" title="class in com.aspose.slides">BaseSlideHeaderFooterManager</a></td>
<td class="colLast">
<div class="block">
 Represents manager which holds behavior of the footer, date-time, page number placeholders for all slide types.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/BaseThemeManager.html" title="class in com.aspose.slides">BaseThemeManager</a></td>
<td class="colLast">
<div class="block">
 Base class for classes that provide access to different types of themes.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/Behavior.html" title="class in com.aspose.slides">Behavior</a></td>
<td class="colLast">
<div class="block">
 Represent base class behavior of effect.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/BehaviorAccumulateType.html" title="class in com.aspose.slides">BehaviorAccumulateType</a></td>
<td class="colLast">
<div class="block">
 Represents types of accumulation of effect behaviors.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/BehaviorAdditiveType.html" title="class in com.aspose.slides">BehaviorAdditiveType</a></td>
<td class="colLast">
<div class="block">
 Represents additive type for effect behavior.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/BehaviorCollection.html" title="class in com.aspose.slides">BehaviorCollection</a></td>
<td class="colLast">
<div class="block">
 Represents collection of behavior effects.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/BehaviorFactory.html" title="class in com.aspose.slides">BehaviorFactory</a></td>
<td class="colLast">
<div class="block">
 Allows to create animation effects</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/BehaviorProperty.html" title="class in com.aspose.slides">BehaviorProperty</a></td>
<td class="colLast">
<div class="block">
 Represent property types for animation behavior.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/BehaviorPropertyCollection.html" title="class in com.aspose.slides">BehaviorPropertyCollection</a></td>
<td class="colLast">
<div class="block">
 Represents timing properties for the effect behavior.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/BevelPresetType.html" title="class in com.aspose.slides">BevelPresetType</a></td>
<td class="colLast">
<div class="block">
 Constants which define 3D bevel of shape.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/BiLevel.html" title="class in com.aspose.slides">BiLevel</a></td>
<td class="colLast">
<div class="block">
 Represents a Bi-Level (black/white) effect.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/BlackWhiteConversionMode.html" title="class in com.aspose.slides">BlackWhiteConversionMode</a></td>
<td class="colLast">
<div class="block">
 Provides options that control how slides' images will be converted to bitonal images.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/BlackWhiteMode.html" title="class in com.aspose.slides">BlackWhiteMode</a></td>
<td class="colLast">
<div class="block">
 Determines how colored shape should be transformed into black and white.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/BlobManagementOptions.html" title="class in com.aspose.slides">BlobManagementOptions</a></td>
<td class="colLast">
<div class="block">
 Represents options which can be used to manage BLOB handling rules and other BLOB settings.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/Blur.html" title="class in com.aspose.slides">Blur</a></td>
<td class="colLast">
<div class="block">
 Represents a Blur effect that is applied to the entire shape, including its fill.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/BrowsedAtKiosk.html" title="class in com.aspose.slides">BrowsedAtKiosk</a></td>
<td class="colLast">
<div class="block">
 Browsed at a kiosk (full screen)</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/BrowsedByIndividual.html" title="class in com.aspose.slides">BrowsedByIndividual</a></td>
<td class="colLast">
<div class="block">
 Browsed by individual (window)</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/BubbleSizeRepresentationType.html" title="class in com.aspose.slides">BubbleSizeRepresentationType</a></td>
<td class="colLast">
<div class="block">
 Specifies the possible ways to represent data as bubble chart sizes.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/BuildType.html" title="class in com.aspose.slides">BuildType</a></td>
<td class="colLast">
<div class="block">
 Determines how text will appear on a shape during animation.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/BuildVersionInfo.html" title="class in com.aspose.slides">BuildVersionInfo</a></td>
<td class="colLast">
<div class="block">
 Contains information about version of Aspose.Slides.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/BulletFormat.html" title="class in com.aspose.slides">BulletFormat</a></td>
<td class="colLast">
<div class="block">
 Represents paragraph bullet formatting properties.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/BulletType.html" title="class in com.aspose.slides">BulletType</a></td>
<td class="colLast">
<div class="block">
 Represents the type of the extended bullets.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/Camera.html" title="class in com.aspose.slides">Camera</a></td>
<td class="colLast">
<div class="block">
 Represents Camera.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/CameraPresetType.html" title="class in com.aspose.slides">CameraPresetType</a></td>
<td class="colLast">
<div class="block">
 Constants which define camera preset type.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/Captions.html" title="class in com.aspose.slides">Captions</a></td>
<td class="colLast">
<div class="block">
 Represents the WebVTT closed captions.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/CaptionsCollection.html" title="class in com.aspose.slides">CaptionsCollection</a></td>
<td class="colLast">
<div class="block">
 Represents a collection of the closed captions.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/CategoryAxisType.html" title="class in com.aspose.slides">CategoryAxisType</a></td>
<td class="colLast">
<div class="block">
 Represents a type of a category axis.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/Cell.html" title="class in com.aspose.slides">Cell</a></td>
<td class="colLast">
<div class="block">
 Represents a cell of a table.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/CellCollection.html" title="class in com.aspose.slides">CellCollection</a></td>
<td class="colLast">
<div class="block">
 Represents a collection of cells.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/CellFormat.html" title="class in com.aspose.slides">CellFormat</a></td>
<td class="colLast">
<div class="block">
 Represents format of a table cell.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/Chart.html" title="class in com.aspose.slides">Chart</a></td>
<td class="colLast">
<div class="block">
 Represents an graphic chart on a slide.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ChartCategory.html" title="class in com.aspose.slides">ChartCategory</a></td>
<td class="colLast">
<div class="block">
 Represents chart categories.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ChartCategoryCollection.html" title="class in com.aspose.slides">ChartCategoryCollection</a></td>
<td class="colLast">
<div class="block">
 Represents collection of <a href="../../../com/aspose/slides/ChartCategory.html" title="class in com.aspose.slides"><code>ChartCategory</code></a></div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ChartCategoryLevelsManager.html" title="class in com.aspose.slides">ChartCategoryLevelsManager</a></td>
<td class="colLast">
<div class="block">
 Managed container of the values of the chart category levels.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ChartCellCollection.html" title="class in com.aspose.slides">ChartCellCollection</a></td>
<td class="colLast">
<div class="block">
 Represents collection of a cells with data.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ChartData.html" title="class in com.aspose.slides">ChartData</a></td>
<td class="colLast">
<div class="block">
 Represents data used for a chart plotting.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ChartDataCell.html" title="class in com.aspose.slides">ChartDataCell</a></td>
<td class="colLast">
<div class="block">
 Represents cell for chart data.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ChartDataPoint.html" title="class in com.aspose.slides">ChartDataPoint</a></td>
<td class="colLast">
<div class="block">
 Represents series data point.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ChartDataPointCollection.html" title="class in com.aspose.slides">ChartDataPointCollection</a></td>
<td class="colLast">
<div class="block">
 Represents collection of a series data point.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ChartDataPointLevel.html" title="class in com.aspose.slides">ChartDataPointLevel</a></td>
<td class="colLast">
<div class="block">
 Represents data point level.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ChartDataPointLevelsManager.html" title="class in com.aspose.slides">ChartDataPointLevelsManager</a></td>
<td class="colLast">
<div class="block">
 Container of data point levels.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ChartDataSourceType.html" title="class in com.aspose.slides">ChartDataSourceType</a></td>
<td class="colLast">
<div class="block">
 Represents a type of data source of the chart</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ChartDataWorkbook.html" title="class in com.aspose.slides">ChartDataWorkbook</a></td>
<td class="colLast">
<div class="block">
 Provides access to embedded Excel workbook</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ChartDataWorksheet.html" title="class in com.aspose.slides">ChartDataWorksheet</a></td>
<td class="colLast">
<div class="block">
 Represents worksheet associated with <a href="../../../com/aspose/slides/IChartDataCell.html" title="interface in com.aspose.slides"><code>IChartDataCell</code></a></div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ChartDataWorksheetCollection.html" title="class in com.aspose.slides">ChartDataWorksheetCollection</a></td>
<td class="colLast">
<div class="block">
 Represents the collection of worksheets of chart data workbook.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ChartLinesFormat.html" title="class in com.aspose.slides">ChartLinesFormat</a></td>
<td class="colLast">
<div class="block">
 Represents gridlines format properties.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ChartPlotArea.html" title="class in com.aspose.slides">ChartPlotArea</a></td>
<td class="colLast">
<div class="block">
 Represents rectangle where chart should be plotted.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ChartPortionFormat.html" title="class in com.aspose.slides">ChartPortionFormat</a></td>
<td class="colLast">
<div class="block">
 This class contains the chart portion formatting properties used in charts.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ChartSeries.html" title="class in com.aspose.slides">ChartSeries</a></td>
<td class="colLast">
<div class="block">
 Represents a chart series.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ChartSeriesCollection.html" title="class in com.aspose.slides">ChartSeriesCollection</a></td>
<td class="colLast">
<div class="block">
 Represents collection of  <a href="../../../com/aspose/slides/ChartSeries.html" title="class in com.aspose.slides"><code>ChartSeries</code></a></div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ChartSeriesGroup.html" title="class in com.aspose.slides">ChartSeriesGroup</a></td>
<td class="colLast">
<div class="block">
 Represents group of series.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ChartShapeType.html" title="class in com.aspose.slides">ChartShapeType</a></td>
<td class="colLast">
<div class="block">
 Represents a shape of chart.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ChartTextFormat.html" title="class in com.aspose.slides">ChartTextFormat</a></td>
<td class="colLast">
<div class="block">
 Specifies default text formatting for chart text elements.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ChartThemeManager.html" title="class in com.aspose.slides">ChartThemeManager</a></td>
<td class="colLast">
<div class="block">
 Provides access to chart theme overriden.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ChartTitle.html" title="class in com.aspose.slides">ChartTitle</a></td>
<td class="colLast">
<div class="block">
 Represents chart title properties.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ChartType.html" title="class in com.aspose.slides">ChartType</a></td>
<td class="colLast">
<div class="block">
 Represents a type of chart.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ChartTypeCharacterizer.html" title="class in com.aspose.slides">ChartTypeCharacterizer</a></td>
<td class="colLast">
<div class="block">
 Helper for getting additional information about charts and series by its ChartType.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ChartWall.html" title="class in com.aspose.slides">ChartWall</a></td>
<td class="colLast">
<div class="block">
 Represents walls on 3d charts.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/Collect.html" title="class in com.aspose.slides">Collect</a></td>
<td class="colLast">
<div class="block">
 Represents a group of methods intended to collect model objects of different types from <a href="../../../com/aspose/slides/Presentation.html" title="class in com.aspose.slides"><code>Presentation</code></a>.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ColorChange.html" title="class in com.aspose.slides">ColorChange</a></td>
<td class="colLast">
<div class="block">
 Represents a Color Change effect.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ColorDirection.html" title="class in com.aspose.slides">ColorDirection</a></td>
<td class="colLast">
<div class="block">
 Represents color direction for color effect behavior.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ColorEffect.html" title="class in com.aspose.slides">ColorEffect</a></td>
<td class="colLast">
<div class="block">
 Represents a color effect for an animation behavior.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ColorFormat.html" title="class in com.aspose.slides">ColorFormat</a></td>
<td class="colLast">
<div class="block">
 Represents a color used in a presentation.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ColorOffset.html" title="class in com.aspose.slides">ColorOffset</a></td>
<td class="colLast">
<div class="block">
 Represent color offset.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ColorOperation.html" title="class in com.aspose.slides">ColorOperation</a></td>
<td class="colLast">
<div class="block">
 Represents different color operations used for color transformations.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ColorOperationCollection.html" title="class in com.aspose.slides">ColorOperationCollection</a></td>
<td class="colLast">
<div class="block">
 Represents a collection of color transform operations.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ColorReplace.html" title="class in com.aspose.slides">ColorReplace</a></td>
<td class="colLast">
<div class="block">
 Represents a Color Replacement effect.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ColorScheme.html" title="class in com.aspose.slides">ColorScheme</a></td>
<td class="colLast">
<div class="block">
 Stores theme-defined colors.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ColorSchemeIndex.html" title="class in com.aspose.slides">ColorSchemeIndex</a></td>
<td class="colLast">
<div class="block">
 Represents an index in a colorscheme.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ColorSpace.html" title="class in com.aspose.slides">ColorSpace</a></td>
<td class="colLast">
<div class="block">
 Represents color space for color effect behavior.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ColorStringFormat.html" title="class in com.aspose.slides">ColorStringFormat</a></td>
<td class="colLast">
<div class="block">
 Represents a type of hyperlink action.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ColorTransformOperation.html" title="class in com.aspose.slides">ColorTransformOperation</a></td>
<td class="colLast">
<div class="block">
 Defines color transform operation.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ColorType.html" title="class in com.aspose.slides">ColorType</a></td>
<td class="colLast">
<div class="block">
 Represents different color modes.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/Column.html" title="class in com.aspose.slides">Column</a></td>
<td class="colLast">
<div class="block">
 Represents a column in a table.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ColumnCollection.html" title="class in com.aspose.slides">ColumnCollection</a></td>
<td class="colLast">
<div class="block">
 Represents collection of columns in a table.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ColumnFormat.html" title="class in com.aspose.slides">ColumnFormat</a></td>
<td class="colLast">
<div class="block">
 Represents format of a table column.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/CombinableSeriesTypesGroup.html" title="class in com.aspose.slides">CombinableSeriesTypesGroup</a></td>
<td class="colLast">
<div class="block">
 Enumeration of groups of combinable series types.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/CommandEffect.html" title="class in com.aspose.slides">CommandEffect</a></td>
<td class="colLast">
<div class="block">
 Represents a command effect for an animation behavior.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/CommandEffectType.html" title="class in com.aspose.slides">CommandEffectType</a></td>
<td class="colLast">
<div class="block">
 Represents command effect type for command effect behavior.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/Comment.html" title="class in com.aspose.slides">Comment</a></td>
<td class="colLast">
<div class="block">
 Represents a comment on a slide.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/CommentAuthor.html" title="class in com.aspose.slides">CommentAuthor</a></td>
<td class="colLast">
<div class="block">
 Represents an author of comments.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/CommentAuthorCollection.html" title="class in com.aspose.slides">CommentAuthorCollection</a></td>
<td class="colLast">
<div class="block">
 Represents a collection of comment authors.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/CommentCollection.html" title="class in com.aspose.slides">CommentCollection</a></td>
<td class="colLast">
<div class="block">
 Represents a collection of comments of one author.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/CommentsPositions.html" title="class in com.aspose.slides">CommentsPositions</a></td>
<td class="colLast">
<div class="block">
 Represents the rule to render comments into exported document</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/CommonSlideViewProperties.html" title="class in com.aspose.slides">CommonSlideViewProperties</a></td>
<td class="colLast">
<div class="block">
 Represents common slide view properties.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/Compress.html" title="class in com.aspose.slides">Compress</a></td>
<td class="colLast">
<div class="block">
 Represents a group of methods intended to compress <a href="../../../com/aspose/slides/Presentation.html" title="class in com.aspose.slides"><code>Presentation</code></a>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/Conformance.html" title="class in com.aspose.slides">Conformance</a></td>
<td class="colLast">
<div class="block">
 Specifies the conformance class to which the PresentationML document conforms.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/Connector.html" title="class in com.aspose.slides">Connector</a></td>
<td class="colLast">
<div class="block">
  Represents a connector.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ConnectorLock.html" title="class in com.aspose.slides">ConnectorLock</a></td>
<td class="colLast">
<div class="block">
 Determines which operations are disabled on the parent Connector.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/Control.html" title="class in com.aspose.slides">Control</a></td>
<td class="colLast">
<div class="block">
 Represents an ActiveX control.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ControlCollection.html" title="class in com.aspose.slides">ControlCollection</a></td>
<td class="colLast">
<div class="block">
 A collection of ActiveX controls.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ControlPropertiesCollection.html" title="class in com.aspose.slides">ControlPropertiesCollection</a></td>
<td class="colLast">
<div class="block">
 A collection of AcitveX properties.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ControlType.html" title="class in com.aspose.slides">ControlType</a></td>
<td class="colLast">
<div class="block">
 Defines a control type which should be embedded by <a href="../../../com/aspose/slides/IControlCollection.html#addControl-int-float-float-float-float-"><code>IControlCollection.addControl(int,float,float,float,float)</code></a>.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/Convert.html" title="class in com.aspose.slides">Convert</a></td>
<td class="colLast">
<div class="block">
 Represents a group of methods intended to convert <a href="../../../com/aspose/slides/Presentation.html" title="class in com.aspose.slides"><code>Presentation</code></a>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/CornerDirectionTransition.html" title="class in com.aspose.slides">CornerDirectionTransition</a></td>
<td class="colLast">
<div class="block">
 Corner direction slide transition effect.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/CrossesType.html" title="class in com.aspose.slides">CrossesType</a></td>
<td class="colLast">
<div class="block">
 Determines where axis will cross.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/CurrentThreadSettings.html" title="class in com.aspose.slides">CurrentThreadSettings</a></td>
<td class="colLast">
<div class="block">Auxiliary class that allows to define default Locale for current thread</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/CustomData.html" title="class in com.aspose.slides">CustomData</a></td>
<td class="colLast">
<div class="block">
 Represents container for custom data.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/CustomXmlPart.html" title="class in com.aspose.slides">CustomXmlPart</a></td>
<td class="colLast">
<div class="block">
 Represents custom xml part.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/CustomXmlPartCollection.html" title="class in com.aspose.slides">CustomXmlPartCollection</a></td>
<td class="colLast">
<div class="block">
 Represents collection of custom xml parts.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/DataLabel.html" title="class in com.aspose.slides">DataLabel</a></td>
<td class="colLast">
<div class="block">
 Represents a series labels.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/DataLabelCollection.html" title="class in com.aspose.slides">DataLabelCollection</a></td>
<td class="colLast">
<div class="block">
 Represents a series labels.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/DataLabelFormat.html" title="class in com.aspose.slides">DataLabelFormat</a></td>
<td class="colLast">
<div class="block">
 Represents formatting options for DataLabel.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/DataSourceType.html" title="class in com.aspose.slides">DataSourceType</a></td>
<td class="colLast">
<div class="block">
 Data source types.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/DataSourceTypeForErrorBarsCustomValues.html" title="class in com.aspose.slides">DataSourceTypeForErrorBarsCustomValues</a></td>
<td class="colLast">
<div class="block">
  Specifies types of values in ChartDataPoint.ErrorBarsCustomValues properties list</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/DataTable.html" title="class in com.aspose.slides">DataTable</a></td>
<td class="colLast">
<div class="block">
 Represents data table properties.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/DigitalSignature.html" title="class in com.aspose.slides">DigitalSignature</a></td>
<td class="colLast">
<div class="block">
 Digital signature in signed file.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/DigitalSignatureCollection.html" title="class in com.aspose.slides">DigitalSignatureCollection</a></td>
<td class="colLast">
<div class="block">
 Represents a collection of digital signatures attached to a document.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/DisplayBlanksAsType.html" title="class in com.aspose.slides">DisplayBlanksAsType</a></td>
<td class="colLast">
<div class="block">
 Determines how missing data will be displayed.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/DisplayUnitType.html" title="class in com.aspose.slides">DisplayUnitType</a></td>
<td class="colLast">
<div class="block">
 Determines multiplicity of the displayed data.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/DocumentProperties.html" title="class in com.aspose.slides">DocumentProperties</a></td>
<td class="colLast">
<div class="block">
 Represents properties of a presentation.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/DomObject.html" title="class in com.aspose.slides">DomObject</a>&lt;TParent&gt;</td>
<td class="colLast">
<div class="block">
 Base DOM object</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/DoubleChartValue.html" title="class in com.aspose.slides">DoubleChartValue</a></td>
<td class="colLast">
<div class="block">
 Represent double value which can be stored in pptx presentation document in two ways:
 1) in cell/cells of workbook related to chart;
 2) as literal value.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/DrawingGuide.html" title="class in com.aspose.slides">DrawingGuide</a></td>
<td class="colLast">
<div class="block">
 Represents an adjustable drawing guide.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/DrawingGuidesCollection.html" title="class in com.aspose.slides">DrawingGuidesCollection</a></td>
<td class="colLast">
<div class="block">
 Represents a collection of the adjustable drawing guides.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/Duotone.html" title="class in com.aspose.slides">Duotone</a></td>
<td class="colLast">
<div class="block">
 Represents a Duotone effect.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/Effect.html" title="class in com.aspose.slides">Effect</a></td>
<td class="colLast">
<div class="block">
 Represents animation effect.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/EffectChartMajorGroupingType.html" title="class in com.aspose.slides">EffectChartMajorGroupingType</a></td>
<td class="colLast">
<div class="block">
 Represents the type of an animation effect for chart's element.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/EffectChartMinorGroupingType.html" title="class in com.aspose.slides">EffectChartMinorGroupingType</a></td>
<td class="colLast">
<div class="block">
 Represents the type of an animation effect for chart's element in series or category.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/EffectFactory.html" title="class in com.aspose.slides">EffectFactory</a></td>
<td class="colLast">
<div class="block">
 Allows to create effects</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/EffectFillType.html" title="class in com.aspose.slides">EffectFillType</a></td>
<td class="colLast">
<div class="block">
 Represent fill types.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/EffectFormat.html" title="class in com.aspose.slides">EffectFormat</a></td>
<td class="colLast">
<div class="block">
 Represents effect properties of shape.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/EffectPresetClassType.html" title="class in com.aspose.slides">EffectPresetClassType</a></td>
<td class="colLast">
<div class="block">
 Represent effect class types.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/EffectRestartType.html" title="class in com.aspose.slides">EffectRestartType</a></td>
<td class="colLast">
<div class="block">
 Represent restart types for timing.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/EffectStyle.html" title="class in com.aspose.slides">EffectStyle</a></td>
<td class="colLast">
<div class="block">
 Represents an effect style.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/EffectStyleCollection.html" title="class in com.aspose.slides">EffectStyleCollection</a></td>
<td class="colLast">
<div class="block">
 Represents a collection of effect styles.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/EffectSubtype.html" title="class in com.aspose.slides">EffectSubtype</a></td>
<td class="colLast">
<div class="block">
 Represents subtypes of animation effect.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/EffectTriggerType.html" title="class in com.aspose.slides">EffectTriggerType</a></td>
<td class="colLast">
<div class="block">
 Represent trigger type of effect.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/EffectType.html" title="class in com.aspose.slides">EffectType</a></td>
<td class="colLast">
<div class="block">
 Represents the type of an animation effect.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/EightDirectionTransition.html" title="class in com.aspose.slides">EightDirectionTransition</a></td>
<td class="colLast">
<div class="block">
 Eight direction slide transition effect.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/EmbedAllFontsHtmlController.html" title="class in com.aspose.slides">EmbedAllFontsHtmlController</a></td>
<td class="colLast">
<div class="block">
 The formatting controller class to use for embedding all presentation fonts in WOFF format.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/EmbeddedEotFontsHtmlController.html" title="class in com.aspose.slides">EmbeddedEotFontsHtmlController</a></td>
<td class="colLast">
<div class="block">
 The formatting controller class to use for fonts embedding in EOT format</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/EmbeddedWoffFontsHtmlController.html" title="class in com.aspose.slides">EmbeddedWoffFontsHtmlController</a></td>
<td class="colLast">
<div class="block">
 The formatting controller class to use for fonts embedding in WOFF format</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/EmbeddingLevel.html" title="class in com.aspose.slides">EmbeddingLevel</a></td>
<td class="colLast">
<div class="block">
 Represents the licensing rights for embedding the font.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/EmbedFontCharacters.html" title="class in com.aspose.slides">EmbedFontCharacters</a></td>
<td class="colLast">
<div class="block">
 Represents the rule to use for adding new embedding font into <a href="../../../com/aspose/slides/IPresentation.html" title="interface in com.aspose.slides"><code>IPresentation</code></a></div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/EmptyTransition.html" title="class in com.aspose.slides">EmptyTransition</a></td>
<td class="colLast">
<div class="block">
 Empty slide transition effect.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ErrorBarsCustomValues.html" title="class in com.aspose.slides">ErrorBarsCustomValues</a></td>
<td class="colLast">
<div class="block">
 Specifies the errors bar values.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ErrorBarsFormat.html" title="class in com.aspose.slides">ErrorBarsFormat</a></td>
<td class="colLast">
<div class="block">
 Represents error bars of chart series.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ErrorBarType.html" title="class in com.aspose.slides">ErrorBarType</a></td>
<td class="colLast">
<div class="block">
 Represents type of error bar</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ErrorBarValueType.html" title="class in com.aspose.slides">ErrorBarValueType</a></td>
<td class="colLast">
<div class="block">
 Represents type of error bar value</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ExternalResourceResolver.html" title="class in com.aspose.slides">ExternalResourceResolver</a></td>
<td class="colLast">
<div class="block">
 Callback class used to resolve external resources during Html, Svg documents import.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ExtraColorScheme.html" title="class in com.aspose.slides">ExtraColorScheme</a></td>
<td class="colLast">
<div class="block">
 Represents an additional color scheme which can be assigned to a slide.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ExtraColorSchemeCollection.html" title="class in com.aspose.slides">ExtraColorSchemeCollection</a></td>
<td class="colLast">
<div class="block">
 Represents a collection of additional color schemes.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/Field.html" title="class in com.aspose.slides">Field</a></td>
<td class="colLast">
<div class="block">
 Represents a field.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/FieldType.html" title="class in com.aspose.slides">FieldType</a></td>
<td class="colLast">
<div class="block">
 Represents a type of field.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/FillBlendMode.html" title="class in com.aspose.slides">FillBlendMode</a></td>
<td class="colLast">
<div class="block">
 Determines blend mode.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/FillFormat.html" title="class in com.aspose.slides">FillFormat</a></td>
<td class="colLast">
<div class="block">
 Represents a fill formatting options.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/FillFormatCollection.html" title="class in com.aspose.slides">FillFormatCollection</a></td>
<td class="colLast">
<div class="block">
 Represents the collection of fill styles.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/FillOverlay.html" title="class in com.aspose.slides">FillOverlay</a></td>
<td class="colLast">
<div class="block">
 Represents a Fill Overlay effect.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/FillType.html" title="class in com.aspose.slides">FillType</a></td>
<td class="colLast">
<div class="block">
 Specifies the interior fill type of various visual objects.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/FilterEffect.html" title="class in com.aspose.slides">FilterEffect</a></td>
<td class="colLast">
<div class="block">
 Represent filter effect of behavior.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/FilterEffectRevealType.html" title="class in com.aspose.slides">FilterEffectRevealType</a></td>
<td class="colLast">
<div class="block">
 Represents filter reveal type.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/FilterEffectSubtype.html" title="class in com.aspose.slides">FilterEffectSubtype</a></td>
<td class="colLast">
<div class="block">
 Represents filter effect subtypes.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/FilterEffectType.html" title="class in com.aspose.slides">FilterEffectType</a></td>
<td class="colLast">
<div class="block">
 Represents filter effect types.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/Flavor.html" title="class in com.aspose.slides">Flavor</a></td>
<td class="colLast">
<div class="block">
 All markdown specifications used in program.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/FlyThroughTransition.html" title="class in com.aspose.slides">FlyThroughTransition</a></td>
<td class="colLast">
<div class="block">
 Fly-through slide transition effect.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/FontAlignment.html" title="class in com.aspose.slides">FontAlignment</a></td>
<td class="colLast">
<div class="block">
 Represents vertical font alignment.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/FontCollectionIndex.html" title="class in com.aspose.slides">FontCollectionIndex</a></td>
<td class="colLast">
<div class="block">
 Represents font's index in a collection.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/FontData.html" title="class in com.aspose.slides">FontData</a></td>
<td class="colLast">
<div class="block">
 Represents a font definition.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/FontDataFactory.html" title="class in com.aspose.slides">FontDataFactory</a></td>
<td class="colLast">
<div class="block">
 FontData factory</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/FontFallBackRule.html" title="class in com.aspose.slides">FontFallBackRule</a></td>
<td class="colLast">
<div class="block">
 Represents font fallback rule</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/FontFallBackRulesCollection.html" title="class in com.aspose.slides">FontFallBackRulesCollection</a></td>
<td class="colLast">
<div class="block">
 Represents a collection of FontFallBack rules, defined by user</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/Fonts.html" title="class in com.aspose.slides">Fonts</a></td>
<td class="colLast">
<div class="block">
 Fonts collection.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/FontScheme.html" title="class in com.aspose.slides">FontScheme</a></td>
<td class="colLast">
<div class="block">
 Stores theme-defined fonts.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/FontsLoader.html" title="class in com.aspose.slides">FontsLoader</a></td>
<td class="colLast">
<div class="block">
 Class for loading custom fonts defined by user.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/FontsManager.html" title="class in com.aspose.slides">FontsManager</a></td>
<td class="colLast">
<div class="block">
 Manages fonts across the presentation.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/FontSources.html" title="class in com.aspose.slides">FontSources</a></td>
<td class="colLast">
<div class="block">
 Provides file and memory sources for external fonts.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/FontStyle.html" title="class in com.aspose.slides">FontStyle</a></td>
<td class="colLast">
<div class="block">
 Font style enumeration</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/FontSubstCondition.html" title="class in com.aspose.slides">FontSubstCondition</a></td>
<td class="colLast">
<div class="block">
 Represents a rule fot font substitution</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/FontSubstitutionInfo.html" title="class in com.aspose.slides">FontSubstitutionInfo</a></td>
<td class="colLast">
<div class="block">
 This structure represents the information about the font replacement when it will be rendered.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/FontSubstRule.html" title="class in com.aspose.slides">FontSubstRule</a></td>
<td class="colLast">
<div class="block">
 Represents font subtituition information</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/FontSubstRuleCollection.html" title="class in com.aspose.slides">FontSubstRuleCollection</a></td>
<td class="colLast">
<div class="block">
 Represents collection of fonts substitution.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ForEach.html" title="class in com.aspose.slides">ForEach</a></td>
<td class="colLast">
<div class="block">
 Represents a group of methods intended to iterate over different <a href="../../../com/aspose/slides/Presentation.html" title="class in com.aspose.slides"><code>Presentation</code></a> model objects.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/Format.html" title="class in com.aspose.slides">Format</a></td>
<td class="colLast">
<div class="block">
 Represents chart format properties.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/FormatFactory.html" title="class in com.aspose.slides">FormatFactory</a></td>
<td class="colLast">
<div class="block">
 Allows to create formats via COM interface.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/FormatScheme.html" title="class in com.aspose.slides">FormatScheme</a></td>
<td class="colLast">
<div class="block">
 Stores theme-defined formats for the shapes.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/FrameTickEventArgs.html" title="class in com.aspose.slides">FrameTickEventArgs</a></td>
<td class="colLast">
<div class="block">
 Represents arguments of the <a href="../../../com/aspose/slides/PresentationPlayer.FrameTick.html" title="interface in com.aspose.slides"><code>PresentationPlayer.FrameTick</code></a> event.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/GeometryPath.html" title="class in com.aspose.slides">GeometryPath</a></td>
<td class="colLast">
<div class="block">
 Represents geometry path of GeometryShape</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/GeometryShape.html" title="class in com.aspose.slides">GeometryShape</a></td>
<td class="colLast">
<div class="block">
 Represents the parent class for all geometric shapes.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/GifOptions.html" title="class in com.aspose.slides">GifOptions</a></td>
<td class="colLast">
<div class="block">
 Represents GIF exporting options.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/GlitterTransition.html" title="class in com.aspose.slides">GlitterTransition</a></td>
<td class="colLast">
<div class="block">
 Glitter slide transition effect.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/GlobalLayoutSlideCollection.html" title="class in com.aspose.slides">GlobalLayoutSlideCollection</a></td>
<td class="colLast">
<div class="block">
 Represents a collection of all layout slides in presentation.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/Glow.html" title="class in com.aspose.slides">Glow</a></td>
<td class="colLast">
<div class="block">
 Represents a Glow effect, in which a color blurred outline 
 is added outside the edges of the object.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/GradientDirection.html" title="class in com.aspose.slides">GradientDirection</a></td>
<td class="colLast">
<div class="block">
 Represents the gradient style.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/GradientFormat.html" title="class in com.aspose.slides">GradientFormat</a></td>
<td class="colLast">
<div class="block">
 Represent a gradient format.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/GradientShape.html" title="class in com.aspose.slides">GradientShape</a></td>
<td class="colLast">
<div class="block">
 Represents the shape of gradient fill.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/GradientStop.html" title="class in com.aspose.slides">GradientStop</a></td>
<td class="colLast">
<div class="block">
 Represents a gradient format.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/GradientStopCollection.html" title="class in com.aspose.slides">GradientStopCollection</a></td>
<td class="colLast">
<div class="block">
 Represnts a collection of gradient stops.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/GradientStopCollectionEffectiveData.html" title="class in com.aspose.slides">GradientStopCollectionEffectiveData</a></td>
<td class="colLast">
<div class="block">
 Represents a collection of GradientStopData objects.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/GradientStopEffectiveData.html" title="class in com.aspose.slides">GradientStopEffectiveData</a></td>
<td class="colLast">
<div class="block">
 Immutable object which represents a gradient stop.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/GradientStyle.html" title="class in com.aspose.slides">GradientStyle</a></td>
<td class="colLast">
<div class="block">
 Represents the available gradient styles.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/GraphicalObject.html" title="class in com.aspose.slides">GraphicalObject</a></td>
<td class="colLast">
<div class="block">
 Represents abstract graphical object.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/GraphicalObjectLock.html" title="class in com.aspose.slides">GraphicalObjectLock</a></td>
<td class="colLast">
<div class="block">
 Determines which operations are disabled on the parent GraphicalObject.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/GrayScale.html" title="class in com.aspose.slides">GrayScale</a></td>
<td class="colLast">
<div class="block">
 Represents a Gray Scale effect.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/GroupShape.html" title="class in com.aspose.slides">GroupShape</a></td>
<td class="colLast">
<div class="block">
  Represents a group of shapes on a slide.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/GroupShapeLock.html" title="class in com.aspose.slides">GroupShapeLock</a></td>
<td class="colLast">
<div class="block">
 Determines which operations are disabled on the parent GroupShape.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/HandleRepeatedSpaces.html" title="class in com.aspose.slides">HandleRepeatedSpaces</a></td>
<td class="colLast">
<div class="block">
 Specifies how repeated regular space characters should be handled
 during Markdown export.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/HandoutLayoutingOptions.html" title="class in com.aspose.slides">HandoutLayoutingOptions</a></td>
<td class="colLast">
<div class="block">
 Represents the handout presentation layout mode for export.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/HandoutType.html" title="class in com.aspose.slides">HandoutType</a></td>
<td class="colLast">
<div class="block">
 Specifies how many slides and in what sequence will be placed on the page.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/HeadingPair.html" title="class in com.aspose.slides">HeadingPair</a></td>
<td class="colLast">
<div class="block">
 Represents a 'Heading pair' property of the document.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/HSL.html" title="class in com.aspose.slides">HSL</a></td>
<td class="colLast">
<div class="block">
 Represents a Hue/Saturation/Luminance effect.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/Html5Options.html" title="class in com.aspose.slides">Html5Options</a></td>
<td class="colLast">
<div class="block">
 Represents a HTML5 exporting options.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/HtmlExternalResolver.html" title="class in com.aspose.slides">HtmlExternalResolver</a></td>
<td class="colLast">Deprecated
<div class="block"><span class="deprecationComment">Obsolete.</span></div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/HtmlFormatter.html" title="class in com.aspose.slides">HtmlFormatter</a></td>
<td class="colLast">
<div class="block">
 Represents HTML file template.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/HtmlGenerator.html" title="class in com.aspose.slides">HtmlGenerator</a></td>
<td class="colLast">
<div class="block">
 Html generator.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/HtmlOptions.html" title="class in com.aspose.slides">HtmlOptions</a></td>
<td class="colLast">
<div class="block">
 Represents a HTML exporting options.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/Hyperlink.html" title="class in com.aspose.slides">Hyperlink</a></td>
<td class="colLast">
<div class="block">
 Represents a hyperlink.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/HyperlinkActionType.html" title="class in com.aspose.slides">HyperlinkActionType</a></td>
<td class="colLast">
<div class="block">
 Represents a type of hyperlink action.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/HyperlinkColorSource.html" title="class in com.aspose.slides">HyperlinkColorSource</a></td>
<td class="colLast">
<div class="block">
 Represents source of hyperlink color.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/HyperlinkManager.html" title="class in com.aspose.slides">HyperlinkManager</a></td>
<td class="colLast">
<div class="block">
 Provide hyperlinks management (adding, removing).</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/HyperlinkQueries.html" title="class in com.aspose.slides">HyperlinkQueries</a></td>
<td class="colLast">
<div class="block">
 Provide easy access to contained hyperlinks.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ImageCollection.html" title="class in com.aspose.slides">ImageCollection</a></td>
<td class="colLast">
<div class="block">
 Represents collection of PPImage.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ImageFormat.html" title="class in com.aspose.slides">ImageFormat</a></td>
<td class="colLast">
<div class="block">
 Represents the file format of the image.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ImagePixelFormat.html" title="class in com.aspose.slides">ImagePixelFormat</a></td>
<td class="colLast">
<div class="block">
 Specifies the pixel format for the generated images.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/Images.html" title="class in com.aspose.slides">Images</a></td>
<td class="colLast">
<div class="block">
 Methods to instantiate and work with <a href="../../../com/aspose/slides/IImage.html" title="interface in com.aspose.slides"><code>IImage</code></a>.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ImageTransformOCollectionEffectiveData.html" title="class in com.aspose.slides">ImageTransformOCollectionEffectiveData</a></td>
<td class="colLast">
<div class="block">
 Immutable object that represents a readonly collection of effective image transform effects.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ImageTransformOperation.html" title="class in com.aspose.slides">ImageTransformOperation</a></td>
<td class="colLast">
<div class="block">
 Represents abstract image transformation effect.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ImageTransformOperationCollection.html" title="class in com.aspose.slides">ImageTransformOperationCollection</a></td>
<td class="colLast">
<div class="block">
 Represents a collection of effects apllied to an image.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ImageTransformOperationFactory.html" title="class in com.aspose.slides">ImageTransformOperationFactory</a></td>
<td class="colLast">
<div class="block">
 Allows to create image transform operations</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/Ink.html" title="class in com.aspose.slides">Ink</a></td>
<td class="colLast">
<div class="block">
 Represents an ink object on a slide.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/InkActions.html" title="class in com.aspose.slides">InkActions</a></td>
<td class="colLast">
<div class="block">
 Represents the root of ink actions.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/InkBrush.html" title="class in com.aspose.slides">InkBrush</a></td>
<td class="colLast">
<div class="block">
 Represents an inkBrush object.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/InkEffectType.html" title="class in com.aspose.slides">InkEffectType</a></td>
<td class="colLast">
<div class="block">
 Specifies a set of predefined visual effects for ink rendering.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/InkOptions.html" title="class in com.aspose.slides">InkOptions</a></td>
<td class="colLast">
<div class="block">
 Provides options that control the look of Ink objects in exported document.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/InkTrace.html" title="class in com.aspose.slides">InkTrace</a></td>
<td class="colLast">
<div class="block">
 Represents an Trace object.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/InnerShadow.html" title="class in com.aspose.slides">InnerShadow</a></td>
<td class="colLast">
<div class="block">
 Represents a Inner Shadow effect.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/InOutTransition.html" title="class in com.aspose.slides">InOutTransition</a></td>
<td class="colLast">
<div class="block">
 In-Out slide transition effect.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/Input.html" title="class in com.aspose.slides">Input</a></td>
<td class="colLast">
<div class="block">
 Represents a collection of input elements (templates).</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/InterruptionToken.html" title="class in com.aspose.slides">InterruptionToken</a></td>
<td class="colLast">
<div class="block">
 This class represents the token to use for signaling long running tasks whether the interruption was requested.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/InterruptionTokenSource.html" title="class in com.aspose.slides">InterruptionTokenSource</a></td>
<td class="colLast">
<div class="block">
 Represents the source of <a href="../../../com/aspose/slides/InterruptionToken.html" title="class in com.aspose.slides"><code>InterruptionToken</code></a>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/LayoutPlaceholderManager.html" title="class in com.aspose.slides">LayoutPlaceholderManager</a></td>
<td class="colLast">
<div class="block">
 Represents manager that allows you to add placeholders to the layout slide.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/LayoutSlide.html" title="class in com.aspose.slides">LayoutSlide</a></td>
<td class="colLast">
<div class="block">
 Represents a layout slide.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/LayoutSlideCollection.html" title="class in com.aspose.slides">LayoutSlideCollection</a></td>
<td class="colLast">
<div class="block">
 Represents a base class for collection of a layout slides.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/LayoutSlideHeaderFooterManager.html" title="class in com.aspose.slides">LayoutSlideHeaderFooterManager</a></td>
<td class="colLast">
<div class="block">
 Represents manager which holds behavior of the layout slide footer, date-time, page number placeholders and all child placeholders.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/LayoutSlideThemeManager.html" title="class in com.aspose.slides">LayoutSlideThemeManager</a></td>
<td class="colLast">
<div class="block">
 Provides access to layout slide theme overriden.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/LayoutTargetType.html" title="class in com.aspose.slides">LayoutTargetType</a></td>
<td class="colLast">
<div class="block">
 If layout of the plot area defined manually this property specifies whether 
 to layout the plot area by its inside (not including axis and axis labels) or outside
 (including axis and axis labels).</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/LeftRightDirectionTransition.html" title="class in com.aspose.slides">LeftRightDirectionTransition</a></td>
<td class="colLast">
<div class="block">
 Left-right direction slide transition effect.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/LegacyDiagram.html" title="class in com.aspose.slides">LegacyDiagram</a></td>
<td class="colLast">
<div class="block">
 Represents a legacy diagram object.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/Legend.html" title="class in com.aspose.slides">Legend</a></td>
<td class="colLast">
<div class="block">
 Represents chart's legend properties.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/LegendDataLabelPosition.html" title="class in com.aspose.slides">LegendDataLabelPosition</a></td>
<td class="colLast">
<div class="block">
 Determines position of data labels.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/LegendEntryCollection.html" title="class in com.aspose.slides">LegendEntryCollection</a></td>
<td class="colLast">
<div class="block">
 Represents legends collection.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/LegendEntryProperties.html" title="class in com.aspose.slides">LegendEntryProperties</a></td>
<td class="colLast">
<div class="block">
 Represents legend properties of a chart.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/LegendPositionType.html" title="class in com.aspose.slides">LegendPositionType</a></td>
<td class="colLast">
<div class="block">
 Determines a position of legend on a chart.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/License.html" title="class in com.aspose.slides">License</a></td>
<td class="colLast">
<div class="block">Provides methods to license the component.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/LightingDirection.html" title="class in com.aspose.slides">LightingDirection</a></td>
<td class="colLast">
<div class="block">
 Constants which define light directions.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/LightRig.html" title="class in com.aspose.slides">LightRig</a></td>
<td class="colLast">
<div class="block">
 Represents LightRig.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/LightRigPresetType.html" title="class in com.aspose.slides">LightRigPresetType</a></td>
<td class="colLast">
<div class="block">
 Constants which define light preset types.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/LineAlignment.html" title="class in com.aspose.slides">LineAlignment</a></td>
<td class="colLast">
<div class="block">
 Represents the lines alignment type.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/LineArrowheadLength.html" title="class in com.aspose.slides">LineArrowheadLength</a></td>
<td class="colLast">
<div class="block">
 Represents the length of an arrowhead.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/LineArrowheadStyle.html" title="class in com.aspose.slides">LineArrowheadStyle</a></td>
<td class="colLast">
<div class="block">
 Represents the style of an arrowhead.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/LineArrowheadWidth.html" title="class in com.aspose.slides">LineArrowheadWidth</a></td>
<td class="colLast">
<div class="block">
 Represents the width of an arrowhead.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/LineCapStyle.html" title="class in com.aspose.slides">LineCapStyle</a></td>
<td class="colLast">
<div class="block">
 Represents the line cap style.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/LineDashStyle.html" title="class in com.aspose.slides">LineDashStyle</a></td>
<td class="colLast">
<div class="block">
 Represents the line dash style.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/LineFillFormat.html" title="class in com.aspose.slides">LineFillFormat</a></td>
<td class="colLast">
<div class="block">
 Represents properties for lines filling.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/LineFormat.html" title="class in com.aspose.slides">LineFormat</a></td>
<td class="colLast">
<div class="block">
 Represents format of a line.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/LineFormatCollection.html" title="class in com.aspose.slides">LineFormatCollection</a></td>
<td class="colLast">
<div class="block">
 Represents the collection of line styles.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/LineJoinStyle.html" title="class in com.aspose.slides">LineJoinStyle</a></td>
<td class="colLast">
<div class="block">
 Represents the lines join style.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/LineSketchType.html" title="class in com.aspose.slides">LineSketchType</a></td>
<td class="colLast">
<div class="block">
 Represents which sketch type or effect a shape has been assigned.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/LineStyle.html" title="class in com.aspose.slides">LineStyle</a></td>
<td class="colLast">
<div class="block">
 Represents the style of a line.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/LinkEmbedDecision.html" title="class in com.aspose.slides">LinkEmbedDecision</a></td>
<td class="colLast">
<div class="block">
 Determines how object will be processed during saving.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/LoadFormat.html" title="class in com.aspose.slides">LoadFormat</a></td>
<td class="colLast">
<div class="block">
 Indicates the format of the document that is to be loaded.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/LoadingStreamBehavior.html" title="class in com.aspose.slides">LoadingStreamBehavior</a></td>
<td class="colLast">
<div class="block">
 The <code>InputStream</code> passed to a method is considered as a Binary Large Object (BLOB) (see
 <a href="../../../com/aspose/slides/IBlobManagementOptions.html" title="interface in com.aspose.slides"><code>IBlobManagementOptions</code></a> description).</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/LoadOptions.html" title="class in com.aspose.slides">LoadOptions</a></td>
<td class="colLast">
<div class="block">
 Allows to specify additional options (such as format or default font) when loading a presentation.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/Luminance.html" title="class in com.aspose.slides">Luminance</a></td>
<td class="colLast">
<div class="block">
 Represents a Luminance effect.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/MarkdownExportType.html" title="class in com.aspose.slides">MarkdownExportType</a></td>
<td class="colLast">
<div class="block">
 Type of rendering document.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/MarkdownSaveOptions.html" title="class in com.aspose.slides">MarkdownSaveOptions</a></td>
<td class="colLast">
<div class="block">
 Represents options that control how presentation should be saved to markdown.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/Marker.html" title="class in com.aspose.slides">Marker</a></td>
<td class="colLast">
<div class="block">
 Represents marker of a chert.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/MarkerStyleType.html" title="class in com.aspose.slides">MarkerStyleType</a></td>
<td class="colLast">
<div class="block">
 Determines form of marker on chart's data point.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/MasterHandoutSlide.html" title="class in com.aspose.slides">MasterHandoutSlide</a></td>
<td class="colLast">
<div class="block">
 Represents master slide for handouts.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/MasterHandoutSlideHeaderFooterManager.html" title="class in com.aspose.slides">MasterHandoutSlideHeaderFooterManager</a></td>
<td class="colLast">
<div class="block">
 Represents manager which holds behavior of the master handout slide placeholders, including header placeholder.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/MasterLayoutSlideCollection.html" title="class in com.aspose.slides">MasterLayoutSlideCollection</a></td>
<td class="colLast">
<div class="block">
 Represents a collections of all layout slides of defined master slide.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/MasterNotesSlide.html" title="class in com.aspose.slides">MasterNotesSlide</a></td>
<td class="colLast">
<div class="block">
 Represents master slide for notes.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/MasterNotesSlideHeaderFooterManager.html" title="class in com.aspose.slides">MasterNotesSlideHeaderFooterManager</a></td>
<td class="colLast">
<div class="block">
 Represents manager which holds behavior of the master notes slide footer, date-time, page number placeholders and all child placeholders.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/MasterSlide.html" title="class in com.aspose.slides">MasterSlide</a></td>
<td class="colLast">
<div class="block">
 Represents a master slide in a presentation.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/MasterSlideCollection.html" title="class in com.aspose.slides">MasterSlideCollection</a></td>
<td class="colLast">
<div class="block">
 Represents a collection of master slides.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/MasterSlideHeaderFooterManager.html" title="class in com.aspose.slides">MasterSlideHeaderFooterManager</a></td>
<td class="colLast">
<div class="block">
 Represents manager which holds behavior of the master slide footer, date-time, page number placeholders and all child placeholders.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/MasterTheme.html" title="class in com.aspose.slides">MasterTheme</a></td>
<td class="colLast">
<div class="block">
 Represents a master theme.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/MasterThemeManager.html" title="class in com.aspose.slides">MasterThemeManager</a></td>
<td class="colLast">
<div class="block">
 Provides access to presentation master theme.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/MaterialPresetType.html" title="class in com.aspose.slides">MaterialPresetType</a></td>
<td class="colLast">
<div class="block">
 Constants which define material of shape.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/MathAccent.html" title="class in com.aspose.slides">MathAccent</a></td>
<td class="colLast">
<div class="block">
 Specifies the accent function, consisting of a base and a combining diacritical mark
 Example: 𝑎́</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/MathAccentFactory.html" title="class in com.aspose.slides">MathAccentFactory</a></td>
<td class="colLast">
<div class="block">
 Allows to create a math accent</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/MathArray.html" title="class in com.aspose.slides">MathArray</a></td>
<td class="colLast">
<div class="block">
 Specifies a vertical array of equations or any mathematical objects</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/MathArrayFactory.html" title="class in com.aspose.slides">MathArrayFactory</a></td>
<td class="colLast">
<div class="block">
 Allows to create a math array</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/MathBar.html" title="class in com.aspose.slides">MathBar</a></td>
<td class="colLast">
<div class="block">
 Specifies the bar function, consisting of a base argument and an overbar or underbar</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/MathBarFactory.html" title="class in com.aspose.slides">MathBarFactory</a></td>
<td class="colLast">
<div class="block">
 Allows to create a math bar</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/MathBlock.html" title="class in com.aspose.slides">MathBlock</a></td>
<td class="colLast">
<div class="block">
 Specifies an instance of mathematical text that contained within a MathParagraph and starts on its own line.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/MathBlockFactory.html" title="class in com.aspose.slides">MathBlockFactory</a></td>
<td class="colLast">
<div class="block">
 Allows to create a math block</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/MathBorderBox.html" title="class in com.aspose.slides">MathBorderBox</a></td>
<td class="colLast">
<div class="block">
 Draws a rectangular or some other border around the IMathElement.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/MathBorderBoxFactory.html" title="class in com.aspose.slides">MathBorderBoxFactory</a></td>
<td class="colLast">
<div class="block">
 Allows to create a math border box</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/MathBox.html" title="class in com.aspose.slides">MathBox</a></td>
<td class="colLast">
<div class="block">
 Specifies the logical boxing (packaging) of mathematical element.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/MathBoxFactory.html" title="class in com.aspose.slides">MathBoxFactory</a></td>
<td class="colLast">
<div class="block">
 Allows to create a math box</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/MathDelimiter.html" title="class in com.aspose.slides">MathDelimiter</a></td>
<td class="colLast">
<div class="block">
 Specifies the delimiter object, consisting of opening and closing characters (such as parentheses, 
 braces, brackets, and vertical bars), and one or more mathematical elements inside, separated by a specified character.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/MathDelimiterFactory.html" title="class in com.aspose.slides">MathDelimiterFactory</a></td>
<td class="colLast">
<div class="block">
 Allows to create a math delimiter</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/MathDelimiterShape.html" title="class in com.aspose.slides">MathDelimiterShape</a></td>
<td class="colLast">
<div class="block">
 The location and size of the delimiters relative to the content of the operands</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/MathElementBase.html" title="class in com.aspose.slides">MathElementBase</a></td>
<td class="colLast">
<div class="block">
 Base class for IMathElement with the implementation of some methods that are common to all inherited classes
 For internal use only.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/MathematicalText.html" title="class in com.aspose.slides">MathematicalText</a></td>
<td class="colLast">
<div class="block">
 Mathematical text</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/MathematicalTextFactory.html" title="class in com.aspose.slides">MathematicalTextFactory</a></td>
<td class="colLast">
<div class="block">
 Allows to create a MathematicalText element</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/MathFraction.html" title="class in com.aspose.slides">MathFraction</a></td>
<td class="colLast">
<div class="block">
 Specifies the fraction object, consisting of a numerator and denominator separated by a fraction bar.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/MathFractionFactory.html" title="class in com.aspose.slides">MathFractionFactory</a></td>
<td class="colLast">
<div class="block">
 Allows to create a math fraction</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/MathFractionTypes.html" title="class in com.aspose.slides">MathFractionTypes</a></td>
<td class="colLast">
<div class="block">
 Fraction Types</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/MathFunction.html" title="class in com.aspose.slides">MathFunction</a></td>
<td class="colLast">
<div class="block">
 Specifies a function of an argument.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/MathFunctionFactory.html" title="class in com.aspose.slides">MathFunctionFactory</a></td>
<td class="colLast">
<div class="block">
 Allows to create a math function</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/MathFunctionsOfOneArgument.html" title="class in com.aspose.slides">MathFunctionsOfOneArgument</a></td>
<td class="colLast">
<div class="block">
 Common mathematical functions of one argument</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/MathFunctionsOfTwoArguments.html" title="class in com.aspose.slides">MathFunctionsOfTwoArguments</a></td>
<td class="colLast">
<div class="block">
 Common mathematical functions of two arguments</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/MathGroupingCharacter.html" title="class in com.aspose.slides">MathGroupingCharacter</a></td>
<td class="colLast">
<div class="block">
 Specifies a grouping symbol above or below an expression, usually to highlight the relationship between elements</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/MathGroupingCharacterFactory.html" title="class in com.aspose.slides">MathGroupingCharacterFactory</a></td>
<td class="colLast">
<div class="block">
 Allows to create a math grouping character</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/MathHorizontalAlignment.html" title="class in com.aspose.slides">MathHorizontalAlignment</a></td>
<td class="colLast">
<div class="block">
 Horizontal Alignment</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/MathIntegralTypes.html" title="class in com.aspose.slides">MathIntegralTypes</a></td>
<td class="colLast">
<div class="block">
 Mathematical integral types</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/MathJustification.html" title="class in com.aspose.slides">MathJustification</a></td>
<td class="colLast">
<div class="block">
 Specifies justification of the math paragraph (a series of adjacent instances of mathematical text within the same paragraph)</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/MathLeftSubSuperscriptElement.html" title="class in com.aspose.slides">MathLeftSubSuperscriptElement</a></td>
<td class="colLast">
<div class="block">
 Specifies the Sub-Superscript object, which consists of a base 
 and a subscript and superscript placed to the left of the base.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/MathLimit.html" title="class in com.aspose.slides">MathLimit</a></td>
<td class="colLast">
<div class="block">
 Specifies the Limit object, consisting of text on the baseline and reduced-size text immediately above or below it.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/MathLimitFactory.html" title="class in com.aspose.slides">MathLimitFactory</a></td>
<td class="colLast">
<div class="block">
 Allows to create IMathLimit</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/MathLimitLocations.html" title="class in com.aspose.slides">MathLimitLocations</a></td>
<td class="colLast">
<div class="block">
 Location of limits (subscript/superscript) in n-ary operators.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/MathMatrix.html" title="class in com.aspose.slides">MathMatrix</a></td>
<td class="colLast">
<div class="block">
 Specifies the Matrix object, consisting of child elements laid out in one or more rows and columns.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/MathMatrixFactory.html" title="class in com.aspose.slides">MathMatrixFactory</a></td>
<td class="colLast">
<div class="block">
 Allows to create a math matrix</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/MathNaryOperator.html" title="class in com.aspose.slides">MathNaryOperator</a></td>
<td class="colLast">
<div class="block">
 Specifies an N-ary mathematical object, such as Summation and Integral.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/MathNaryOperatorFactory.html" title="class in com.aspose.slides">MathNaryOperatorFactory</a></td>
<td class="colLast">
<div class="block">
 Allows to create IMathNaryOperator</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/MathNaryOperatorTypes.html" title="class in com.aspose.slides">MathNaryOperatorTypes</a></td>
<td class="colLast">
<div class="block">
 Nary operator IMathNaryOperator types (excluding integrals)
 For integrals <a href="../../../com/aspose/slides/MathIntegralTypes.html" title="class in com.aspose.slides"><code>MathIntegralTypes</code></a></div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/MathParagraph.html" title="class in com.aspose.slides">MathParagraph</a></td>
<td class="colLast">
<div class="block">
 Mathematical paragraph that is a container for mathematical blocks (IMathBlock)</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/MathParagraphFactory.html" title="class in com.aspose.slides">MathParagraphFactory</a></td>
<td class="colLast">
<div class="block">
 Allows to create a math paragraph</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/MathPortion.html" title="class in com.aspose.slides">MathPortion</a></td>
<td class="colLast">
<div class="block">
 Represents a portion with mathematical context inside.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/MathRadical.html" title="class in com.aspose.slides">MathRadical</a></td>
<td class="colLast">
<div class="block">
 Specifies the radical function, consisting of a base, and an optional degree.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/MathRadicalFactory.html" title="class in com.aspose.slides">MathRadicalFactory</a></td>
<td class="colLast">
<div class="block">
 Allows to create math radical</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/MathRightSubSuperscriptElement.html" title="class in com.aspose.slides">MathRightSubSuperscriptElement</a></td>
<td class="colLast">
<div class="block">
 Specifies the Sub-Superscript object, which consists of a base 
 and a subscript and superscript placed to the right of the base.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/MathRightSubSuperscriptElementFactory.html" title="class in com.aspose.slides">MathRightSubSuperscriptElementFactory</a></td>
<td class="colLast">
<div class="block">
 Allows to create IMathRightSubSuperscriptElementFactory</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/MathRowSpacingRule.html" title="class in com.aspose.slides">MathRowSpacingRule</a></td>
<td class="colLast">
<div class="block">
 The type of vertical spacing between columns in a matrix or array</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/MathSpacingRules.html" title="class in com.aspose.slides">MathSpacingRules</a></td>
<td class="colLast">
<div class="block">
 Types of gap (horizontal spacing) between columns of a matrix</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/MathSubscriptElement.html" title="class in com.aspose.slides">MathSubscriptElement</a></td>
<td class="colLast">
<div class="block">
 Specifies the subscript object, which consists of a base 
 and a reduced-size subscript placed below and to the right.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/MathSubscriptElementFactory.html" title="class in com.aspose.slides">MathSubscriptElementFactory</a></td>
<td class="colLast">
<div class="block">
 Allows to create IMathSubscriptElement</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/MathSuperscriptElement.html" title="class in com.aspose.slides">MathSuperscriptElement</a></td>
<td class="colLast">
<div class="block">
 Specifies the superscript object, which consists of a base 
 and a reduced-size superscript placed above and to the right</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/MathSuperscriptElementFactory.html" title="class in com.aspose.slides">MathSuperscriptElementFactory</a></td>
<td class="colLast">
<div class="block">
 Allows to create IMathSuperscriptElement</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/MathTopBotPositions.html" title="class in com.aspose.slides">MathTopBotPositions</a></td>
<td class="colLast">
<div class="block">
 Top/bottom positions enumeration</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/MathVerticalAlignment.html" title="class in com.aspose.slides">MathVerticalAlignment</a></td>
<td class="colLast">
<div class="block">
 Vertical Alignment</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/Metered.html" title="class in com.aspose.slides">Metered</a></td>
<td class="colLast">
<div class="block">Provides methods to set metered key.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ModernComment.html" title="class in com.aspose.slides">ModernComment</a></td>
<td class="colLast">
<div class="block">
 Represents a comment on a slide.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ModernCommentStatus.html" title="class in com.aspose.slides">ModernCommentStatus</a></td>
<td class="colLast">
<div class="block">
 Represents the status of a modern comment.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/MorphTransition.html" title="class in com.aspose.slides">MorphTransition</a></td>
<td class="colLast">
<div class="block">
 Ripple slide transition effect.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/MotionCmdPath.html" title="class in com.aspose.slides">MotionCmdPath</a></td>
<td class="colLast">
<div class="block">
 Represent one command of a path.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/MotionCommandPathType.html" title="class in com.aspose.slides">MotionCommandPathType</a></td>
<td class="colLast">
<div class="block">
 Represent types of command for animation motion effect behavior.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/MotionEffect.html" title="class in com.aspose.slides">MotionEffect</a></td>
<td class="colLast">
<div class="block">
 Represent motion effect behavior of effect.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/MotionOriginType.html" title="class in com.aspose.slides">MotionOriginType</a></td>
<td class="colLast">
<div class="block">
 Specifies what the origin of the motion path is relative to.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/MotionPath.html" title="class in com.aspose.slides">MotionPath</a></td>
<td class="colLast">
<div class="block">
 Represent motion path.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/MotionPathEditMode.html" title="class in com.aspose.slides">MotionPathEditMode</a></td>
<td class="colLast">
<div class="block">
 Specifies how the motion path moves when the target shape is moved</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/MotionPathPointsType.html" title="class in com.aspose.slides">MotionPathPointsType</a></td>
<td class="colLast">
<div class="block">
 Represent types of points in animation motion path.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/NewLineType.html" title="class in com.aspose.slides">NewLineType</a></td>
<td class="colLast">
<div class="block">
 Type of new line that will be used in generated document.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/NormalViewProperties.html" title="class in com.aspose.slides">NormalViewProperties</a></td>
<td class="colLast">
<div class="block">
 Represents normal view properties.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/NormalViewRestoredProperties.html" title="class in com.aspose.slides">NormalViewRestoredProperties</a></td>
<td class="colLast">
<div class="block">
 Specifies the sizing of the slide region ((width when a child of restoredTop, height when a
 child of restoredLeft) of the normal view, when the region is of a variable restored size(neither minimized nor maximized).</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/NotesCommentsLayoutingOptions.html" title="class in com.aspose.slides">NotesCommentsLayoutingOptions</a></td>
<td class="colLast">
<div class="block">
 Provides options that control the look of layouting of notes and comments in exported document.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/NotesPositions.html" title="class in com.aspose.slides">NotesPositions</a></td>
<td class="colLast">
<div class="block">
 Represents the rule to render notes into exported document</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/NotesSize.html" title="class in com.aspose.slides">NotesSize</a></td>
<td class="colLast">
<div class="block">
 Represents a size of notes slide.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/NotesSlide.html" title="class in com.aspose.slides">NotesSlide</a></td>
<td class="colLast">
<div class="block">
 Represents a notes slide in a presentation.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/NotesSlideHeaderFooterManager.html" title="class in com.aspose.slides">NotesSlideHeaderFooterManager</a></td>
<td class="colLast">
<div class="block">
 Represents manager which holds behavior of the notes slide placeholders, including header placeholder.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/NotesSlideManager.html" title="class in com.aspose.slides">NotesSlideManager</a></td>
<td class="colLast">
<div class="block">
 Notes slide manager.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/NotesSlideThemeManager.html" title="class in com.aspose.slides">NotesSlideThemeManager</a></td>
<td class="colLast">
<div class="block">
 Provides access to notes slide theme overriden.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/NullableBool.html" title="class in com.aspose.slides">NullableBool</a></td>
<td class="colLast">
<div class="block">
 Represents triple boolean values.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/NumberedBulletStyle.html" title="class in com.aspose.slides">NumberedBulletStyle</a></td>
<td class="colLast">
<div class="block">
 Represents the style of the numbered bullets.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/OleEmbeddedDataInfo.html" title="class in com.aspose.slides">OleEmbeddedDataInfo</a></td>
<td class="colLast">
<div class="block">
 Represents embedded data info for OLE object.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/OleObjectFrame.html" title="class in com.aspose.slides">OleObjectFrame</a></td>
<td class="colLast">
<div class="block">
 Represents an OLE object on a slide.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/OpenAIWebClient.html" title="class in com.aspose.slides">OpenAIWebClient</a></td>
<td class="colLast">
<div class="block">
 Build-in lightweight OpenAI web client</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/OptionalBlackTransition.html" title="class in com.aspose.slides">OptionalBlackTransition</a></td>
<td class="colLast">
<div class="block">
 Optional black slide transition effect.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/OrganizationChartLayoutType.html" title="class in com.aspose.slides">OrganizationChartLayoutType</a></td>
<td class="colLast">
<div class="block">
 Represents formatting type the child nodes in an organization chart</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/Orientation.html" title="class in com.aspose.slides">Orientation</a></td>
<td class="colLast">
<div class="block">
 Represents the orientation of a shape.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/OrientationTransition.html" title="class in com.aspose.slides">OrientationTransition</a></td>
<td class="colLast">
<div class="block">
 Orientation slide transition effect.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/OuterShadow.html" title="class in com.aspose.slides">OuterShadow</a></td>
<td class="colLast">
<div class="block">
 Represents an Outer Shadow effect.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/Output.html" title="class in com.aspose.slides">Output</a></td>
<td class="colLast">
<div class="block">
 Represents a collection of output elements for IWebDocument.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/OutputFile.html" title="class in com.aspose.slides">OutputFile</a></td>
<td class="colLast">
<div class="block">
 Represents an output file.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/OverrideTheme.html" title="class in com.aspose.slides">OverrideTheme</a></td>
<td class="colLast">
<div class="block">
 Represents a overriding theme.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/Paragraph.html" title="class in com.aspose.slides">Paragraph</a></td>
<td class="colLast">
<div class="block">
 Represents a paragraph of text.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ParagraphCollection.html" title="class in com.aspose.slides">ParagraphCollection</a></td>
<td class="colLast">
<div class="block">
 Represents a collection of a paragraphs.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ParagraphFactory.html" title="class in com.aspose.slides">ParagraphFactory</a></td>
<td class="colLast">
<div class="block">
 Allows to create paragraphs</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ParagraphFormat.html" title="class in com.aspose.slides">ParagraphFormat</a></td>
<td class="colLast">
<div class="block">
 This class contains the paragraph formatting properties.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ParentLabelLayoutType.html" title="class in com.aspose.slides">ParentLabelLayoutType</a></td>
<td class="colLast">
<div class="block">
 Represents layout of category data labels.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/PathCommandType.html" title="class in com.aspose.slides">PathCommandType</a></td>
<td class="colLast">
<div class="block">
 Represents graphics path commands</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/PathFillModeType.html" title="class in com.aspose.slides">PathFillModeType</a></td>
<td class="colLast">
<div class="block">
 Specifies the manner in which a path should be filled</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/PathSegment.html" title="class in com.aspose.slides">PathSegment</a></td>
<td class="colLast">
<div class="block">
 Represents segment of graphics path of GeometryShape</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/PatternFormat.html" title="class in com.aspose.slides">PatternFormat</a></td>
<td class="colLast">
<div class="block">
 Represents a pattern to fill a shape.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/PatternStyle.html" title="class in com.aspose.slides">PatternStyle</a></td>
<td class="colLast">
<div class="block">
 Represents the pattern style.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/PdfAccessPermissions.html" title="class in com.aspose.slides">PdfAccessPermissions</a></td>
<td class="colLast">
<div class="block">
 Contains a set of flags specifying which access permissions should be granted when the document is opened with 
 user access.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/PdfCompliance.html" title="class in com.aspose.slides">PdfCompliance</a></td>
<td class="colLast">
<div class="block">
 Constants which define the PDF standards compliance level.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/PdfImportOptions.html" title="class in com.aspose.slides">PdfImportOptions</a></td>
<td class="colLast">
<div class="block">
 Represents the PDF import options</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/PdfOptions.html" title="class in com.aspose.slides">PdfOptions</a></td>
<td class="colLast">
<div class="block">
 Provides options that control how a presentation is saved in Pdf format.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/PdfTextCompression.html" title="class in com.aspose.slides">PdfTextCompression</a></td>
<td class="colLast">
<div class="block">
 Constants which define the type of a compression applied to all content in the PDF file except images.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/PersistenceType.html" title="class in com.aspose.slides">PersistenceType</a></td>
<td class="colLast">
<div class="block">
 Specifies the method used to store properties of the ActiveX control.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/Picture.html" title="class in com.aspose.slides">Picture</a></td>
<td class="colLast">
<div class="block">
 Represents a picture in a presentation.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/PictureFillFormat.html" title="class in com.aspose.slides">PictureFillFormat</a></td>
<td class="colLast">
<div class="block">
 Represents a picture fill style.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/PictureFillMode.html" title="class in com.aspose.slides">PictureFillMode</a></td>
<td class="colLast">
<div class="block">
 Determines how picture will fill area.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/PictureFrame.html" title="class in com.aspose.slides">PictureFrame</a></td>
<td class="colLast">
<div class="block">
 Represents a frame with a picture inside.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/PictureFrameLock.html" title="class in com.aspose.slides">PictureFrameLock</a></td>
<td class="colLast">
<div class="block">
 Determines which operations are disabled on the parent PictureFrame.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/PicturesCompression.html" title="class in com.aspose.slides">PicturesCompression</a></td>
<td class="colLast">
<div class="block">
 Represents the pictures compression level</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/PictureType.html" title="class in com.aspose.slides">PictureType</a></td>
<td class="colLast">
<div class="block">
 Determines mode of bar picture filling.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/PieSplitCustomPointCollection.html" title="class in com.aspose.slides">PieSplitCustomPointCollection</a></td>
<td class="colLast">
<div class="block">
 Represents a collection of points for splitting point in a bar-of-pie or pie-of-pie chart with a custom split.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/PieSplitType.html" title="class in com.aspose.slides">PieSplitType</a></td>
<td class="colLast">
<div class="block">
 Represents a type of splitting points in the second pie or bar 
 on a pie-of-pie or bar-of-pie chart.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/Placeholder.html" title="class in com.aspose.slides">Placeholder</a></td>
<td class="colLast">
<div class="block">
 Represents a placeholder on a slide.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/PlaceholderSize.html" title="class in com.aspose.slides">PlaceholderSize</a></td>
<td class="colLast">
<div class="block">
 Represents the size of a placeholder.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/PlaceholderType.html" title="class in com.aspose.slides">PlaceholderType</a></td>
<td class="colLast">
<div class="block">
 Represents the type of a placeholder.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/Point.html" title="class in com.aspose.slides">Point</a></td>
<td class="colLast">
<div class="block">
 Represent animation point.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/PointCollection.html" title="class in com.aspose.slides">PointCollection</a></td>
<td class="colLast">
<div class="block">
 Represent collection of animation points.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/Portion.html" title="class in com.aspose.slides">Portion</a></td>
<td class="colLast">
<div class="block">
 Represents a portion of text inside a text paragraph.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/PortionCollection.html" title="class in com.aspose.slides">PortionCollection</a></td>
<td class="colLast">
<div class="block">
 Represents a collection of portions.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/PortionFactory.html" title="class in com.aspose.slides">PortionFactory</a></td>
<td class="colLast">
<div class="block">
 Allows to create test portions</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/PortionFormat.html" title="class in com.aspose.slides">PortionFormat</a></td>
<td class="colLast">
<div class="block">
 This class contains the text portion formatting properties.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/PPImage.html" title="class in com.aspose.slides">PPImage</a></td>
<td class="colLast">
<div class="block">
 Represents an image in a presentation.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/PptOptions.html" title="class in com.aspose.slides">PptOptions</a></td>
<td class="colLast">
<div class="block">
 Provides options that control how a presentation is saved in PPT format.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/PptxOptions.html" title="class in com.aspose.slides">PptxOptions</a></td>
<td class="colLast">
<div class="block">
 Represents options for saving OpenXml presentations (PPTX, PPSX, POTX, PPTM, PPSM, POTM).</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/Presentation.html" title="class in com.aspose.slides">Presentation</a></td>
<td class="colLast">
<div class="block">
  Represents a Microsoft PowerPoint presentation.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/PresentationAnimationsGenerator.html" title="class in com.aspose.slides">PresentationAnimationsGenerator</a></td>
<td class="colLast">
<div class="block">
 Represents a generator of the animations in the <a href="../../../com/aspose/slides/Presentation.html" title="class in com.aspose.slides"><code>Presentation</code></a>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/PresentationContentAmountType.html" title="class in com.aspose.slides">PresentationContentAmountType</a></td>
<td class="colLast">
<div class="block">
 Specifies the amount of content included in the generated presentation, influencing both the number of slides and the level of detail per slide.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/PresentationFactory.html" title="class in com.aspose.slides">PresentationFactory</a></td>
<td class="colLast">
<div class="block">
 Allows to create presentation via COM interface</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/PresentationHeaderFooterManager.html" title="class in com.aspose.slides">PresentationHeaderFooterManager</a></td>
<td class="colLast">
<div class="block">
 Represents manager which holds behavior of all footer, date-time and page number placeholders of presentation.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/PresentationInfo.html" title="class in com.aspose.slides">PresentationInfo</a></td>
<td class="colLast">
<div class="block">
 Information about presentation file</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/PresentationLockingBehavior.html" title="class in com.aspose.slides">PresentationLockingBehavior</a></td>
<td class="colLast">
<div class="block">
 Represents the behavior regarding treating the <a href="../../../com/aspose/slides/IPresentation.html" title="interface in com.aspose.slides"><code>IPresentation</code></a> source (file or 
 <code>InputStream</code>) while loading and working with an instance of <a href="../../../com/aspose/slides/IPresentation.html" title="interface in com.aspose.slides"><code>IPresentation</code></a>.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/PresentationPlayer.html" title="class in com.aspose.slides">PresentationPlayer</a></td>
<td class="colLast">
<div class="block">
 Represents the player of animations associated with the <a href="../../../com/aspose/slides/Presentation.html" title="class in com.aspose.slides"><code>Presentation</code></a>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/PresentationText.html" title="class in com.aspose.slides">PresentationText</a></td>
<td class="colLast">
<div class="block">
 Represents the text extracted from the presentation</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/PresentedBySpeaker.html" title="class in com.aspose.slides">PresentedBySpeaker</a></td>
<td class="colLast">
<div class="block">
 Presented by a speaker (full screen)</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/PresetColor.html" title="class in com.aspose.slides">PresetColor</a></td>
<td class="colLast">
<div class="block">
 Represents predefined color presets.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/PresetShadow.html" title="class in com.aspose.slides">PresetShadow</a></td>
<td class="colLast">
<div class="block">
 Represents a Preset Shadow effect.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/PresetShadowType.html" title="class in com.aspose.slides">PresetShadowType</a></td>
<td class="colLast">
<div class="block">
 Represents a preset for a shadow effect.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/PropertyCalcModeType.html" title="class in com.aspose.slides">PropertyCalcModeType</a></td>
<td class="colLast">
<div class="block">
 Represent calc mode for animation property.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/PropertyEffect.html" title="class in com.aspose.slides">PropertyEffect</a></td>
<td class="colLast">
<div class="block">
 Represent property effect behavior.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/PropertyValueType.html" title="class in com.aspose.slides">PropertyValueType</a></td>
<td class="colLast">
<div class="block">
 Represent property value types.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ProtectionManager.html" title="class in com.aspose.slides">ProtectionManager</a></td>
<td class="colLast">
<div class="block">
 Presentation password protection management.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/PVIObject.html" title="class in com.aspose.slides">PVIObject</a></td>
<td class="colLast">
<div class="block">
 Encapsulates basic service infrastructure for objects can be a subject of property value inheritance.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/QuartileMethodType.html" title="class in com.aspose.slides">QuartileMethodType</a></td>
<td class="colLast">
<div class="block">
 Returns type of quartile method</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/RectangleAlignment.html" title="class in com.aspose.slides">RectangleAlignment</a></td>
<td class="colLast">
<div class="block">
 Defines 2-dimension allignment.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/Reflection.html" title="class in com.aspose.slides">Reflection</a></td>
<td class="colLast">
<div class="block">
 Represents a Reflection effect.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/RenderingOptions.html" title="class in com.aspose.slides">RenderingOptions</a></td>
<td class="colLast">
<div class="block">
 Provides options that control how a presentation/slide is rendered.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ResourceLoadingAction.html" title="class in com.aspose.slides">ResourceLoadingAction</a></td>
<td class="colLast">
<div class="block">
 Specifies the mode of external resource loading.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ResponsiveHtmlController.html" title="class in com.aspose.slides">ResponsiveHtmlController</a></td>
<td class="colLast">
<div class="block">
 Responsive HTML Controller</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ReturnAction.html" title="class in com.aspose.slides">ReturnAction</a></td>
<td class="colLast">
<div class="block">
 Represents warning callback decision options.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/RevealTransition.html" title="class in com.aspose.slides">RevealTransition</a></td>
<td class="colLast">
<div class="block">
 Reveal slide transition effect.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/RippleTransition.html" title="class in com.aspose.slides">RippleTransition</a></td>
<td class="colLast">
<div class="block">
 Ripple slide transition effect.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/Rotation3D.html" title="class in com.aspose.slides">Rotation3D</a></td>
<td class="colLast">
<div class="block">
 Represents 3D rotation of a chart.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/RotationEffect.html" title="class in com.aspose.slides">RotationEffect</a></td>
<td class="colLast">
<div class="block">
 Represent rotation behavior of effect.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/Row.html" title="class in com.aspose.slides">Row</a></td>
<td class="colLast">
<div class="block">
 Represents a row in a table.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/RowCollection.html" title="class in com.aspose.slides">RowCollection</a></td>
<td class="colLast">
<div class="block">
 Represents table row collection.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/RowFormat.html" title="class in com.aspose.slides">RowFormat</a></td>
<td class="colLast">
<div class="block">
 Represents format of a table row.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/SaveFormat.html" title="class in com.aspose.slides">SaveFormat</a></td>
<td class="colLast">
<div class="block">
 Constants which define the format of a saved presentation.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/SaveOptions.html" title="class in com.aspose.slides">SaveOptions</a></td>
<td class="colLast">
<div class="block">
 Abstract class with options that control how a presentation is saved.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/SaveOptionsFactory.html" title="class in com.aspose.slides">SaveOptionsFactory</a></td>
<td class="colLast">
<div class="block">
 Allows to create save options' instances</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ScaleEffect.html" title="class in com.aspose.slides">ScaleEffect</a></td>
<td class="colLast">
<div class="block">
 Represents animation scale effect.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/SchemeColor.html" title="class in com.aspose.slides">SchemeColor</a></td>
<td class="colLast">
<div class="block">
 Represents colors in a color scheme.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/Section.html" title="class in com.aspose.slides">Section</a></td>
<td class="colLast">
<div class="block">
 Represents section of slides.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/SectionCollection.html" title="class in com.aspose.slides">SectionCollection</a></td>
<td class="colLast">
<div class="block">
 Represents a collection of sections.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/SectionSlideCollection.html" title="class in com.aspose.slides">SectionSlideCollection</a></td>
<td class="colLast">
<div class="block">
 Represents a collection of slides in the section.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/SectionZoomFrame.html" title="class in com.aspose.slides">SectionZoomFrame</a></td>
<td class="colLast">
<div class="block">
 Represents a Section Zoom object in a slide.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/Sequence.html" title="class in com.aspose.slides">Sequence</a></td>
<td class="colLast">
<div class="block">
 Represents sequence (collection of effects).</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/SequenceCollection.html" title="class in com.aspose.slides">SequenceCollection</a></td>
<td class="colLast">
<div class="block">
 Represents collection of interactive sequences.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/SetEffect.html" title="class in com.aspose.slides">SetEffect</a></td>
<td class="colLast">
<div class="block">
 Represents a set effect for an animation behavior.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/Shape.html" title="class in com.aspose.slides">Shape</a></td>
<td class="colLast">
<div class="block">
  Represents a shape on a slide.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ShapeAdjustmentType.html" title="class in com.aspose.slides">ShapeAdjustmentType</a></td>
<td class="colLast">
<div class="block">
 Specifies different types of shape adjustment values.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ShapeBevel.html" title="class in com.aspose.slides">ShapeBevel</a></td>
<td class="colLast">
<div class="block">
 Contains the properties of shape's main face relief.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ShapeCollection.html" title="class in com.aspose.slides">ShapeCollection</a></td>
<td class="colLast">
<div class="block">
 Represents a collection of shapes.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ShapeElement.html" title="class in com.aspose.slides">ShapeElement</a></td>
<td class="colLast">
<div class="block">
 Represents a part of shape with same outline and fill properties.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ShapeElementFillSource.html" title="class in com.aspose.slides">ShapeElementFillSource</a></td>
<td class="colLast">
<div class="block">
 Represents how shape element should be filled.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ShapeElementStrokeSource.html" title="class in com.aspose.slides">ShapeElementStrokeSource</a></td>
<td class="colLast">
<div class="block">
 Represents how shape element should be drawn.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ShapeFrame.html" title="class in com.aspose.slides">ShapeFrame</a></td>
<td class="colLast">
<div class="block">
 Represents shape frame's properties.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ShapesAlignmentType.html" title="class in com.aspose.slides">ShapesAlignmentType</a></td>
<td class="colLast">
<div class="block">
 Defines a way to change the placement of selected shapes on the slide.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ShapeStyle.html" title="class in com.aspose.slides">ShapeStyle</a></td>
<td class="colLast">
<div class="block">
 Represent shape's style reference.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ShapeThumbnailBounds.html" title="class in com.aspose.slides">ShapeThumbnailBounds</a></td>
<td class="colLast">
<div class="block">
 Enumeration of types of shape thumbnail bounds.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ShapeType.html" title="class in com.aspose.slides">ShapeType</a></td>
<td class="colLast">
<div class="block">
 Represents preset geometry of geometry shapes.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ShapeUtil.html" title="class in com.aspose.slides">ShapeUtil</a></td>
<td class="colLast">
<div class="block">
 Offer methods which helps to process shapes objects.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ShredTransition.html" title="class in com.aspose.slides">ShredTransition</a></td>
<td class="colLast">
<div class="block">
 Shred slide transition effect.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/SideDirectionTransition.html" title="class in com.aspose.slides">SideDirectionTransition</a></td>
<td class="colLast">
<div class="block">
 Side direction slide transition effect.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/SketchFormat.html" title="class in com.aspose.slides">SketchFormat</a></td>
<td class="colLast">
<div class="block">
 Represents properties for lines sketch format.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/Slide.html" title="class in com.aspose.slides">Slide</a></td>
<td class="colLast">
<div class="block">
  Represents a slide in a presentation.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/SlideCollection.html" title="class in com.aspose.slides">SlideCollection</a></td>
<td class="colLast">
<div class="block">
 Represents a collection of a slides.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/SlideHeaderFooterManager.html" title="class in com.aspose.slides">SlideHeaderFooterManager</a></td>
<td class="colLast">
<div class="block">
 Represents manager which holds behavior of the slide footer, date-time, page number placeholders.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/SlideImageFormat.html" title="class in com.aspose.slides">SlideImageFormat</a></td>
<td class="colLast">
<div class="block">
 Determines format in which slide image will be saved for presentation to HTML export.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/SlideLayoutType.html" title="class in com.aspose.slides">SlideLayoutType</a></td>
<td class="colLast">
<div class="block">
 Represents the slide layout type.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/SlideOrientation.html" title="class in com.aspose.slides">SlideOrientation</a></td>
<td class="colLast">
<div class="block">
 Represents the slide orientation.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/SlidesAIAgent.html" title="class in com.aspose.slides">SlidesAIAgent</a></td>
<td class="colLast">
<div class="block">
 Provides AI-powered features for processing presentations.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/SlideShowSettings.html" title="class in com.aspose.slides">SlideShowSettings</a></td>
<td class="colLast">
<div class="block">
 Represents the slide show settings for the presentation.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/SlideShowTransition.html" title="class in com.aspose.slides">SlideShowTransition</a></td>
<td class="colLast">
<div class="block">
 Represents slide show transition.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/SlideShowType.html" title="class in com.aspose.slides">SlideShowType</a></td>
<td class="colLast">
<div class="block">
  Base slide show settings.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/SlideSize.html" title="class in com.aspose.slides">SlideSize</a></td>
<td class="colLast">
<div class="block">
 Represents the size and orientation of a slide.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/SlideSizeScaleType.html" title="class in com.aspose.slides">SlideSizeScaleType</a></td>
<td class="colLast">
<div class="block">
 Represents the scale type of slide content.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/SlideSizeType.html" title="class in com.aspose.slides">SlideSizeType</a></td>
<td class="colLast">
<div class="block">
 Represents the slide size preset.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/SlidesRange.html" title="class in com.aspose.slides">SlidesRange</a></td>
<td class="colLast">
<div class="block">
 Slides range</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/SlideThemeManager.html" title="class in com.aspose.slides">SlideThemeManager</a></td>
<td class="colLast">
<div class="block">
 Provides access to slide theme overriden.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/SlideUtil.html" title="class in com.aspose.slides">SlideUtil</a></td>
<td class="colLast">
<div class="block">
 Offer methods which help to search shapes and text in a presentation.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/SmartArt.html" title="class in com.aspose.slides">SmartArt</a></td>
<td class="colLast">
<div class="block">
 Represents a SmartArt diagram</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/SmartArtColorType.html" title="class in com.aspose.slides">SmartArtColorType</a></td>
<td class="colLast">
<div class="block">
 Represents color scheme of a SmartArt diagram.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/SmartArtLayoutType.html" title="class in com.aspose.slides">SmartArtLayoutType</a></td>
<td class="colLast">
<div class="block">
 Represents layout type of a SmartArt diagram.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/SmartArtNode.html" title="class in com.aspose.slides">SmartArtNode</a></td>
<td class="colLast">
<div class="block">
 Represents node of a SmartArt object</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/SmartArtNodeCollection.html" title="class in com.aspose.slides">SmartArtNodeCollection</a></td>
<td class="colLast">
<div class="block">
 Represents a collection of SmartArt nodes.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/SmartArtQuickStyleType.html" title="class in com.aspose.slides">SmartArtQuickStyleType</a></td>
<td class="colLast">
<div class="block">
 Represents style scheme of a SmartArt diagram.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/SmartArtShape.html" title="class in com.aspose.slides">SmartArtShape</a></td>
<td class="colLast">
<div class="block">
 Represents SmartArt shape</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/SmartArtShapeCollection.html" title="class in com.aspose.slides">SmartArtShapeCollection</a></td>
<td class="colLast">
<div class="block">
 Represents a collection of a SmartArt shapes</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/SoftEdge.html" title="class in com.aspose.slides">SoftEdge</a></td>
<td class="colLast">
<div class="block">
 Represents a soft edge effect.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/SourceFormat.html" title="class in com.aspose.slides">SourceFormat</a></td>
<td class="colLast">
<div class="block">
 Represents source file format.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/SplitterBarStateType.html" title="class in com.aspose.slides">SplitterBarStateType</a></td>
<td class="colLast">
<div class="block">
 Specifies the state that the splitter bar should be shown in.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/SplitTransition.html" title="class in com.aspose.slides">SplitTransition</a></td>
<td class="colLast">
<div class="block">
 Split slide transition effect.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/SpreadsheetOptions.html" title="class in com.aspose.slides">SpreadsheetOptions</a></td>
<td class="colLast">
<div class="block">
 Represents options which can be used to specify additional spreadsheets behavior.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/Storage.html" title="class in com.aspose.slides">Storage</a></td>
<td class="colLast">
<div class="block">
 Represents a temporary data storage for <a href="../../../com/aspose/slides/WebDocument.html" title="class in com.aspose.slides"><code>WebDocument</code></a>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/StringChartValue.html" title="class in com.aspose.slides">StringChartValue</a></td>
<td class="colLast">
<div class="block">
 Represent string value which can be stored in pptx presentation document in two ways:
 1) in cell/cells of workbook related to chart;
 2) as literal value.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/StringOrDoubleChartValue.html" title="class in com.aspose.slides">StringOrDoubleChartValue</a></td>
<td class="colLast">
<div class="block">
 Represent string or double value which can be stored in pptx presentation document in two ways:
 1) in cell/cells of workbook related to chart;
 2) as literal value.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/StyleType.html" title="class in com.aspose.slides">StyleType</a></td>
<td class="colLast">
<div class="block">
 Represents chart style.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/SummaryZoomFrame.html" title="class in com.aspose.slides">SummaryZoomFrame</a></td>
<td class="colLast">
<div class="block">
 Represents a Summary Zoom object in a slide.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/SummaryZoomSection.html" title="class in com.aspose.slides">SummaryZoomSection</a></td>
<td class="colLast">
<div class="block">
 Represents a Summary Zoom Section object in a Summary Zoom frame.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/SummaryZoomSectionCollection.html" title="class in com.aspose.slides">SummaryZoomSectionCollection</a></td>
<td class="colLast">
<div class="block">
 Represents a collection of Summary Zoom Section objects.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/SvgCoordinateUnit.html" title="class in com.aspose.slides">SvgCoordinateUnit</a></td>
<td class="colLast">
<div class="block">
 Represents CSS2 coordinate units used to define SVG coordinates.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/SvgEvent.html" title="class in com.aspose.slides">SvgEvent</a></td>
<td class="colLast">
<div class="block">
 Represents options for SVG shape.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/SvgExternalFontsHandling.html" title="class in com.aspose.slides">SvgExternalFontsHandling</a></td>
<td class="colLast">
<div class="block">
 Represents a way to handle external fonts used for text drawing.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/SvgImage.html" title="class in com.aspose.slides">SvgImage</a></td>
<td class="colLast">
<div class="block">
 Represents an SVG image.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/SVGOptions.html" title="class in com.aspose.slides">SVGOptions</a></td>
<td class="colLast">
<div class="block">
 Represents an SVG options.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/SvgShape.html" title="class in com.aspose.slides">SvgShape</a></td>
<td class="colLast">
<div class="block">
 Represents options for SVG shape.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/SvgTSpan.html" title="class in com.aspose.slides">SvgTSpan</a></td>
<td class="colLast">
<div class="block">
 Represents options for SVG text portion ("tspan").</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/SwfOptions.html" title="class in com.aspose.slides">SwfOptions</a></td>
<td class="colLast">
<div class="block">
 Provides options that control how a presentation is saved in Swf format.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/SystemColor.html" title="class in com.aspose.slides">SystemColor</a></td>
<td class="colLast">
<div class="block">
 Represents predefined system colors.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/Tab.html" title="class in com.aspose.slides">Tab</a></td>
<td class="colLast">
<div class="block">
 Represents a tabulation for a text.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/TabAlignment.html" title="class in com.aspose.slides">TabAlignment</a></td>
<td class="colLast">
<div class="block">
 Represents the tab alignment.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/TabCollection.html" title="class in com.aspose.slides">TabCollection</a></td>
<td class="colLast">
<div class="block">
 Represents a collection of tabs.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/TabFactory.html" title="class in com.aspose.slides">TabFactory</a></td>
<td class="colLast">
<div class="block">
 Allows to create ITab instances</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/Table.html" title="class in com.aspose.slides">Table</a></td>
<td class="colLast">
<div class="block">
 Represents a table on a slide.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/TableFormat.html" title="class in com.aspose.slides">TableFormat</a></td>
<td class="colLast">
<div class="block">
 Represents format of a table.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/TableStylePreset.html" title="class in com.aspose.slides">TableStylePreset</a></td>
<td class="colLast">
<div class="block">
 Represents builtin table styles.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/TagCollection.html" title="class in com.aspose.slides">TagCollection</a></td>
<td class="colLast">
<div class="block">
 Represents the collection of tags (user defined pairs of strings)</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/TemplateContext.html" title="class in com.aspose.slides">TemplateContext</a>&lt;TObject&gt;</td>
<td class="colLast">
<div class="block">
 Represents a model object interface for a template engine.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/TextAlignment.html" title="class in com.aspose.slides">TextAlignment</a></td>
<td class="colLast">
<div class="block">
 Represents different text alignment styles.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/TextAnchorType.html" title="class in com.aspose.slides">TextAnchorType</a></td>
<td class="colLast">
<div class="block">
 text box alignment within a text area.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/TextAnimation.html" title="class in com.aspose.slides">TextAnimation</a></td>
<td class="colLast">
<div class="block">
 Represent text animation.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/TextAnimationCollection.html" title="class in com.aspose.slides">TextAnimationCollection</a></td>
<td class="colLast">
<div class="block">
 Represents collection of text animations.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/TextAutofitType.html" title="class in com.aspose.slides">TextAutofitType</a></td>
<td class="colLast">
<div class="block">
 Represents text autofit mode.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/TextCapType.html" title="class in com.aspose.slides">TextCapType</a></td>
<td class="colLast">
<div class="block">
 Represents the type of text capitalisation.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/TextExtractionArrangingMode.html" title="class in com.aspose.slides">TextExtractionArrangingMode</a></td>
<td class="colLast">
<div class="block">
 Represents the mode to use during text extraction</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/TextFrame.html" title="class in com.aspose.slides">TextFrame</a></td>
<td class="colLast">
<div class="block">
  Represents a TextFrame.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/TextFrameFormat.html" title="class in com.aspose.slides">TextFrameFormat</a></td>
<td class="colLast">
<div class="block">
  Contains the TextFrame's formatTextFrameFormatting properties.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/TextHighlightingOptions.html" title="class in com.aspose.slides">TextHighlightingOptions</a></td>
<td class="colLast">Deprecated
<div class="block"><span class="deprecationComment">The interface ITextHighlightingOptions will be removed after release of version 24.10.</span></div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/TextInheritanceLimit.html" title="class in com.aspose.slides">TextInheritanceLimit</a></td>
<td class="colLast">
<div class="block">
 Controls the depth of the text properties inheritance.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/TextSearchOptions.html" title="class in com.aspose.slides">TextSearchOptions</a></td>
<td class="colLast">
<div class="block">
 Represents options that can be used to search for text in a Presentation, Slide or TextFrame.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/TextShapeType.html" title="class in com.aspose.slides">TextShapeType</a></td>
<td class="colLast">
<div class="block">
 Represents text wrapping shape.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/TextStrikethroughType.html" title="class in com.aspose.slides">TextStrikethroughType</a></td>
<td class="colLast">
<div class="block">
 Represents the type of text strikethrough.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/TextStyle.html" title="class in com.aspose.slides">TextStyle</a></td>
<td class="colLast">
<div class="block">
 This class contains the text style formatting properties.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/TextToHtmlConversionOptions.html" title="class in com.aspose.slides">TextToHtmlConversionOptions</a></td>
<td class="colLast">
<div class="block">
 Options for extracting HTML from the Pptx text.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/TextUnderlineType.html" title="class in com.aspose.slides">TextUnderlineType</a></td>
<td class="colLast">
<div class="block">
 Represents the type of text underline.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/TextVerticalOverflowType.html" title="class in com.aspose.slides">TextVerticalOverflowType</a></td>
<td class="colLast">
<div class="block">
 Represents text vertical overflow type.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/TextVerticalType.html" title="class in com.aspose.slides">TextVerticalType</a></td>
<td class="colLast">
<div class="block">
 Determines vertical writing mode for a text.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/Theme.html" title="class in com.aspose.slides">Theme</a></td>
<td class="colLast">
<div class="block">
 Represents a theme.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ThreeDFormat.html" title="class in com.aspose.slides">ThreeDFormat</a></td>
<td class="colLast">
<div class="block">
 Represents 3-D properties.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/TickLabelPositionType.html" title="class in com.aspose.slides">TickLabelPositionType</a></td>
<td class="colLast">
<div class="block">
 Represents the position type of tick-mark labels on the specified axis.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/TickMarkType.html" title="class in com.aspose.slides">TickMarkType</a></td>
<td class="colLast">
<div class="block">
 Represents the tick mark type for the specified axis.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/TiffCompressionTypes.html" title="class in com.aspose.slides">TiffCompressionTypes</a></td>
<td class="colLast">
<div class="block">
 Provides options that control how a presentation is compressed in TIFF format.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/TiffOptions.html" title="class in com.aspose.slides">TiffOptions</a></td>
<td class="colLast">
<div class="block">
 Provides options that control how a presentation is saved in TIFF format.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/TileFlip.html" title="class in com.aspose.slides">TileFlip</a></td>
<td class="colLast">
<div class="block">
 Defines tile flipping mode.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/TimeUnitType.html" title="class in com.aspose.slides">TimeUnitType</a></td>
<td class="colLast">
<div class="block">
 Represents the base unit for the category axis</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/Timing.html" title="class in com.aspose.slides">Timing</a></td>
<td class="colLast">
<div class="block">
 Represents animation timing.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/Tint.html" title="class in com.aspose.slides">Tint</a></td>
<td class="colLast">
<div class="block">
 Represents a Tint effect.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/TransitionCornerAndCenterDirectionType.html" title="class in com.aspose.slides">TransitionCornerAndCenterDirectionType</a></td>
<td class="colLast">
<div class="block">
 Specifies a direction restricted to the corners and center.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/TransitionCornerDirectionType.html" title="class in com.aspose.slides">TransitionCornerDirectionType</a></td>
<td class="colLast">
<div class="block">
 Represent corner direction transition types.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/TransitionEightDirectionType.html" title="class in com.aspose.slides">TransitionEightDirectionType</a></td>
<td class="colLast">
<div class="block">
 Represent eight direction transition types.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/TransitionInOutDirectionType.html" title="class in com.aspose.slides">TransitionInOutDirectionType</a></td>
<td class="colLast">
<div class="block">
 Represent in or out direction transition types.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/TransitionLeftRightDirectionType.html" title="class in com.aspose.slides">TransitionLeftRightDirectionType</a></td>
<td class="colLast">
<div class="block">
 Specifies a direction restricted to the values of left and right.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/TransitionMorphType.html" title="class in com.aspose.slides">TransitionMorphType</a></td>
<td class="colLast">
<div class="block">
 Represent a type of morph transition.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/TransitionPattern.html" title="class in com.aspose.slides">TransitionPattern</a></td>
<td class="colLast">
<div class="block">
 Specifies a geometric pattern that tiles together to fill a larger area.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/TransitionShredPattern.html" title="class in com.aspose.slides">TransitionShredPattern</a></td>
<td class="colLast">
<div class="block">
 Specifies a geometric shape that tiles together to fill a larger area.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/TransitionSideDirectionType.html" title="class in com.aspose.slides">TransitionSideDirectionType</a></td>
<td class="colLast">
<div class="block">
 Represent side direction transition types.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/TransitionSoundMode.html" title="class in com.aspose.slides">TransitionSoundMode</a></td>
<td class="colLast">
<div class="block">
 Represent sound mode of transition.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/TransitionSpeed.html" title="class in com.aspose.slides">TransitionSpeed</a></td>
<td class="colLast">
<div class="block">
 Represent transition speed types.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/TransitionType.html" title="class in com.aspose.slides">TransitionType</a></td>
<td class="colLast">
<div class="block">
 Represent slide show transition type.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/TransitionValueBase.html" title="class in com.aspose.slides">TransitionValueBase</a></td>
<td class="colLast">
<div class="block">
 Base class for slide transition effects.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/Trendline.html" title="class in com.aspose.slides">Trendline</a></td>
<td class="colLast">
<div class="block">
 Class represents trend line of chart series</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/TrendlineCollection.html" title="class in com.aspose.slides">TrendlineCollection</a></td>
<td class="colLast">
<div class="block">
 Represents a collection of Trendline</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/TrendlineType.html" title="class in com.aspose.slides">TrendlineType</a></td>
<td class="colLast">
<div class="block">
 Represents type of trend line</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/UpDownBarsManager.html" title="class in com.aspose.slides">UpDownBarsManager</a></td>
<td class="colLast">
<div class="block">
 Provide access to up/down bars of Line- or Stock-chart.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/VbaModule.html" title="class in com.aspose.slides">VbaModule</a></td>
<td class="colLast">
<div class="block">
 Represents module that is contained in VBA project.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/VbaModuleCollection.html" title="class in com.aspose.slides">VbaModuleCollection</a></td>
<td class="colLast">
<div class="block">
 Represents a collection of a VBA Project modules.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/VbaProject.html" title="class in com.aspose.slides">VbaProject</a></td>
<td class="colLast">
<div class="block">
 Represents VBA project with presentation macros.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/VbaProjectFactory.html" title="class in com.aspose.slides">VbaProjectFactory</a></td>
<td class="colLast">
<div class="block">
 Allows to create VBA project via COM interface</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/VbaReferenceCollection.html" title="class in com.aspose.slides">VbaReferenceCollection</a></td>
<td class="colLast">
<div class="block">
 Represents a collection of a VBA Project references.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/VbaReferenceFactory.html" title="class in com.aspose.slides">VbaReferenceFactory</a></td>
<td class="colLast">
<div class="block">
 Allows to create VBA project references via COM interface</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/VbaReferenceOleTypeLib.html" title="class in com.aspose.slides">VbaReferenceOleTypeLib</a></td>
<td class="colLast">
<div class="block">
 Represents OLE Automation type library reference.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/Video.html" title="class in com.aspose.slides">Video</a></td>
<td class="colLast">
<div class="block">
 Represents an image embedded into a presentation.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/VideoCollection.html" title="class in com.aspose.slides">VideoCollection</a></td>
<td class="colLast">
<div class="block">
 Represents a collection of Video objects.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/VideoFrame.html" title="class in com.aspose.slides">VideoFrame</a></td>
<td class="colLast">
<div class="block">
  Represents a video clip on a slide.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/VideoPlayerHtmlController.html" title="class in com.aspose.slides">VideoPlayerHtmlController</a></td>
<td class="colLast">
<div class="block">
 This class allows export of video and audio files into a HTML</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/VideoPlayerHtmlControllerFactory.html" title="class in com.aspose.slides">VideoPlayerHtmlControllerFactory</a></td>
<td class="colLast">
<div class="block">
 Allows to create VideoPlayerHtmlController.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/VideoPlayModePreset.html" title="class in com.aspose.slides">VideoPlayModePreset</a></td>
<td class="colLast">
<div class="block">
 Constants which define how a video is played.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ViewProperties.html" title="class in com.aspose.slides">ViewProperties</a></td>
<td class="colLast">
<div class="block">
 Presentation wide view properties.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ViewType.html" title="class in com.aspose.slides">ViewType</a></td>
<td class="colLast">
<div class="block">
 Presentation view types</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/WarningType.html" title="class in com.aspose.slides">WarningType</a></td>
<td class="colLast">
<div class="block">
 Represents a type of warning.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/WebDocument.html" title="class in com.aspose.slides">WebDocument</a></td>
<td class="colLast">
<div class="block">
 Represents a transition form of the presentation for saving into a web format.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/WebDocumentOptions.html" title="class in com.aspose.slides">WebDocumentOptions</a></td>
<td class="colLast">
<div class="block">
 Represents an options set for <a href="../../../com/aspose/slides/WebDocument.html" title="class in com.aspose.slides"><code>WebDocument</code></a> saving.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/WheelTransition.html" title="class in com.aspose.slides">WheelTransition</a></td>
<td class="colLast">
<div class="block">
 Wheel slide transition effect.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/XamlOptions.html" title="class in com.aspose.slides">XamlOptions</a></td>
<td class="colLast">
<div class="block">
 Options that control how a XAML document is saved.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/XpsOptions.html" title="class in com.aspose.slides">XpsOptions</a></td>
<td class="colLast">
<div class="block">
 Provides options that control how a presentation is saved in XPS format.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/Zip64Mode.html" title="class in com.aspose.slides">Zip64Mode</a></td>
<td class="colLast">
<div class="block">
 Specifies when to use ZIP64 format extensions for OpenXML file.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ZoomFrame.html" title="class in com.aspose.slides">ZoomFrame</a></td>
<td class="colLast">
<div class="block">
 Represents a Slide Zoom object in a slide.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ZoomImageType.html" title="class in com.aspose.slides">ZoomImageType</a></td>
<td class="colLast">
<div class="block">
 Defines whether the Zoom object is using the slide preview or a cover image.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ZoomLayout.html" title="class in com.aspose.slides">ZoomLayout</a></td>
<td class="colLast">
<div class="block">
 Specifies the summary zoom layout.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/ZoomObject.html" title="class in com.aspose.slides">ZoomObject</a></td>
<td class="colLast">
<div class="block">
 Represents an Zoom object in a slide.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="typeSummary" border="0" cellpadding="3" cellspacing="0" summary="Exception Summary table, listing exceptions, and an explanation">
<caption><span>Exception Summary</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Exception</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/AsposeLicenseException.html" title="class in com.aspose.slides">AsposeLicenseException</a></td>
<td class="colLast">
<div class="block">This exception is thrown if any errors with license are detected.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/AxesCompositionNotCombinableException.html" title="class in com.aspose.slides">AxesCompositionNotCombinableException</a></td>
<td class="colLast">
<div class="block">
 Exception which thrown when axes composition of the series is not combinable with present axes 
 composition in chart.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/CannotCombine2DAnd3DChartsException.html" title="class in com.aspose.slides">CannotCombine2DAnd3DChartsException</a></td>
<td class="colLast">
<div class="block">
 Exception which thrown when trying to combine 2D and 3D chart types.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/CellCircularReferenceException.html" title="class in com.aspose.slides">CellCircularReferenceException</a></td>
<td class="colLast">
<div class="block">
 The exception that is thrown when one or more circular references are detected where a formula refers to its
 own cell either directly or indirectly.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/CellInvalidFormulaException.html" title="class in com.aspose.slides">CellInvalidFormulaException</a></td>
<td class="colLast">
<div class="block">
 The exception that is thrown when a calculated formula is not correct or was not parsed.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/CellInvalidReferenceException.html" title="class in com.aspose.slides">CellInvalidReferenceException</a></td>
<td class="colLast">
<div class="block">
 The exception that is thrown when an invalid cell reference is encountered.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/CellUnsupportedDataException.html" title="class in com.aspose.slides">CellUnsupportedDataException</a></td>
<td class="colLast">
<div class="block">
 The exception that is thrown when an unsupported data is encountered in a spreadsheet cell.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/InvalidPasswordException.html" title="class in com.aspose.slides">InvalidPasswordException</a></td>
<td class="colLast">
<div class="block">
 Exception which thrown when presentation file format is unsupported.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/OdpException.html" title="class in com.aspose.slides">OdpException</a></td>
<td class="colLast">
<div class="block">
 Represents a standard internal exception type.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/OdpReadException.html" title="class in com.aspose.slides">OdpReadException</a></td>
<td class="colLast">
<div class="block">
 Represents an exception which thrown on presentation reading errors.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/OOXMLCorruptFileException.html" title="class in com.aspose.slides">OOXMLCorruptFileException</a></td>
<td class="colLast">
<div class="block">
 Exception which thrown when Office Open XML file format is corrupted.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/OOXMLException.html" title="class in com.aspose.slides">OOXMLException</a></td>
<td class="colLast">
<div class="block">
 Represents a standard internal exception type related to Office Open XML file format.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/PptCorruptFileException.html" title="class in com.aspose.slides">PptCorruptFileException</a></td>
<td class="colLast">
<div class="block">
 Exception which thrown when presentation file is probably corrupt.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/PptEditException.html" title="class in com.aspose.slides">PptEditException</a></td>
<td class="colLast">
<div class="block">
 Represents an exception thrown when edit presentation error is detected.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/PptException.html" title="class in com.aspose.slides">PptException</a></td>
<td class="colLast">
<div class="block">
 Represents a standard internal exception type.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/PptReadException.html" title="class in com.aspose.slides">PptReadException</a></td>
<td class="colLast">
<div class="block">
 Represents an exception which thrown on presentation reading errors.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/PptUnsupportedFormatException.html" title="class in com.aspose.slides">PptUnsupportedFormatException</a></td>
<td class="colLast">
<div class="block">
 Exception which thrown when presentation file format is unsupported.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/PptxCorruptFileException.html" title="class in com.aspose.slides">PptxCorruptFileException</a></td>
<td class="colLast">
<div class="block">
 Exception which thrown when presentation file is probably corrupt.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/PptxEditException.html" title="class in com.aspose.slides">PptxEditException</a></td>
<td class="colLast">
<div class="block">
 Represents an exception thrown when edit presentation error is detected.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/PptxException.html" title="class in com.aspose.slides">PptxException</a></td>
<td class="colLast">
<div class="block">
 Represents a standard internal exception type.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/PptxReadException.html" title="class in com.aspose.slides">PptxReadException</a></td>
<td class="colLast">
<div class="block">
 Represents an exception which thrown on presentation reading errors.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/aspose/slides/PptxUnsupportedFormatException.html" title="class in com.aspose.slides">PptxUnsupportedFormatException</a></td>
<td class="colLast">
<div class="block">
 Exception which thrown when presentation file format is unsupported.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/aspose/slides/SlidesAIAgentException.html" title="class in com.aspose.slides">SlidesAIAgentException</a></td>
<td class="colLast">
<div class="block">
 Represents Slides AI Agent related exceptions.</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li>Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev&nbsp;Package</li>
<li>Next&nbsp;Package</li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/package-summary.html" target="_top">Frames</a></li>
<li><a href="package-summary.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><i>Copyright &copy; 2004-2025 Aspose Pty Ltd. All Rights Reserved.</i></small></p>
</body>
</html>
