<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_421) on Fri Jul 11 05:32:43 CDT 2025 -->
<title>PresentationFactory (Aspose.Slides for Java (25.7) API Documentation)</title>
<meta name="date" content="2025-07-11">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="PresentationFactory (Aspose.Slides for Java (25.7) API Documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":9,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/PresentationContentAmountType.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/PresentationHeaderFooterManager.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/PresentationFactory.html" target="_top">Frames</a></li>
<li><a href="PresentationFactory.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.aspose.slides</div>
<h2 title="Class PresentationFactory" class="title">Class PresentationFactory</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.aspose.slides.PresentationFactory</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../com/aspose/slides/IPresentationFactory.html" title="interface in com.aspose.slides">IPresentationFactory</a></dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">PresentationFactory</span>
extends java.lang.Object
implements <a href="../../../com/aspose/slides/IPresentationFactory.html" title="interface in com.aspose.slides">IPresentationFactory</a></pre>
<div class="block"><p>
 Allows to create presentation via COM interface
 </p><p><hr><blockquote><pre>
 The following example shows how to checking a Presentation Format.
 <pre>
 IPresentationInfo info = PresentationFactory.getInstance().getPresentationInfo("pres.pptx");
 System.out.println(info.getLoadFormat()); // PPTX
 IPresentationInfo info2 = PresentationFactory.getInstance().getPresentationInfo("pres.ppt");
 System.out.println(info2.getLoadFormat()); // PPT
 IPresentationInfo info3 = PresentationFactory.getInstance().getPresentationInfo("pres.odp");
 System.out.println(info3.getLoadFormat()); // ODP
 </pre>
 The following example shows how to getting the properties of a Presentation.
 <pre>
 IPresentationInfo info = PresentationFactory.getInstance().getPresentationInfo("pres.pptx");
 IDocumentProperties props = info.readDocumentProperties();
 System.out.println(props.getCreatedTime());
 System.out.println(props.getSubject());
 System.out.println(props.getTitle());
 // ..
 </pre>
 The following example shows how to updating the properties of a Presentation.
 <pre>
 IPresentationInfo info = PresentationFactory.getInstance().getPresentationInfo("pres.pptx");
 IDocumentProperties props = info.readDocumentProperties();
 props.setTitle("My title");
 info.updateDocumentProperties(props);
 </pre>
 </pre></blockquote></hr></p></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresentationFactory.html#PresentationFactory--">PresentationFactory</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IPresentation.html" title="interface in com.aspose.slides">IPresentation</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresentationFactory.html#createPresentation--">createPresentation</a></span>()</code>
<div class="block">
 Creates new presentation.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IPresentation.html" title="interface in com.aspose.slides">IPresentation</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresentationFactory.html#createPresentation-com.aspose.slides.ILoadOptions-">createPresentation</a></span>(<a href="../../../com/aspose/slides/ILoadOptions.html" title="interface in com.aspose.slides">ILoadOptions</a>&nbsp;options)</code>
<div class="block">
 Creates new presentation with additional load options</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>static <a href="../../../com/aspose/slides/PresentationFactory.html" title="class in com.aspose.slides">PresentationFactory</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresentationFactory.html#getInstance--">getInstance</a></span>()</code>
<div class="block">
 Presentation factory static instance.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IPresentationInfo.html" title="interface in com.aspose.slides">IPresentationInfo</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresentationFactory.html#getPresentationInfo-java.io.InputStream-">getPresentationInfo</a></span>(java.io.InputStream&nbsp;stream)</code>
<div class="block">
 Creates new PresentationInfo object from stream and binds presentation to it.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IPresentationInfo.html" title="interface in com.aspose.slides">IPresentationInfo</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresentationFactory.html#getPresentationInfo-java.lang.String-">getPresentationInfo</a></span>(java.lang.String&nbsp;file)</code>
<div class="block">
 Creates new PresentationInfo object from file and binds presentation to it.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IPresentationText.html" title="interface in com.aspose.slides">IPresentationText</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresentationFactory.html#getPresentationText-java.io.InputStream-int-">getPresentationText</a></span>(java.io.InputStream&nbsp;stream,
                   int&nbsp;mode)</code>
<div class="block">
 Retrieves the raw text from the slides</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IPresentationText.html" title="interface in com.aspose.slides">IPresentationText</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresentationFactory.html#getPresentationText-java.io.InputStream-int-com.aspose.slides.ILoadOptions-">getPresentationText</a></span>(java.io.InputStream&nbsp;stream,
                   int&nbsp;mode,
                   <a href="../../../com/aspose/slides/ILoadOptions.html" title="interface in com.aspose.slides">ILoadOptions</a>&nbsp;options)</code>
<div class="block">
 Retrieves the raw text from the slides</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IPresentationText.html" title="interface in com.aspose.slides">IPresentationText</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresentationFactory.html#getPresentationText-java.lang.String-int-">getPresentationText</a></span>(java.lang.String&nbsp;file,
                   int&nbsp;mode)</code>
<div class="block">
 Retrieves the raw text from the slides</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IPresentation.html" title="interface in com.aspose.slides">IPresentation</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresentationFactory.html#readPresentation-byte:A-">readPresentation</a></span>(byte[]&nbsp;data)</code>
<div class="block">
 Reads an existing presentation from array</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IPresentation.html" title="interface in com.aspose.slides">IPresentation</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresentationFactory.html#readPresentation-byte:A-com.aspose.slides.ILoadOptions-">readPresentation</a></span>(byte[]&nbsp;data,
                <a href="../../../com/aspose/slides/ILoadOptions.html" title="interface in com.aspose.slides">ILoadOptions</a>&nbsp;options)</code>
<div class="block">
 Reads an existing presentation from array with additional load options</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IPresentation.html" title="interface in com.aspose.slides">IPresentation</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresentationFactory.html#readPresentation-java.io.InputStream-">readPresentation</a></span>(java.io.InputStream&nbsp;stream)</code>
<div class="block">
 Reads an existing presentation from stream</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IPresentation.html" title="interface in com.aspose.slides">IPresentation</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresentationFactory.html#readPresentation-java.io.InputStream-com.aspose.slides.ILoadOptions-">readPresentation</a></span>(java.io.InputStream&nbsp;stream,
                <a href="../../../com/aspose/slides/ILoadOptions.html" title="interface in com.aspose.slides">ILoadOptions</a>&nbsp;options)</code>
<div class="block">
 Reads an existing presentation from stream with additional load options</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IPresentation.html" title="interface in com.aspose.slides">IPresentation</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresentationFactory.html#readPresentation-java.lang.String-">readPresentation</a></span>(java.lang.String&nbsp;file)</code>
<div class="block">
 Reads an existing presentation from file</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IPresentation.html" title="interface in com.aspose.slides">IPresentation</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresentationFactory.html#readPresentation-java.lang.String-com.aspose.slides.ILoadOptions-">readPresentation</a></span>(java.lang.String&nbsp;file,
                <a href="../../../com/aspose/slides/ILoadOptions.html" title="interface in com.aspose.slides">ILoadOptions</a>&nbsp;options)</code>
<div class="block">
 Reads an existing presentation from stream with additional load options</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="PresentationFactory--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>PresentationFactory</h4>
<pre>public&nbsp;PresentationFactory()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getInstance--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getInstance</h4>
<pre>public static&nbsp;<a href="../../../com/aspose/slides/PresentationFactory.html" title="class in com.aspose.slides">PresentationFactory</a>&nbsp;getInstance()</pre>
<div class="block"><p>
 Presentation factory static instance.
 Read-only <a href="../../../com/aspose/slides/PresentationFactory.html" title="class in com.aspose.slides"><code>PresentationFactory</code></a>.
 </p></div>
</li>
</ul>
<a name="createPresentation--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createPresentation</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IPresentation.html" title="interface in com.aspose.slides">IPresentation</a>&nbsp;createPresentation()</pre>
<div class="block"><p>
 Creates new presentation.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPresentationFactory.html#createPresentation--">createPresentation</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPresentationFactory.html" title="interface in com.aspose.slides">IPresentationFactory</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>New presentation</dd>
</dl>
</li>
</ul>
<a name="createPresentation-com.aspose.slides.ILoadOptions-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createPresentation</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IPresentation.html" title="interface in com.aspose.slides">IPresentation</a>&nbsp;createPresentation(<a href="../../../com/aspose/slides/ILoadOptions.html" title="interface in com.aspose.slides">ILoadOptions</a>&nbsp;options)</pre>
<div class="block"><p>
 Creates new presentation with additional load options
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPresentationFactory.html#createPresentation-com.aspose.slides.ILoadOptions-">createPresentation</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPresentationFactory.html" title="interface in com.aspose.slides">IPresentationFactory</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>options</code> - Load options</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>New presentation</dd>
</dl>
</li>
</ul>
<a name="getPresentationInfo-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPresentationInfo</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IPresentationInfo.html" title="interface in com.aspose.slides">IPresentationInfo</a>&nbsp;getPresentationInfo(java.lang.String&nbsp;file)</pre>
<div class="block"><p>
 Creates new PresentationInfo object from file and binds presentation to it.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPresentationFactory.html#getPresentationInfo-java.lang.String-">getPresentationInfo</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPresentationFactory.html" title="interface in com.aspose.slides">IPresentationFactory</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>file</code> - Presentation file.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Presentation info binded to presentation.</dd>
</dl>
</li>
</ul>
<a name="getPresentationInfo-java.io.InputStream-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPresentationInfo</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IPresentationInfo.html" title="interface in com.aspose.slides">IPresentationInfo</a>&nbsp;getPresentationInfo(java.io.InputStream&nbsp;stream)</pre>
<div class="block"><p>
 Creates new PresentationInfo object from stream and binds presentation to it.
 Gets info about presentation in specified stream.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPresentationFactory.html#getPresentationInfo-java.io.InputStream-">getPresentationInfo</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPresentationFactory.html" title="interface in com.aspose.slides">IPresentationFactory</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>stream</code> - Presentation stream.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Presentation info binded to presentation.</dd>
</dl>
</li>
</ul>
<a name="readPresentation-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>readPresentation</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IPresentation.html" title="interface in com.aspose.slides">IPresentation</a>&nbsp;readPresentation(byte[]&nbsp;data)</pre>
<div class="block"><p>
 Reads an existing presentation from array
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPresentationFactory.html#readPresentation-byte:A-">readPresentation</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPresentationFactory.html" title="interface in com.aspose.slides">IPresentationFactory</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>data</code> - Array to read</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Read presentation</dd>
</dl>
</li>
</ul>
<a name="readPresentation-byte:A-com.aspose.slides.ILoadOptions-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>readPresentation</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IPresentation.html" title="interface in com.aspose.slides">IPresentation</a>&nbsp;readPresentation(byte[]&nbsp;data,
                                            <a href="../../../com/aspose/slides/ILoadOptions.html" title="interface in com.aspose.slides">ILoadOptions</a>&nbsp;options)</pre>
<div class="block"><p>
 Reads an existing presentation from array with additional load options
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPresentationFactory.html#readPresentation-byte:A-com.aspose.slides.ILoadOptions-">readPresentation</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPresentationFactory.html" title="interface in com.aspose.slides">IPresentationFactory</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>data</code> - Array to read</dd>
<dd><code>options</code> - Load options</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Read presentation</dd>
</dl>
</li>
</ul>
<a name="readPresentation-java.io.InputStream-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>readPresentation</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IPresentation.html" title="interface in com.aspose.slides">IPresentation</a>&nbsp;readPresentation(java.io.InputStream&nbsp;stream)</pre>
<div class="block"><p>
 Reads an existing presentation from stream
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPresentationFactory.html#readPresentation-java.io.InputStream-">readPresentation</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPresentationFactory.html" title="interface in com.aspose.slides">IPresentationFactory</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>stream</code> - Input stream to read</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Read presentation</dd>
</dl>
</li>
</ul>
<a name="readPresentation-java.io.InputStream-com.aspose.slides.ILoadOptions-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>readPresentation</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IPresentation.html" title="interface in com.aspose.slides">IPresentation</a>&nbsp;readPresentation(java.io.InputStream&nbsp;stream,
                                            <a href="../../../com/aspose/slides/ILoadOptions.html" title="interface in com.aspose.slides">ILoadOptions</a>&nbsp;options)</pre>
<div class="block"><p>
 Reads an existing presentation from stream with additional load options
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPresentationFactory.html#readPresentation-java.io.InputStream-com.aspose.slides.ILoadOptions-">readPresentation</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPresentationFactory.html" title="interface in com.aspose.slides">IPresentationFactory</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>stream</code> - Input stream to read</dd>
<dd><code>options</code> - Load options</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Read presentation</dd>
</dl>
</li>
</ul>
<a name="readPresentation-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>readPresentation</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IPresentation.html" title="interface in com.aspose.slides">IPresentation</a>&nbsp;readPresentation(java.lang.String&nbsp;file)</pre>
<div class="block"><p>
 Reads an existing presentation from file
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPresentationFactory.html#readPresentation-java.lang.String-">readPresentation</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPresentationFactory.html" title="interface in com.aspose.slides">IPresentationFactory</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>file</code> - File name</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Read presentation</dd>
</dl>
</li>
</ul>
<a name="readPresentation-java.lang.String-com.aspose.slides.ILoadOptions-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>readPresentation</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IPresentation.html" title="interface in com.aspose.slides">IPresentation</a>&nbsp;readPresentation(java.lang.String&nbsp;file,
                                            <a href="../../../com/aspose/slides/ILoadOptions.html" title="interface in com.aspose.slides">ILoadOptions</a>&nbsp;options)</pre>
<div class="block"><p>
 Reads an existing presentation from stream with additional load options
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPresentationFactory.html#readPresentation-java.lang.String-com.aspose.slides.ILoadOptions-">readPresentation</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPresentationFactory.html" title="interface in com.aspose.slides">IPresentationFactory</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>file</code> - File name</dd>
<dd><code>options</code> - Load options</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Read presentation</dd>
</dl>
</li>
</ul>
<a name="getPresentationText-java.lang.String-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPresentationText</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IPresentationText.html" title="interface in com.aspose.slides">IPresentationText</a>&nbsp;getPresentationText(java.lang.String&nbsp;file,
                                                   int&nbsp;mode)</pre>
<div class="block"><p>
 Retrieves the raw text from the slides
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPresentationFactory.html#getPresentationText-java.lang.String-int-">getPresentationText</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPresentationFactory.html" title="interface in com.aspose.slides">IPresentationFactory</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>file</code> - Input file</dd>
<dd><code>mode</code> - Extraction mode</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The instance of PresentationText containing the SlideText array representing the raw slides text</dd>
</dl>
</li>
</ul>
<a name="getPresentationText-java.io.InputStream-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPresentationText</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IPresentationText.html" title="interface in com.aspose.slides">IPresentationText</a>&nbsp;getPresentationText(java.io.InputStream&nbsp;stream,
                                                   int&nbsp;mode)</pre>
<div class="block"><p>
 Retrieves the raw text from the slides
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPresentationFactory.html#getPresentationText-java.io.InputStream-int-">getPresentationText</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPresentationFactory.html" title="interface in com.aspose.slides">IPresentationFactory</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>stream</code> - Input stream</dd>
<dd><code>mode</code> - Extraction mode</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The instance of PresentationText containing the SlideText array representing the raw slides text</dd>
</dl>
</li>
</ul>
<a name="getPresentationText-java.io.InputStream-int-com.aspose.slides.ILoadOptions-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getPresentationText</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IPresentationText.html" title="interface in com.aspose.slides">IPresentationText</a>&nbsp;getPresentationText(java.io.InputStream&nbsp;stream,
                                                   int&nbsp;mode,
                                                   <a href="../../../com/aspose/slides/ILoadOptions.html" title="interface in com.aspose.slides">ILoadOptions</a>&nbsp;options)</pre>
<div class="block"><p>
 Retrieves the raw text from the slides
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPresentationFactory.html#getPresentationText-java.io.InputStream-int-com.aspose.slides.ILoadOptions-">getPresentationText</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPresentationFactory.html" title="interface in com.aspose.slides">IPresentationFactory</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>stream</code> - Input stream</dd>
<dd><code>mode</code> - Extraction mode</dd>
<dd><code>options</code> - Load options</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The instance of PresentationText containing the SlideText array representing the raw slides text</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/PresentationContentAmountType.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/PresentationHeaderFooterManager.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/PresentationFactory.html" target="_top">Frames</a></li>
<li><a href="PresentationFactory.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><i>Copyright &copy; 2004-2025 Aspose Pty Ltd. All Rights Reserved.</i></small></p>
</body>
</html>
