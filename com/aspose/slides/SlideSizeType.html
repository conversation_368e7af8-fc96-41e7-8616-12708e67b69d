<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_421) on Fri Jul 11 05:32:44 CDT 2025 -->
<title>SlideSizeType (Aspose.Slides for Java (25.7) API Documentation)</title>
<meta name="date" content="2025-07-11">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="SlideSizeType (Aspose.Slides for Java (25.7) API Documentation)";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/SlideSizeScaleType.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/SlidesRange.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/SlideSizeType.html" target="_top">Frames</a></li>
<li><a href="SlideSizeType.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.classes.inherited.from.class.com.aspose.ms.System.Enum">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#methods.inherited.from.class.com.aspose.ms.System.Enum">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li>Method</li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.aspose.slides</div>
<h2 title="Class SlideSizeType" class="title">Class SlideSizeType</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.aspose.ms.System.ValueType&lt;com.aspose.ms.System.Enum&gt;</li>
<li>
<ul class="inheritance">
<li>com.aspose.ms.System.Enum</li>
<li>
<ul class="inheritance">
<li>com.aspose.slides.SlideSizeType</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public final class <span class="typeNameLabel">SlideSizeType</span>
extends com.aspose.ms.System.Enum</pre>
<div class="block"><p>
 Represents the slide size preset.
 </p></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.com.aspose.ms.System.Enum">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from class&nbsp;com.aspose.ms.System.Enum</h3>
<code>com.aspose.ms.System.Enum.AbstractEnum, com.aspose.ms.System.Enum.FlaggedEnum, com.aspose.ms.System.Enum.ObjectEnum, com.aspose.ms.System.Enum.SimpleEnum</code></li>
</ul>
</li>
</ul>
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideSizeType.html#A3Paper">A3Paper</a></span></code>
<div class="block">
 A3</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideSizeType.html#A4Paper">A4Paper</a></span></code>
<div class="block">
 A4</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideSizeType.html#B4IsoPaper">B4IsoPaper</a></span></code>
<div class="block">
 B4 ISO</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideSizeType.html#B4JisPaper">B4JisPaper</a></span></code>
<div class="block">
 B4 JIS</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideSizeType.html#B5IsoPaper">B5IsoPaper</a></span></code>
<div class="block">
 B5 ISO</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideSizeType.html#B5JisPaper">B5JisPaper</a></span></code>
<div class="block">
 B5 JIS</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideSizeType.html#Banner">Banner</a></span></code>
<div class="block">
 Banner</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideSizeType.html#Custom">Custom</a></span></code>
<div class="block">
 Custom size</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideSizeType.html#HagakiCard">HagakiCard</a></span></code>
<div class="block">
 Hagaki card</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideSizeType.html#Ledger">Ledger</a></span></code>
<div class="block">
 Ledger</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideSizeType.html#LetterPaper">LetterPaper</a></span></code>
<div class="block">
 Letter</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideSizeType.html#OnScreen">OnScreen</a></span></code>
<div class="block">
 On screen 4x3</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideSizeType.html#OnScreen16x10">OnScreen16x10</a></span></code>
<div class="block">
 On Screen 16x10</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideSizeType.html#OnScreen16x9">OnScreen16x9</a></span></code>
<div class="block">
 On screen 16x9</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideSizeType.html#Overhead">Overhead</a></span></code>
<div class="block">
 Overhead</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideSizeType.html#Slide35mm">Slide35mm</a></span></code>
<div class="block">
 Slide 35mm</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideSizeType.html#Widescreen">Widescreen</a></span></code>
<div class="block">
 Widescreen</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.com.aspose.ms.System.Enum">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;com.aspose.ms.System.Enum</h3>
<code>EnumSeparatorCharArray</code></li>
</ul>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.aspose.ms.System.Enum">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;com.aspose.ms.System.Enum</h3>
<code>Clone, CloneTo, format, format, get_Caption, get_Value, getName, getName, getNames, getNames, getUnderlyingType, getUnderlyingType, getValue, getValues, isDefined, isDefined, isDefined, isDefined, parse, parse, parse, parse, register, toObject, toString</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="OnScreen">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OnScreen</h4>
<pre>public static final&nbsp;int OnScreen</pre>
<div class="block"><p>
 On screen 4x3
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SlideSizeType.OnScreen">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="LetterPaper">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LetterPaper</h4>
<pre>public static final&nbsp;int LetterPaper</pre>
<div class="block"><p>
 Letter
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SlideSizeType.LetterPaper">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="A4Paper">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>A4Paper</h4>
<pre>public static final&nbsp;int A4Paper</pre>
<div class="block"><p>
 A4
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SlideSizeType.A4Paper">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Slide35mm">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Slide35mm</h4>
<pre>public static final&nbsp;int Slide35mm</pre>
<div class="block"><p>
 Slide 35mm
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SlideSizeType.Slide35mm">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Overhead">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Overhead</h4>
<pre>public static final&nbsp;int Overhead</pre>
<div class="block"><p>
 Overhead
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SlideSizeType.Overhead">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Banner">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Banner</h4>
<pre>public static final&nbsp;int Banner</pre>
<div class="block"><p>
 Banner
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SlideSizeType.Banner">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Custom">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Custom</h4>
<pre>public static final&nbsp;int Custom</pre>
<div class="block"><p>
 Custom size
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SlideSizeType.Custom">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Ledger">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Ledger</h4>
<pre>public static final&nbsp;int Ledger</pre>
<div class="block"><p>
 Ledger
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SlideSizeType.Ledger">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="A3Paper">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>A3Paper</h4>
<pre>public static final&nbsp;int A3Paper</pre>
<div class="block"><p>
 A3
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SlideSizeType.A3Paper">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="B4IsoPaper">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>B4IsoPaper</h4>
<pre>public static final&nbsp;int B4IsoPaper</pre>
<div class="block"><p>
 B4 ISO
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SlideSizeType.B4IsoPaper">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="B5IsoPaper">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>B5IsoPaper</h4>
<pre>public static final&nbsp;int B5IsoPaper</pre>
<div class="block"><p>
 B5 ISO
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SlideSizeType.B5IsoPaper">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="B4JisPaper">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>B4JisPaper</h4>
<pre>public static final&nbsp;int B4JisPaper</pre>
<div class="block"><p>
 B4 JIS
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SlideSizeType.B4JisPaper">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="B5JisPaper">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>B5JisPaper</h4>
<pre>public static final&nbsp;int B5JisPaper</pre>
<div class="block"><p>
 B5 JIS
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SlideSizeType.B5JisPaper">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="HagakiCard">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>HagakiCard</h4>
<pre>public static final&nbsp;int HagakiCard</pre>
<div class="block"><p>
 Hagaki card
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SlideSizeType.HagakiCard">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="OnScreen16x9">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OnScreen16x9</h4>
<pre>public static final&nbsp;int OnScreen16x9</pre>
<div class="block"><p>
 On screen 16x9
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SlideSizeType.OnScreen16x9">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="OnScreen16x10">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OnScreen16x10</h4>
<pre>public static final&nbsp;int OnScreen16x10</pre>
<div class="block"><p>
 On Screen 16x10
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SlideSizeType.OnScreen16x10">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Widescreen">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>Widescreen</h4>
<pre>public static final&nbsp;int Widescreen</pre>
<div class="block"><p>
 Widescreen
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SlideSizeType.Widescreen">Constant Field Values</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/SlideSizeScaleType.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/SlidesRange.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/SlideSizeType.html" target="_top">Frames</a></li>
<li><a href="SlideSizeType.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.classes.inherited.from.class.com.aspose.ms.System.Enum">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#methods.inherited.from.class.com.aspose.ms.System.Enum">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li>Method</li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><i>Copyright &copy; 2004-2025 Aspose Pty Ltd. All Rights Reserved.</i></small></p>
</body>
</html>
