<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_421) on Fri Jul 11 05:32:44 CDT 2025 -->
<title>SvgImage (Aspose.Slides for Java (25.7) API Documentation)</title>
<meta name="date" content="2025-07-11">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="SvgImage (Aspose.Slides for Java (25.7) API Documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/SvgExternalFontsHandling.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/SVGOptions.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/SvgImage.html" target="_top">Frames</a></li>
<li><a href="SvgImage.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.aspose.slides</div>
<h2 title="Class SvgImage" class="title">Class SvgImage</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.aspose.slides.SvgImage</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../com/aspose/slides/ISvgImage.html" title="interface in com.aspose.slides">ISvgImage</a></dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">SvgImage</span>
extends java.lang.Object
implements <a href="../../../com/aspose/slides/ISvgImage.html" title="interface in com.aspose.slides">ISvgImage</a></pre>
<div class="block"><p>
 Represents an SVG image.
 </p></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SvgImage.html#SvgImage-byte:A-">SvgImage</a></span>(byte[]&nbsp;data)</code>
<div class="block">
 Creates new SvgImage object.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SvgImage.html#SvgImage-byte:A-com.aspose.slides.IExternalResourceResolver-java.lang.String-">SvgImage</a></span>(byte[]&nbsp;data,
        <a href="../../../com/aspose/slides/IExternalResourceResolver.html" title="interface in com.aspose.slides">IExternalResourceResolver</a>&nbsp;externalResResolver,
        java.lang.String&nbsp;baseUri)</code>
<div class="block">
 Creates new SvgImage object.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SvgImage.html#SvgImage-java.io.InputStream-">SvgImage</a></span>(java.io.InputStream&nbsp;stream)</code>
<div class="block">
 Creates new SvgImage object.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SvgImage.html#SvgImage-java.io.InputStream-com.aspose.slides.IExternalResourceResolver-java.lang.String-">SvgImage</a></span>(java.io.InputStream&nbsp;stream,
        <a href="../../../com/aspose/slides/IExternalResourceResolver.html" title="interface in com.aspose.slides">IExternalResourceResolver</a>&nbsp;externalResResolver,
        java.lang.String&nbsp;baseUri)</code>
<div class="block">
 Creates new SvgImage object.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SvgImage.html#SvgImage-java.lang.String-">SvgImage</a></span>(java.lang.String&nbsp;svgContent)</code>
<div class="block">
 Creates new SvgImage object.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SvgImage.html#SvgImage-java.lang.String-com.aspose.slides.IExternalResourceResolver-java.lang.String-">SvgImage</a></span>(java.lang.String&nbsp;svgContent,
        <a href="../../../com/aspose/slides/IExternalResourceResolver.html" title="interface in com.aspose.slides">IExternalResourceResolver</a>&nbsp;externalResResolver,
        java.lang.String&nbsp;baseUri)</code>
<div class="block">
 Creates new SvgImage object.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SvgImage.html#getBaseUri--">getBaseUri</a></span>()</code>
<div class="block">
 Returns base URI of the specified Svg.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IExternalResourceResolver.html" title="interface in com.aspose.slides">IExternalResourceResolver</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SvgImage.html#getExternalResourceResolver--">getExternalResourceResolver</a></span>()</code>
<div class="block">
 Return callback interface used to resolve external resources during Svg documents import.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SvgImage.html#getSvgContent--">getSvgContent</a></span>()</code>
<div class="block">
 Returns SVG content.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SvgImage.html#getSvgData--">getSvgData</a></span>()</code>
<div class="block">
 Returns SVG data.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SvgImage.html#writeAsEmf-java.io.OutputStream-">writeAsEmf</a></span>(java.io.OutputStream&nbsp;stream)</code>
<div class="block">
 Saves the SVG image as an EMF file.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="SvgImage-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SvgImage</h4>
<pre>public&nbsp;SvgImage(byte[]&nbsp;data)</pre>
<div class="block"><p>
 Creates new SvgImage object. 
 </p></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>data</code> - Svg data.</dd>
</dl>
</li>
</ul>
<a name="SvgImage-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SvgImage</h4>
<pre>public&nbsp;SvgImage(java.lang.String&nbsp;svgContent)</pre>
<div class="block"><p>
 Creates new SvgImage object. 
 </p></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>svgContent</code> - Svg content.</dd>
</dl>
</li>
</ul>
<a name="SvgImage-java.io.InputStream-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SvgImage</h4>
<pre>public&nbsp;SvgImage(java.io.InputStream&nbsp;stream)</pre>
<div class="block"><p>
 Creates new SvgImage object. 
 </p></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>stream</code> - Svg stream.</dd>
</dl>
</li>
</ul>
<a name="SvgImage-byte:A-com.aspose.slides.IExternalResourceResolver-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SvgImage</h4>
<pre>public&nbsp;SvgImage(byte[]&nbsp;data,
                <a href="../../../com/aspose/slides/IExternalResourceResolver.html" title="interface in com.aspose.slides">IExternalResourceResolver</a>&nbsp;externalResResolver,
                java.lang.String&nbsp;baseUri)</pre>
<div class="block"><p>
 Creates new SvgImage object. 
 </p></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>data</code> - Svg data.</dd>
<dd><code>externalResResolver</code> - A callback object used to fetch external objects. If this parameter is null all external objects will be ignored.</dd>
<dd><code>baseUri</code> - Base URI of the specified Svg. Used to resolve relative links.</dd>
</dl>
</li>
</ul>
<a name="SvgImage-java.lang.String-com.aspose.slides.IExternalResourceResolver-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SvgImage</h4>
<pre>public&nbsp;SvgImage(java.lang.String&nbsp;svgContent,
                <a href="../../../com/aspose/slides/IExternalResourceResolver.html" title="interface in com.aspose.slides">IExternalResourceResolver</a>&nbsp;externalResResolver,
                java.lang.String&nbsp;baseUri)</pre>
<div class="block"><p>
 Creates new SvgImage object. 
 </p></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>svgContent</code> - Svg content.</dd>
<dd><code>externalResResolver</code> - A callback object used to fetch external objects. If this parameter is null all external objects will be ignored.</dd>
<dd><code>baseUri</code> - Base URI of the specified Svg. Used to resolve relative links.</dd>
</dl>
</li>
</ul>
<a name="SvgImage-java.io.InputStream-com.aspose.slides.IExternalResourceResolver-java.lang.String-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>SvgImage</h4>
<pre>public&nbsp;SvgImage(java.io.InputStream&nbsp;stream,
                <a href="../../../com/aspose/slides/IExternalResourceResolver.html" title="interface in com.aspose.slides">IExternalResourceResolver</a>&nbsp;externalResResolver,
                java.lang.String&nbsp;baseUri)</pre>
<div class="block"><p>
 Creates new SvgImage object. 
 </p></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>stream</code> - Svg stream.</dd>
<dd><code>externalResResolver</code> - A callback object used to fetch external objects. If this parameter is null all external objects will be ignored.</dd>
<dd><code>baseUri</code> - Base URI of the specified Svg. Used to resolve relative links.</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getSvgData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSvgData</h4>
<pre>public final&nbsp;byte[]&nbsp;getSvgData()</pre>
<div class="block"><p>
 Returns SVG data.
 Read-only <code>byte[]</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISvgImage.html#getSvgData--">getSvgData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISvgImage.html" title="interface in com.aspose.slides">ISvgImage</a></code></dd>
</dl>
</li>
</ul>
<a name="getExternalResourceResolver--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getExternalResourceResolver</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IExternalResourceResolver.html" title="interface in com.aspose.slides">IExternalResourceResolver</a>&nbsp;getExternalResourceResolver()</pre>
<div class="block"><p>
 Return callback interface used to resolve external resources during Svg documents import.
 Read-only <a href="../../../com/aspose/slides/IExternalResourceResolver.html" title="interface in com.aspose.slides"><code>IExternalResourceResolver</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISvgImage.html#getExternalResourceResolver--">getExternalResourceResolver</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISvgImage.html" title="interface in com.aspose.slides">ISvgImage</a></code></dd>
</dl>
</li>
</ul>
<a name="getBaseUri--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBaseUri</h4>
<pre>public final&nbsp;java.lang.String&nbsp;getBaseUri()</pre>
<div class="block"><p>
 Returns base URI of the specified Svg. Used to resolve relative links.
 Read-only <code>String</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISvgImage.html#getBaseUri--">getBaseUri</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISvgImage.html" title="interface in com.aspose.slides">ISvgImage</a></code></dd>
</dl>
</li>
</ul>
<a name="getSvgContent--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSvgContent</h4>
<pre>public final&nbsp;java.lang.String&nbsp;getSvgContent()</pre>
<div class="block"><p>
 Returns SVG content.
 Read-only <code>String</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISvgImage.html#getSvgContent--">getSvgContent</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISvgImage.html" title="interface in com.aspose.slides">ISvgImage</a></code></dd>
</dl>
</li>
</ul>
<a name="writeAsEmf-java.io.OutputStream-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>writeAsEmf</h4>
<pre>public final&nbsp;void&nbsp;writeAsEmf(java.io.OutputStream&nbsp;stream)</pre>
<div class="block"><p>
 Saves the SVG image as an EMF file.
 </p><p><hr><blockquote><pre>
 The following example shows how to save the SVG image to the metafile.
 <pre>
 // Creates the new SVG image
 ISvgImage svgImage = new SvgImage(new FileInputStream("content.svg"));
 // Saves the SVG image as a metafille
 FileOutputStream fileStream = new FileOutputStream("SvgAsEmf.emf");
 svgImage.writeAsEmf(fileStream);
 </pre>
 This sample demonstrates how to add the SVG image as a metafile to the presentation image collection.
 <pre>
 Presentation pres = new Presentation();
 try {
     // Creates the new SVG image
     ISvgImage svgImage = new SvgImage(new FileInputStream("content.svg"));
     ByteArrayOutputStream byteStream = new ByteArrayOutputStream();
     // Saves the SVG image as a metafille
     svgImage.writeAsEmf(byteStream);
     // Adds metafile to the image collection
     pres.getImages().addImage(byteStream.toByteArray());
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISvgImage.html#writeAsEmf-java.io.OutputStream-">writeAsEmf</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISvgImage.html" title="interface in com.aspose.slides">ISvgImage</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>stream</code> - Target stream</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>com.aspose.ms.System.ArgumentNullException</code> - Target stream is <b>null</b></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/SvgExternalFontsHandling.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/SVGOptions.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/SvgImage.html" target="_top">Frames</a></li>
<li><a href="SvgImage.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><i>Copyright &copy; 2004-2025 Aspose Pty Ltd. All Rights Reserved.</i></small></p>
</body>
</html>
