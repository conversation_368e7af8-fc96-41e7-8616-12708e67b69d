<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_421) on Fri Jul 11 05:32:44 CDT 2025 -->
<title>TextFrame (Aspose.Slides for Java (25.7) API Documentation)</title>
<meta name="date" content="2025-07-11">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="TextFrame (Aspose.Slides for Java (25.7) API Documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":42,"i11":10,"i12":42,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"],32:["t6","Deprecated Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/TextExtractionArrangingMode.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/TextFrameFormat.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/TextFrame.html" target="_top">Frames</a></li>
<li><a href="TextFrame.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.aspose.slides</div>
<h2 title="Class TextFrame" class="title">Class TextFrame</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.aspose.slides.TextFrame</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../com/aspose/slides/IPresentationComponent.html" title="interface in com.aspose.slides">IPresentationComponent</a>, <a href="../../../com/aspose/slides/ISlideComponent.html" title="interface in com.aspose.slides">ISlideComponent</a>, <a href="../../../com/aspose/slides/ITextFrame.html" title="interface in com.aspose.slides">ITextFrame</a></dd>
</dl>
<hr>
<br>
<pre>public final class <span class="typeNameLabel">TextFrame</span>
extends java.lang.Object
implements <a href="../../../com/aspose/slides/ITextFrame.html" title="interface in com.aspose.slides">ITextFrame</a></pre>
<div class="block"><p>
  Represents a TextFrame.
 </p></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t6" class="tableTab"><span><a href="javascript:show(32);">Deprecated Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IHyperlinkQueries.html" title="interface in com.aspose.slides">IHyperlinkQueries</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TextFrame.html#getHyperlinkQueries--">getHyperlinkQueries</a></span>()</code>
<div class="block">
 Provides easy access to contained hyperlinks.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IParagraphCollection.html" title="interface in com.aspose.slides">IParagraphCollection</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TextFrame.html#getParagraphs--">getParagraphs</a></span>()</code>
<div class="block">
 Returns the list of all paragraphs in a frame.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>com.aspose.slides.IDOMObject</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TextFrame.html#getParent_Immediate--">getParent_Immediate</a></span>()</code>
<div class="block">
 Returns Parent_Immediate object.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/ICell.html" title="interface in com.aspose.slides">ICell</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TextFrame.html#getParentCell--">getParentCell</a></span>()</code>
<div class="block">
 Returns the parent cell or null if the parent object does not implement the ICell interface.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TextFrame.html#getParentShape--">getParentShape</a></span>()</code>
<div class="block">
 Returns the parent shape or null if the parent object does not implement the IShape interface
 Read-only <a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides"><code>IShape</code></a>.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IPresentation.html" title="interface in com.aspose.slides">IPresentation</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TextFrame.html#getPresentation--">getPresentation</a></span>()</code>
<div class="block">
 Returns the parent presentation of a TextFrame.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IBaseSlide.html" title="interface in com.aspose.slides">IBaseSlide</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TextFrame.html#getSlide--">getSlide</a></span>()</code>
<div class="block">
 Returns the parent slide of a TextFrame.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TextFrame.html#getText--">getText</a></span>()</code>
<div class="block">
 Gets or sets the plain text for a TextFrame.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/ITextFrameFormat.html" title="interface in com.aspose.slides">ITextFrameFormat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TextFrame.html#getTextFrameFormat--">getTextFrameFormat</a></span>()</code>
<div class="block">
 Returns the formatting object for this TextFrame object.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TextFrame.html#highlightRegex-java.util.regex.Pattern-java.awt.Color-com.aspose.slides.IFindResultCallback-">highlightRegex</a></span>(java.util.regex.Pattern&nbsp;regex,
              java.awt.Color&nbsp;highlightColor,
              <a href="../../../com/aspose/slides/IFindResultCallback.html" title="interface in com.aspose.slides">IFindResultCallback</a>&nbsp;callback)</code>
<div class="block">
 Highlights all matches of the regular expression with the specified color.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TextFrame.html#highlightRegex-java.lang.String-java.awt.Color-com.aspose.slides.ITextHighlightingOptions-">highlightRegex</a></span>(java.lang.String&nbsp;regex,
              java.awt.Color&nbsp;highlightColor,
              <a href="../../../com/aspose/slides/ITextHighlightingOptions.html" title="interface in com.aspose.slides">ITextHighlightingOptions</a>&nbsp;options)</code>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;
<div class="block"><span class="deprecationComment">Use HighlightRegex(Regex regex, Color highlightColor, IFindResultCallback callback) method instead. The method will be removed after release of version 24.10.</span></div>
</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TextFrame.html#highlightText-java.lang.String-java.awt.Color-">highlightText</a></span>(java.lang.String&nbsp;text,
             java.awt.Color&nbsp;highlightColor)</code>
<div class="block">
 Highlights all matches of the sample text with the specified color.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TextFrame.html#highlightText-java.lang.String-java.awt.Color-com.aspose.slides.ITextHighlightingOptions-">highlightText</a></span>(java.lang.String&nbsp;text,
             java.awt.Color&nbsp;highlightColor,
             <a href="../../../com/aspose/slides/ITextHighlightingOptions.html" title="interface in com.aspose.slides">ITextHighlightingOptions</a>&nbsp;options)</code>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;
<div class="block"><span class="deprecationComment">Use HighlightText(string text, Color highlightColor, ITextSearchOptions options) method instead. The method will be removed after release of version 24.10.</span></div>
</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TextFrame.html#highlightText-java.lang.String-java.awt.Color-com.aspose.slides.ITextSearchOptions-com.aspose.slides.IFindResultCallback-">highlightText</a></span>(java.lang.String&nbsp;text,
             java.awt.Color&nbsp;highlightColor,
             <a href="../../../com/aspose/slides/ITextSearchOptions.html" title="interface in com.aspose.slides">ITextSearchOptions</a>&nbsp;options,
             <a href="../../../com/aspose/slides/IFindResultCallback.html" title="interface in com.aspose.slides">IFindResultCallback</a>&nbsp;callback)</code>
<div class="block">
 Highlights all matches of the sample text with the specified color.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TextFrame.html#joinPortionsWithSameFormatting--">joinPortionsWithSameFormatting</a></span>()</code>
<div class="block">
 Joins runs with same formatting in all paragraphs.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TextFrame.html#replaceRegex-java.util.regex.Pattern-java.lang.String-com.aspose.slides.IFindResultCallback-">replaceRegex</a></span>(java.util.regex.Pattern&nbsp;regex,
            java.lang.String&nbsp;newText,
            <a href="../../../com/aspose/slides/IFindResultCallback.html" title="interface in com.aspose.slides">IFindResultCallback</a>&nbsp;callback)</code>
<div class="block">
 Replaces all matches of regular expression with specified string.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TextFrame.html#replaceText-java.lang.String-java.lang.String-com.aspose.slides.ITextSearchOptions-com.aspose.slides.IFindResultCallback-">replaceText</a></span>(java.lang.String&nbsp;oldText,
           java.lang.String&nbsp;newText,
           <a href="../../../com/aspose/slides/ITextSearchOptions.html" title="interface in com.aspose.slides">ITextSearchOptions</a>&nbsp;options,
           <a href="../../../com/aspose/slides/IFindResultCallback.html" title="interface in com.aspose.slides">IFindResultCallback</a>&nbsp;callback)</code>
<div class="block">
 Replaces all occurrences of the specified text with another specified text.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TextFrame.html#setText-java.lang.String-">setText</a></span>(java.lang.String&nbsp;value)</code>
<div class="block">
 Gets or sets the plain text for a TextFrame.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>java.lang.String[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TextFrame.html#splitTextByColumns--">splitTextByColumns</a></span>()</code>
<div class="block">
 Splits the text content of the <a href="../../../com/aspose/slides/ITextFrame.html" title="interface in com.aspose.slides"><code>ITextFrame</code></a> into an array of strings,  
 where each element corresponds to a separate text column within the frame.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getParent_Immediate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getParent_Immediate</h4>
<pre>public final&nbsp;com.aspose.slides.IDOMObject&nbsp;getParent_Immediate()</pre>
<div class="block"><p>
 Returns Parent_Immediate object.
 Read-only <code>IDOMObject</code>.
 </p></div>
</li>
</ul>
<a name="getParagraphs--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getParagraphs</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IParagraphCollection.html" title="interface in com.aspose.slides">IParagraphCollection</a>&nbsp;getParagraphs()</pre>
<div class="block"><p>
 Returns the list of all paragraphs in a frame.
 Read-only <a href="../../../com/aspose/slides/IParagraphCollection.html" title="interface in com.aspose.slides"><code>IParagraphCollection</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITextFrame.html#getParagraphs--">getParagraphs</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITextFrame.html" title="interface in com.aspose.slides">ITextFrame</a></code></dd>
</dl>
</li>
</ul>
<a name="getText--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getText</h4>
<pre>public final&nbsp;java.lang.String&nbsp;getText()</pre>
<div class="block"><p>
 Gets or sets the plain text for a TextFrame.
 Read/write <code>String</code>.
 </p>Value: 
 The text.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITextFrame.html#getText--">getText</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITextFrame.html" title="interface in com.aspose.slides">ITextFrame</a></code></dd>
</dl>
</li>
</ul>
<a name="setText-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setText</h4>
<pre>public final&nbsp;void&nbsp;setText(java.lang.String&nbsp;value)</pre>
<div class="block"><p>
 Gets or sets the plain text for a TextFrame.
 Read/write <code>String</code>.
 </p>Value: 
 The text.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITextFrame.html#setText-java.lang.String-">setText</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITextFrame.html" title="interface in com.aspose.slides">ITextFrame</a></code></dd>
</dl>
</li>
</ul>
<a name="getTextFrameFormat--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTextFrameFormat</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/ITextFrameFormat.html" title="interface in com.aspose.slides">ITextFrameFormat</a>&nbsp;getTextFrameFormat()</pre>
<div class="block"><p>
 Returns the formatting object for this TextFrame object.
 Read-only <a href="../../../com/aspose/slides/ITextFrameFormat.html" title="interface in com.aspose.slides"><code>ITextFrameFormat</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITextFrame.html#getTextFrameFormat--">getTextFrameFormat</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITextFrame.html" title="interface in com.aspose.slides">ITextFrame</a></code></dd>
</dl>
</li>
</ul>
<a name="getHyperlinkQueries--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHyperlinkQueries</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IHyperlinkQueries.html" title="interface in com.aspose.slides">IHyperlinkQueries</a>&nbsp;getHyperlinkQueries()</pre>
<div class="block"><p>
 Provides easy access to contained hyperlinks.
 Read-only <a href="../../../com/aspose/slides/IHyperlinkQueries.html" title="interface in com.aspose.slides"><code>IHyperlinkQueries</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITextFrame.html#getHyperlinkQueries--">getHyperlinkQueries</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITextFrame.html" title="interface in com.aspose.slides">ITextFrame</a></code></dd>
</dl>
</li>
</ul>
<a name="joinPortionsWithSameFormatting--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>joinPortionsWithSameFormatting</h4>
<pre>public final&nbsp;void&nbsp;joinPortionsWithSameFormatting()</pre>
<div class="block"><p>
 Joins runs with same formatting in all paragraphs.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITextFrame.html#joinPortionsWithSameFormatting--">joinPortionsWithSameFormatting</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITextFrame.html" title="interface in com.aspose.slides">ITextFrame</a></code></dd>
</dl>
</li>
</ul>
<a name="highlightText-java.lang.String-java.awt.Color-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>highlightText</h4>
<pre>public final&nbsp;void&nbsp;highlightText(java.lang.String&nbsp;text,
                                java.awt.Color&nbsp;highlightColor)</pre>
<div class="block"><p>
 Highlights all matches of the sample text with the specified color.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITextFrame.html#highlightText-java.lang.String-java.awt.Color-">highlightText</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITextFrame.html" title="interface in com.aspose.slides">ITextFrame</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>text</code> - Text sample to highlight.</dd>
<dd><code>highlightColor</code> - The color to highlight the text.</dd>
</dl>
</li>
</ul>
<a name="highlightText-java.lang.String-java.awt.Color-com.aspose.slides.ITextHighlightingOptions-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>highlightText</h4>
<pre>@Deprecated
public final&nbsp;void&nbsp;highlightText(java.lang.String&nbsp;text,
                                             java.awt.Color&nbsp;highlightColor,
                                             <a href="../../../com/aspose/slides/ITextHighlightingOptions.html" title="interface in com.aspose.slides">ITextHighlightingOptions</a>&nbsp;options)</pre>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;<span class="deprecationComment">Use HighlightText(string text, Color highlightColor, ITextSearchOptions options) method instead. The method will be removed after release of version 24.10.</span></div>
<div class="block"><p>
 Highlights all matches of the sample text with the specified color.
 </p><p><hr><blockquote><pre>
 The following sample code shows how to Highlight Text in a TextFrame.
 <pre>
 try {
     TextHighlightingOptions textHighlightingOptions = new TextHighlightingOptions();
     textHighlightingOptions.setWholeWordsOnly(true);
     // highlighting all words 'important'
     ((AutoShape)pres.getSlides().get_Item(0).getShapes().get_Item(0)).getTextFrame().highlightText("title", Color.BLUE);
     // highlighting all separate 'the' occurrences
     ((AutoShape)pres.getSlides().get_Item(0).getShapes().get_Item(0)).getTextFrame().highlightText("to", Color.MAGENTA, textHighlightingOptions);
     pres.save("SomePresentation-out2.pptx", SaveFormat.Pptx);
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITextFrame.html#highlightText-java.lang.String-java.awt.Color-com.aspose.slides.ITextHighlightingOptions-">highlightText</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITextFrame.html" title="interface in com.aspose.slides">ITextFrame</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>text</code> - The text to highlight.</dd>
<dd><code>highlightColor</code> - The color to highlight the text.</dd>
<dd><code>options</code> - Highlighting options.</dd>
</dl>
</li>
</ul>
<a name="splitTextByColumns--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>splitTextByColumns</h4>
<pre>public final&nbsp;java.lang.String[]&nbsp;splitTextByColumns()</pre>
<div class="block"><p>
 Splits the text content of the <a href="../../../com/aspose/slides/ITextFrame.html" title="interface in com.aspose.slides"><code>ITextFrame</code></a> into an array of strings,  
 where each element corresponds to a separate text column within the frame.
 </p><p><hr><blockquote><pre>
 The following example demonstrates how to use <a href="../../../com/aspose/slides/TextFrame.html#splitTextByColumns--"><code>splitTextByColumns()</code></a>:
 <pre>
 Presentation pres = new Presentation("example.pptx");
 try {
     // Get the first shape on the slide and cast it to ITextFrame
     ITextFrame textFrame = (ITextFrame) pres.getSlides().get_Item(0).getShapes().get_Item(0);
     // Split the text frame content into columns
     String[] columnsText = textFrame.splitTextByColumns();
     // Print each column's text to the console
     for (String column : columnsText)
         System.out.println(column);
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITextFrame.html#splitTextByColumns--">splitTextByColumns</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITextFrame.html" title="interface in com.aspose.slides">ITextFrame</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>An array of strings, where each string represents the text content of a specific column  
 in the <a href="../../../com/aspose/slides/ITextFrame.html" title="interface in com.aspose.slides"><code>ITextFrame</code></a>.
 <p><hr>
 If the text frame does not contain multiple columns, the returned array will have a single element  
 containing the full text.  
 Empty columns will be represented as empty strings in the array.
 </hr></p></dd>
</dl>
</li>
</ul>
<a name="highlightText-java.lang.String-java.awt.Color-com.aspose.slides.ITextSearchOptions-com.aspose.slides.IFindResultCallback-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>highlightText</h4>
<pre>public final&nbsp;void&nbsp;highlightText(java.lang.String&nbsp;text,
                                java.awt.Color&nbsp;highlightColor,
                                <a href="../../../com/aspose/slides/ITextSearchOptions.html" title="interface in com.aspose.slides">ITextSearchOptions</a>&nbsp;options,
                                <a href="../../../com/aspose/slides/IFindResultCallback.html" title="interface in com.aspose.slides">IFindResultCallback</a>&nbsp;callback)</pre>
<div class="block"><p>
 Highlights all matches of the sample text with the specified color.
 </p><p><hr><blockquote><pre>
 The following code sample shows how to highlight text in a TextFrame.
 <pre>
 Presentation presentation = new Presentation("SomePresentation.pptx");
 try {
     TextSearchOptions textSearchOptions = new TextSearchOptions();
     textSearchOptions.setWholeWordsOnly(true);
     // highlighting all words 'important'
     ((AutoShape)presentation.getSlides().get_Item(0).getShapes().get_Item(0)).getTextFrame().highlightText("important", Color.BLUE);
     // highlighting all separate 'the' occurrences
     ((AutoShape)presentation.getSlides().get_Item(0).getShapes().get_Item(0)).getTextFrame().highlightText("the", Color.MAGENTA, textSearchOptions, null);
     presentation.save("SomePresentation-out2.pptx", SaveFormat.Pptx);
 } finally {
     if (presentation != null) presentation.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITextFrame.html#highlightText-java.lang.String-java.awt.Color-com.aspose.slides.ITextSearchOptions-com.aspose.slides.IFindResultCallback-">highlightText</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITextFrame.html" title="interface in com.aspose.slides">ITextFrame</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>text</code> - The text to highlight.</dd>
<dd><code>highlightColor</code> - The color to highlight the text.</dd>
<dd><code>options</code> - Text search options <a href="../../../com/aspose/slides/ITextSearchOptions.html" title="interface in com.aspose.slides"><code>ITextSearchOptions</code></a>.</dd>
<dd><code>callback</code> - The callback object for receiving search results <a href="../../../com/aspose/slides/IFindResultCallback.html" title="interface in com.aspose.slides"><code>IFindResultCallback</code></a>.</dd>
</dl>
</li>
</ul>
<a name="highlightRegex-java.lang.String-java.awt.Color-com.aspose.slides.ITextHighlightingOptions-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>highlightRegex</h4>
<pre>@Deprecated
public final&nbsp;void&nbsp;highlightRegex(java.lang.String&nbsp;regex,
                                              java.awt.Color&nbsp;highlightColor,
                                              <a href="../../../com/aspose/slides/ITextHighlightingOptions.html" title="interface in com.aspose.slides">ITextHighlightingOptions</a>&nbsp;options)</pre>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;<span class="deprecationComment">Use HighlightRegex(Regex regex, Color highlightColor, IFindResultCallback callback) method instead. The method will be removed after release of version 24.10.</span></div>
<div class="block"><p>
 Highlights all matches of the regular expression with the specified color.
 </p><p><hr><blockquote><pre>
 The following code sample shows how to highlight text in a TextFrame using a regular expression.
 <pre>
 Presentation pres = new Presentation("SomePresentation.pptx");
 try {
     TextHighlightingOptions options = new TextHighlightingOptions();
     // highlighting all words with 10 symbols or longer
     ((AutoShape) pres.getSlides().get_Item(0).getShapes().get_Item(0)).getTextFrame().highlightRegex("\\b[^\\s){5,}\\b", Color.BLUE, options);
     pres.save("SomePresentation-out.pptx", SaveFormat.Pptx);
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITextFrame.html#highlightRegex-java.lang.String-java.awt.Color-com.aspose.slides.ITextHighlightingOptions-">highlightRegex</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITextFrame.html" title="interface in com.aspose.slides">ITextFrame</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>regex</code> - Text of regular expression to get text to highlight.</dd>
<dd><code>highlightColor</code> - The color to highlight the text.</dd>
<dd><code>options</code> - Highlighting options.</dd>
</dl>
</li>
</ul>
<a name="highlightRegex-java.util.regex.Pattern-java.awt.Color-com.aspose.slides.IFindResultCallback-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>highlightRegex</h4>
<pre>public final&nbsp;void&nbsp;highlightRegex(java.util.regex.Pattern&nbsp;regex,
                                 java.awt.Color&nbsp;highlightColor,
                                 <a href="../../../com/aspose/slides/IFindResultCallback.html" title="interface in com.aspose.slides">IFindResultCallback</a>&nbsp;callback)</pre>
<div class="block"><p>
 Highlights all matches of the regular expression with the specified color.
 </p><p><hr><blockquote><pre>
 The following code sample shows how to highlight text in a TextFrame using a regular expression.
 <pre>
 Presentation presentation = new Presentation("SomePresentation.pptx");
 try {
     Pattern regex = Pattern.compile("\\b[^\\s]{5,}\\b");
     // highlighting all words with 5 symbols or longer
     ((AutoShape)presentation.getSlides().get_Item(0).getShapes().get_Item(0)).getTextFrame().highlightRegex(regex, Color.BLUE, null);
     presentation.save("SomePresentation-out.pptx", SaveFormat.Pptx);
 } finally {
     if (presentation != null) presentation.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITextFrame.html#highlightRegex-java.util.regex.Pattern-java.awt.Color-com.aspose.slides.IFindResultCallback-">highlightRegex</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITextFrame.html" title="interface in com.aspose.slides">ITextFrame</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>regex</code> - The regular expression <code>Pattern</code> to get strings to highlight.</dd>
<dd><code>highlightColor</code> - The color to highlight the text.</dd>
<dd><code>callback</code> - The callback object for receiving search results <a href="../../../com/aspose/slides/IFindResultCallback.html" title="interface in com.aspose.slides"><code>IFindResultCallback</code></a>.</dd>
</dl>
</li>
</ul>
<a name="replaceText-java.lang.String-java.lang.String-com.aspose.slides.ITextSearchOptions-com.aspose.slides.IFindResultCallback-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>replaceText</h4>
<pre>public final&nbsp;void&nbsp;replaceText(java.lang.String&nbsp;oldText,
                              java.lang.String&nbsp;newText,
                              <a href="../../../com/aspose/slides/ITextSearchOptions.html" title="interface in com.aspose.slides">ITextSearchOptions</a>&nbsp;options,
                              <a href="../../../com/aspose/slides/IFindResultCallback.html" title="interface in com.aspose.slides">IFindResultCallback</a>&nbsp;callback)</pre>
<div class="block"><p>
 Replaces all occurrences of the specified text with another specified text.
 </p><p><hr><blockquote><pre>
 The following sample code shows how to replace one speified string with another speified string.
 <pre>
 Presentation presentation = new Presentation("SomePresentation.pptx");
 try {
     TextSearchOptions textSearchOptions = new TextSearchOptions();
     textSearchOptions.setWholeWordsOnly(true);
     // Replace all separate 'the' occurrences with '***'
     ((AutoShape)presentation.getSlides().get_Item(0).getShapes().get_Item(0)).getTextFrame().replaceText("the", "***", textSearchOptions, null);
     presentation.save("SomePresentation-out2.pptx", SaveFormat.Pptx);
 } finally {
     if (presentation != null) presentation.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITextFrame.html#replaceText-java.lang.String-java.lang.String-com.aspose.slides.ITextSearchOptions-com.aspose.slides.IFindResultCallback-">replaceText</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITextFrame.html" title="interface in com.aspose.slides">ITextFrame</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>oldText</code> - The string to be replaced.</dd>
<dd><code>newText</code> - The string to replace all occurrences of oldText.</dd>
<dd><code>options</code> - Text search options <a href="../../../com/aspose/slides/ITextSearchOptions.html" title="interface in com.aspose.slides"><code>ITextSearchOptions</code></a>.</dd>
<dd><code>callback</code> - Callback object for saving replacement operation result <a href="../../../com/aspose/slides/IFindResultCallback.html" title="interface in com.aspose.slides"><code>IFindResultCallback</code></a>.</dd>
</dl>
</li>
</ul>
<a name="replaceRegex-java.util.regex.Pattern-java.lang.String-com.aspose.slides.IFindResultCallback-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>replaceRegex</h4>
<pre>public final&nbsp;void&nbsp;replaceRegex(java.util.regex.Pattern&nbsp;regex,
                               java.lang.String&nbsp;newText,
                               <a href="../../../com/aspose/slides/IFindResultCallback.html" title="interface in com.aspose.slides">IFindResultCallback</a>&nbsp;callback)</pre>
<div class="block"><p>
 Replaces all matches of regular expression with specified string.
 </p><p><hr><blockquote><pre>
 The following sample code shows how to replace text using regular expression with specified string.
 <pre>
 Presentation presentation = new Presentation("SomePresentation.pptx");
 try {
     Pattern regex = Pattern.compile("\\b[^\\s]{5,}\\b");
     // Replace all words with 5 symbols or longer with '***'
     ((AutoShape)presentation.getSlides().get_Item(0).getShapes().get_Item(0)).getTextFrame().replaceRegex(regex, "***", null);
     presentation.save("SomePresentation-out.pptx", SaveFormat.Pptx);
 } finally {
     if (presentation != null) presentation.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITextFrame.html#replaceRegex-java.util.regex.Pattern-java.lang.String-com.aspose.slides.IFindResultCallback-">replaceRegex</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITextFrame.html" title="interface in com.aspose.slides">ITextFrame</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>regex</code> - The regular expression <code>Pattern</code> to get strings to be replaced.</dd>
<dd><code>newText</code> - The string to replace all occurrences of strings to be replaced.</dd>
<dd><code>callback</code> - Callback object for saving replacement operation result <a href="../../../com/aspose/slides/IFindResultCallback.html" title="interface in com.aspose.slides"><code>IFindResultCallback</code></a>.</dd>
</dl>
</li>
</ul>
<a name="getSlide--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSlide</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IBaseSlide.html" title="interface in com.aspose.slides">IBaseSlide</a>&nbsp;getSlide()</pre>
<div class="block"><p>
 Returns the parent slide of a TextFrame.
 Read-only <a href="../../../com/aspose/slides/IBaseSlide.html" title="interface in com.aspose.slides"><code>IBaseSlide</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISlideComponent.html#getSlide--">getSlide</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISlideComponent.html" title="interface in com.aspose.slides">ISlideComponent</a></code></dd>
</dl>
</li>
</ul>
<a name="getPresentation--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPresentation</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IPresentation.html" title="interface in com.aspose.slides">IPresentation</a>&nbsp;getPresentation()</pre>
<div class="block"><p>
 Returns the parent presentation of a TextFrame.
 Read-only <a href="../../../com/aspose/slides/IPresentation.html" title="interface in com.aspose.slides"><code>IPresentation</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPresentationComponent.html#getPresentation--">getPresentation</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPresentationComponent.html" title="interface in com.aspose.slides">IPresentationComponent</a></code></dd>
</dl>
</li>
</ul>
<a name="getParentShape--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getParentShape</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a>&nbsp;getParentShape()</pre>
<div class="block"><p>
 Returns the parent shape or null if the parent object does not implement the IShape interface
 Read-only <a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides"><code>IShape</code></a>.
 </p><p><hr><blockquote><pre>
 The following code sample shows 
 <pre>
 Presentation presentation = new Presentation("SomePresentation.pptx");
 try {
     AutoShape autoShape = (AutoShape)presentation.getSlides().get_Item(0).getShapes().get_Item(0);
     Table table = (Table)presentation.getSlides().get_Item(0).getShapes().get_Item(1);

     // These assertions are always true
     Assert.assertTrue(autoShape.getTextFrame().getParentShape() == autoShape);
     Assert.assertTrue((table.get_Item(0,0).getTextFrame()).getParentShape() == null);
 } finally {
     if (presentation != null) presentation.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITextFrame.html#getParentShape--">getParentShape</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITextFrame.html" title="interface in com.aspose.slides">ITextFrame</a></code></dd>
</dl>
</li>
</ul>
<a name="getParentCell--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getParentCell</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/ICell.html" title="interface in com.aspose.slides">ICell</a>&nbsp;getParentCell()</pre>
<div class="block"><p>
 Returns the parent cell or null if the parent object does not implement the ICell interface.
 Read-only <a href="../../../com/aspose/slides/ICell.html" title="interface in com.aspose.slides"><code>ICell</code></a>.
 </p><p><hr><blockquote><pre>
 The following code sample shows 
 <pre>
 Presentation presentation = new Presentation("SomePresentation.pptx");
 try {
     AutoShape autoShape = (AutoShape)presentation.getSlides().get_Item(0).getShapes().get_Item(0);
     Table table = (Table)presentation.getSlides().get_Item(0).getShapes().get_Item(1);

     // These assertions are always true
     Assert.assertTrue(table.get_Item(0,0).getTextFrame().getParentCell() == table.get_Item(0,0));
     Assert.assertTrue(autoShape.getTextFrame().getParentCell() == null);
 } finally {
     if (presentation != null) presentation.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITextFrame.html#getParentCell--">getParentCell</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITextFrame.html" title="interface in com.aspose.slides">ITextFrame</a></code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/TextExtractionArrangingMode.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/TextFrameFormat.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/TextFrame.html" target="_top">Frames</a></li>
<li><a href="TextFrame.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><i>Copyright &copy; 2004-2025 Aspose Pty Ltd. All Rights Reserved.</i></small></p>
</body>
</html>
