<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_421) on Fri Jul 11 05:32:45 CDT 2025 -->
<title>TransitionType (Aspose.Slides for Java (25.7) API Documentation)</title>
<meta name="date" content="2025-07-11">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="TransitionType (Aspose.Slides for Java (25.7) API Documentation)";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/TransitionSpeed.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/TransitionValueBase.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/TransitionType.html" target="_top">Frames</a></li>
<li><a href="TransitionType.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.classes.inherited.from.class.com.aspose.ms.System.Enum">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#methods.inherited.from.class.com.aspose.ms.System.Enum">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li>Method</li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.aspose.slides</div>
<h2 title="Class TransitionType" class="title">Class TransitionType</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.aspose.ms.System.ValueType&lt;com.aspose.ms.System.Enum&gt;</li>
<li>
<ul class="inheritance">
<li>com.aspose.ms.System.Enum</li>
<li>
<ul class="inheritance">
<li>com.aspose.slides.TransitionType</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public final class <span class="typeNameLabel">TransitionType</span>
extends com.aspose.ms.System.Enum</pre>
<div class="block"><p>
 Represent slide show transition type.
 </p></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.com.aspose.ms.System.Enum">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from class&nbsp;com.aspose.ms.System.Enum</h3>
<code>com.aspose.ms.System.Enum.AbstractEnum, com.aspose.ms.System.Enum.FlaggedEnum, com.aspose.ms.System.Enum.ObjectEnum, com.aspose.ms.System.Enum.SimpleEnum</code></li>
</ul>
</li>
</ul>
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TransitionType.html#Airplane">Airplane</a></span></code>
<div class="block">Available in PowerPoint 2013.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TransitionType.html#Blinds">Blinds</a></span></code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TransitionType.html#Box">Box</a></span></code>
<div class="block">Available in PowerPoint 2010.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TransitionType.html#Checker">Checker</a></span></code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TransitionType.html#Circle">Circle</a></span></code>
<div class="block">
 Relates to transition Shape (with option Circle) in PowerPoint 2010.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TransitionType.html#Comb">Comb</a></span></code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TransitionType.html#Conveyor">Conveyor</a></span></code>
<div class="block">Available in PowerPoint 2010.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TransitionType.html#Cover">Cover</a></span></code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TransitionType.html#Crush">Crush</a></span></code>
<div class="block">Available in PowerPoint 2013.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TransitionType.html#Cube">Cube</a></span></code>
<div class="block">Available in PowerPoint 2010.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TransitionType.html#Curtains">Curtains</a></span></code>
<div class="block">Available in PowerPoint 2013.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TransitionType.html#Cut">Cut</a></span></code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TransitionType.html#Diamond">Diamond</a></span></code>
<div class="block">
 Relates to transition Shape (with option Diamond) in PowerPoint 2010.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TransitionType.html#Dissolve">Dissolve</a></span></code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TransitionType.html#Doors">Doors</a></span></code>
<div class="block">Available in PowerPoint 2010.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TransitionType.html#Drape">Drape</a></span></code>
<div class="block">Available in PowerPoint 2013.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TransitionType.html#Fade">Fade</a></span></code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TransitionType.html#FallOver">FallOver</a></span></code>
<div class="block">Available in PowerPoint 2013.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TransitionType.html#Ferris">Ferris</a></span></code>
<div class="block">Available in PowerPoint 2010.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TransitionType.html#Flash">Flash</a></span></code>
<div class="block">Available in PowerPoint 2010.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TransitionType.html#Flip">Flip</a></span></code>
<div class="block">Available in PowerPoint 2010.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TransitionType.html#Flythrough">Flythrough</a></span></code>
<div class="block">Available in PowerPoint 2010.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TransitionType.html#Fracture">Fracture</a></span></code>
<div class="block">Available in PowerPoint 2013.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TransitionType.html#Gallery">Gallery</a></span></code>
<div class="block">Available in PowerPoint 2010.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TransitionType.html#Glitter">Glitter</a></span></code>
<div class="block">Available in PowerPoint 2010.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TransitionType.html#Honeycomb">Honeycomb</a></span></code>
<div class="block">Available in PowerPoint 2010.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TransitionType.html#Morph">Morph</a></span></code>
<div class="block">Relates to transition Morph (with option Type) in PowerPoint 2019.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TransitionType.html#Newsflash">Newsflash</a></span></code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TransitionType.html#None">None</a></span></code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TransitionType.html#Orbit">Orbit</a></span></code>
<div class="block">Available in PowerPoint 2010.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TransitionType.html#Origami">Origami</a></span></code>
<div class="block">Available in PowerPoint 2013.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TransitionType.html#PageCurlDouble">PageCurlDouble</a></span></code>
<div class="block">Available in PowerPoint 2013.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TransitionType.html#PageCurlSingle">PageCurlSingle</a></span></code>
<div class="block">Available in PowerPoint 2013.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TransitionType.html#Pan">Pan</a></span></code>
<div class="block">Available in PowerPoint 2010.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TransitionType.html#PeelOff">PeelOff</a></span></code>
<div class="block">Available in PowerPoint 2013.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TransitionType.html#Plus">Plus</a></span></code>
<div class="block">
 Relates to transition Shape (with option Plus) in PowerPoint 2010.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TransitionType.html#Prestige">Prestige</a></span></code>
<div class="block">Available in PowerPoint 2013.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TransitionType.html#Pull">Pull</a></span></code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TransitionType.html#Push">Push</a></span></code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TransitionType.html#Random">Random</a></span></code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TransitionType.html#RandomBar">RandomBar</a></span></code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TransitionType.html#Reveal">Reveal</a></span></code>
<div class="block">Available in PowerPoint 2010.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TransitionType.html#Ripple">Ripple</a></span></code>
<div class="block">Available in PowerPoint 2010.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TransitionType.html#Rotate">Rotate</a></span></code>
<div class="block">Available in PowerPoint 2010.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TransitionType.html#Shred">Shred</a></span></code>
<div class="block">Available in PowerPoint 2010.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TransitionType.html#Split">Split</a></span></code>
<div class="block">
 Equivalent to transition Wipe in PowerPoint 2010.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TransitionType.html#Strips">Strips</a></span></code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TransitionType.html#Switch">Switch</a></span></code>
<div class="block">Available in PowerPoint 2010.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TransitionType.html#Vortex">Vortex</a></span></code>
<div class="block">Available in PowerPoint 2010.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TransitionType.html#Warp">Warp</a></span></code>
<div class="block">
 Relates to transition Zoom in PowerPoint 2010.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TransitionType.html#Wedge">Wedge</a></span></code>
<div class="block">
 Relates to transition Clock (with option Wedge) in PowerPoint 2010.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TransitionType.html#Wheel">Wheel</a></span></code>
<div class="block">
 Relates to transition Clock (with option Clockwise) in PowerPoint 2010.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TransitionType.html#WheelReverse">WheelReverse</a></span></code>
<div class="block">
 Relates to transition Clock (with option Counterclockwise) in PowerPoint 2010.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TransitionType.html#Wind">Wind</a></span></code>
<div class="block">Available in PowerPoint 2013.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TransitionType.html#Window">Window</a></span></code>
<div class="block">Available in PowerPoint 2010.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TransitionType.html#Wipe">Wipe</a></span></code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TransitionType.html#Zoom">Zoom</a></span></code>
<div class="block">
 Relates to transition Shape (with options In/Out) in PowerPoint 2010.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.com.aspose.ms.System.Enum">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;com.aspose.ms.System.Enum</h3>
<code>EnumSeparatorCharArray</code></li>
</ul>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.aspose.ms.System.Enum">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;com.aspose.ms.System.Enum</h3>
<code>Clone, CloneTo, format, format, get_Caption, get_Value, getName, getName, getNames, getNames, getUnderlyingType, getUnderlyingType, getValue, getValues, isDefined, isDefined, isDefined, isDefined, parse, parse, parse, parse, register, toObject, toString</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="None">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>None</h4>
<pre>public static final&nbsp;int None</pre>
<div class="block"><p></p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TransitionType.None">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Blinds">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Blinds</h4>
<pre>public static final&nbsp;int Blinds</pre>
<div class="block"><p></p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TransitionType.Blinds">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Checker">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Checker</h4>
<pre>public static final&nbsp;int Checker</pre>
<div class="block"><p></p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TransitionType.Checker">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Circle">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Circle</h4>
<pre>public static final&nbsp;int Circle</pre>
<div class="block"><p>
 Relates to transition Shape (with option Circle) in PowerPoint 2010.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TransitionType.Circle">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Comb">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Comb</h4>
<pre>public static final&nbsp;int Comb</pre>
<div class="block"><p></p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TransitionType.Comb">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Cover">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Cover</h4>
<pre>public static final&nbsp;int Cover</pre>
<div class="block"><p></p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TransitionType.Cover">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Cut">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Cut</h4>
<pre>public static final&nbsp;int Cut</pre>
<div class="block"><p></p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TransitionType.Cut">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Diamond">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Diamond</h4>
<pre>public static final&nbsp;int Diamond</pre>
<div class="block"><p>
 Relates to transition Shape (with option Diamond) in PowerPoint 2010.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TransitionType.Diamond">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Dissolve">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Dissolve</h4>
<pre>public static final&nbsp;int Dissolve</pre>
<div class="block"><p></p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TransitionType.Dissolve">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Fade">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Fade</h4>
<pre>public static final&nbsp;int Fade</pre>
<div class="block"><p></p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TransitionType.Fade">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Newsflash">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Newsflash</h4>
<pre>public static final&nbsp;int Newsflash</pre>
<div class="block"><p></p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TransitionType.Newsflash">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Plus">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Plus</h4>
<pre>public static final&nbsp;int Plus</pre>
<div class="block"><p>
 Relates to transition Shape (with option Plus) in PowerPoint 2010.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TransitionType.Plus">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Pull">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Pull</h4>
<pre>public static final&nbsp;int Pull</pre>
<div class="block"><p></p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TransitionType.Pull">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Push">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Push</h4>
<pre>public static final&nbsp;int Push</pre>
<div class="block"><p></p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TransitionType.Push">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Random">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Random</h4>
<pre>public static final&nbsp;int Random</pre>
<div class="block"><p></p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TransitionType.Random">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="RandomBar">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RandomBar</h4>
<pre>public static final&nbsp;int RandomBar</pre>
<div class="block"><p></p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TransitionType.RandomBar">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Split">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Split</h4>
<pre>public static final&nbsp;int Split</pre>
<div class="block"><p>
 Equivalent to transition Wipe in PowerPoint 2010.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TransitionType.Split">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Strips">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Strips</h4>
<pre>public static final&nbsp;int Strips</pre>
<div class="block"><p></p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TransitionType.Strips">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Wedge">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Wedge</h4>
<pre>public static final&nbsp;int Wedge</pre>
<div class="block"><p>
 Relates to transition Clock (with option Wedge) in PowerPoint 2010.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TransitionType.Wedge">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Wheel">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Wheel</h4>
<pre>public static final&nbsp;int Wheel</pre>
<div class="block"><p>
 Relates to transition Clock (with option Clockwise) in PowerPoint 2010.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TransitionType.Wheel">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Wipe">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Wipe</h4>
<pre>public static final&nbsp;int Wipe</pre>
<div class="block"><p></p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TransitionType.Wipe">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Zoom">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Zoom</h4>
<pre>public static final&nbsp;int Zoom</pre>
<div class="block"><p>
 Relates to transition Shape (with options In/Out) in PowerPoint 2010.
 See also <a href="../../../com/aspose/slides/TransitionType.html#Warp"><code>Warp</code></a> that relates to transition Zoom in PowerPoint 2010.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TransitionType.Zoom">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Vortex">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Vortex</h4>
<pre>public static final&nbsp;int Vortex</pre>
<div class="block"><p>Available in PowerPoint 2010.</p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TransitionType.Vortex">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Switch">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Switch</h4>
<pre>public static final&nbsp;int Switch</pre>
<div class="block"><p>Available in PowerPoint 2010.</p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TransitionType.Switch">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Flip">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Flip</h4>
<pre>public static final&nbsp;int Flip</pre>
<div class="block"><p>Available in PowerPoint 2010.</p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TransitionType.Flip">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Ripple">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Ripple</h4>
<pre>public static final&nbsp;int Ripple</pre>
<div class="block"><p>Available in PowerPoint 2010.</p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TransitionType.Ripple">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Honeycomb">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Honeycomb</h4>
<pre>public static final&nbsp;int Honeycomb</pre>
<div class="block"><p>Available in PowerPoint 2010.</p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TransitionType.Honeycomb">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Cube">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Cube</h4>
<pre>public static final&nbsp;int Cube</pre>
<div class="block"><p>Available in PowerPoint 2010.</p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TransitionType.Cube">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Box">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Box</h4>
<pre>public static final&nbsp;int Box</pre>
<div class="block"><p>Available in PowerPoint 2010.</p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TransitionType.Box">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Rotate">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Rotate</h4>
<pre>public static final&nbsp;int Rotate</pre>
<div class="block"><p>Available in PowerPoint 2010.</p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TransitionType.Rotate">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Orbit">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Orbit</h4>
<pre>public static final&nbsp;int Orbit</pre>
<div class="block"><p>Available in PowerPoint 2010.</p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TransitionType.Orbit">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Doors">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Doors</h4>
<pre>public static final&nbsp;int Doors</pre>
<div class="block"><p>Available in PowerPoint 2010.</p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TransitionType.Doors">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Window">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Window</h4>
<pre>public static final&nbsp;int Window</pre>
<div class="block"><p>Available in PowerPoint 2010.</p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TransitionType.Window">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Ferris">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Ferris</h4>
<pre>public static final&nbsp;int Ferris</pre>
<div class="block"><p>Available in PowerPoint 2010.</p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TransitionType.Ferris">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Gallery">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Gallery</h4>
<pre>public static final&nbsp;int Gallery</pre>
<div class="block"><p>Available in PowerPoint 2010.</p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TransitionType.Gallery">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Conveyor">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Conveyor</h4>
<pre>public static final&nbsp;int Conveyor</pre>
<div class="block"><p>Available in PowerPoint 2010.</p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TransitionType.Conveyor">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Pan">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Pan</h4>
<pre>public static final&nbsp;int Pan</pre>
<div class="block"><p>Available in PowerPoint 2010.</p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TransitionType.Pan">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Glitter">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Glitter</h4>
<pre>public static final&nbsp;int Glitter</pre>
<div class="block"><p>Available in PowerPoint 2010.</p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TransitionType.Glitter">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Warp">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Warp</h4>
<pre>public static final&nbsp;int Warp</pre>
<div class="block"><p>
 Relates to transition Zoom in PowerPoint 2010.
 Available in PowerPoint 2010.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TransitionType.Warp">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Flythrough">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Flythrough</h4>
<pre>public static final&nbsp;int Flythrough</pre>
<div class="block"><p>Available in PowerPoint 2010.</p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TransitionType.Flythrough">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Flash">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Flash</h4>
<pre>public static final&nbsp;int Flash</pre>
<div class="block"><p>Available in PowerPoint 2010.</p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TransitionType.Flash">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Shred">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Shred</h4>
<pre>public static final&nbsp;int Shred</pre>
<div class="block"><p>Available in PowerPoint 2010.</p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TransitionType.Shred">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Reveal">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Reveal</h4>
<pre>public static final&nbsp;int Reveal</pre>
<div class="block"><p>Available in PowerPoint 2010.</p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TransitionType.Reveal">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="WheelReverse">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>WheelReverse</h4>
<pre>public static final&nbsp;int WheelReverse</pre>
<div class="block"><p>
 Relates to transition Clock (with option Counterclockwise) in PowerPoint 2010.
 Available in PowerPoint 2010.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TransitionType.WheelReverse">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="FallOver">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FallOver</h4>
<pre>public static final&nbsp;int FallOver</pre>
<div class="block"><p>Available in PowerPoint 2013.</p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TransitionType.FallOver">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Drape">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Drape</h4>
<pre>public static final&nbsp;int Drape</pre>
<div class="block"><p>Available in PowerPoint 2013.</p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TransitionType.Drape">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Curtains">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Curtains</h4>
<pre>public static final&nbsp;int Curtains</pre>
<div class="block"><p>Available in PowerPoint 2013.</p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TransitionType.Curtains">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Wind">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Wind</h4>
<pre>public static final&nbsp;int Wind</pre>
<div class="block"><p>Available in PowerPoint 2013.</p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TransitionType.Wind">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Prestige">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Prestige</h4>
<pre>public static final&nbsp;int Prestige</pre>
<div class="block"><p>Available in PowerPoint 2013.</p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TransitionType.Prestige">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Fracture">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Fracture</h4>
<pre>public static final&nbsp;int Fracture</pre>
<div class="block"><p>Available in PowerPoint 2013.</p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TransitionType.Fracture">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Crush">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Crush</h4>
<pre>public static final&nbsp;int Crush</pre>
<div class="block"><p>Available in PowerPoint 2013.</p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TransitionType.Crush">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="PeelOff">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PeelOff</h4>
<pre>public static final&nbsp;int PeelOff</pre>
<div class="block"><p>Available in PowerPoint 2013.</p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TransitionType.PeelOff">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="PageCurlDouble">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PageCurlDouble</h4>
<pre>public static final&nbsp;int PageCurlDouble</pre>
<div class="block"><p>Available in PowerPoint 2013.</p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TransitionType.PageCurlDouble">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="PageCurlSingle">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PageCurlSingle</h4>
<pre>public static final&nbsp;int PageCurlSingle</pre>
<div class="block"><p>Available in PowerPoint 2013.</p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TransitionType.PageCurlSingle">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Airplane">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Airplane</h4>
<pre>public static final&nbsp;int Airplane</pre>
<div class="block"><p>Available in PowerPoint 2013.</p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TransitionType.Airplane">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Origami">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Origami</h4>
<pre>public static final&nbsp;int Origami</pre>
<div class="block"><p>Available in PowerPoint 2013.</p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TransitionType.Origami">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Morph">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>Morph</h4>
<pre>public static final&nbsp;int Morph</pre>
<div class="block"><p>Relates to transition Morph (with option Type) in PowerPoint 2019.</p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TransitionType.Morph">Constant Field Values</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/TransitionSpeed.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/TransitionValueBase.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/TransitionType.html" target="_top">Frames</a></li>
<li><a href="TransitionType.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.classes.inherited.from.class.com.aspose.ms.System.Enum">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#methods.inherited.from.class.com.aspose.ms.System.Enum">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li>Method</li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><i>Copyright &copy; 2004-2025 Aspose Pty Ltd. All Rights Reserved.</i></small></p>
</body>
</html>
