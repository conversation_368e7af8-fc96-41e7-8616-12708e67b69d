<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_421) on Fri Jul 11 05:32:44 CDT 2025 -->
<title>PresetShadowType (Aspose.Slides for Java (25.7) API Documentation)</title>
<meta name="date" content="2025-07-11">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="PresetShadowType (Aspose.Slides for Java (25.7) API Documentation)";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/PresetShadow.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/PropertyCalcModeType.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/PresetShadowType.html" target="_top">Frames</a></li>
<li><a href="PresetShadowType.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.classes.inherited.from.class.com.aspose.ms.System.Enum">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#methods.inherited.from.class.com.aspose.ms.System.Enum">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li>Method</li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.aspose.slides</div>
<h2 title="Class PresetShadowType" class="title">Class PresetShadowType</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.aspose.ms.System.ValueType&lt;com.aspose.ms.System.Enum&gt;</li>
<li>
<ul class="inheritance">
<li>com.aspose.ms.System.Enum</li>
<li>
<ul class="inheritance">
<li>com.aspose.slides.PresetShadowType</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public final class <span class="typeNameLabel">PresetShadowType</span>
extends com.aspose.ms.System.Enum</pre>
<div class="block"><p>
 Represents a preset for a shadow effect.
 </p></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.com.aspose.ms.System.Enum">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from class&nbsp;com.aspose.ms.System.Enum</h3>
<code>com.aspose.ms.System.Enum.AbstractEnum, com.aspose.ms.System.Enum.FlaggedEnum, com.aspose.ms.System.Enum.ObjectEnum, com.aspose.ms.System.Enum.SimpleEnum</code></li>
</ul>
</li>
</ul>
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetShadowType.html#BackCenterPerspectiveShadow">BackCenterPerspectiveShadow</a></span></code>
<div class="block">
 Represents Back Center Perspective Shadow.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetShadowType.html#BackLeftLongPerspectiveShadow">BackLeftLongPerspectiveShadow</a></span></code>
<div class="block">
 Represents Back Left Long Perspective Shadow</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetShadowType.html#BackLeftPerspectiveShadow">BackLeftPerspectiveShadow</a></span></code>
<div class="block">
 Represents Back Left Perspective Shadow.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetShadowType.html#BackRightLongPerspectiveShadow">BackRightLongPerspectiveShadow</a></span></code>
<div class="block">
 Represents Back Right Long Perspective Shadow</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetShadowType.html#BackRightPerspectiveShadow">BackRightPerspectiveShadow</a></span></code>
<div class="block">
 Represents Back Right Perspective Shadow.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetShadowType.html#BottomLeftDropShadow">BottomLeftDropShadow</a></span></code>
<div class="block">
 Represents Bottom Left Drop Shadow.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetShadowType.html#BottomRightDropShadow">BottomRightDropShadow</a></span></code>
<div class="block">
 Represents Bottom Right Drop Shadow.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetShadowType.html#BottomRightSmallDropShadow">BottomRightSmallDropShadow</a></span></code>
<div class="block">
 Represents Bottom Right Small Drop Shadow.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetShadowType.html#FrontBottomShadow">FrontBottomShadow</a></span></code>
<div class="block">
 Represents Front Bottom Shadow.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetShadowType.html#FrontLeftLongPerspectiveShadow">FrontLeftLongPerspectiveShadow</a></span></code>
<div class="block">
 Represents Front Left Long Perspective Shadow.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetShadowType.html#FrontLeftPerspectiveShadow">FrontLeftPerspectiveShadow</a></span></code>
<div class="block">
 Represents Front Left Perspective Shadow.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetShadowType.html#FrontRightLongPerspectiveShadow">FrontRightLongPerspectiveShadow</a></span></code>
<div class="block">
 Represents Front Right Long Perspective Shadow.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetShadowType.html#FrontRightPerspectiveShadow">FrontRightPerspectiveShadow</a></span></code>
<div class="block">
 Represents Front Right Perspective Shadow.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetShadowType.html#InnerBoxShadow3D">InnerBoxShadow3D</a></span></code>
<div class="block">
 Represents Inner Box Shadow 3D.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetShadowType.html#OuterBoxShadow3D">OuterBoxShadow3D</a></span></code>
<div class="block">
 Represents Outer Box Shadow 3D.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetShadowType.html#TopLeftDoubleDropShadow">TopLeftDoubleDropShadow</a></span></code>
<div class="block">
 Represents Top Left Double Drop Shadow.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetShadowType.html#TopLeftDropShadow">TopLeftDropShadow</a></span></code>
<div class="block">
 Represents Top Left Drop Shadow.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetShadowType.html#TopLeftLargeDropShadow">TopLeftLargeDropShadow</a></span></code>
<div class="block">
 Represents Top Left Large Drop Shadow.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetShadowType.html#TopLeftSmallDropShadow">TopLeftSmallDropShadow</a></span></code>
<div class="block">
 Represents Top Left Small Drop Shadow.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetShadowType.html#TopRightDropShadow">TopRightDropShadow</a></span></code>
<div class="block">
 Represents Top Right Drop Shadow.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.com.aspose.ms.System.Enum">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;com.aspose.ms.System.Enum</h3>
<code>EnumSeparatorCharArray</code></li>
</ul>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.aspose.ms.System.Enum">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;com.aspose.ms.System.Enum</h3>
<code>Clone, CloneTo, format, format, get_Caption, get_Value, getName, getName, getNames, getNames, getUnderlyingType, getUnderlyingType, getValue, getValues, isDefined, isDefined, isDefined, isDefined, parse, parse, parse, parse, register, toObject, toString</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="TopLeftDropShadow">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TopLeftDropShadow</h4>
<pre>public static final&nbsp;int TopLeftDropShadow</pre>
<div class="block"><p>
 Represents Top Left Drop Shadow.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetShadowType.TopLeftDropShadow">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="TopLeftLargeDropShadow">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TopLeftLargeDropShadow</h4>
<pre>public static final&nbsp;int TopLeftLargeDropShadow</pre>
<div class="block"><p>
 Represents Top Left Large Drop Shadow.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetShadowType.TopLeftLargeDropShadow">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="BackLeftLongPerspectiveShadow">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BackLeftLongPerspectiveShadow</h4>
<pre>public static final&nbsp;int BackLeftLongPerspectiveShadow</pre>
<div class="block"><p>
 Represents Back Left Long Perspective Shadow
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetShadowType.BackLeftLongPerspectiveShadow">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="BackRightLongPerspectiveShadow">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BackRightLongPerspectiveShadow</h4>
<pre>public static final&nbsp;int BackRightLongPerspectiveShadow</pre>
<div class="block"><p>
 Represents Back Right Long Perspective Shadow
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetShadowType.BackRightLongPerspectiveShadow">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="TopLeftDoubleDropShadow">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TopLeftDoubleDropShadow</h4>
<pre>public static final&nbsp;int TopLeftDoubleDropShadow</pre>
<div class="block"><p>
 Represents Top Left Double Drop Shadow.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetShadowType.TopLeftDoubleDropShadow">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="BottomRightSmallDropShadow">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BottomRightSmallDropShadow</h4>
<pre>public static final&nbsp;int BottomRightSmallDropShadow</pre>
<div class="block"><p>
 Represents Bottom Right Small Drop Shadow.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetShadowType.BottomRightSmallDropShadow">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="FrontLeftLongPerspectiveShadow">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FrontLeftLongPerspectiveShadow</h4>
<pre>public static final&nbsp;int FrontLeftLongPerspectiveShadow</pre>
<div class="block"><p>
 Represents Front Left Long Perspective Shadow.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetShadowType.FrontLeftLongPerspectiveShadow">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="FrontRightLongPerspectiveShadow">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FrontRightLongPerspectiveShadow</h4>
<pre>public static final&nbsp;int FrontRightLongPerspectiveShadow</pre>
<div class="block"><p>
 Represents Front Right Long Perspective Shadow.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetShadowType.FrontRightLongPerspectiveShadow">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="OuterBoxShadow3D">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OuterBoxShadow3D</h4>
<pre>public static final&nbsp;int OuterBoxShadow3D</pre>
<div class="block"><p>
 Represents Outer Box Shadow 3D.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetShadowType.OuterBoxShadow3D">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="InnerBoxShadow3D">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>InnerBoxShadow3D</h4>
<pre>public static final&nbsp;int InnerBoxShadow3D</pre>
<div class="block"><p>
 Represents Inner Box Shadow 3D.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetShadowType.InnerBoxShadow3D">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="BackCenterPerspectiveShadow">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BackCenterPerspectiveShadow</h4>
<pre>public static final&nbsp;int BackCenterPerspectiveShadow</pre>
<div class="block"><p>
 Represents Back Center Perspective Shadow.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetShadowType.BackCenterPerspectiveShadow">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="TopRightDropShadow">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TopRightDropShadow</h4>
<pre>public static final&nbsp;int TopRightDropShadow</pre>
<div class="block"><p>
 Represents Top Right Drop Shadow.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetShadowType.TopRightDropShadow">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="FrontBottomShadow">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FrontBottomShadow</h4>
<pre>public static final&nbsp;int FrontBottomShadow</pre>
<div class="block"><p>
 Represents Front Bottom Shadow.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetShadowType.FrontBottomShadow">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="BackLeftPerspectiveShadow">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BackLeftPerspectiveShadow</h4>
<pre>public static final&nbsp;int BackLeftPerspectiveShadow</pre>
<div class="block"><p>
 Represents Back Left Perspective Shadow.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetShadowType.BackLeftPerspectiveShadow">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="BackRightPerspectiveShadow">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BackRightPerspectiveShadow</h4>
<pre>public static final&nbsp;int BackRightPerspectiveShadow</pre>
<div class="block"><p>
 Represents Back Right Perspective Shadow.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetShadowType.BackRightPerspectiveShadow">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="BottomLeftDropShadow">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BottomLeftDropShadow</h4>
<pre>public static final&nbsp;int BottomLeftDropShadow</pre>
<div class="block"><p>
 Represents Bottom Left Drop Shadow.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetShadowType.BottomLeftDropShadow">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="BottomRightDropShadow">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BottomRightDropShadow</h4>
<pre>public static final&nbsp;int BottomRightDropShadow</pre>
<div class="block"><p>
 Represents Bottom Right Drop Shadow.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetShadowType.BottomRightDropShadow">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="FrontLeftPerspectiveShadow">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FrontLeftPerspectiveShadow</h4>
<pre>public static final&nbsp;int FrontLeftPerspectiveShadow</pre>
<div class="block"><p>
 Represents Front Left Perspective Shadow.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetShadowType.FrontLeftPerspectiveShadow">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="FrontRightPerspectiveShadow">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FrontRightPerspectiveShadow</h4>
<pre>public static final&nbsp;int FrontRightPerspectiveShadow</pre>
<div class="block"><p>
 Represents Front Right Perspective Shadow.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetShadowType.FrontRightPerspectiveShadow">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="TopLeftSmallDropShadow">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>TopLeftSmallDropShadow</h4>
<pre>public static final&nbsp;int TopLeftSmallDropShadow</pre>
<div class="block"><p>
 Represents Top Left Small Drop Shadow.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetShadowType.TopLeftSmallDropShadow">Constant Field Values</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/PresetShadow.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/PropertyCalcModeType.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/PresetShadowType.html" target="_top">Frames</a></li>
<li><a href="PresetShadowType.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.classes.inherited.from.class.com.aspose.ms.System.Enum">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#methods.inherited.from.class.com.aspose.ms.System.Enum">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li>Method</li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><i>Copyright &copy; 2004-2025 Aspose Pty Ltd. All Rights Reserved.</i></small></p>
</body>
</html>
