<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_421) on Fri Jul 11 05:32:44 CDT 2025 -->
<title>SVGOptions (Aspose.Slides for Java (25.7) API Documentation)</title>
<meta name="date" content="2025-07-11">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="SVGOptions (Aspose.Slides for Java (25.7) API Documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":9,"i13":10,"i14":10,"i15":10,"i16":9,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10,"i26":10,"i27":10,"i28":10,"i29":10};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/SvgImage.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/SvgShape.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/SVGOptions.html" target="_top">Frames</a></li>
<li><a href="SVGOptions.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.aspose.slides</div>
<h2 title="Class SVGOptions" class="title">Class SVGOptions</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../com/aspose/slides/SaveOptions.html" title="class in com.aspose.slides">com.aspose.slides.SaveOptions</a></li>
<li>
<ul class="inheritance">
<li>com.aspose.slides.SVGOptions</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../com/aspose/slides/ISaveOptions.html" title="interface in com.aspose.slides">ISaveOptions</a>, <a href="../../../com/aspose/slides/ISVGOptions.html" title="interface in com.aspose.slides">ISVGOptions</a>, java.lang.Cloneable</dd>
</dl>
<hr>
<br>
<pre>public final class <span class="typeNameLabel">SVGOptions</span>
extends <a href="../../../com/aspose/slides/SaveOptions.html" title="class in com.aspose.slides">SaveOptions</a>
implements <a href="../../../com/aspose/slides/ISVGOptions.html" title="interface in com.aspose.slides">ISVGOptions</a>, java.lang.Cloneable</pre>
<div class="block"><p>
 Represents an SVG options.
 </p></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SVGOptions.html#SVGOptions--">SVGOptions</a></span>()</code>
<div class="block">
 Initializes a new instance of the SVGOptions class.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SVGOptions.html#SVGOptions-com.aspose.slides.ILinkEmbedController-">SVGOptions</a></span>(<a href="../../../com/aspose/slides/ILinkEmbedController.html" title="interface in com.aspose.slides">ILinkEmbedController</a>&nbsp;linkEmbedController)</code>
<div class="block">
 Initializes a new instance of the SVGOptions class specifying the link embedding controller object.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static <a href="../../../com/aspose/slides/SVGOptions.html" title="class in com.aspose.slides">SVGOptions</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SVGOptions.html#getDefault--">getDefault</a></span>()</code>
<div class="block">
 Returns default settings.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SVGOptions.html#getDeletePicturesCroppedAreas--">getDeletePicturesCroppedAreas</a></span>()</code>
<div class="block">
 A boolean flag indicates if the cropped parts remain as part of the document.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SVGOptions.html#getDisable3DText--">getDisable3DText</a></span>()</code>
<div class="block">
 Determines whether the 3D text is disabled in SVG.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SVGOptions.html#getDisableFontLigatures--">getDisableFontLigatures</a></span>()</code>
<div class="block">
 Gets or sets a value indicating whether text is rendered without using ligatures.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SVGOptions.html#getDisableGradientSplit--">getDisableGradientSplit</a></span>()</code>
<div class="block">
 Disables splitting FromCornerX and FromCenter gradients.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SVGOptions.html#getDisableLineEndCropping--">getDisableLineEndCropping</a></span>()</code>
<div class="block">
 SVG 1.1 lacks ability to define insets for markers.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SVGOptions.html#getExternalFontsHandling--">getExternalFontsHandling</a></span>()</code>
<div class="block">
 Determines a way of handling externally loaded fonts.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IInkOptions.html" title="interface in com.aspose.slides">IInkOptions</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SVGOptions.html#getInkOptions--">getInkOptions</a></span>()</code>
<div class="block">
 Provides options that control the look of Ink objects in exported document.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SVGOptions.html#getJpegQuality--">getJpegQuality</a></span>()</code>
<div class="block">
 Determines JPEG encoding quality.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SVGOptions.html#getMetafileRasterizationDpi--">getMetafileRasterizationDpi</a></span>()</code>
<div class="block">
 Returns or sets the lower resolution limit for metafile rasterization.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SVGOptions.html#getPicturesCompression--">getPicturesCompression</a></span>()</code>
<div class="block">
 Represents the pictures compression level</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/ISvgShapeFormattingController.html" title="interface in com.aspose.slides">ISvgShapeFormattingController</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SVGOptions.html#getShapeFormattingController--">getShapeFormattingController</a></span>()</code>
<div class="block">
 Returns and sets a callback interface which allows user to control shape conversion.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>static <a href="../../../com/aspose/slides/SVGOptions.html" title="class in com.aspose.slides">SVGOptions</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SVGOptions.html#getSimple--">getSimple</a></span>()</code>
<div class="block">
 Returns settings for simpliest and smallest SVG file generation.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SVGOptions.html#getUseFrameRotation--">getUseFrameRotation</a></span>()</code>
<div class="block">
 Determines whether to perform the specified rotation of the shape when rendering or not.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SVGOptions.html#getUseFrameSize--">getUseFrameSize</a></span>()</code>
<div class="block">
 Determines whether the text frame will be included in a rendering area or not.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SVGOptions.html#getVectorizeText--">getVectorizeText</a></span>()</code>
<div class="block">
 Determines whether the text on a slide will be saved as graphics.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>static <a href="../../../com/aspose/slides/SVGOptions.html" title="class in com.aspose.slides">SVGOptions</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SVGOptions.html#getWYSIWYG--">getWYSIWYG</a></span>()</code>
<div class="block">
 Returns settings for most accurate SVG file generation.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SVGOptions.html#setDeletePicturesCroppedAreas-boolean-">setDeletePicturesCroppedAreas</a></span>(boolean&nbsp;value)</code>
<div class="block">
 A boolean flag indicates if the cropped parts remain as part of the document.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SVGOptions.html#setDisable3DText-boolean-">setDisable3DText</a></span>(boolean&nbsp;value)</code>
<div class="block">
 Determines whether the 3D text is disabled in SVG.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SVGOptions.html#setDisableFontLigatures-boolean-">setDisableFontLigatures</a></span>(boolean&nbsp;value)</code>
<div class="block">
 Gets or sets a value indicating whether text is rendered without using ligatures.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SVGOptions.html#setDisableGradientSplit-boolean-">setDisableGradientSplit</a></span>(boolean&nbsp;value)</code>
<div class="block">
 Disables splitting FromCornerX and FromCenter gradients.</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SVGOptions.html#setDisableLineEndCropping-boolean-">setDisableLineEndCropping</a></span>(boolean&nbsp;value)</code>
<div class="block">
 SVG 1.1 lacks ability to define insets for markers.</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SVGOptions.html#setExternalFontsHandling-int-">setExternalFontsHandling</a></span>(int&nbsp;value)</code>
<div class="block">
 Determines a way of handling externally loaded fonts.</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SVGOptions.html#setJpegQuality-int-">setJpegQuality</a></span>(int&nbsp;value)</code>
<div class="block">
 Determines JPEG encoding quality.</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SVGOptions.html#setMetafileRasterizationDpi-int-">setMetafileRasterizationDpi</a></span>(int&nbsp;value)</code>
<div class="block">
 Returns or sets the lower resolution limit for metafile rasterization.</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SVGOptions.html#setPicturesCompression-int-">setPicturesCompression</a></span>(int&nbsp;value)</code>
<div class="block">
 Represents the pictures compression level</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SVGOptions.html#setShapeFormattingController-com.aspose.slides.ISvgShapeFormattingController-">setShapeFormattingController</a></span>(<a href="../../../com/aspose/slides/ISvgShapeFormattingController.html" title="interface in com.aspose.slides">ISvgShapeFormattingController</a>&nbsp;value)</code>
<div class="block">
 Returns and sets a callback interface which allows user to control shape conversion.</div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SVGOptions.html#setUseFrameRotation-boolean-">setUseFrameRotation</a></span>(boolean&nbsp;value)</code>
<div class="block">
 Determines whether to perform the specified rotation of the shape when rendering or not.</div>
</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SVGOptions.html#setUseFrameSize-boolean-">setUseFrameSize</a></span>(boolean&nbsp;value)</code>
<div class="block">
 Determines whether the text frame will be included in a rendering area or not.</div>
</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SVGOptions.html#setVectorizeText-boolean-">setVectorizeText</a></span>(boolean&nbsp;value)</code>
<div class="block">
 Determines whether the text on a slide will be saved as graphics.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.aspose.slides.SaveOptions">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;com.aspose.slides.<a href="../../../com/aspose/slides/SaveOptions.html" title="class in com.aspose.slides">SaveOptions</a></h3>
<code><a href="../../../com/aspose/slides/SaveOptions.html#getDefaultRegularFont--">getDefaultRegularFont</a>, <a href="../../../com/aspose/slides/SaveOptions.html#getGradientStyle--">getGradientStyle</a>, <a href="../../../com/aspose/slides/SaveOptions.html#getProgressCallback--">getProgressCallback</a>, <a href="../../../com/aspose/slides/SaveOptions.html#getSkipJavaScriptLinks--">getSkipJavaScriptLinks</a>, <a href="../../../com/aspose/slides/SaveOptions.html#getWarningCallback--">getWarningCallback</a>, <a href="../../../com/aspose/slides/SaveOptions.html#setDefaultRegularFont-java.lang.String-">setDefaultRegularFont</a>, <a href="../../../com/aspose/slides/SaveOptions.html#setGradientStyle-int-">setGradientStyle</a>, <a href="../../../com/aspose/slides/SaveOptions.html#setProgressCallback-com.aspose.slides.IProgressCallback-">setProgressCallback</a>, <a href="../../../com/aspose/slides/SaveOptions.html#setSkipJavaScriptLinks-boolean-">setSkipJavaScriptLinks</a>, <a href="../../../com/aspose/slides/SaveOptions.html#setWarningCallback-com.aspose.slides.IWarningCallback-">setWarningCallback</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.aspose.slides.ISaveOptions">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;com.aspose.slides.<a href="../../../com/aspose/slides/ISaveOptions.html" title="interface in com.aspose.slides">ISaveOptions</a></h3>
<code><a href="../../../com/aspose/slides/ISaveOptions.html#getDefaultRegularFont--">getDefaultRegularFont</a>, <a href="../../../com/aspose/slides/ISaveOptions.html#getGradientStyle--">getGradientStyle</a>, <a href="../../../com/aspose/slides/ISaveOptions.html#getProgressCallback--">getProgressCallback</a>, <a href="../../../com/aspose/slides/ISaveOptions.html#getSkipJavaScriptLinks--">getSkipJavaScriptLinks</a>, <a href="../../../com/aspose/slides/ISaveOptions.html#getWarningCallback--">getWarningCallback</a>, <a href="../../../com/aspose/slides/ISaveOptions.html#setDefaultRegularFont-java.lang.String-">setDefaultRegularFont</a>, <a href="../../../com/aspose/slides/ISaveOptions.html#setGradientStyle-int-">setGradientStyle</a>, <a href="../../../com/aspose/slides/ISaveOptions.html#setProgressCallback-com.aspose.slides.IProgressCallback-">setProgressCallback</a>, <a href="../../../com/aspose/slides/ISaveOptions.html#setSkipJavaScriptLinks-boolean-">setSkipJavaScriptLinks</a>, <a href="../../../com/aspose/slides/ISaveOptions.html#setWarningCallback-com.aspose.slides.IWarningCallback-">setWarningCallback</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="SVGOptions--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SVGOptions</h4>
<pre>public&nbsp;SVGOptions()</pre>
<div class="block"><p>
 Initializes a new instance of the SVGOptions class.
 </p></div>
</li>
</ul>
<a name="SVGOptions-com.aspose.slides.ILinkEmbedController-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>SVGOptions</h4>
<pre>public&nbsp;SVGOptions(<a href="../../../com/aspose/slides/ILinkEmbedController.html" title="interface in com.aspose.slides">ILinkEmbedController</a>&nbsp;linkEmbedController)</pre>
<div class="block"><p>
 Initializes a new instance of the SVGOptions class specifying the link embedding controller object.
 </p></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>linkEmbedController</code> - The link embedding controller reference.
 <p><hr>Link embedding controller is a delegate object that is responsible for making decisions if resources (such as images) need to be
 embedded or referenced as external resources.</hr></p></dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../com/aspose/slides/ILinkEmbedController.html" title="interface in com.aspose.slides"><code>ILinkEmbedController</code></a></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getInkOptions--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getInkOptions</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IInkOptions.html" title="interface in com.aspose.slides">IInkOptions</a>&nbsp;getInkOptions()</pre>
<div class="block"><p>
 Provides options that control the look of Ink objects in exported document.
 Read-only <a href="../../../com/aspose/slides/IInkOptions.html" title="interface in com.aspose.slides"><code>IInkOptions</code></a>
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISVGOptions.html#getInkOptions--">getInkOptions</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISVGOptions.html" title="interface in com.aspose.slides">ISVGOptions</a></code></dd>
</dl>
</li>
</ul>
<a name="getUseFrameSize--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getUseFrameSize</h4>
<pre>public final&nbsp;boolean&nbsp;getUseFrameSize()</pre>
<div class="block"><p>
 Determines whether the text frame will be included in a rendering area or not.
 Read/write <code>boolean</code>.
 Default value is false.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISVGOptions.html#getUseFrameSize--">getUseFrameSize</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISVGOptions.html" title="interface in com.aspose.slides">ISVGOptions</a></code></dd>
</dl>
</li>
</ul>
<a name="setUseFrameSize-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setUseFrameSize</h4>
<pre>public final&nbsp;void&nbsp;setUseFrameSize(boolean&nbsp;value)</pre>
<div class="block"><p>
 Determines whether the text frame will be included in a rendering area or not.
 Read/write <code>boolean</code>.
 Default value is false.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISVGOptions.html#setUseFrameSize-boolean-">setUseFrameSize</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISVGOptions.html" title="interface in com.aspose.slides">ISVGOptions</a></code></dd>
</dl>
</li>
</ul>
<a name="getUseFrameRotation--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getUseFrameRotation</h4>
<pre>public final&nbsp;boolean&nbsp;getUseFrameRotation()</pre>
<div class="block"><p>
 Determines whether to perform the specified rotation of the shape when rendering or not.
 Read/write <code>boolean</code>.
 Default value is true.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISVGOptions.html#getUseFrameRotation--">getUseFrameRotation</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISVGOptions.html" title="interface in com.aspose.slides">ISVGOptions</a></code></dd>
</dl>
</li>
</ul>
<a name="setUseFrameRotation-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setUseFrameRotation</h4>
<pre>public final&nbsp;void&nbsp;setUseFrameRotation(boolean&nbsp;value)</pre>
<div class="block"><p>
 Determines whether to perform the specified rotation of the shape when rendering or not.
 Read/write <code>boolean</code>.
 Default value is true.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISVGOptions.html#setUseFrameRotation-boolean-">setUseFrameRotation</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISVGOptions.html" title="interface in com.aspose.slides">ISVGOptions</a></code></dd>
</dl>
</li>
</ul>
<a name="getVectorizeText--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVectorizeText</h4>
<pre>public final&nbsp;boolean&nbsp;getVectorizeText()</pre>
<div class="block"><p>
 Determines whether the text on a slide will be saved as graphics.
 Read/write <code>boolean</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISVGOptions.html#getVectorizeText--">getVectorizeText</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISVGOptions.html" title="interface in com.aspose.slides">ISVGOptions</a></code></dd>
</dl>
</li>
</ul>
<a name="setVectorizeText-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setVectorizeText</h4>
<pre>public final&nbsp;void&nbsp;setVectorizeText(boolean&nbsp;value)</pre>
<div class="block"><p>
 Determines whether the text on a slide will be saved as graphics.
 Read/write <code>boolean</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISVGOptions.html#setVectorizeText-boolean-">setVectorizeText</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISVGOptions.html" title="interface in com.aspose.slides">ISVGOptions</a></code></dd>
</dl>
</li>
</ul>
<a name="getMetafileRasterizationDpi--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMetafileRasterizationDpi</h4>
<pre>public final&nbsp;int&nbsp;getMetafileRasterizationDpi()</pre>
<div class="block"><p>
 Returns or sets the lower resolution limit for metafile rasterization.
 Read/write <code>int</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISVGOptions.html#getMetafileRasterizationDpi--">getMetafileRasterizationDpi</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISVGOptions.html" title="interface in com.aspose.slides">ISVGOptions</a></code></dd>
</dl>
</li>
</ul>
<a name="setMetafileRasterizationDpi-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMetafileRasterizationDpi</h4>
<pre>public final&nbsp;void&nbsp;setMetafileRasterizationDpi(int&nbsp;value)</pre>
<div class="block"><p>
 Returns or sets the lower resolution limit for metafile rasterization.
 Read/write <code>int</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISVGOptions.html#setMetafileRasterizationDpi-int-">setMetafileRasterizationDpi</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISVGOptions.html" title="interface in com.aspose.slides">ISVGOptions</a></code></dd>
</dl>
</li>
</ul>
<a name="getDisable3DText--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDisable3DText</h4>
<pre>public final&nbsp;boolean&nbsp;getDisable3DText()</pre>
<div class="block"><p>
 Determines whether the 3D text is disabled in SVG.
 Read/write <code>boolean</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISVGOptions.html#getDisable3DText--">getDisable3DText</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISVGOptions.html" title="interface in com.aspose.slides">ISVGOptions</a></code></dd>
</dl>
</li>
</ul>
<a name="setDisable3DText-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDisable3DText</h4>
<pre>public final&nbsp;void&nbsp;setDisable3DText(boolean&nbsp;value)</pre>
<div class="block"><p>
 Determines whether the 3D text is disabled in SVG.
 Read/write <code>boolean</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISVGOptions.html#setDisable3DText-boolean-">setDisable3DText</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISVGOptions.html" title="interface in com.aspose.slides">ISVGOptions</a></code></dd>
</dl>
</li>
</ul>
<a name="getDisableGradientSplit--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDisableGradientSplit</h4>
<pre>public final&nbsp;boolean&nbsp;getDisableGradientSplit()</pre>
<div class="block"><p>
 Disables splitting FromCornerX and FromCenter gradients.
 Read/write <code>boolean</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISVGOptions.html#getDisableGradientSplit--">getDisableGradientSplit</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISVGOptions.html" title="interface in com.aspose.slides">ISVGOptions</a></code></dd>
</dl>
</li>
</ul>
<a name="setDisableGradientSplit-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDisableGradientSplit</h4>
<pre>public final&nbsp;void&nbsp;setDisableGradientSplit(boolean&nbsp;value)</pre>
<div class="block"><p>
 Disables splitting FromCornerX and FromCenter gradients.
 Read/write <code>boolean</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISVGOptions.html#setDisableGradientSplit-boolean-">setDisableGradientSplit</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISVGOptions.html" title="interface in com.aspose.slides">ISVGOptions</a></code></dd>
</dl>
</li>
</ul>
<a name="getDisableLineEndCropping--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDisableLineEndCropping</h4>
<pre>public final&nbsp;boolean&nbsp;getDisableLineEndCropping()</pre>
<div class="block"><p>
 SVG 1.1 lacks ability to define insets for markers.
 Aspose.Slides SVG writing engine has workaround for that problem:
 it crops end of line with arrow, so, line doesn't overlap markers.
 This option switches off such behavior.
 Read/write <code>boolean</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISVGOptions.html#getDisableLineEndCropping--">getDisableLineEndCropping</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISVGOptions.html" title="interface in com.aspose.slides">ISVGOptions</a></code></dd>
</dl>
</li>
</ul>
<a name="setDisableLineEndCropping-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDisableLineEndCropping</h4>
<pre>public final&nbsp;void&nbsp;setDisableLineEndCropping(boolean&nbsp;value)</pre>
<div class="block"><p>
 SVG 1.1 lacks ability to define insets for markers.
 Aspose.Slides SVG writing engine has workaround for that problem:
 it crops end of line with arrow, so, line doesn't overlap markers.
 This option switches off such behavior.
 Read/write <code>boolean</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISVGOptions.html#setDisableLineEndCropping-boolean-">setDisableLineEndCropping</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISVGOptions.html" title="interface in com.aspose.slides">ISVGOptions</a></code></dd>
</dl>
</li>
</ul>
<a name="getDefault--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDefault</h4>
<pre>public static&nbsp;<a href="../../../com/aspose/slides/SVGOptions.html" title="class in com.aspose.slides">SVGOptions</a>&nbsp;getDefault()</pre>
<div class="block"><p>
 Returns default settings.
 Read-only <a href="../../../com/aspose/slides/SVGOptions.html" title="class in com.aspose.slides"><code>SVGOptions</code></a>.
 </p></div>
</li>
</ul>
<a name="getSimple--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSimple</h4>
<pre>public static&nbsp;<a href="../../../com/aspose/slides/SVGOptions.html" title="class in com.aspose.slides">SVGOptions</a>&nbsp;getSimple()</pre>
<div class="block"><p>
 Returns settings for simpliest and smallest SVG file generation.
 Read-only <a href="../../../com/aspose/slides/SVGOptions.html" title="class in com.aspose.slides"><code>SVGOptions</code></a>.
 </p></div>
</li>
</ul>
<a name="getWYSIWYG--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWYSIWYG</h4>
<pre>public static&nbsp;<a href="../../../com/aspose/slides/SVGOptions.html" title="class in com.aspose.slides">SVGOptions</a>&nbsp;getWYSIWYG()</pre>
<div class="block"><p>
 Returns settings for most accurate SVG file generation.
 Read-only <a href="../../../com/aspose/slides/SVGOptions.html" title="class in com.aspose.slides"><code>SVGOptions</code></a>.
 </p></div>
</li>
</ul>
<a name="getJpegQuality--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getJpegQuality</h4>
<pre>public final&nbsp;int&nbsp;getJpegQuality()</pre>
<div class="block"><p>
 Determines JPEG encoding quality.
 Read/write <code>int</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISVGOptions.html#getJpegQuality--">getJpegQuality</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISVGOptions.html" title="interface in com.aspose.slides">ISVGOptions</a></code></dd>
</dl>
</li>
</ul>
<a name="setJpegQuality-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setJpegQuality</h4>
<pre>public final&nbsp;void&nbsp;setJpegQuality(int&nbsp;value)</pre>
<div class="block"><p>
 Determines JPEG encoding quality.
 Read/write <code>int</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISVGOptions.html#setJpegQuality-int-">setJpegQuality</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISVGOptions.html" title="interface in com.aspose.slides">ISVGOptions</a></code></dd>
</dl>
</li>
</ul>
<a name="getShapeFormattingController--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getShapeFormattingController</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/ISvgShapeFormattingController.html" title="interface in com.aspose.slides">ISvgShapeFormattingController</a>&nbsp;getShapeFormattingController()</pre>
<div class="block"><p>
 Returns and sets a callback interface which allows user to control shape conversion.
 Read/write <a href="../../../com/aspose/slides/ISvgShapeFormattingController.html" title="interface in com.aspose.slides"><code>ISvgShapeFormattingController</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISVGOptions.html#getShapeFormattingController--">getShapeFormattingController</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISVGOptions.html" title="interface in com.aspose.slides">ISVGOptions</a></code></dd>
</dl>
</li>
</ul>
<a name="setShapeFormattingController-com.aspose.slides.ISvgShapeFormattingController-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setShapeFormattingController</h4>
<pre>public final&nbsp;void&nbsp;setShapeFormattingController(<a href="../../../com/aspose/slides/ISvgShapeFormattingController.html" title="interface in com.aspose.slides">ISvgShapeFormattingController</a>&nbsp;value)</pre>
<div class="block"><p>
 Returns and sets a callback interface which allows user to control shape conversion.
 Read/write <a href="../../../com/aspose/slides/ISvgShapeFormattingController.html" title="interface in com.aspose.slides"><code>ISvgShapeFormattingController</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISVGOptions.html#setShapeFormattingController-com.aspose.slides.ISvgShapeFormattingController-">setShapeFormattingController</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISVGOptions.html" title="interface in com.aspose.slides">ISVGOptions</a></code></dd>
</dl>
</li>
</ul>
<a name="getPicturesCompression--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPicturesCompression</h4>
<pre>public final&nbsp;int&nbsp;getPicturesCompression()</pre>
<div class="block"><p>
 Represents the pictures compression level
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISVGOptions.html#getPicturesCompression--">getPicturesCompression</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISVGOptions.html" title="interface in com.aspose.slides">ISVGOptions</a></code></dd>
</dl>
</li>
</ul>
<a name="setPicturesCompression-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPicturesCompression</h4>
<pre>public final&nbsp;void&nbsp;setPicturesCompression(int&nbsp;value)</pre>
<div class="block"><p>
 Represents the pictures compression level
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISVGOptions.html#setPicturesCompression-int-">setPicturesCompression</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISVGOptions.html" title="interface in com.aspose.slides">ISVGOptions</a></code></dd>
</dl>
</li>
</ul>
<a name="getDeletePicturesCroppedAreas--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDeletePicturesCroppedAreas</h4>
<pre>public final&nbsp;boolean&nbsp;getDeletePicturesCroppedAreas()</pre>
<div class="block"><p>
 A boolean flag indicates if the cropped parts remain as part of the document. If true the cropped 
 parts will removed, if false they will be serialized in the document (which can possible lead to a 
 larger file)
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISVGOptions.html#getDeletePicturesCroppedAreas--">getDeletePicturesCroppedAreas</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISVGOptions.html" title="interface in com.aspose.slides">ISVGOptions</a></code></dd>
</dl>
</li>
</ul>
<a name="setDeletePicturesCroppedAreas-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDeletePicturesCroppedAreas</h4>
<pre>public final&nbsp;void&nbsp;setDeletePicturesCroppedAreas(boolean&nbsp;value)</pre>
<div class="block"><p>
 A boolean flag indicates if the cropped parts remain as part of the document. If true the cropped 
 parts will removed, if false they will be serialized in the document (which can possible lead to a 
 larger file)
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISVGOptions.html#setDeletePicturesCroppedAreas-boolean-">setDeletePicturesCroppedAreas</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISVGOptions.html" title="interface in com.aspose.slides">ISVGOptions</a></code></dd>
</dl>
</li>
</ul>
<a name="getExternalFontsHandling--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getExternalFontsHandling</h4>
<pre>public final&nbsp;int&nbsp;getExternalFontsHandling()</pre>
<div class="block"><p>
 Determines a way of handling externally loaded fonts.
 Read/write <a href="../../../com/aspose/slides/SvgExternalFontsHandling.html" title="class in com.aspose.slides"><code>SvgExternalFontsHandling</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISVGOptions.html#getExternalFontsHandling--">getExternalFontsHandling</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISVGOptions.html" title="interface in com.aspose.slides">ISVGOptions</a></code></dd>
</dl>
</li>
</ul>
<a name="setExternalFontsHandling-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setExternalFontsHandling</h4>
<pre>public final&nbsp;void&nbsp;setExternalFontsHandling(int&nbsp;value)</pre>
<div class="block"><p>
 Determines a way of handling externally loaded fonts.
 Read/write <a href="../../../com/aspose/slides/SvgExternalFontsHandling.html" title="class in com.aspose.slides"><code>SvgExternalFontsHandling</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISVGOptions.html#setExternalFontsHandling-int-">setExternalFontsHandling</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISVGOptions.html" title="interface in com.aspose.slides">ISVGOptions</a></code></dd>
</dl>
</li>
</ul>
<a name="getDisableFontLigatures--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDisableFontLigatures</h4>
<pre>public final&nbsp;boolean&nbsp;getDisableFontLigatures()</pre>
<div class="block"><p>
 Gets or sets a value indicating whether text is rendered without using ligatures.
 When set to true, ligatures will be disabled in the rendered output. By default, this property is set to false.
 </p><p><hr><blockquote><pre>Example:
 <pre>
 Presentation pres = new Presentation("pres.pptx");
 try {
     SVGOptions options = new SVGOptions();
     options.setDisableFontLigatures(true);

     FileOutputStream fileStream = new FileOutputStream("slide-0.svg");
     pres.getSlides().get_Item(0).writeAsSvg(fileStream);
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISVGOptions.html#getDisableFontLigatures--">getDisableFontLigatures</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISVGOptions.html" title="interface in com.aspose.slides">ISVGOptions</a></code></dd>
</dl>
</li>
</ul>
<a name="setDisableFontLigatures-boolean-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setDisableFontLigatures</h4>
<pre>public final&nbsp;void&nbsp;setDisableFontLigatures(boolean&nbsp;value)</pre>
<div class="block"><p>
 Gets or sets a value indicating whether text is rendered without using ligatures.
 When set to true, ligatures will be disabled in the rendered output. By default, this property is set to false.
 </p><p><hr><blockquote><pre>Example:
 <pre>
 Presentation pres = new Presentation("pres.pptx");
 try {
     SVGOptions options = new SVGOptions();
     options.setDisableFontLigatures(true);

     FileOutputStream fileStream = new FileOutputStream("slide-0.svg");
     pres.getSlides().get_Item(0).writeAsSvg(fileStream);
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISVGOptions.html#setDisableFontLigatures-boolean-">setDisableFontLigatures</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISVGOptions.html" title="interface in com.aspose.slides">ISVGOptions</a></code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/SvgImage.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/SvgShape.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/SVGOptions.html" target="_top">Frames</a></li>
<li><a href="SVGOptions.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><i>Copyright &copy; 2004-2025 Aspose Pty Ltd. All Rights Reserved.</i></small></p>
</body>
</html>
