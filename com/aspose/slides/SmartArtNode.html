<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_421) on Fri Jul 11 05:32:44 CDT 2025 -->
<title>SmartArtNode (Aspose.Slides for Java (25.7) API Documentation)</title>
<meta name="date" content="2025-07-11">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="SmartArtNode (Aspose.Slides for Java (25.7) API Documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/SmartArtLayoutType.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/SmartArtNodeCollection.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/SmartArtNode.html" target="_top">Frames</a></li>
<li><a href="SmartArtNode.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.aspose.slides</div>
<h2 title="Class SmartArtNode" class="title">Class SmartArtNode</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.aspose.slides.SmartArtNode</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../com/aspose/slides/ISmartArtNode.html" title="interface in com.aspose.slides">ISmartArtNode</a></dd>
</dl>
<hr>
<br>
<pre>public final class <span class="typeNameLabel">SmartArtNode</span>
extends java.lang.Object
implements <a href="../../../com/aspose/slides/ISmartArtNode.html" title="interface in com.aspose.slides">ISmartArtNode</a></pre>
<div class="block"><p>
 Represents node of a SmartArt object
 </p></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IFillFormat.html" title="interface in com.aspose.slides">IFillFormat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtNode.html#getBulletFillFormat--">getBulletFillFormat</a></span>()</code>
<div class="block">
 Returns the FillFormat object that contains fill formatting properties for a node bullet.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/ISmartArtNodeCollection.html" title="interface in com.aspose.slides">ISmartArtNodeCollection</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtNode.html#getChildNodes--">getChildNodes</a></span>()</code>
<div class="block">
 Returns collections of all child nodes of the current node.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtNode.html#getLevel--">getLevel</a></span>()</code>
<div class="block">
 Returns nesting level of the node.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtNode.html#getOrganizationChartLayout--">getOrganizationChartLayout</a></span>()</code>
<div class="block">
 Returns or sets organization chart layout type associated with current node.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtNode.html#getPosition--">getPosition</a></span>()</code>
<div class="block">
 Returns or sets zero-based position of node among sibling nodes.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/ISmartArtShapeCollection.html" title="interface in com.aspose.slides">ISmartArtShapeCollection</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtNode.html#getShapes--">getShapes</a></span>()</code>
<div class="block">
 Returns collections of all shapes associated with the node.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/ITextFrame.html" title="interface in com.aspose.slides">ITextFrame</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtNode.html#getTextFrame--">getTextFrame</a></span>()</code>
<div class="block">
 Returns text frame of the node.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtNode.html#isAssistant--">isAssistant</a></span>()</code>
<div class="block">
 Returns or sets the node as assistant.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtNode.html#isHidden--">isHidden</a></span>()</code>
<div class="block">
 Returns true if this node is a hidden node in the data model.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtNode.html#remove--">remove</a></span>()</code>
<div class="block">
 Remove current node.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtNode.html#setAssistant-boolean-">setAssistant</a></span>(boolean&nbsp;value)</code>
<div class="block">
 Returns or sets the node as assistant.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtNode.html#setOrganizationChartLayout-int-">setOrganizationChartLayout</a></span>(int&nbsp;value)</code>
<div class="block">
 Returns or sets organization chart layout type associated with current node.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtNode.html#setPosition-int-">setPosition</a></span>(int&nbsp;value)</code>
<div class="block">
 Returns or sets zero-based position of node among sibling nodes.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getChildNodes--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getChildNodes</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/ISmartArtNodeCollection.html" title="interface in com.aspose.slides">ISmartArtNodeCollection</a>&nbsp;getChildNodes()</pre>
<div class="block"><p>
 Returns collections of all child nodes of the current node.
 Read-only <a href="../../../com/aspose/slides/ISmartArtNodeCollection.html" title="interface in com.aspose.slides"><code>ISmartArtNodeCollection</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISmartArtNode.html#getChildNodes--">getChildNodes</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISmartArtNode.html" title="interface in com.aspose.slides">ISmartArtNode</a></code></dd>
</dl>
</li>
</ul>
<a name="getShapes--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getShapes</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/ISmartArtShapeCollection.html" title="interface in com.aspose.slides">ISmartArtShapeCollection</a>&nbsp;getShapes()</pre>
<div class="block"><p>
 Returns collections of all shapes associated with the node.
 Read-only <a href="../../../com/aspose/slides/ISmartArtShapeCollection.html" title="interface in com.aspose.slides"><code>ISmartArtShapeCollection</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISmartArtNode.html#getShapes--">getShapes</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISmartArtNode.html" title="interface in com.aspose.slides">ISmartArtNode</a></code></dd>
</dl>
</li>
</ul>
<a name="getTextFrame--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTextFrame</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/ITextFrame.html" title="interface in com.aspose.slides">ITextFrame</a>&nbsp;getTextFrame()</pre>
<div class="block"><p>
 Returns text frame of the node.
 Read-only <a href="../../../com/aspose/slides/ITextFrame.html" title="interface in com.aspose.slides"><code>ITextFrame</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISmartArtNode.html#getTextFrame--">getTextFrame</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISmartArtNode.html" title="interface in com.aspose.slides">ISmartArtNode</a></code></dd>
</dl>
</li>
</ul>
<a name="isAssistant--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isAssistant</h4>
<pre>public final&nbsp;boolean&nbsp;isAssistant()</pre>
<div class="block"><p>
 Returns or sets the node as assistant.
 Read/write boolean.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISmartArtNode.html#isAssistant--">isAssistant</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISmartArtNode.html" title="interface in com.aspose.slides">ISmartArtNode</a></code></dd>
</dl>
</li>
</ul>
<a name="setAssistant-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAssistant</h4>
<pre>public final&nbsp;void&nbsp;setAssistant(boolean&nbsp;value)</pre>
<div class="block"><p>
 Returns or sets the node as assistant.
 Read/write boolean.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISmartArtNode.html#setAssistant-boolean-">setAssistant</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISmartArtNode.html" title="interface in com.aspose.slides">ISmartArtNode</a></code></dd>
</dl>
</li>
</ul>
<a name="getLevel--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLevel</h4>
<pre>public final&nbsp;int&nbsp;getLevel()</pre>
<div class="block"><p>
 Returns nesting level of the node.
 Read-only <code>int</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISmartArtNode.html#getLevel--">getLevel</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISmartArtNode.html" title="interface in com.aspose.slides">ISmartArtNode</a></code></dd>
</dl>
</li>
</ul>
<a name="getBulletFillFormat--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBulletFillFormat</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IFillFormat.html" title="interface in com.aspose.slides">IFillFormat</a>&nbsp;getBulletFillFormat()</pre>
<div class="block"><p>
 Returns the FillFormat object that contains fill formatting properties for a node bullet.
 Note: can return null for certain types of SmartArt layout which does not provide bullets for nodes.
 Read-only <a href="../../../com/aspose/slides/IFillFormat.html" title="interface in com.aspose.slides"><code>IFillFormat</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISmartArtNode.html#getBulletFillFormat--">getBulletFillFormat</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISmartArtNode.html" title="interface in com.aspose.slides">ISmartArtNode</a></code></dd>
</dl>
</li>
</ul>
<a name="getPosition--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPosition</h4>
<pre>public final&nbsp;int&nbsp;getPosition()</pre>
<div class="block"><p>
 Returns or sets zero-based position of node among sibling nodes.
 Read/write <code>int</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISmartArtNode.html#getPosition--">getPosition</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISmartArtNode.html" title="interface in com.aspose.slides">ISmartArtNode</a></code></dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>com.aspose.ms.System.ArgumentOutOfRangeException</code> - value is less than 0.  -or- value is equal to or greater than siblings count</dd>
</dl>
</li>
</ul>
<a name="setPosition-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPosition</h4>
<pre>public final&nbsp;void&nbsp;setPosition(int&nbsp;value)</pre>
<div class="block"><p>
 Returns or sets zero-based position of node among sibling nodes.
 Read/write <code>int</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISmartArtNode.html#setPosition-int-">setPosition</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISmartArtNode.html" title="interface in com.aspose.slides">ISmartArtNode</a></code></dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>com.aspose.ms.System.ArgumentOutOfRangeException</code> - value is less than 0.  -or- value is equal to or greater than siblings count</dd>
</dl>
</li>
</ul>
<a name="isHidden--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isHidden</h4>
<pre>public final&nbsp;boolean&nbsp;isHidden()</pre>
<div class="block"><p>
 Returns true if this node is a hidden node in the data model.
 Read-only boolean.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISmartArtNode.html#isHidden--">isHidden</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISmartArtNode.html" title="interface in com.aspose.slides">ISmartArtNode</a></code></dd>
</dl>
</li>
</ul>
<a name="getOrganizationChartLayout--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getOrganizationChartLayout</h4>
<pre>public final&nbsp;int&nbsp;getOrganizationChartLayout()</pre>
<div class="block"><p>
 Returns or sets organization chart layout type associated with current node.
 Read/write <a href="../../../com/aspose/slides/OrganizationChartLayoutType.html" title="class in com.aspose.slides"><code>OrganizationChartLayoutType</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISmartArtNode.html#getOrganizationChartLayout--">getOrganizationChartLayout</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISmartArtNode.html" title="interface in com.aspose.slides">ISmartArtNode</a></code></dd>
</dl>
</li>
</ul>
<a name="setOrganizationChartLayout-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setOrganizationChartLayout</h4>
<pre>public final&nbsp;void&nbsp;setOrganizationChartLayout(int&nbsp;value)</pre>
<div class="block"><p>
 Returns or sets organization chart layout type associated with current node.
 Read/write <a href="../../../com/aspose/slides/OrganizationChartLayoutType.html" title="class in com.aspose.slides"><code>OrganizationChartLayoutType</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISmartArtNode.html#setOrganizationChartLayout-int-">setOrganizationChartLayout</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISmartArtNode.html" title="interface in com.aspose.slides">ISmartArtNode</a></code></dd>
</dl>
</li>
</ul>
<a name="remove--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>remove</h4>
<pre>public final&nbsp;boolean&nbsp;remove()</pre>
<div class="block"><p>
 Remove current node.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISmartArtNode.html#remove--">remove</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISmartArtNode.html" title="interface in com.aspose.slides">ISmartArtNode</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true if removed succesfully, otherwise false</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/SmartArtLayoutType.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/SmartArtNodeCollection.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/SmartArtNode.html" target="_top">Frames</a></li>
<li><a href="SmartArtNode.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><i>Copyright &copy; 2004-2025 Aspose Pty Ltd. All Rights Reserved.</i></small></p>
</body>
</html>
