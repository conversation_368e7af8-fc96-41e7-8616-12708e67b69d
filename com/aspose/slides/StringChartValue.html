<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_421) on Fri Jul 11 05:32:44 CDT 2025 -->
<title>StringChartValue (Aspose.Slides for Java (25.7) API Documentation)</title>
<meta name="date" content="2025-07-11">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="StringChartValue (Aspose.Slides for Java (25.7) API Documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/Storage.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/StringOrDoubleChartValue.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/StringChartValue.html" target="_top">Frames</a></li>
<li><a href="StringChartValue.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.aspose.slides</div>
<h2 title="Class StringChartValue" class="title">Class StringChartValue</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../com/aspose/slides/BaseChartValue.html" title="class in com.aspose.slides">com.aspose.slides.BaseChartValue</a></li>
<li>
<ul class="inheritance">
<li>com.aspose.slides.StringChartValue</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../com/aspose/slides/IBaseChartValue.html" title="interface in com.aspose.slides">IBaseChartValue</a>, <a href="../../../com/aspose/slides/IMultipleCellChartValue.html" title="interface in com.aspose.slides">IMultipleCellChartValue</a>, <a href="../../../com/aspose/slides/IStringChartValue.html" title="interface in com.aspose.slides">IStringChartValue</a></dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">StringChartValue</span>
extends <a href="../../../com/aspose/slides/BaseChartValue.html" title="class in com.aspose.slides">BaseChartValue</a>
implements <a href="../../../com/aspose/slides/IStringChartValue.html" title="interface in com.aspose.slides">IStringChartValue</a></pre>
<div class="block"><p>
 Represent string value which can be stored in pptx presentation document in two ways:
 1) in cell/cells of workbook related to chart;
 2) as literal value.
 </p></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IChartCellCollection.html" title="interface in com.aspose.slides">IChartCellCollection</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/StringChartValue.html#getAsCells--">getAsCells</a></span>()</code>
<div class="block">
 Null value assigning is not allowed.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/StringChartValue.html#getAsLiteralString--">getAsLiteralString</a></span>()</code>
<div class="block">
 Returns or sets value as literal string.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/StringChartValue.html#getCellsAddressInWorkbook--">getCellsAddressInWorkbook</a></span>()</code>
<div class="block">
 If DataSourceType property is DataSourceType.Worksheet then this method returns address
 of the cells in workbook which represent the string data.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>java.lang.Object</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/StringChartValue.html#getData--">getData</a></span>()</code>
<div class="block">
 Returns or sets Data object.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/StringChartValue.html#setAsCells-com.aspose.slides.IChartCellCollection-">setAsCells</a></span>(<a href="../../../com/aspose/slides/IChartCellCollection.html" title="interface in com.aspose.slides">IChartCellCollection</a>&nbsp;value)</code>
<div class="block">
 Null value assigning is not allowed.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/StringChartValue.html#setAsLiteralString-java.lang.String-">setAsLiteralString</a></span>(java.lang.String&nbsp;value)</code>
<div class="block">
 Returns or sets value as literal string.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/StringChartValue.html#setData-java.lang.Object-">setData</a></span>(java.lang.Object&nbsp;value)</code>
<div class="block">
 Returns or sets Data object.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/StringChartValue.html#setFromOneCell-com.aspose.slides.IChartDataCell-">setFromOneCell</a></span>(<a href="../../../com/aspose/slides/IChartDataCell.html" title="interface in com.aspose.slides">IChartDataCell</a>&nbsp;cell)</code>
<div class="block">
 Sets value from specified cell.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/StringChartValue.html#toString--">toString</a></span>()</code>
<div class="block">
 Returns string value data.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.aspose.slides.BaseChartValue">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;com.aspose.slides.<a href="../../../com/aspose/slides/BaseChartValue.html" title="class in com.aspose.slides">BaseChartValue</a></h3>
<code><a href="../../../com/aspose/slides/BaseChartValue.html#getDataSourceType--">getDataSourceType</a>, <a href="../../../com/aspose/slides/BaseChartValue.html#getParent_Immediate--">getParent_Immediate</a>, <a href="../../../com/aspose/slides/BaseChartValue.html#setDataSourceType-int-">setDataSourceType</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, wait, wait, wait</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.aspose.slides.IBaseChartValue">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;com.aspose.slides.<a href="../../../com/aspose/slides/IBaseChartValue.html" title="interface in com.aspose.slides">IBaseChartValue</a></h3>
<code><a href="../../../com/aspose/slides/IBaseChartValue.html#getDataSourceType--">getDataSourceType</a>, <a href="../../../com/aspose/slides/IBaseChartValue.html#setDataSourceType-int-">setDataSourceType</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getAsCells--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAsCells</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IChartCellCollection.html" title="interface in com.aspose.slides">IChartCellCollection</a>&nbsp;getAsCells()</pre>
<div class="block"><p>
 Null value assigning is not allowed.
 Returning value always is not null.
 Read/write <a href="../../../com/aspose/slides/IChartCellCollection.html" title="interface in com.aspose.slides"><code>IChartCellCollection</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IMultipleCellChartValue.html#getAsCells--">getAsCells</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IMultipleCellChartValue.html" title="interface in com.aspose.slides">IMultipleCellChartValue</a></code></dd>
</dl>
</li>
</ul>
<a name="setAsCells-com.aspose.slides.IChartCellCollection-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAsCells</h4>
<pre>public final&nbsp;void&nbsp;setAsCells(<a href="../../../com/aspose/slides/IChartCellCollection.html" title="interface in com.aspose.slides">IChartCellCollection</a>&nbsp;value)</pre>
<div class="block"><p>
 Null value assigning is not allowed.
 Returning value always is not null.
 Read/write <a href="../../../com/aspose/slides/IChartCellCollection.html" title="interface in com.aspose.slides"><code>IChartCellCollection</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IMultipleCellChartValue.html#setAsCells-com.aspose.slides.IChartCellCollection-">setAsCells</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IMultipleCellChartValue.html" title="interface in com.aspose.slides">IMultipleCellChartValue</a></code></dd>
</dl>
</li>
</ul>
<a name="getAsLiteralString--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAsLiteralString</h4>
<pre>public final&nbsp;java.lang.String&nbsp;getAsLiteralString()</pre>
<div class="block"><p>
 Returns or sets value as literal string.
 Read/write <code>String</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IStringChartValue.html#getAsLiteralString--">getAsLiteralString</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IStringChartValue.html" title="interface in com.aspose.slides">IStringChartValue</a></code></dd>
</dl>
</li>
</ul>
<a name="setAsLiteralString-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAsLiteralString</h4>
<pre>public final&nbsp;void&nbsp;setAsLiteralString(java.lang.String&nbsp;value)</pre>
<div class="block"><p>
 Returns or sets value as literal string.
 Read/write <code>String</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IStringChartValue.html#setAsLiteralString-java.lang.String-">setAsLiteralString</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IStringChartValue.html" title="interface in com.aspose.slides">IStringChartValue</a></code></dd>
</dl>
</li>
</ul>
<a name="getData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getData</h4>
<pre>public&nbsp;java.lang.Object&nbsp;getData()</pre>
<div class="block"><p>
 Returns or sets Data object.
 Read/write <code>Object</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IBaseChartValue.html#getData--">getData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IBaseChartValue.html" title="interface in com.aspose.slides">IBaseChartValue</a></code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/BaseChartValue.html#getData--">getData</a></code>&nbsp;in class&nbsp;<code><a href="../../../com/aspose/slides/BaseChartValue.html" title="class in com.aspose.slides">BaseChartValue</a></code></dd>
</dl>
</li>
</ul>
<a name="setData-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setData</h4>
<pre>public&nbsp;void&nbsp;setData(java.lang.Object&nbsp;value)</pre>
<div class="block"><p>
 Returns or sets Data object.
 Read/write <code>Object</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IBaseChartValue.html#setData-java.lang.Object-">setData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IBaseChartValue.html" title="interface in com.aspose.slides">IBaseChartValue</a></code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/BaseChartValue.html#setData-java.lang.Object-">setData</a></code>&nbsp;in class&nbsp;<code><a href="../../../com/aspose/slides/BaseChartValue.html" title="class in com.aspose.slides">BaseChartValue</a></code></dd>
</dl>
</li>
</ul>
<a name="toString--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>toString</h4>
<pre>public&nbsp;java.lang.String&nbsp;toString()</pre>
<div class="block"><p>
 Returns string value data. 
 Return null if DataSourceType is false and no string value was assigned.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IStringChartValue.html#toString--">toString</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IStringChartValue.html" title="interface in com.aspose.slides">IStringChartValue</a></code></dd>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code>toString</code>&nbsp;in class&nbsp;<code>java.lang.Object</code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>String representation of a value <code>String</code></dd>
</dl>
</li>
</ul>
<a name="setFromOneCell-com.aspose.slides.IChartDataCell-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFromOneCell</h4>
<pre>public final&nbsp;void&nbsp;setFromOneCell(<a href="../../../com/aspose/slides/IChartDataCell.html" title="interface in com.aspose.slides">IChartDataCell</a>&nbsp;cell)</pre>
<div class="block"><p>
 Sets value from specified cell.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IStringChartValue.html#setFromOneCell-com.aspose.slides.IChartDataCell-">setFromOneCell</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IStringChartValue.html" title="interface in com.aspose.slides">IStringChartValue</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>cell</code> - Cell.</dd>
</dl>
</li>
</ul>
<a name="getCellsAddressInWorkbook--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getCellsAddressInWorkbook</h4>
<pre>public final&nbsp;java.lang.String&nbsp;getCellsAddressInWorkbook()</pre>
<div class="block"><p>
 If DataSourceType property is DataSourceType.Worksheet then this method returns address
 of the cells in workbook which represent the string data. Otherwise return
 empty string.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IStringChartValue.html#getCellsAddressInWorkbook--">getCellsAddressInWorkbook</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IStringChartValue.html" title="interface in com.aspose.slides">IStringChartValue</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>String value <code>String</code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/Storage.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/StringOrDoubleChartValue.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/StringChartValue.html" target="_top">Frames</a></li>
<li><a href="StringChartValue.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><i>Copyright &copy; 2004-2025 Aspose Pty Ltd. All Rights Reserved.</i></small></p>
</body>
</html>
