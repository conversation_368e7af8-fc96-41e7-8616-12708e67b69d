<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_421) on Fri Jul 11 05:32:44 CDT 2025 -->
<title>SmartArtLayoutType (Aspose.Slides for Java (25.7) API Documentation)</title>
<meta name="date" content="2025-07-11">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="SmartArtLayoutType (Aspose.Slides for Java (25.7) API Documentation)";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/SmartArtColorType.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/SmartArtNode.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/SmartArtLayoutType.html" target="_top">Frames</a></li>
<li><a href="SmartArtLayoutType.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.classes.inherited.from.class.com.aspose.ms.System.Enum">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#methods.inherited.from.class.com.aspose.ms.System.Enum">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li>Method</li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.aspose.slides</div>
<h2 title="Class SmartArtLayoutType" class="title">Class SmartArtLayoutType</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.aspose.ms.System.ValueType&lt;com.aspose.ms.System.Enum&gt;</li>
<li>
<ul class="inheritance">
<li>com.aspose.ms.System.Enum</li>
<li>
<ul class="inheritance">
<li>com.aspose.slides.SmartArtLayoutType</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public final class <span class="typeNameLabel">SmartArtLayoutType</span>
extends com.aspose.ms.System.Enum</pre>
<div class="block"><p>
 Represents layout type of a SmartArt diagram.
 </p></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.com.aspose.ms.System.Enum">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from class&nbsp;com.aspose.ms.System.Enum</h3>
<code>com.aspose.ms.System.Enum.AbstractEnum, com.aspose.ms.System.Enum.FlaggedEnum, com.aspose.ms.System.Enum.ObjectEnum, com.aspose.ms.System.Enum.SimpleEnum</code></li>
</ul>
</li>
</ul>
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#AccentedPicture">AccentedPicture</a></span></code>
<div class="block">
 Use to show a central, photographic idea with related ideas on the side.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#AccentProcess">AccentProcess</a></span></code>
<div class="block">
 Use to show a progression, a timeline, or sequential steps in a task, process, or workflow.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#AlternatingFlow">AlternatingFlow</a></span></code>
<div class="block">
 Use to show groups of information or sequential steps in a task, process, or workflow.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#AlternatingHexagons">AlternatingHexagons</a></span></code>
<div class="block">
 Use to represent a series of interconnected ideas.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#AlternatingPictureBlocks">AlternatingPictureBlocks</a></span></code>
<div class="block">
 Use to show a series of pictures from top to bottom.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#AlternatingPictureCircles">AlternatingPictureCircles</a></span></code>
<div class="block">
 Use to show a set of pictures with text.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#ArrowRibbon">ArrowRibbon</a></span></code>
<div class="block">
 Use to show either related or contrasting concepts with some connection, such as opposing forces.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#AscendingPictureAccentProcess">AscendingPictureAccentProcess</a></span></code>
<div class="block">
 Use to show an ascending series of pictures with descriptive text.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#Balance">Balance</a></span></code>
<div class="block">
 Use to compare or show the relationship between two ideas.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#BasicBendingProcess">BasicBendingProcess</a></span></code>
<div class="block">
 Use to show a progression or sequential steps in a task, process, or workflow.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#BasicBlockList">BasicBlockList</a></span></code>
<div class="block">
 Use to show non-sequential or grouped blocks of information.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#BasicChevronProcess">BasicChevronProcess</a></span></code>
<div class="block">
 Use to show a progression; a timeline; sequential steps in a task, process, or workflow; or to emphasize movement or direction.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#BasicCycle">BasicCycle</a></span></code>
<div class="block">
 Use to represent a continuing sequence of stages, tasks, or events in a circular flow.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#BasicMatrix">BasicMatrix</a></span></code>
<div class="block">
 Use to show the relationship of components to a whole in quadrants.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#BasicPie">BasicPie</a></span></code>
<div class="block">
 Use to show how individual parts form a whole.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#BasicProcess">BasicProcess</a></span></code>
<div class="block">
 Use to show a progression or sequential steps in a task, process, or workflow.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#BasicPyramid">BasicPyramid</a></span></code>
<div class="block">
 Use to show proportional, interconnected, or hierarchical relationships with the largest component on the bottom and narrowing up.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#BasicRadial">BasicRadial</a></span></code>
<div class="block">
 Use to show the relationship to a central idea in a cycle.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#BasicTarget">BasicTarget</a></span></code>
<div class="block">
 Use to show containment, gradations, or hierarchical relationships.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#BasicTimeline">BasicTimeline</a></span></code>
<div class="block">
 Use to show sequential steps in a task, process, or workflow, or to show timeline information.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#BasicVenn">BasicVenn</a></span></code>
<div class="block">
 Use to show overlapping or interconnected relationships.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#BendingPictureAccentList">BendingPictureAccentList</a></span></code>
<div class="block">
 Use to show non-sequential or grouped blocks of information.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#BendingPictureBlocks">BendingPictureBlocks</a></span></code>
<div class="block">
 Use to show a series of pictures.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#BendingPictureCaption">BendingPictureCaption</a></span></code>
<div class="block">
 Use to show a sequential series of pictures.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#BendingPictureCaptionList">BendingPictureCaptionList</a></span></code>
<div class="block">
 Use to show a series of pictures.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#BendingPictureSemiTransparentText">BendingPictureSemiTransparentText</a></span></code>
<div class="block">
 Use to show a series of pictures.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#BlockCycle">BlockCycle</a></span></code>
<div class="block">
 Use to represent a continuing sequence of stages, tasks, or events in a circular flow.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#BubblePictureList">BubblePictureList</a></span></code>
<div class="block">
 Use to show a series of pictures.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#CaptionedPictures">CaptionedPictures</a></span></code>
<div class="block">
 Use to show pictures with multiple levels of text.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#ChevronList">ChevronList</a></span></code>
<div class="block">
 Use to show a progression through several processes that make up an overall workflow.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#CircleAccentTimeline">CircleAccentTimeline</a></span></code>
<div class="block">
 Use to show a series of events or timeline information.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#CircleArrowProcess">CircleArrowProcess</a></span></code>
<div class="block">
 Use to show sequential items with supporting text for each item.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#CirclePictureHierarchy">CirclePictureHierarchy</a></span></code>
<div class="block">
 Use to show hierarchical information or reporting relationships in an organization.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#CircleRelationship">CircleRelationship</a></span></code>
<div class="block">
 Use to show the relationship to or from a central idea.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#CircularBendingProcess">CircularBendingProcess</a></span></code>
<div class="block">
 Use to show a long or non-linear sequence or steps in a task, process, or workflow.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#CircularPictureCallout">CircularPictureCallout</a></span></code>
<div class="block">
 Use to show a central idea and sub-ideas or related items.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#ClosedChevronProcess">ClosedChevronProcess</a></span></code>
<div class="block">
 Use to show a progression, a timeline, or sequential steps in a task, process, or workflow, or to emphasize movement or direction.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#ContinuousArrowProcess">ContinuousArrowProcess</a></span></code>
<div class="block">
 Use to show a timeline or sequential steps in a task, process, or workflow.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#ContinuousBlockProcess">ContinuousBlockProcess</a></span></code>
<div class="block">
 Use to show a progression or sequential steps in a task, process, or workflow.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#ContinuousCycle">ContinuousCycle</a></span></code>
<div class="block">
 Use to represent a continuing sequence of stages, tasks, or events in a circular flow.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#ContinuousPictureList">ContinuousPictureList</a></span></code>
<div class="block">
 Use to show groups of interconnected information.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#ConvergingArrows">ConvergingArrows</a></span></code>
<div class="block">
 Use to show ideas or concepts that converge to a central point.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#ConvergingRadial">ConvergingRadial</a></span></code>
<div class="block">
 Use to show relationships of concepts or components to a central idea in a cycle.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#CounterbalanceArrows">CounterbalanceArrows</a></span></code>
<div class="block">
 Use to show two opposing ideas or concepts.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#Custom">Custom</a></span></code>
<div class="block">
 Represents a SmartArt diagram with custom layout template</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#CycleMatrix">CycleMatrix</a></span></code>
<div class="block">
 Use to show the relationship to a central idea in a cyclical progression.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#DescendingBlockList">DescendingBlockList</a></span></code>
<div class="block">
 Use to show groups of related ideas or lists of information.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#DescendingProcess">DescendingProcess</a></span></code>
<div class="block">
 Use to show a descending series of events.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#DetailedProcess">DetailedProcess</a></span></code>
<div class="block">
 Use with large amounts of Level 2 text to show a progression through stages.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#DivergingArrows">DivergingArrows</a></span></code>
<div class="block">
 Use to show ideas or concepts that progress outward from a central source.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#DivergingRadial">DivergingRadial</a></span></code>
<div class="block">
 Use to show relationships to a central idea in a cycle.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#Equation">Equation</a></span></code>
<div class="block">
 Use to show sequential steps or tasks that depict a plan or result.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#FramedTextPicture">FramedTextPicture</a></span></code>
<div class="block">
 Use to show pictures with corresponding Level 1 text displayed in a frame.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#Funnel">Funnel</a></span></code>
<div class="block">
 Use to show the filtering of information or how parts merge into a whole.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#Gear">Gear</a></span></code>
<div class="block">
 Use to show interlocking ideas.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#GridMatrix">GridMatrix</a></span></code>
<div class="block">
 Use to show the placement of concepts along two axes.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#GroupedList">GroupedList</a></span></code>
<div class="block">
 Use to show groups and sub-groups of information, or steps and sub-steps in a task, process, or workflow.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#HalfCircleOrganizationChart">HalfCircleOrganizationChart</a></span></code>
<div class="block">
 Use to show hierarchical information or reporting relationships in an organization.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#HexagonCluster">HexagonCluster</a></span></code>
<div class="block">
 Use to show pictures with associated descriptive text.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#Hierarchy">Hierarchy</a></span></code>
<div class="block">
 Use to show hierarchical relationships progressing from top to bottom.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#HierarchyList">HierarchyList</a></span></code>
<div class="block">
 Use to show hierarchical relationships progressing across groups.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#HorizontalBulletList">HorizontalBulletList</a></span></code>
<div class="block">
 Use to show non-sequential or grouped lists of information.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#HorizontalHierarchy">HorizontalHierarchy</a></span></code>
<div class="block">
 Use to show hierarchical relationships progressing horizontally.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#HorizontalLabeledHierarchy">HorizontalLabeledHierarchy</a></span></code>
<div class="block">
 Use to show hierarchical relationships progressing horizontally and grouped hierarchically.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#HorizontalMultiLevelHierarchy">HorizontalMultiLevelHierarchy</a></span></code>
<div class="block">
 Use to show large amounts of hierarchical information progressing horizontally.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#HorizontalOrganizationChart">HorizontalOrganizationChart</a></span></code>
<div class="block">
 Use to show hierarchical information horizontally or reporting relationships in an organization.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#HorizontalPictureList">HorizontalPictureList</a></span></code>
<div class="block">
 Use to show non-sequential or grouped information with an emphasis on related pictures.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#IncreasingArrowsProcess">IncreasingArrowsProcess</a></span></code>
<div class="block">
 Use to show sequential and overlapping steps in a process.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#IncreasingCircleProcess">IncreasingCircleProcess</a></span></code>
<div class="block">
 Use to show a series of steps, with the interior of the circle increasing with each step.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#InvertedPyramid">InvertedPyramid</a></span></code>
<div class="block">
 Use to show proportional, interconnected, or hierarchical relationships with the largest component on the top and narrowing down.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#LabeledHierarchy">LabeledHierarchy</a></span></code>
<div class="block">
 Use to show hierarchical relationships progressing from top to bottom and grouped hierarchically.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#LinearVenn">LinearVenn</a></span></code>
<div class="block">
 Use to show overlapping relationships in a sequence.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#LinedList">LinedList</a></span></code>
<div class="block">
 Use to show large amounts of text divided into categories and subcategories.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#MultidirectionalCycle">MultidirectionalCycle</a></span></code>
<div class="block">
 Use to represent a continuing sequence of stages, tasks, or events that can occur in any direction.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#NameandTitleOrganizationChart">NameandTitleOrganizationChart</a></span></code>
<div class="block">
 Use to show hierarchical information or reporting relationships in an organization.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#NestedTarget">NestedTarget</a></span></code>
<div class="block">
 Use to show containment relationships.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#NondirectionalCycle">NondirectionalCycle</a></span></code>
<div class="block">
 Use to represent a continuing sequence of stages, tasks, or events in a circular flow.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#OpposingArrows">OpposingArrows</a></span></code>
<div class="block">
 Use to show two opposing ideas, or ideas that diverge from a central point.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#OpposingIdeas">OpposingIdeas</a></span></code>
<div class="block">
 Use to show two opposing or contrasting ideas.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#OrganizationChart">OrganizationChart</a></span></code>
<div class="block">
 Use to show hierarchical information or reporting relationships in an organization.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#PhasedProcess">PhasedProcess</a></span></code>
<div class="block">
 Use to show three phases of a process.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#PictureAccentBlocks">PictureAccentBlocks</a></span></code>
<div class="block">
 Use to show a group of pictures in blocks starting from the corner.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#PictureAccentList">PictureAccentList</a></span></code>
<div class="block">
 Use to show grouped or related information.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#PictureAccentProcess">PictureAccentProcess</a></span></code>
<div class="block">
 Use to show sequential steps in a task, process, or workflow.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#PictureCaptionList">PictureCaptionList</a></span></code>
<div class="block">
 Use to show non-sequential or grouped blocks of information.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#PictureGrid">PictureGrid</a></span></code>
<div class="block">
 Use to show pictures laid out on a square grid.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#PictureLineup">PictureLineup</a></span></code>
<div class="block">
 Use to show a series of pictures side by side.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#PictureOrganizationChart">PictureOrganizationChart</a></span></code>
<div class="block">
 Use to show hierarchical information or reporting relationships in an organization, with corresponding pictures.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#PictureStrips">PictureStrips</a></span></code>
<div class="block">
 Use to show a series of pictures from top to bottom with Level 1 text beside each.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#PieProcess">PieProcess</a></span></code>
<div class="block">
 Use to show steps in a process with each pie slice increasing in size up to seven shapes.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#PlusandMinus">PlusandMinus</a></span></code>
<div class="block">
 Use to show the pros and cons of  two ideas.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#ProcessArrows">ProcessArrows</a></span></code>
<div class="block">
 Use to show information illustrating a process or workflow.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#ProcessList">ProcessList</a></span></code>
<div class="block">
 Use to show multiple groups of information or steps and sub-steps in a task, process, or workflow.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#PyramidList">PyramidList</a></span></code>
<div class="block">
 Use to show proportional, interconnected, or hierarchical relationships.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#RadialCluster">RadialCluster</a></span></code>
<div class="block">
 Use to show data that relates to a central idea or theme.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#RadialCycle">RadialCycle</a></span></code>
<div class="block">
 Use to show the relationship to a central idea.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#RadialList">RadialList</a></span></code>
<div class="block">
 Use to show relationships to a central idea in a cycle.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#RadialVenn">RadialVenn</a></span></code>
<div class="block">
 Use to show both overlapping relationships and the relationship to a central idea in a cycle.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#RandomToResultProcess">RandomToResultProcess</a></span></code>
<div class="block">
 Use to show, through a series of steps, how several chaotic  ideas can result in a unified goal or idea.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#RepeatingBendingProcess">RepeatingBendingProcess</a></span></code>
<div class="block">
 Use to show a progression or sequential steps in a task, process, or workflow.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#ReverseList">ReverseList</a></span></code>
<div class="block">
 Use to change between two items.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#SegmentedCycle">SegmentedCycle</a></span></code>
<div class="block">
 Use to show a progression or a sequence of stages, tasks, or events in a circular flow.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#SegmentedProcess">SegmentedProcess</a></span></code>
<div class="block">
 Use to show a progression or sequential steps in a task, process, or workflow.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#SegmentedPyramid">SegmentedPyramid</a></span></code>
<div class="block">
 Use to show containment, proportional, or interconnected relationships.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#SnapshotPictureList">SnapshotPictureList</a></span></code>
<div class="block">
 Use to show pictures with explanatory text.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#SpiralPicture">SpiralPicture</a></span></code>
<div class="block">
 Use to show a series of up to five pictures with corresponding Level 1 captions that spiral in to the center.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#SquareAccentList">SquareAccentList</a></span></code>
<div class="block">
 Use to show lists of information divided into categories.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#StackedList">StackedList</a></span></code>
<div class="block">
 Use to show groups of information or steps in a task, process, or workflow.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#StackedVenn">StackedVenn</a></span></code>
<div class="block">
 Use to show overlapping relationships.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#StaggeredProcess">StaggeredProcess</a></span></code>
<div class="block">
 Use to show a downward progression through stages.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#StepDownProcess">StepDownProcess</a></span></code>
<div class="block">
 Use to show a descending process with multiple steps and sub-steps.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#StepUpProcess">StepUpProcess</a></span></code>
<div class="block">
 Use to show an ascending series of steps or lists of information.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#SubStepProcess">SubStepProcess</a></span></code>
<div class="block">
 Use to show a multi-step process with sub-steps between each instance of Level 1 text.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#TableHierarchy">TableHierarchy</a></span></code>
<div class="block">
 Use to show groups of information built from top to bottom, and the hierarchies within each group.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#TableList">TableList</a></span></code>
<div class="block">
 Use to show grouped or related information of equal value.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#TargetList">TargetList</a></span></code>
<div class="block">
 Use to show interrelated or overlapping information.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#TextCycle">TextCycle</a></span></code>
<div class="block">
 Use to represent a continuing sequence of stages, tasks, or events in a circular flow.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#TitledMatrix">TitledMatrix</a></span></code>
<div class="block">
 Use to show the relationships of four quadrants to a whole.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#TitledPictureAccentList">TitledPictureAccentList</a></span></code>
<div class="block">
 Use to show lists of information with an accent picture for each Level 2 text.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#TitledPictureBlocks">TitledPictureBlocks</a></span></code>
<div class="block">
 Use to show a series of pictures.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#TitlePictureLineup">TitlePictureLineup</a></span></code>
<div class="block">
 Use to show a series of pictures that each have their own title and description.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#TrapezoidList">TrapezoidList</a></span></code>
<div class="block">
 Use to show grouped or related information of equal value.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#UpwardArrow">UpwardArrow</a></span></code>
<div class="block">
 Use to show a progression or steps that trend upward in a task, process, or workflow.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#VerticalAccentList">VerticalAccentList</a></span></code>
<div class="block">
 Use to show lists of information.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#VerticalArrowList">VerticalArrowList</a></span></code>
<div class="block">
 Use to show a progression or sequential steps in a task, process, or workflow that move toward a common goal.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#VerticalBendingProcess">VerticalBendingProcess</a></span></code>
<div class="block">
 Use to show a progression or sequential steps in a task, process, or workflow.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#VerticalBlockList">VerticalBlockList</a></span></code>
<div class="block">
 Use to show groups of information or steps in a task, process, or workflow.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#VerticalBoxList">VerticalBoxList</a></span></code>
<div class="block">
 Use to show several groups of information, especially groups with large amounts of Level 2 text.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#VerticalBulletList">VerticalBulletList</a></span></code>
<div class="block">
 Use to show non-sequential or grouped blocks of information.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#VerticalChevronList">VerticalChevronList</a></span></code>
<div class="block">
 Use to show a progression or sequential steps in a task, process, or workflow, or to emphasize movement or direction.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#VerticalCircleList">VerticalCircleList</a></span></code>
<div class="block">
 Use to show sequential or grouped data.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#VerticalCurvedList">VerticalCurvedList</a></span></code>
<div class="block">
 Use to show a curved list of information.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#VerticalEquation">VerticalEquation</a></span></code>
<div class="block">
 Use to show sequential steps or tasks that depict a plan or result.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#VerticalPictureAccentList">VerticalPictureAccentList</a></span></code>
<div class="block">
 Use to show non-sequential or grouped blocks of information.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#VerticalPictureList">VerticalPictureList</a></span></code>
<div class="block">
 Use to show non-sequential or grouped blocks of information.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtLayoutType.html#VerticalProcess">VerticalProcess</a></span></code>
<div class="block">
 Use to show a progression or sequential steps in a task, process, or workflow from top to bottom.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.com.aspose.ms.System.Enum">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;com.aspose.ms.System.Enum</h3>
<code>EnumSeparatorCharArray</code></li>
</ul>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.aspose.ms.System.Enum">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;com.aspose.ms.System.Enum</h3>
<code>Clone, CloneTo, format, format, get_Caption, get_Value, getName, getName, getNames, getNames, getUnderlyingType, getUnderlyingType, getValue, getValues, isDefined, isDefined, isDefined, isDefined, parse, parse, parse, parse, register, toObject, toString</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="AccentProcess">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>AccentProcess</h4>
<pre>public static final&nbsp;int AccentProcess</pre>
<div class="block"><p>
 Use to show a progression, a timeline, or sequential steps in a task, process, or workflow. Works well for illustrating both Level 1 and Level 2 text.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.AccentProcess">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="AccentedPicture">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>AccentedPicture</h4>
<pre>public static final&nbsp;int AccentedPicture</pre>
<div class="block"><p>
 Use to show a central, photographic idea with related ideas on the side. The top Level 1 text appears over the central picture. Corresponding text for other Level 1 shapes appear next to the small circular pictures. This layout also works well with no text.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.AccentedPicture">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="AlternatingFlow">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>AlternatingFlow</h4>
<pre>public static final&nbsp;int AlternatingFlow</pre>
<div class="block"><p>
 Use to show groups of information or sequential steps in a task, process, or workflow. Emphasizes the interaction or relationships among the groups of information.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.AlternatingFlow">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="AlternatingHexagons">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>AlternatingHexagons</h4>
<pre>public static final&nbsp;int AlternatingHexagons</pre>
<div class="block"><p>
 Use to represent a series of interconnected ideas. Level 1 text appears inside the hexagons. Level 2 text appears outside the shapes.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.AlternatingHexagons">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="AlternatingPictureBlocks">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>AlternatingPictureBlocks</h4>
<pre>public static final&nbsp;int AlternatingPictureBlocks</pre>
<div class="block"><p>
 Use to show a series of pictures from top to bottom. Text appears alternately on the right or left of the picture.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.AlternatingPictureBlocks">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="AlternatingPictureCircles">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>AlternatingPictureCircles</h4>
<pre>public static final&nbsp;int AlternatingPictureCircles</pre>
<div class="block"><p>
 Use to show a set of pictures with text. The corresponding text appears in the central circles with the images alternating from left to right.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.AlternatingPictureCircles">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="ArrowRibbon">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ArrowRibbon</h4>
<pre>public static final&nbsp;int ArrowRibbon</pre>
<div class="block"><p>
 Use to show either related or contrasting concepts with some connection, such as opposing forces. The first two lines of Level 1 text are used for text in the arrows. Unused text does not appear, but remains available if you switch layouts.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.ArrowRibbon">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="AscendingPictureAccentProcess">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>AscendingPictureAccentProcess</h4>
<pre>public static final&nbsp;int AscendingPictureAccentProcess</pre>
<div class="block"><p>
 Use to show an ascending series of pictures with descriptive text. Works best with a small amount of text.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.AscendingPictureAccentProcess">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Balance">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Balance</h4>
<pre>public static final&nbsp;int Balance</pre>
<div class="block"><p>
 Use to compare or show the relationship between two ideas. Each of the first two lines of Level 1 text corresponds to text at the top of one side of the center point. Emphasizes Level 2 text, which is limited to four shapes on each side of the center point. The balance tips towards the side with the most shapes containing Level 2 text. Unused text does not appear, but remains available if you switch layouts. 
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.Balance">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="BasicBendingProcess">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BasicBendingProcess</h4>
<pre>public static final&nbsp;int BasicBendingProcess</pre>
<div class="block"><p>
 Use to show a progression or sequential steps in a task, process, or workflow. Maximizes both horizontal and vertical display space for shapes.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.BasicBendingProcess">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="BasicBlockList">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BasicBlockList</h4>
<pre>public static final&nbsp;int BasicBlockList</pre>
<div class="block"><p>
 Use to show non-sequential or grouped blocks of information. Maximizes both horizontal and vertical display space for shapes.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.BasicBlockList">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="BasicChevronProcess">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BasicChevronProcess</h4>
<pre>public static final&nbsp;int BasicChevronProcess</pre>
<div class="block"><p>
 Use to show a progression; a timeline; sequential steps in a task, process, or workflow; or to emphasize movement or direction. Level 1 text appears inside an arrow shape while Level 2 text appears below the arrow shapes.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.BasicChevronProcess">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="BasicCycle">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BasicCycle</h4>
<pre>public static final&nbsp;int BasicCycle</pre>
<div class="block"><p>
 Use to represent a continuing sequence of stages, tasks, or events in a circular flow. Emphasizes the stages or steps rather than the connecting arrows or flow. Works best with Level 1 text only.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.BasicCycle">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="BasicMatrix">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BasicMatrix</h4>
<pre>public static final&nbsp;int BasicMatrix</pre>
<div class="block"><p>
 Use to show the relationship of components to a whole in quadrants. The first four lines of Level 1 text appear in the quadrants. Unused text does not appear, but remains available if you switch layouts.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.BasicMatrix">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="BasicPie">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BasicPie</h4>
<pre>public static final&nbsp;int BasicPie</pre>
<div class="block"><p>
 Use to show how individual parts form a whole. The first seven lines of Level 1 text correspond to the evenly distributed wedge or pie shapes. The top Level 1 text shape appears outside of the rest of the pie for emphasis. Unused text does not appear, but remains available if you switch layouts.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.BasicPie">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="BasicProcess">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BasicProcess</h4>
<pre>public static final&nbsp;int BasicProcess</pre>
<div class="block"><p>
 Use to show a progression or sequential steps in a task, process, or workflow.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.BasicProcess">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="BasicPyramid">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BasicPyramid</h4>
<pre>public static final&nbsp;int BasicPyramid</pre>
<div class="block"><p>
 Use to show proportional, interconnected, or hierarchical relationships with the largest component on the bottom and narrowing up. Level 1 text appears in the pyramid segments and Level 2 text appears in shapes alongside each segment.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.BasicPyramid">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="BasicRadial">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BasicRadial</h4>
<pre>public static final&nbsp;int BasicRadial</pre>
<div class="block"><p>
 Use to show the relationship to a central idea in a cycle. The first line of Level 1 text corresponds to the central shape, and its Level 2 text corresponds to the surrounding circular shapes. Unused text does not appear, but remains available if you switch layouts.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.BasicRadial">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="BasicTarget">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BasicTarget</h4>
<pre>public static final&nbsp;int BasicTarget</pre>
<div class="block"><p>
 Use to show containment, gradations, or hierarchical relationships. The first five lines of Level 1 text are associated with a circle. Unused text does not appear, but remains available if you switch layouts.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.BasicTarget">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="BasicTimeline">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BasicTimeline</h4>
<pre>public static final&nbsp;int BasicTimeline</pre>
<div class="block"><p>
 Use to show sequential steps in a task, process, or workflow, or to show timeline information. Works well with both Level 1 and Level 2 text.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.BasicTimeline">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="BasicVenn">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BasicVenn</h4>
<pre>public static final&nbsp;int BasicVenn</pre>
<div class="block"><p>
 Use to show overlapping or interconnected relationships. The first seven lines of Level 1 text correspond with a circle. If there are four or fewer lines of Level 1 text, the text is inside the circles. If there are more than four lines of Level 1 text, the text is outside of the circles. Unused text does not appear, but remains available if you switch layouts.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.BasicVenn">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="BendingPictureAccentList">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BendingPictureAccentList</h4>
<pre>public static final&nbsp;int BendingPictureAccentList</pre>
<div class="block"><p>
 Use to show non-sequential or grouped blocks of information. The small circular shapes are designed to contain pictures. Works well for illustrating both Level 1 and Level 2 text. Maximizes both horizontal and vertical display space for shapes.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.BendingPictureAccentList">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="BendingPictureBlocks">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BendingPictureBlocks</h4>
<pre>public static final&nbsp;int BendingPictureBlocks</pre>
<div class="block"><p>
 Use to show a series of pictures. The box covering the bottom corner can contain small amounts of text.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.BendingPictureBlocks">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="BendingPictureCaption">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BendingPictureCaption</h4>
<pre>public static final&nbsp;int BendingPictureCaption</pre>
<div class="block"><p>
 Use to show a sequential series of pictures. The box covering the bottom corner can contain small amounts of text.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.BendingPictureCaption">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="BendingPictureCaptionList">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BendingPictureCaptionList</h4>
<pre>public static final&nbsp;int BendingPictureCaptionList</pre>
<div class="block"><p>
 Use to show a series of pictures. The title and description appear in a callout shape under each picture.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.BendingPictureCaptionList">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="BendingPictureSemiTransparentText">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BendingPictureSemiTransparentText</h4>
<pre>public static final&nbsp;int BendingPictureSemiTransparentText</pre>
<div class="block"><p>
 Use to show a series of pictures. A semi-transparent box covers the lower portion of the picture and contains all levels of text.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.BendingPictureSemiTransparentText">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="BlockCycle">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BlockCycle</h4>
<pre>public static final&nbsp;int BlockCycle</pre>
<div class="block"><p>
 Use to represent a continuing sequence of stages, tasks, or events in a circular flow. Emphasizes the stages or steps rather than the connecting arrows or flow.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.BlockCycle">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="BubblePictureList">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BubblePictureList</h4>
<pre>public static final&nbsp;int BubblePictureList</pre>
<div class="block"><p>
 Use to show a series of pictures. Can contain up to eight Level 1 pictures. Unused text and pictures do not appear, but remain available if you switch layouts. Works best with small amounts of text.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.BubblePictureList">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CaptionedPictures">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CaptionedPictures</h4>
<pre>public static final&nbsp;int CaptionedPictures</pre>
<div class="block"><p>
 Use to show pictures with multiple levels of text.  Works best with a small amount of Level 1 text and a medium amount of Level 2 text.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.CaptionedPictures">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="ChevronList">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ChevronList</h4>
<pre>public static final&nbsp;int ChevronList</pre>
<div class="block"><p>
 Use to show a progression through several processes that make up an overall workflow. Also works for illustrating contrasting processes. The Level 1 text corresponds to the first arrow shape on the left, while the Level 2 text corresponds to horizontal sub-steps for each shape that contains Level 1 text.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.ChevronList">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CircleAccentTimeline">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CircleAccentTimeline</h4>
<pre>public static final&nbsp;int CircleAccentTimeline</pre>
<div class="block"><p>
 Use to show a series of events or timeline information. Level 1 text appears next to larger circular shapes. Level 2 text appears next to smaller circular shapes.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.CircleAccentTimeline">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CircleArrowProcess">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CircleArrowProcess</h4>
<pre>public static final&nbsp;int CircleArrowProcess</pre>
<div class="block"><p>
 Use to show sequential items with supporting text for each item. This diagram works best with small amounts of Level 1 text.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.CircleArrowProcess">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CirclePictureHierarchy">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CirclePictureHierarchy</h4>
<pre>public static final&nbsp;int CirclePictureHierarchy</pre>
<div class="block"><p>
 Use to show hierarchical information or reporting relationships in an organization. Pictures appear in circles and corresponding text appears next to the pictures.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.CirclePictureHierarchy">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CircleRelationship">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CircleRelationship</h4>
<pre>public static final&nbsp;int CircleRelationship</pre>
<div class="block"><p>
 Use to show the relationship to or from a central idea. Level 2 text is added non-sequentially and is limited to five items. There can only be one Level 1 item.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.CircleRelationship">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CircularBendingProcess">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CircularBendingProcess</h4>
<pre>public static final&nbsp;int CircularBendingProcess</pre>
<div class="block"><p>
 Use to show a long or non-linear sequence or steps in a task, process, or workflow. Works best with Level 1 text only. Maximizes both horizontal and vertical display space for shapes.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.CircularBendingProcess">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CircularPictureCallout">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CircularPictureCallout</h4>
<pre>public static final&nbsp;int CircularPictureCallout</pre>
<div class="block"><p>
 Use to show a central idea and sub-ideas or related items. The text for the first picture covers the lower portion of the picture. The corresponding text for other Level 1 shapes appears next to the small circular pictures. This diagram also works well with no text.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.CircularPictureCallout">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="ClosedChevronProcess">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ClosedChevronProcess</h4>
<pre>public static final&nbsp;int ClosedChevronProcess</pre>
<div class="block"><p>
 Use to show a progression, a timeline, or sequential steps in a task, process, or workflow, or to emphasize movement or direction. Can be used to emphasize information in the starting shape. Works best with Level 1 text only.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.ClosedChevronProcess">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="ContinuousArrowProcess">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ContinuousArrowProcess</h4>
<pre>public static final&nbsp;int ContinuousArrowProcess</pre>
<div class="block"><p>
 Use to show a timeline or sequential steps in a task, process, or workflow. Works best with Level 1 text because each line of Level 1 text appears inside the arrow shape. Level 2 text appears outside the arrow shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.ContinuousArrowProcess">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="ContinuousBlockProcess">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ContinuousBlockProcess</h4>
<pre>public static final&nbsp;int ContinuousBlockProcess</pre>
<div class="block"><p>
 Use to show a progression or sequential steps in a task, process, or workflow. Works best with minimal Level 1 and Level 2 text.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.ContinuousBlockProcess">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="ContinuousCycle">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ContinuousCycle</h4>
<pre>public static final&nbsp;int ContinuousCycle</pre>
<div class="block"><p>
 Use to represent a continuing sequence of stages, tasks, or events in a circular flow. Emphasizes the connection between all components. Works best with Level 1 text only.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.ContinuousCycle">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="ContinuousPictureList">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ContinuousPictureList</h4>
<pre>public static final&nbsp;int ContinuousPictureList</pre>
<div class="block"><p>
 Use to show groups of interconnected information. The circular shapes are designed to contain pictures.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.ContinuousPictureList">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="ConvergingArrows">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ConvergingArrows</h4>
<pre>public static final&nbsp;int ConvergingArrows</pre>
<div class="block"><p>
 Use to show ideas or concepts that converge to a central point. Works best with Level 1 text only.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.ConvergingArrows">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="ConvergingRadial">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ConvergingRadial</h4>
<pre>public static final&nbsp;int ConvergingRadial</pre>
<div class="block"><p>
 Use to show relationships of concepts or components to a central idea in a cycle. The first line of Level 1 text corresponds to the central circular shape and the lines of Level 2 text correspond to the surrounding rectangular shapes. Unused text does not appear, but remains available if you switch layouts.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.ConvergingRadial">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CounterbalanceArrows">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CounterbalanceArrows</h4>
<pre>public static final&nbsp;int CounterbalanceArrows</pre>
<div class="block"><p>
 Use to show two opposing ideas or concepts. Each of the first two lines of Level 1 text corresponds to an arrow and works well with Level 2 text. Unused text does not appear, but remains available if you switch layouts.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.CounterbalanceArrows">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CycleMatrix">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CycleMatrix</h4>
<pre>public static final&nbsp;int CycleMatrix</pre>
<div class="block"><p>
 Use to show the relationship to a central idea in a cyclical progression. Each of the first four lines of Level 1 text corresponds to a wedge or pie shape, and Level 2 text appears in a rectangular shape to the side of the wedge or pie shape. Unused text does not appear, but remains available if you switch layouts.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.CycleMatrix">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DescendingBlockList">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DescendingBlockList</h4>
<pre>public static final&nbsp;int DescendingBlockList</pre>
<div class="block"><p>
 Use to show groups of related ideas or lists of information. The text shapes decrease in height sequentially, and the Level 1 text displays vertically.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.DescendingBlockList">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DescendingProcess">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DescendingProcess</h4>
<pre>public static final&nbsp;int DescendingProcess</pre>
<div class="block"><p>
 Use to show a descending series of events. The first Level 1 text is at the top of arrow, and the last Level 1 text displays at the bottom of the arrow. Only the first seven Level 1 items appear. Works best with small to medium amounts of text.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.DescendingProcess">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DetailedProcess">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DetailedProcess</h4>
<pre>public static final&nbsp;int DetailedProcess</pre>
<div class="block"><p>
 Use with large amounts of Level 2 text to show a progression through stages.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.DetailedProcess">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DivergingArrows">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DivergingArrows</h4>
<pre>public static final&nbsp;int DivergingArrows</pre>
<div class="block"><p>
 Use to show ideas or concepts that progress outward from a central source. Works best with Level 1 text only.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.DivergingArrows">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DivergingRadial">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DivergingRadial</h4>
<pre>public static final&nbsp;int DivergingRadial</pre>
<div class="block"><p>
 Use to show relationships to a central idea in a cycle. The first Level 1 line of text corresponds to the central circular shape. Emphasizes the surrounding circles rather than the central idea. Unused text does not appear, but remains available if you switch layouts.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.DivergingRadial">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Equation">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Equation</h4>
<pre>public static final&nbsp;int Equation</pre>
<div class="block"><p>
 Use to show sequential steps or tasks that depict a plan or result. The last Level 1 line of text appears after the equals sign (=).Works best with Level 1 text only.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.Equation">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="FramedTextPicture">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FramedTextPicture</h4>
<pre>public static final&nbsp;int FramedTextPicture</pre>
<div class="block"><p>
 Use to show pictures with corresponding Level 1 text displayed in a frame.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.FramedTextPicture">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Funnel">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Funnel</h4>
<pre>public static final&nbsp;int Funnel</pre>
<div class="block"><p>
 Use to show the filtering of information or how parts merge into a whole. Emphasizes the final outcome. Can contain up to four lines of Level 1 text; the last of these four Level 1 text lines appears below the funnel and the other lines  correspond to a circular shape. Unused text does not appear, but remains available if you switch layouts.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.Funnel">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Gear">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Gear</h4>
<pre>public static final&nbsp;int Gear</pre>
<div class="block"><p>
 Use to show interlocking ideas. Each of the first three lines of Level 1 text corresponds to a gear shape, and their corresponding Level 2 text appears in rectangles next to the gear shape. Unused text does not appear, but remains available if you switch layouts.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.Gear">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="GridMatrix">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>GridMatrix</h4>
<pre>public static final&nbsp;int GridMatrix</pre>
<div class="block"><p>
 Use to show the placement of concepts along two axes. Emphasizes the individual components rather than the whole. The first four lines of Level 1 text appear in the quadrants. Unused text does not appear, but remains available if you switch layouts.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.GridMatrix">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="GroupedList">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>GroupedList</h4>
<pre>public static final&nbsp;int GroupedList</pre>
<div class="block"><p>
 Use to show groups and sub-groups of information, or steps and sub-steps in a task, process, or workflow. Level 1 text corresponds to the top level horizontal shapes, and Level 2 text corresponds to vertical sub-steps under each related top level shape. Works well for emphasizing sub-groups or sub-steps, hierarchical information, or multiple lists of information.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.GroupedList">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="HalfCircleOrganizationChart">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>HalfCircleOrganizationChart</h4>
<pre>public static final&nbsp;int HalfCircleOrganizationChart</pre>
<div class="block"><p>
 Use to show hierarchical information or reporting relationships in an organization. The assistant shapes and Org Chart hanging layouts are available with this layout. 
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.HalfCircleOrganizationChart">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="HexagonCluster">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>HexagonCluster</h4>
<pre>public static final&nbsp;int HexagonCluster</pre>
<div class="block"><p>
 Use to show pictures with associated descriptive text. Small hexagons indicate the picture and text pair. Works best with small amounts of text.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.HexagonCluster">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Hierarchy">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Hierarchy</h4>
<pre>public static final&nbsp;int Hierarchy</pre>
<div class="block"><p>
 Use to show hierarchical relationships progressing from top to bottom.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.Hierarchy">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="HierarchyList">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>HierarchyList</h4>
<pre>public static final&nbsp;int HierarchyList</pre>
<div class="block"><p>
 Use to show hierarchical relationships progressing across groups. Can also be used to group or list information.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.HierarchyList">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="HorizontalBulletList">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>HorizontalBulletList</h4>
<pre>public static final&nbsp;int HorizontalBulletList</pre>
<div class="block"><p>
 Use to show non-sequential or grouped lists of information. Works well with large amounts of text. All text has the same level of emphasis, and direction is not implied.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.HorizontalBulletList">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="HorizontalHierarchy">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>HorizontalHierarchy</h4>
<pre>public static final&nbsp;int HorizontalHierarchy</pre>
<div class="block"><p>
 Use to show hierarchical relationships progressing horizontally. Works well for decision trees.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.HorizontalHierarchy">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="HorizontalLabeledHierarchy">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>HorizontalLabeledHierarchy</h4>
<pre>public static final&nbsp;int HorizontalLabeledHierarchy</pre>
<div class="block"><p>
 Use to show hierarchical relationships progressing horizontally and grouped hierarchically. Emphasizes heading or level 1 text. The first line of Level 1 text appears in the shape at the beginning of the hierarchy, and the second and all subsequent lines of Level 1 text appear at the top of the tall rectangles.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.HorizontalLabeledHierarchy">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="HorizontalMultiLevelHierarchy">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>HorizontalMultiLevelHierarchy</h4>
<pre>public static final&nbsp;int HorizontalMultiLevelHierarchy</pre>
<div class="block"><p>
 Use to show large amounts of hierarchical information progressing horizontally. The top of the hierarchy is displayed vertically. This layout supports many levels in the hierarchy.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.HorizontalMultiLevelHierarchy">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="HorizontalOrganizationChart">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>HorizontalOrganizationChart</h4>
<pre>public static final&nbsp;int HorizontalOrganizationChart</pre>
<div class="block"><p>
 Use to show hierarchical information horizontally or reporting relationships in an organization. The assistant shape and the Org Chart hanging layouts are available with this layout.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.HorizontalOrganizationChart">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="HorizontalPictureList">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>HorizontalPictureList</h4>
<pre>public static final&nbsp;int HorizontalPictureList</pre>
<div class="block"><p>
 Use to show non-sequential or grouped information with an emphasis on related pictures. The top shapes are designed to contain pictures.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.HorizontalPictureList">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="IncreasingArrowsProcess">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IncreasingArrowsProcess</h4>
<pre>public static final&nbsp;int IncreasingArrowsProcess</pre>
<div class="block"><p>
 Use to show sequential and overlapping steps in a process. Limited to five Level 1 items. Level 2 can contain large amounts of text.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.IncreasingArrowsProcess">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="IncreasingCircleProcess">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IncreasingCircleProcess</h4>
<pre>public static final&nbsp;int IncreasingCircleProcess</pre>
<div class="block"><p>
 Use to show a series of steps, with the interior of the circle increasing with each step. Limited to seven Level 1 steps but unlimited Level 2 items. Works well with large amounts of Level 2 text.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.IncreasingCircleProcess">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="InvertedPyramid">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>InvertedPyramid</h4>
<pre>public static final&nbsp;int InvertedPyramid</pre>
<div class="block"><p>
 Use to show proportional, interconnected, or hierarchical relationships with the largest component on the top and narrowing down. Level 1 text appears in the pyramid segments and Level 2 text appears in shapes alongside each segment.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.InvertedPyramid">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="LabeledHierarchy">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LabeledHierarchy</h4>
<pre>public static final&nbsp;int LabeledHierarchy</pre>
<div class="block"><p>
 Use to show hierarchical relationships progressing from top to bottom and grouped hierarchically. Emphasizes heading or level 1 text. The first line of Level 1 text appears in the shape at the beginning of the hierarchy, and all subsequent lines of Level 1 text appear to the left of the long rectangles.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.LabeledHierarchy">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="LinearVenn">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LinearVenn</h4>
<pre>public static final&nbsp;int LinearVenn</pre>
<div class="block"><p>
 Use to show overlapping relationships in a sequence. Works best with Level 1 text only.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.LinearVenn">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="LinedList">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LinedList</h4>
<pre>public static final&nbsp;int LinedList</pre>
<div class="block"><p>
 Use to show large amounts of text divided into categories and subcategories. Works well with multiple levels of text. Text at the same level is separated by lines.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.LinedList">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MultidirectionalCycle">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MultidirectionalCycle</h4>
<pre>public static final&nbsp;int MultidirectionalCycle</pre>
<div class="block"><p>
 Use to represent a continuing sequence of stages, tasks, or events that can occur in any direction.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.MultidirectionalCycle">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="NameandTitleOrganizationChart">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NameandTitleOrganizationChart</h4>
<pre>public static final&nbsp;int NameandTitleOrganizationChart</pre>
<div class="block"><p>
 Use to show hierarchical information or reporting relationships in an organization. To enter text in the title box, type directly in the smaller rectangular shape. The assistant shape and Org Chart hanging layouts are available with this layout.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.NameandTitleOrganizationChart">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="NestedTarget">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NestedTarget</h4>
<pre>public static final&nbsp;int NestedTarget</pre>
<div class="block"><p>
 Use to show containment relationships. Each of the first three lines of Level 1 text correspond to the upper left text in the shapes, and Level 2 text corresponds to the smaller shapes. Works best with minimal Level 2 lines of text. Unused text does not appear, but remains available if you switch layouts.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.NestedTarget">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="NondirectionalCycle">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NondirectionalCycle</h4>
<pre>public static final&nbsp;int NondirectionalCycle</pre>
<div class="block"><p>
 Use to represent a continuing sequence of stages, tasks, or events in a circular flow. Each shape has the same level of importance. Works well when direction does not need to be indicated.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.NondirectionalCycle">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="OpposingArrows">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OpposingArrows</h4>
<pre>public static final&nbsp;int OpposingArrows</pre>
<div class="block"><p>
 Use to show two opposing ideas, or ideas that diverge from a central point. Each of the first two lines of Level 1 text corresponds to an arrow. Unused text does not appear, but remains available if you switch layouts.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.OpposingArrows">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="OpposingIdeas">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OpposingIdeas</h4>
<pre>public static final&nbsp;int OpposingIdeas</pre>
<div class="block"><p>
 Use to show two opposing or contrasting ideas. Can have one or two Level 1 items. Each Level 1 text can contain multiple sub-levels. Works well with large amounts of text.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.OpposingIdeas">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="OrganizationChart">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OrganizationChart</h4>
<pre>public static final&nbsp;int OrganizationChart</pre>
<div class="block"><p>
 Use to show hierarchical information or reporting relationships in an organization. The assistant shape and the Org Chart hanging layouts are available with this layout.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.OrganizationChart">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="PhasedProcess">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PhasedProcess</h4>
<pre>public static final&nbsp;int PhasedProcess</pre>
<div class="block"><p>
 Use to show three phases of a process. Limited to three Level 1 items. The first two Level 1 items can each contain four Level 2 items, and the third Level 1 item can contain an unlimited number of Level 2 items.  Works best with small amounts of text.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.PhasedProcess">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="PictureAccentBlocks">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PictureAccentBlocks</h4>
<pre>public static final&nbsp;int PictureAccentBlocks</pre>
<div class="block"><p>
 Use to show a group of pictures in blocks starting from the corner. The corresponding text displays vertically. Works well as an accent on title or sub-title slides or for section breaks of a document.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.PictureAccentBlocks">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="PictureAccentList">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PictureAccentList</h4>
<pre>public static final&nbsp;int PictureAccentList</pre>
<div class="block"><p>
 Use to show grouped or related information. The small shapes on the upper corners are designed to contain pictures. Emphasizes Level 2 text over Level 1 text, and is a good choice for large amounts of Level 2 text.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.PictureAccentList">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="PictureAccentProcess">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PictureAccentProcess</h4>
<pre>public static final&nbsp;int PictureAccentProcess</pre>
<div class="block"><p>
 Use to show sequential steps in a task, process, or workflow. The rectangular shapes in the background are designed to contain pictures.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.PictureAccentProcess">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="PictureCaptionList">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PictureCaptionList</h4>
<pre>public static final&nbsp;int PictureCaptionList</pre>
<div class="block"><p>
 Use to show non-sequential or grouped blocks of information. The top shapes are designed to contain pictures and pictures are emphasized over text. Works well for pictures with short text captions.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.PictureCaptionList">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="PictureGrid">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PictureGrid</h4>
<pre>public static final&nbsp;int PictureGrid</pre>
<div class="block"><p>
 Use to show pictures laid out on a square grid. Best with a small amount of Level 1 text, which appears above the picture.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.PictureGrid">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="PictureLineup">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PictureLineup</h4>
<pre>public static final&nbsp;int PictureLineup</pre>
<div class="block"><p>
 Use to show a series of pictures side by side. Level 1 text covers the top of the picture. Level 2 text appears below the picture.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.PictureLineup">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="PictureStrips">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PictureStrips</h4>
<pre>public static final&nbsp;int PictureStrips</pre>
<div class="block"><p>
 Use to show a series of pictures from top to bottom with Level 1 text beside each.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.PictureStrips">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="PieProcess">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PieProcess</h4>
<pre>public static final&nbsp;int PieProcess</pre>
<div class="block"><p>
 Use to show steps in a process with each pie slice increasing in size up to seven shapes.  Level 1 text displays vertically.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.PieProcess">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="PlusandMinus">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PlusandMinus</h4>
<pre>public static final&nbsp;int PlusandMinus</pre>
<div class="block"><p>
 Use to show the pros and cons of  two ideas. Each Level 1 text can contain multiple sub-levels. Works well with large amounts of text. Limited to two Level 1 items.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.PlusandMinus">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="ProcessArrows">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ProcessArrows</h4>
<pre>public static final&nbsp;int ProcessArrows</pre>
<div class="block"><p>
 Use to show information illustrating a process or workflow. Level 1 text appears in the circular shapes and Level 2 text appears in the arrow shapes. Works best for minimal text and to emphasize movement or direction.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.ProcessArrows">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="ProcessList">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ProcessList</h4>
<pre>public static final&nbsp;int ProcessList</pre>
<div class="block"><p>
 Use to show multiple groups of information or steps and sub-steps in a task, process, or workflow. Level 1 text corresponds to the top horizontal shapes, and Level 2 text corresponds to vertical sub-steps under each related top level shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.ProcessList">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="PyramidList">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PyramidList</h4>
<pre>public static final&nbsp;int PyramidList</pre>
<div class="block"><p>
 Use to show proportional, interconnected, or hierarchical relationships. Text appears in the rectangular shapes on top of the pyramid background.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.PyramidList">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="RadialCluster">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RadialCluster</h4>
<pre>public static final&nbsp;int RadialCluster</pre>
<div class="block"><p>
 Use to show data that relates to a central idea or theme. The top Level 1 text appears in the center. Level 2 text appears in surrounding shapes. Can contain up to seven Level 2 shapes. Unused text does not appear, but remains available if you switch layouts. Works best with small amounts of text.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.RadialCluster">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="RadialCycle">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RadialCycle</h4>
<pre>public static final&nbsp;int RadialCycle</pre>
<div class="block"><p>
 Use to show the relationship to a central idea. Emphasizes both information in the center circle and how information in the outer ring of circles contributes to the central idea. The first Level 1 line of text corresponds to the central circle, and its Level 2 text corresponds to the outer ring of circles. Unused text does not appear, but remains available if you switch layouts.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.RadialCycle">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="RadialList">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RadialList</h4>
<pre>public static final&nbsp;int RadialList</pre>
<div class="block"><p>
 Use to show relationships to a central idea in a cycle. The center shape can contain a picture. Level 1 text appears in the smaller circles and any related Level 2 text appears to the side of the smaller circles.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.RadialList">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="RadialVenn">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RadialVenn</h4>
<pre>public static final&nbsp;int RadialVenn</pre>
<div class="block"><p>
 Use to show both overlapping relationships and the relationship to a central idea in a cycle. The first line of Level 1 text corresponds to the central shape and the lines of Level 2 text correspond to the surrounding circular shapes. Unused text does not appear, but remains available if you switch layouts.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.RadialVenn">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="RandomToResultProcess">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RandomToResultProcess</h4>
<pre>public static final&nbsp;int RandomToResultProcess</pre>
<div class="block"><p>
 Use to show, through a series of steps, how several chaotic  ideas can result in a unified goal or idea. Supports multiple items of Level 1 text, but the first and last Level 1 corresponding shapes are fixed. Works best with small amounts of Level 1 text and medium amounts of Level 2 text.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.RandomToResultProcess">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="RepeatingBendingProcess">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RepeatingBendingProcess</h4>
<pre>public static final&nbsp;int RepeatingBendingProcess</pre>
<div class="block"><p>
 Use to show a progression or sequential steps in a task, process, or workflow. Maximizes both horizontal and vertical display space for shapes.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.RepeatingBendingProcess">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="ReverseList">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ReverseList</h4>
<pre>public static final&nbsp;int ReverseList</pre>
<div class="block"><p>
 Use to change between two items. Only the first two items of text display, and each item can contain a large amount of text. Works well to show a change between two items or shift in order.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.ReverseList">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="SegmentedCycle">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SegmentedCycle</h4>
<pre>public static final&nbsp;int SegmentedCycle</pre>
<div class="block"><p>
 Use to show a progression or a sequence of stages, tasks, or events in a circular flow. Emphasizes the interconnected pieces. Each of the first seven lines of Level 1 text corresponds to a wedge or pie shape. Unused text does not appear, but remains available if you switch layouts.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.SegmentedCycle">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="SegmentedProcess">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SegmentedProcess</h4>
<pre>public static final&nbsp;int SegmentedProcess</pre>
<div class="block"><p>
 Use to show a progression or sequential steps in a task, process, or workflow. Emphasizes Level 2 text, since each line appears in a separate shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.SegmentedProcess">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="SegmentedPyramid">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SegmentedPyramid</h4>
<pre>public static final&nbsp;int SegmentedPyramid</pre>
<div class="block"><p>
 Use to show containment, proportional, or interconnected relationships. The first nine lines of Level 1 text appear in the triangular shapes. Unused text does not appear, but remains available if you switch layouts. Works best with Level 1 text only.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.SegmentedPyramid">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="SnapshotPictureList">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SnapshotPictureList</h4>
<pre>public static final&nbsp;int SnapshotPictureList</pre>
<div class="block"><p>
 Use to show pictures with explanatory text. Level 2 text can display lists of information. Works well with a large amount of  text.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.SnapshotPictureList">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="SpiralPicture">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SpiralPicture</h4>
<pre>public static final&nbsp;int SpiralPicture</pre>
<div class="block"><p>
 Use to show a series of up to five pictures with corresponding Level 1 captions that spiral in to the center. 
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.SpiralPicture">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="SquareAccentList">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SquareAccentList</h4>
<pre>public static final&nbsp;int SquareAccentList</pre>
<div class="block"><p>
 Use to show lists of information divided into categories. Level 2 text appears beside a small square shape. Works well with large amounts of Level 2 text. 
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.SquareAccentList">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="StackedList">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>StackedList</h4>
<pre>public static final&nbsp;int StackedList</pre>
<div class="block"><p>
 Use to show groups of information or steps in a task, process, or workflow. Circular shapes contain Level 1 text, and the corresponding rectangles contain Level 2 text. Works well for numerous details and minimal Level 1 text.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.StackedList">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="StackedVenn">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>StackedVenn</h4>
<pre>public static final&nbsp;int StackedVenn</pre>
<div class="block"><p>
 Use to show overlapping relationships. A good choice for emphasizing growth or gradation. Works best with Level 1 text only. The first seven lines of Level 1 text correspond to a circular shape. Unused text does not appear, but remains available if you switch layouts.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.StackedVenn">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="StaggeredProcess">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>StaggeredProcess</h4>
<pre>public static final&nbsp;int StaggeredProcess</pre>
<div class="block"><p>
 Use to show a downward progression through stages. Each of the first five lines of Level 1 text corresponds with a rectangle. Unused text does not appear, but remains available if you switch layouts.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.StaggeredProcess">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="StepDownProcess">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>StepDownProcess</h4>
<pre>public static final&nbsp;int StepDownProcess</pre>
<div class="block"><p>
 Use to show a descending process with multiple steps and sub-steps. Works best with small amounts of text.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.StepDownProcess">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="StepUpProcess">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>StepUpProcess</h4>
<pre>public static final&nbsp;int StepUpProcess</pre>
<div class="block"><p>
 Use to show an ascending series of steps or lists of information.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.StepUpProcess">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="SubStepProcess">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SubStepProcess</h4>
<pre>public static final&nbsp;int SubStepProcess</pre>
<div class="block"><p>
 Use to show a multi-step process with sub-steps between each instance of Level 1 text. Works best with small amounts of text and is limited to seven Level 1 steps. Each Level 1 step can have unlimited sub-steps.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.SubStepProcess">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="TableHierarchy">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TableHierarchy</h4>
<pre>public static final&nbsp;int TableHierarchy</pre>
<div class="block"><p>
 Use to show groups of information built from top to bottom, and the hierarchies within each group. This layout does not contain connecting lines.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.TableHierarchy">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="TableList">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TableList</h4>
<pre>public static final&nbsp;int TableList</pre>
<div class="block"><p>
 Use to show grouped or related information of equal value. The first Level 1 line of text corresponds to the top shape and its Level 2 text is used for the subsequent lists.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.TableList">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="TargetList">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TargetList</h4>
<pre>public static final&nbsp;int TargetList</pre>
<div class="block"><p>
 Use to show interrelated or overlapping information. Each of the first seven lines of Level 1 text appears in the rectangular shape. Unused text does not appear, but remains available if you switch layouts. Works well with both Level 1 and Level 2 text.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.TargetList">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="TextCycle">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TextCycle</h4>
<pre>public static final&nbsp;int TextCycle</pre>
<div class="block"><p>
 Use to represent a continuing sequence of stages, tasks, or events in a circular flow. Emphasizes the arrows or flow rather than the stages or steps. Works best with Level 1 text only.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.TextCycle">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="TitlePictureLineup">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TitlePictureLineup</h4>
<pre>public static final&nbsp;int TitlePictureLineup</pre>
<div class="block"><p>
 Use to show a series of pictures that each have their own title and description. Level 1 text appears in the box above the picture. Level 2 text appears below the picture.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.TitlePictureLineup">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="TitledMatrix">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TitledMatrix</h4>
<pre>public static final&nbsp;int TitledMatrix</pre>
<div class="block"><p>
 Use to show the relationships of four quadrants to a whole. The first line of Level 1 text corresponds to the central shape, and the first four lines of Level 2 text appear in the quadrants. Unused text does not appear, but remains available if you switch layouts.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.TitledMatrix">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="TitledPictureAccentList">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TitledPictureAccentList</h4>
<pre>public static final&nbsp;int TitledPictureAccentList</pre>
<div class="block"><p>
 Use to show lists of information with an accent picture for each Level 2 text. Level 1 text displays in a separate box at the top of the list.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.TitledPictureAccentList">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="TitledPictureBlocks">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TitledPictureBlocks</h4>
<pre>public static final&nbsp;int TitledPictureBlocks</pre>
<div class="block"><p>
 Use to show a series of pictures. Level 1 text appears above each picture. Level 2 text appears to the side and slightly overlapping each picture.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.TitledPictureBlocks">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="TrapezoidList">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TrapezoidList</h4>
<pre>public static final&nbsp;int TrapezoidList</pre>
<div class="block"><p>
 Use to show grouped or related information of equal value. Works well with large amounts of text.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.TrapezoidList">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="UpwardArrow">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UpwardArrow</h4>
<pre>public static final&nbsp;int UpwardArrow</pre>
<div class="block"><p>
 Use to show a progression or steps that trend upward in a task, process, or workflow. Each of the first five lines of Level 1 text corresponds to a point on the arrow. Works best with minimal text. Unused text does not appear, but remains available if you switch layouts.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.UpwardArrow">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="VerticalAccentList">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>VerticalAccentList</h4>
<pre>public static final&nbsp;int VerticalAccentList</pre>
<div class="block"><p>
 Use to show lists of information. Level 2 text appears in rectangular shapes over vertical chevrons. Emphasizes Level 2 text over Level 1 text, and is a good choice for medium amounts of Level 2 text.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.VerticalAccentList">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="VerticalArrowList">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>VerticalArrowList</h4>
<pre>public static final&nbsp;int VerticalArrowList</pre>
<div class="block"><p>
 Use to show a progression or sequential steps in a task, process, or workflow that move toward a common goal. Works well for bulleted lists of information.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.VerticalArrowList">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="VerticalBendingProcess">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>VerticalBendingProcess</h4>
<pre>public static final&nbsp;int VerticalBendingProcess</pre>
<div class="block"><p>
 Use to show a progression or sequential steps in a task, process, or workflow. Maximizes both horizontal and vertical display space for shapes. Places more emphasis on the interrelationships among the shapes than on direction or movement.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.VerticalBendingProcess">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="VerticalBlockList">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>VerticalBlockList</h4>
<pre>public static final&nbsp;int VerticalBlockList</pre>
<div class="block"><p>
 Use to show groups of information or steps in a task, process, or workflow. Works well with large amounts of Level 2 text. A good choice for text with a main point and multiple sub-points.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.VerticalBlockList">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="VerticalBoxList">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>VerticalBoxList</h4>
<pre>public static final&nbsp;int VerticalBoxList</pre>
<div class="block"><p>
 Use to show several groups of information, especially groups with large amounts of Level 2 text. A good choice for bulleted lists of information.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.VerticalBoxList">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="VerticalBulletList">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>VerticalBulletList</h4>
<pre>public static final&nbsp;int VerticalBulletList</pre>
<div class="block"><p>
 Use to show non-sequential or grouped blocks of information. Works well for lists with long headings or top level information.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.VerticalBulletList">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="VerticalChevronList">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>VerticalChevronList</h4>
<pre>public static final&nbsp;int VerticalChevronList</pre>
<div class="block"><p>
 Use to show a progression or sequential steps in a task, process, or workflow, or to emphasize movement or direction. Emphasizes Level 2 text over Level 1 text, and is a good choice for large amounts of Level 2 text.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.VerticalChevronList">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="VerticalCircleList">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>VerticalCircleList</h4>
<pre>public static final&nbsp;int VerticalCircleList</pre>
<div class="block"><p>
 Use to show sequential or grouped data. Works best for Level 1 text, which displays next to a large circular shape. Lower levels of text are separated with smaller circular shapes.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.VerticalCircleList">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="VerticalCurvedList">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>VerticalCurvedList</h4>
<pre>public static final&nbsp;int VerticalCurvedList</pre>
<div class="block"><p>
 Use to show a curved list of information. To add pictures to the accent circle shapes, apply a picture fill.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.VerticalCurvedList">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="VerticalEquation">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>VerticalEquation</h4>
<pre>public static final&nbsp;int VerticalEquation</pre>
<div class="block"><p>
 Use to show sequential steps or tasks that depict a plan or result. The last Level 1 line of text appears after the arrow. Works best with Level 1 text only.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.VerticalEquation">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="VerticalPictureAccentList">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>VerticalPictureAccentList</h4>
<pre>public static final&nbsp;int VerticalPictureAccentList</pre>
<div class="block"><p>
 Use to show non-sequential or grouped blocks of information. The small circles are designed to contain pictures.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.VerticalPictureAccentList">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="VerticalPictureList">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>VerticalPictureList</h4>
<pre>public static final&nbsp;int VerticalPictureList</pre>
<div class="block"><p>
 Use to show non-sequential or grouped blocks of information. The small shapes on the left are designed to contain pictures.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.VerticalPictureList">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="VerticalProcess">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>VerticalProcess</h4>
<pre>public static final&nbsp;int VerticalProcess</pre>
<div class="block"><p>
 Use to show a progression or sequential steps in a task, process, or workflow from top to bottom. Works best with Level 1 text, since the vertical space is limited.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.VerticalProcess">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Custom">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Custom</h4>
<pre>public static final&nbsp;int Custom</pre>
<div class="block"><p>
 Represents a SmartArt diagram with custom layout template
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.Custom">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="PictureOrganizationChart">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>PictureOrganizationChart</h4>
<pre>public static final&nbsp;int PictureOrganizationChart</pre>
<div class="block"><p>
 Use to show hierarchical information or reporting relationships in an organization, with corresponding pictures. The assistant shape and Org Chart hanging layouts are available with this layout.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtLayoutType.PictureOrganizationChart">Constant Field Values</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/SmartArtColorType.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/SmartArtNode.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/SmartArtLayoutType.html" target="_top">Frames</a></li>
<li><a href="SmartArtLayoutType.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.classes.inherited.from.class.com.aspose.ms.System.Enum">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#methods.inherited.from.class.com.aspose.ms.System.Enum">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li>Method</li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><i>Copyright &copy; 2004-2025 Aspose Pty Ltd. All Rights Reserved.</i></small></p>
</body>
</html>
