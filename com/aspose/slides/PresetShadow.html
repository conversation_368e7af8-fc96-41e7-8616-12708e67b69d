<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_421) on Fri Jul 11 05:32:43 CDT 2025 -->
<title>PresetShadow (Aspose.Slides for Java (25.7) API Documentation)</title>
<meta name="date" content="2025-07-11">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="PresetShadow (Aspose.Slides for Java (25.7) API Documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/PresetColor.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/PresetShadowType.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/PresetShadow.html" target="_top">Frames</a></li>
<li><a href="PresetShadow.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.aspose.slides</div>
<h2 title="Class PresetShadow" class="title">Class PresetShadow</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.aspose.slides.PresetShadow</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../com/aspose/slides/IAccessiblePVIObject.html" title="interface in com.aspose.slides">IAccessiblePVIObject</a>&lt;<a href="../../../com/aspose/slides/IPresetShadowEffectiveData.html" title="interface in com.aspose.slides">IPresetShadowEffectiveData</a>&gt;, <a href="../../../com/aspose/slides/IImageTransformOperation.html" title="interface in com.aspose.slides">IImageTransformOperation</a>, <a href="../../../com/aspose/slides/IPresetShadow.html" title="interface in com.aspose.slides">IPresetShadow</a>, java.lang.Cloneable</dd>
</dl>
<hr>
<br>
<pre>public final class <span class="typeNameLabel">PresetShadow</span>
extends java.lang.Object
implements <a href="../../../com/aspose/slides/IPresetShadow.html" title="interface in com.aspose.slides">IPresetShadow</a>, java.lang.Cloneable</pre>
<div class="block"><p>
 Represents a Preset Shadow effect.
 </p></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetShadow.html#equals-java.lang.Object-">equals</a></span>(java.lang.Object&nbsp;obj)</code>
<div class="block">
 Determines whether the specified <a href="../../../com/aspose/slides/PresetShadow.html" title="class in com.aspose.slides"><code>PresetShadow</code></a> is equal to the current <a href="../../../com/aspose/slides/PresetShadow.html" title="class in com.aspose.slides"><code>PresetShadow</code></a>.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetShadow.html#getDirection--">getDirection</a></span>()</code>
<div class="block">
 Direction of shadow.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetShadow.html#getDistance--">getDistance</a></span>()</code>
<div class="block">
 Distance of shadow.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IPresetShadowEffectiveData.html" title="interface in com.aspose.slides">IPresetShadowEffectiveData</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetShadow.html#getEffective--">getEffective</a></span>()</code>
<div class="block">
 Gets effective Preset Shadow effect data with the inheritance applied.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>com.aspose.slides.IDOMObject</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetShadow.html#getParent_Immediate--">getParent_Immediate</a></span>()</code>
<div class="block">
 Returns Parent_Immediate object.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IPresentationComponent.html" title="interface in com.aspose.slides">IPresentationComponent</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetShadow.html#getParent_IPresentationComponent--">getParent_IPresentationComponent</a></span>()</code>
<div class="block">
 Returns parent IPresentationComponent.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetShadow.html#getPreset--">getPreset</a></span>()</code>
<div class="block">
 Preset.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IColorFormat.html" title="interface in com.aspose.slides">IColorFormat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetShadow.html#getShadowColor--">getShadowColor</a></span>()</code>
<div class="block">
 Color of shadow.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetShadow.html#getVersion--">getVersion</a></span>()</code>
<div class="block">
 Version.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetShadow.html#hashCode--">hashCode</a></span>()</code>
<div class="block">
 Serves as a hash function for a particular type.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetShadow.html#setDirection-float-">setDirection</a></span>(float&nbsp;value)</code>
<div class="block">
 Direction of shadow.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetShadow.html#setDistance-double-">setDistance</a></span>(double&nbsp;value)</code>
<div class="block">
 Distance of shadow.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetShadow.html#setPreset-int-">setPreset</a></span>(int&nbsp;value)</code>
<div class="block">
 Preset.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>getClass, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getDirection--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDirection</h4>
<pre>public final&nbsp;float&nbsp;getDirection()</pre>
<div class="block"><p>
 Direction of shadow.
 Read/write <code>float</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPresetShadow.html#getDirection--">getDirection</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPresetShadow.html" title="interface in com.aspose.slides">IPresetShadow</a></code></dd>
</dl>
</li>
</ul>
<a name="setDirection-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDirection</h4>
<pre>public final&nbsp;void&nbsp;setDirection(float&nbsp;value)</pre>
<div class="block"><p>
 Direction of shadow.
 Read/write <code>float</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPresetShadow.html#setDirection-float-">setDirection</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPresetShadow.html" title="interface in com.aspose.slides">IPresetShadow</a></code></dd>
</dl>
</li>
</ul>
<a name="getDistance--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDistance</h4>
<pre>public final&nbsp;double&nbsp;getDistance()</pre>
<div class="block"><p>
 Distance of shadow.
 Read/write <code>double</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPresetShadow.html#getDistance--">getDistance</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPresetShadow.html" title="interface in com.aspose.slides">IPresetShadow</a></code></dd>
</dl>
</li>
</ul>
<a name="setDistance-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDistance</h4>
<pre>public final&nbsp;void&nbsp;setDistance(double&nbsp;value)</pre>
<div class="block"><p>
 Distance of shadow.
 Read/write <code>double</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPresetShadow.html#setDistance-double-">setDistance</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPresetShadow.html" title="interface in com.aspose.slides">IPresetShadow</a></code></dd>
</dl>
</li>
</ul>
<a name="getShadowColor--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getShadowColor</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IColorFormat.html" title="interface in com.aspose.slides">IColorFormat</a>&nbsp;getShadowColor()</pre>
<div class="block"><p>
 Color of shadow.
 Read-only <a href="../../../com/aspose/slides/IColorFormat.html" title="interface in com.aspose.slides"><code>IColorFormat</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPresetShadow.html#getShadowColor--">getShadowColor</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPresetShadow.html" title="interface in com.aspose.slides">IPresetShadow</a></code></dd>
</dl>
</li>
</ul>
<a name="getPreset--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPreset</h4>
<pre>public final&nbsp;int&nbsp;getPreset()</pre>
<div class="block"><p>
 Preset.
 Read/write <a href="../../../com/aspose/slides/PresetShadowType.html" title="class in com.aspose.slides"><code>PresetShadowType</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPresetShadow.html#getPreset--">getPreset</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPresetShadow.html" title="interface in com.aspose.slides">IPresetShadow</a></code></dd>
</dl>
</li>
</ul>
<a name="setPreset-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPreset</h4>
<pre>public final&nbsp;void&nbsp;setPreset(int&nbsp;value)</pre>
<div class="block"><p>
 Preset.
 Read/write <a href="../../../com/aspose/slides/PresetShadowType.html" title="class in com.aspose.slides"><code>PresetShadowType</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IPresetShadow.html#setPreset-int-">setPreset</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IPresetShadow.html" title="interface in com.aspose.slides">IPresetShadow</a></code></dd>
</dl>
</li>
</ul>
<a name="getEffective--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEffective</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IPresetShadowEffectiveData.html" title="interface in com.aspose.slides">IPresetShadowEffectiveData</a>&nbsp;getEffective()</pre>
<div class="block"><p>
 Gets effective Preset Shadow effect data with the inheritance applied.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IAccessiblePVIObject.html#getEffective--">getEffective</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IAccessiblePVIObject.html" title="interface in com.aspose.slides">IAccessiblePVIObject</a>&lt;<a href="../../../com/aspose/slides/IPresetShadowEffectiveData.html" title="interface in com.aspose.slides">IPresetShadowEffectiveData</a>&gt;</code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>A <a href="../../../com/aspose/slides/IPresetShadowEffectiveData.html" title="interface in com.aspose.slides"><code>IPresetShadowEffectiveData</code></a>.</dd>
</dl>
</li>
</ul>
<a name="getParent_Immediate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getParent_Immediate</h4>
<pre>public final&nbsp;com.aspose.slides.IDOMObject&nbsp;getParent_Immediate()</pre>
<div class="block"><p>
 Returns Parent_Immediate object.
 Read-only <code>IDOMObject</code>.
 </p></div>
</li>
</ul>
<a name="getVersion--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVersion</h4>
<pre>public final&nbsp;long&nbsp;getVersion()</pre>
<div class="block"><p>
 Version.
 Read-only <code>long</code>.
 </p></div>
</li>
</ul>
<a name="getParent_IPresentationComponent--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getParent_IPresentationComponent</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IPresentationComponent.html" title="interface in com.aspose.slides">IPresentationComponent</a>&nbsp;getParent_IPresentationComponent()</pre>
<div class="block"><p>
 Returns parent IPresentationComponent.
 Read-only <a href="../../../com/aspose/slides/IPresentationComponent.html" title="interface in com.aspose.slides"><code>IPresentationComponent</code></a>.
 </p></div>
</li>
</ul>
<a name="equals-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>equals</h4>
<pre>public&nbsp;boolean&nbsp;equals(java.lang.Object&nbsp;obj)</pre>
<div class="block"><p>
 Determines whether the specified <a href="../../../com/aspose/slides/PresetShadow.html" title="class in com.aspose.slides"><code>PresetShadow</code></a> is equal to the current <a href="../../../com/aspose/slides/PresetShadow.html" title="class in com.aspose.slides"><code>PresetShadow</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code>equals</code>&nbsp;in class&nbsp;<code>java.lang.Object</code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>obj</code> - The <a href="../../../com/aspose/slides/PresetShadow.html" title="class in com.aspose.slides"><code>PresetShadow</code></a> to compare.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true if objects are equal; otherwise, false.</dd>
</dl>
</li>
</ul>
<a name="hashCode--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>hashCode</h4>
<pre>public&nbsp;int&nbsp;hashCode()</pre>
<div class="block"><p>
 Serves as a hash function for a particular type.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code>hashCode</code>&nbsp;in class&nbsp;<code>java.lang.Object</code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>A hash code for the current object.</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/PresetColor.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/PresetShadowType.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/PresetShadow.html" target="_top">Frames</a></li>
<li><a href="PresetShadow.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><i>Copyright &copy; 2004-2025 Aspose Pty Ltd. All Rights Reserved.</i></small></p>
</body>
</html>
