<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_421) on Fri Jul 11 05:32:44 CDT 2025 -->
<title>SlideUtil (Aspose.Slides for Java (25.7) API Documentation)</title>
<meta name="date" content="2025-07-11">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="SlideUtil (Aspose.Slides for Java (25.7) API Documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":9,"i2":9,"i3":9,"i4":9,"i5":9,"i6":9,"i7":9,"i8":9,"i9":9};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/SlideThemeManager.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/SmartArt.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/SlideUtil.html" target="_top">Frames</a></li>
<li><a href="SlideUtil.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.aspose.slides</div>
<h2 title="Class SlideUtil" class="title">Class SlideUtil</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.aspose.slides.SlideUtil</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">SlideUtil</span>
extends java.lang.Object</pre>
<div class="block"><p>
 Offer methods which help to search shapes and text in a presentation.
 </p></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideUtil.html#SlideUtil--">SlideUtil</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideUtil.html#alignShapes-int-boolean-com.aspose.slides.IBaseSlide-">alignShapes</a></span>(int&nbsp;alignmentType,
           boolean&nbsp;alignToSlide,
           <a href="../../../com/aspose/slides/IBaseSlide.html" title="interface in com.aspose.slides">IBaseSlide</a>&nbsp;slide)</code>
<div class="block">
 Changes the placement of all shapes on the slide.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideUtil.html#alignShapes-int-boolean-com.aspose.slides.IBaseSlide-int:A-">alignShapes</a></span>(int&nbsp;alignmentType,
           boolean&nbsp;alignToSlide,
           <a href="../../../com/aspose/slides/IBaseSlide.html" title="interface in com.aspose.slides">IBaseSlide</a>&nbsp;slide,
           int[]&nbsp;shapeIndexes)</code>
<div class="block">
  Changes the placement of selected shapes on the slide.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideUtil.html#alignShapes-int-boolean-com.aspose.slides.IGroupShape-">alignShapes</a></span>(int&nbsp;alignmentType,
           boolean&nbsp;alignToSlide,
           <a href="../../../com/aspose/slides/IGroupShape.html" title="interface in com.aspose.slides">IGroupShape</a>&nbsp;groupShape)</code>
<div class="block">
 Changes the placement of all shapes within group shape.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideUtil.html#alignShapes-int-boolean-com.aspose.slides.IGroupShape-int:A-">alignShapes</a></span>(int&nbsp;alignmentType,
           boolean&nbsp;alignToSlide,
           <a href="../../../com/aspose/slides/IGroupShape.html" title="interface in com.aspose.slides">IGroupShape</a>&nbsp;groupShape,
           int[]&nbsp;shapeIndexes)</code>
<div class="block">
 Changes the placement of selected shapes within group shape.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideUtil.html#findAndReplaceText-com.aspose.slides.IPresentation-boolean-java.lang.String-java.lang.String-">findAndReplaceText</a></span>(<a href="../../../com/aspose/slides/IPresentation.html" title="interface in com.aspose.slides">IPresentation</a>&nbsp;presentation,
                  boolean&nbsp;withMasters,
                  java.lang.String&nbsp;find,
                  java.lang.String&nbsp;replace)</code>
<div class="block">
 Finds and replaces text in presentation with given format</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideUtil.html#findAndReplaceText-com.aspose.slides.IPresentation-boolean-java.lang.String-java.lang.String-com.aspose.slides.PortionFormat-">findAndReplaceText</a></span>(<a href="../../../com/aspose/slides/IPresentation.html" title="interface in com.aspose.slides">IPresentation</a>&nbsp;presentation,
                  boolean&nbsp;withMasters,
                  java.lang.String&nbsp;find,
                  java.lang.String&nbsp;replace,
                  <a href="../../../com/aspose/slides/PortionFormat.html" title="class in com.aspose.slides">PortionFormat</a>&nbsp;format)</code>
<div class="block">
 Finds and replaces text in presentation with given format</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>static <a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideUtil.html#findShape-com.aspose.slides.IBaseSlide-java.lang.String-">findShape</a></span>(<a href="../../../com/aspose/slides/IBaseSlide.html" title="interface in com.aspose.slides">IBaseSlide</a>&nbsp;slide,
         java.lang.String&nbsp;altText)</code>
<div class="block">
 Find shape by alternative text on a slide in a PPTX presentation.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>static <a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideUtil.html#findShape-com.aspose.slides.IPresentation-java.lang.String-">findShape</a></span>(<a href="../../../com/aspose/slides/IPresentation.html" title="interface in com.aspose.slides">IPresentation</a>&nbsp;pres,
         java.lang.String&nbsp;altText)</code>
<div class="block">
 Find shape by alternative text in a PPTX presentation.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>static <a href="../../../com/aspose/slides/ITextFrame.html" title="interface in com.aspose.slides">ITextFrame</a>[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideUtil.html#getAllTextBoxes-com.aspose.slides.IBaseSlide-">getAllTextBoxes</a></span>(<a href="../../../com/aspose/slides/IBaseSlide.html" title="interface in com.aspose.slides">IBaseSlide</a>&nbsp;slide)</code>
<div class="block">
 Returns all text frames on a slide in a PPTX presentation.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>static <a href="../../../com/aspose/slides/ITextFrame.html" title="interface in com.aspose.slides">ITextFrame</a>[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideUtil.html#getAllTextFrames-com.aspose.slides.IPresentation-boolean-">getAllTextFrames</a></span>(<a href="../../../com/aspose/slides/IPresentation.html" title="interface in com.aspose.slides">IPresentation</a>&nbsp;pres,
                boolean&nbsp;withMasters)</code>
<div class="block">
 Returns all text frames in a PPTX presentation.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="SlideUtil--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>SlideUtil</h4>
<pre>public&nbsp;SlideUtil()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="findShape-com.aspose.slides.IPresentation-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>findShape</h4>
<pre>public static&nbsp;<a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a>&nbsp;findShape(<a href="../../../com/aspose/slides/IPresentation.html" title="interface in com.aspose.slides">IPresentation</a>&nbsp;pres,
                               java.lang.String&nbsp;altText)</pre>
<div class="block"><p>
 Find shape by alternative text in a PPTX presentation.
 </p></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>pres</code> - Scanned presentation.</dd>
<dd><code>altText</code> - Alternative text of a shape.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Shape or null.</dd>
</dl>
</li>
</ul>
<a name="findShape-com.aspose.slides.IBaseSlide-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>findShape</h4>
<pre>public static&nbsp;<a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a>&nbsp;findShape(<a href="../../../com/aspose/slides/IBaseSlide.html" title="interface in com.aspose.slides">IBaseSlide</a>&nbsp;slide,
                               java.lang.String&nbsp;altText)</pre>
<div class="block"><p>
 Find shape by alternative text on a slide in a PPTX presentation.
 </p></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>slide</code> - Scanned slide.</dd>
<dd><code>altText</code> - Alternative text of a shape.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Shape or null.</dd>
</dl>
</li>
</ul>
<a name="alignShapes-int-boolean-com.aspose.slides.IBaseSlide-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>alignShapes</h4>
<pre>public static&nbsp;void&nbsp;alignShapes(int&nbsp;alignmentType,
                               boolean&nbsp;alignToSlide,
                               <a href="../../../com/aspose/slides/IBaseSlide.html" title="interface in com.aspose.slides">IBaseSlide</a>&nbsp;slide)</pre>
<div class="block"><p>
 Changes the placement of all shapes on the slide. Aligns shapes to the margins or the edge of the slide
 or align them relative to each other.
 </p><p><hr><blockquote><pre>Example:
 <pre>
 Presentation pres = new Presentation("pres.pptx");
 try {
     SlideUtil.alignShapes(ShapesAlignmentType.AlignBottom, true, pres.getSlides().get_Item(0));
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>alignmentType</code> - Determines which type of alignment will be applied.</dd>
<dd><code>alignToSlide</code> - If true, shapes will be aligned relative to the slide edges.</dd>
<dd><code>slide</code> - Parent slide.</dd>
</dl>
</li>
</ul>
<a name="alignShapes-int-boolean-com.aspose.slides.IBaseSlide-int:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>alignShapes</h4>
<pre>public static&nbsp;void&nbsp;alignShapes(int&nbsp;alignmentType,
                               boolean&nbsp;alignToSlide,
                               <a href="../../../com/aspose/slides/IBaseSlide.html" title="interface in com.aspose.slides">IBaseSlide</a>&nbsp;slide,
                               int[]&nbsp;shapeIndexes)</pre>
<div class="block"><p>
  Changes the placement of selected shapes on the slide. Aligns shapes to the margins or the edge of the slide
  or align them relative to each other.
  </p><p><hr><blockquote><pre>Example:
  <pre>
  Presentation pres = new Presentation("pres.pptx");
  try {
     ISlide slide = pres.getSlides().get_Item(0);
     IShape shape1 = slide.getShapes().get_Item(0);
     IShape shape2 = slide.getShapes().get_Item(1);
     SlideUtil.alignShapes(ShapesAlignmentType.AlignBottom, false, pres.getSlides().get_Item(0), new int[]
     {
         slide.getShapes().indexOf(shape1),
         slide.getShapes().indexOf(shape2)
     });
  } finally {
     if (pres != null) pres.dispose();
  }
  </pre>
  </pre></blockquote></hr></p></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>alignmentType</code> - Determines which type of alignment will be applied.</dd>
<dd><code>alignToSlide</code> - If true, shapes will be aligned relative to the slide edges.</dd>
<dd><code>slide</code> - Parent slide.</dd>
<dd><code>shapeIndexes</code> - Indexes of shapes to be aligned.</dd>
</dl>
</li>
</ul>
<a name="alignShapes-int-boolean-com.aspose.slides.IGroupShape-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>alignShapes</h4>
<pre>public static&nbsp;void&nbsp;alignShapes(int&nbsp;alignmentType,
                               boolean&nbsp;alignToSlide,
                               <a href="../../../com/aspose/slides/IGroupShape.html" title="interface in com.aspose.slides">IGroupShape</a>&nbsp;groupShape)</pre>
<div class="block"><p>
 Changes the placement of all shapes within group shape. Aligns shapes to the margins or the edge of the slide
 or align them relative to each other.
 </p><p><hr><blockquote><pre>Example:
 <pre>
 Presentation pres = new Presentation("pres.pptx");
 try {
     ISlide slide = pres.getSlides().get_Item(0);
     SlideUtil.alignShapes(ShapesAlignmentType.AlignLeft, false, (GroupShape) slide.getShapes().get_Item(0));
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>alignmentType</code> - Determines which type of alignment will be applied.</dd>
<dd><code>alignToSlide</code> - If true, shapes will be aligned relative to the slide edges.</dd>
<dd><code>groupShape</code> - Parent group shape.</dd>
</dl>
</li>
</ul>
<a name="alignShapes-int-boolean-com.aspose.slides.IGroupShape-int:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>alignShapes</h4>
<pre>public static&nbsp;void&nbsp;alignShapes(int&nbsp;alignmentType,
                               boolean&nbsp;alignToSlide,
                               <a href="../../../com/aspose/slides/IGroupShape.html" title="interface in com.aspose.slides">IGroupShape</a>&nbsp;groupShape,
                               int[]&nbsp;shapeIndexes)</pre>
<div class="block"><p>
 Changes the placement of selected shapes within group shape. Aligns shapes to the margins or the edge of the slide
 or align them relative to each other.
 </p><p><hr><blockquote><pre>Example:
 <pre>
 Presentation pres = new Presentation("pres.pptx");
 try {
     ISlide slide = pres.getSlides().get_Item(0);
     SlideUtil.alignShapes(ShapesAlignmentType.AlignLeft, false, (GroupShape)slide.getShapes().get_Item(0), new int[] { 0, 2 });
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>alignmentType</code> - Determines which type of alignment will be applied.</dd>
<dd><code>alignToSlide</code> - If true, shapes will be aligned relative to the slide edges.</dd>
<dd><code>groupShape</code> - Parent group shape.</dd>
<dd><code>shapeIndexes</code> - Indexes of shapes to be aligned.</dd>
</dl>
</li>
</ul>
<a name="findAndReplaceText-com.aspose.slides.IPresentation-boolean-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>findAndReplaceText</h4>
<pre>public static&nbsp;void&nbsp;findAndReplaceText(<a href="../../../com/aspose/slides/IPresentation.html" title="interface in com.aspose.slides">IPresentation</a>&nbsp;presentation,
                                      boolean&nbsp;withMasters,
                                      java.lang.String&nbsp;find,
                                      java.lang.String&nbsp;replace)</pre>
<div class="block"><p>
 Finds and replaces text in presentation with given format
 </p><p><hr><blockquote><pre>
 <pre>
 Presentation pres = new Presentation("pres.pptx");
 try {
     PortionFormat format = new PortionFormat();
     format.setFontHeight(24f);
     format.setFontItalic(NullableBool.True);
     format.getFillFormat().setFillType(FillType.Solid);
     format.getFillFormat().getSolidFillColor().setColor(Color.RED);

     SlideUtil.findAndReplaceText(pres, true, "[this block] ", "my text ", format);
     pres.save("replaced.pptx", SaveFormat.Pptx);
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>presentation</code> - Scanned presentation.</dd>
<dd><code>withMasters</code> - Determines whether master slides should be scanned.</dd>
<dd><code>find</code> - String value to find.</dd>
<dd><code>replace</code> - String value to replace.
 character of the found string</dd>
</dl>
</li>
</ul>
<a name="findAndReplaceText-com.aspose.slides.IPresentation-boolean-java.lang.String-java.lang.String-com.aspose.slides.PortionFormat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>findAndReplaceText</h4>
<pre>public static&nbsp;void&nbsp;findAndReplaceText(<a href="../../../com/aspose/slides/IPresentation.html" title="interface in com.aspose.slides">IPresentation</a>&nbsp;presentation,
                                      boolean&nbsp;withMasters,
                                      java.lang.String&nbsp;find,
                                      java.lang.String&nbsp;replace,
                                      <a href="../../../com/aspose/slides/PortionFormat.html" title="class in com.aspose.slides">PortionFormat</a>&nbsp;format)</pre>
<div class="block"><p>
 Finds and replaces text in presentation with given format
 </p><p><hr><blockquote><pre>
 <pre>
 Presentation pres = new Presentation("pres.pptx");
 try {
     PortionFormat format = new PortionFormat();
     format.setFontHeight(24f);
     format.setFontItalic(NullableBool.True);
     format.getFillFormat().setFillType(FillType.Solid);
     format.getFillFormat().getSolidFillColor().setColor(Color.RED);

     SlideUtil.findAndReplaceText(pres, true, "[this block] ", "my text ", format);
     pres.save("replaced.pptx", SaveFormat.Pptx);
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>presentation</code> - Scanned presentation.</dd>
<dd><code>withMasters</code> - Determines whether master slides should be scanned.</dd>
<dd><code>find</code> - String value to find.</dd>
<dd><code>replace</code> - String value to replace.</dd>
<dd><code>format</code> - Format for replacing text portion. If null then will be used format of the first 
 character of the found string</dd>
</dl>
</li>
</ul>
<a name="getAllTextBoxes-com.aspose.slides.IBaseSlide-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAllTextBoxes</h4>
<pre>public static&nbsp;<a href="../../../com/aspose/slides/ITextFrame.html" title="interface in com.aspose.slides">ITextFrame</a>[]&nbsp;getAllTextBoxes(<a href="../../../com/aspose/slides/IBaseSlide.html" title="interface in com.aspose.slides">IBaseSlide</a>&nbsp;slide)</pre>
<div class="block"><p>
 Returns all text frames on a slide in a PPTX presentation.
 </p></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>slide</code> - Scanned slide.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Array of <a href="../../../com/aspose/slides/TextFrame.html" title="class in com.aspose.slides"><code>TextFrame</code></a> objects.</dd>
</dl>
</li>
</ul>
<a name="getAllTextFrames-com.aspose.slides.IPresentation-boolean-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getAllTextFrames</h4>
<pre>public static&nbsp;<a href="../../../com/aspose/slides/ITextFrame.html" title="interface in com.aspose.slides">ITextFrame</a>[]&nbsp;getAllTextFrames(<a href="../../../com/aspose/slides/IPresentation.html" title="interface in com.aspose.slides">IPresentation</a>&nbsp;pres,
                                            boolean&nbsp;withMasters)</pre>
<div class="block"><p>
 Returns all text frames in a PPTX presentation.
 </p></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>pres</code> - Scanned presentation.</dd>
<dd><code>withMasters</code> - Determines whether master slides should be scanned.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Array of <a href="../../../com/aspose/slides/TextFrame.html" title="class in com.aspose.slides"><code>TextFrame</code></a> objects.</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/SlideThemeManager.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/SmartArt.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/SlideUtil.html" target="_top">Frames</a></li>
<li><a href="SlideUtil.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><i>Copyright &copy; 2004-2025 Aspose Pty Ltd. All Rights Reserved.</i></small></p>
</body>
</html>
