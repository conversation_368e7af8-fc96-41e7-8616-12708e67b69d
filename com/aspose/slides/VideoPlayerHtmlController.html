<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_421) on Fri Jul 11 05:32:45 CDT 2025 -->
<title>VideoPlayerHtmlController (Aspose.Slides for Java (25.7) API Documentation)</title>
<meta name="date" content="2025-07-11">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="VideoPlayerHtmlController (Aspose.Slides for Java (25.7) API Documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/VideoFrame.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/VideoPlayerHtmlControllerFactory.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/VideoPlayerHtmlController.html" target="_top">Frames</a></li>
<li><a href="VideoPlayerHtmlController.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.aspose.slides</div>
<h2 title="Class VideoPlayerHtmlController" class="title">Class VideoPlayerHtmlController</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.aspose.slides.VideoPlayerHtmlController</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../com/aspose/slides/IHtmlFormattingController.html" title="interface in com.aspose.slides">IHtmlFormattingController</a>, <a href="../../../com/aspose/slides/ILinkEmbedController.html" title="interface in com.aspose.slides">ILinkEmbedController</a>, <a href="../../../com/aspose/slides/ISvgShapeFormattingController.html" title="interface in com.aspose.slides">ISvgShapeFormattingController</a>, <a href="../../../com/aspose/slides/IVideoPlayerHtmlController.html" title="interface in com.aspose.slides">IVideoPlayerHtmlController</a></dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">VideoPlayerHtmlController</span>
extends java.lang.Object
implements <a href="../../../com/aspose/slides/IVideoPlayerHtmlController.html" title="interface in com.aspose.slides">IVideoPlayerHtmlController</a></pre>
<div class="block"><p>
 This class allows export of video and audio files into a HTML
 </p></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/VideoPlayerHtmlController.html#VideoPlayerHtmlController-java.lang.String-java.lang.String-java.lang.String-">VideoPlayerHtmlController</a></span>(java.lang.String&nbsp;path,
                         java.lang.String&nbsp;fileName,
                         java.lang.String&nbsp;baseUri)</code>
<div class="block">
 Creates a new instance of controller</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/VideoPlayerHtmlController.html#formatShape-com.aspose.slides.ISvgShape-com.aspose.slides.IShape-">formatShape</a></span>(<a href="../../../com/aspose/slides/ISvgShape.html" title="interface in com.aspose.slides">ISvgShape</a>&nbsp;svgShape,
           <a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a>&nbsp;shape)</code>
<div class="block">
 This function is called before rendering of shape to SVG to allow user to control resulting SVG.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/VideoPlayerHtmlController.html#getObjectStoringLocation-int-byte:A-java.lang.String-java.lang.String-java.lang.String-">getObjectStoringLocation</a></span>(int&nbsp;id,
                        byte[]&nbsp;entityData,
                        java.lang.String&nbsp;semanticName,
                        java.lang.String&nbsp;contentType,
                        java.lang.String&nbsp;recomendedExtension)</code>
<div class="block">
 Determines where object should be stored.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/VideoPlayerHtmlController.html#getUrl-int-int-">getUrl</a></span>(int&nbsp;id,
      int&nbsp;referrer)</code>
<div class="block">
 Returns an URL to an external object.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/VideoPlayerHtmlController.html#saveExternal-int-byte:A-">saveExternal</a></span>(int&nbsp;id,
            byte[]&nbsp;entityData)</code>
<div class="block">
 Saves external object.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/VideoPlayerHtmlController.html#writeDocumentEnd-com.aspose.slides.IHtmlGenerator-com.aspose.slides.IPresentation-">writeDocumentEnd</a></span>(<a href="../../../com/aspose/slides/IHtmlGenerator.html" title="interface in com.aspose.slides">IHtmlGenerator</a>&nbsp;generator,
                <a href="../../../com/aspose/slides/IPresentation.html" title="interface in com.aspose.slides">IPresentation</a>&nbsp;presentation)</code>
<div class="block">
 Called to write html document footer.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/VideoPlayerHtmlController.html#writeDocumentStart-com.aspose.slides.IHtmlGenerator-com.aspose.slides.IPresentation-">writeDocumentStart</a></span>(<a href="../../../com/aspose/slides/IHtmlGenerator.html" title="interface in com.aspose.slides">IHtmlGenerator</a>&nbsp;generator,
                  <a href="../../../com/aspose/slides/IPresentation.html" title="interface in com.aspose.slides">IPresentation</a>&nbsp;presentation)</code>
<div class="block">
 Called to write html document header.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/VideoPlayerHtmlController.html#writeShapeEnd-com.aspose.slides.IHtmlGenerator-com.aspose.slides.IShape-">writeShapeEnd</a></span>(<a href="../../../com/aspose/slides/IHtmlGenerator.html" title="interface in com.aspose.slides">IHtmlGenerator</a>&nbsp;generator,
             <a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a>&nbsp;shape)</code>
<div class="block">
 Called before shape's rendering.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/VideoPlayerHtmlController.html#writeShapeStart-com.aspose.slides.IHtmlGenerator-com.aspose.slides.IShape-">writeShapeStart</a></span>(<a href="../../../com/aspose/slides/IHtmlGenerator.html" title="interface in com.aspose.slides">IHtmlGenerator</a>&nbsp;generator,
               <a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a>&nbsp;shape)</code>
<div class="block">
 Called before shape's rendering.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/VideoPlayerHtmlController.html#writeSlideEnd-com.aspose.slides.IHtmlGenerator-com.aspose.slides.ISlide-">writeSlideEnd</a></span>(<a href="../../../com/aspose/slides/IHtmlGenerator.html" title="interface in com.aspose.slides">IHtmlGenerator</a>&nbsp;generator,
             <a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a>&nbsp;slide)</code>
<div class="block">
 Called to write html slide footer.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/VideoPlayerHtmlController.html#writeSlideStart-com.aspose.slides.IHtmlGenerator-com.aspose.slides.ISlide-">writeSlideStart</a></span>(<a href="../../../com/aspose/slides/IHtmlGenerator.html" title="interface in com.aspose.slides">IHtmlGenerator</a>&nbsp;generator,
               <a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a>&nbsp;slide)</code>
<div class="block">
 Called to write html slide header.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="VideoPlayerHtmlController-java.lang.String-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>VideoPlayerHtmlController</h4>
<pre>public&nbsp;VideoPlayerHtmlController(java.lang.String&nbsp;path,
                                 java.lang.String&nbsp;fileName,
                                 java.lang.String&nbsp;baseUri)</pre>
<div class="block"><p>
 Creates a new instance of controller
 </p></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>path</code> - The path where video and audio files will be generated</dd>
<dd><code>fileName</code> - The name of the HTML file</dd>
<dd><code>baseUri</code> - The base URI which will be used for links generating</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="writeDocumentStart-com.aspose.slides.IHtmlGenerator-com.aspose.slides.IPresentation-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>writeDocumentStart</h4>
<pre>public final&nbsp;void&nbsp;writeDocumentStart(<a href="../../../com/aspose/slides/IHtmlGenerator.html" title="interface in com.aspose.slides">IHtmlGenerator</a>&nbsp;generator,
                                     <a href="../../../com/aspose/slides/IPresentation.html" title="interface in com.aspose.slides">IPresentation</a>&nbsp;presentation)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../com/aspose/slides/IHtmlFormattingController.html#writeDocumentStart-com.aspose.slides.IHtmlGenerator-com.aspose.slides.IPresentation-">IHtmlFormattingController</a></code></span></div>
<div class="block"><p>
 Called to write html document header. Called once per presentation conversion.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IHtmlFormattingController.html#writeDocumentStart-com.aspose.slides.IHtmlGenerator-com.aspose.slides.IPresentation-">writeDocumentStart</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IHtmlFormattingController.html" title="interface in com.aspose.slides">IHtmlFormattingController</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>generator</code> - Output object.</dd>
<dd><code>presentation</code> - Presentation which being currently rendered.</dd>
</dl>
</li>
</ul>
<a name="writeDocumentEnd-com.aspose.slides.IHtmlGenerator-com.aspose.slides.IPresentation-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>writeDocumentEnd</h4>
<pre>public final&nbsp;void&nbsp;writeDocumentEnd(<a href="../../../com/aspose/slides/IHtmlGenerator.html" title="interface in com.aspose.slides">IHtmlGenerator</a>&nbsp;generator,
                                   <a href="../../../com/aspose/slides/IPresentation.html" title="interface in com.aspose.slides">IPresentation</a>&nbsp;presentation)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../com/aspose/slides/IHtmlFormattingController.html#writeDocumentEnd-com.aspose.slides.IHtmlGenerator-com.aspose.slides.IPresentation-">IHtmlFormattingController</a></code></span></div>
<div class="block"><p>
 Called to write html document footer. Called once per presentation conversion.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IHtmlFormattingController.html#writeDocumentEnd-com.aspose.slides.IHtmlGenerator-com.aspose.slides.IPresentation-">writeDocumentEnd</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IHtmlFormattingController.html" title="interface in com.aspose.slides">IHtmlFormattingController</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>generator</code> - Output object.</dd>
<dd><code>presentation</code> - Presentation which being currently rendered.</dd>
</dl>
</li>
</ul>
<a name="writeSlideStart-com.aspose.slides.IHtmlGenerator-com.aspose.slides.ISlide-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>writeSlideStart</h4>
<pre>public final&nbsp;void&nbsp;writeSlideStart(<a href="../../../com/aspose/slides/IHtmlGenerator.html" title="interface in com.aspose.slides">IHtmlGenerator</a>&nbsp;generator,
                                  <a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a>&nbsp;slide)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../com/aspose/slides/IHtmlFormattingController.html#writeSlideStart-com.aspose.slides.IHtmlGenerator-com.aspose.slides.ISlide-">IHtmlFormattingController</a></code></span></div>
<div class="block"><p>
 Called to write html slide header. Called once per each of slides.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IHtmlFormattingController.html#writeSlideStart-com.aspose.slides.IHtmlGenerator-com.aspose.slides.ISlide-">writeSlideStart</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IHtmlFormattingController.html" title="interface in com.aspose.slides">IHtmlFormattingController</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>generator</code> - Output object.</dd>
<dd><code>slide</code> - Slide which being currently rendered.</dd>
</dl>
</li>
</ul>
<a name="writeSlideEnd-com.aspose.slides.IHtmlGenerator-com.aspose.slides.ISlide-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>writeSlideEnd</h4>
<pre>public final&nbsp;void&nbsp;writeSlideEnd(<a href="../../../com/aspose/slides/IHtmlGenerator.html" title="interface in com.aspose.slides">IHtmlGenerator</a>&nbsp;generator,
                                <a href="../../../com/aspose/slides/ISlide.html" title="interface in com.aspose.slides">ISlide</a>&nbsp;slide)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../com/aspose/slides/IHtmlFormattingController.html#writeSlideEnd-com.aspose.slides.IHtmlGenerator-com.aspose.slides.ISlide-">IHtmlFormattingController</a></code></span></div>
<div class="block"><p>
 Called to write html slide footer. Called once per each of slides.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IHtmlFormattingController.html#writeSlideEnd-com.aspose.slides.IHtmlGenerator-com.aspose.slides.ISlide-">writeSlideEnd</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IHtmlFormattingController.html" title="interface in com.aspose.slides">IHtmlFormattingController</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>generator</code> - Output object.</dd>
<dd><code>slide</code> - Slide which being currently rendered.</dd>
</dl>
</li>
</ul>
<a name="writeShapeStart-com.aspose.slides.IHtmlGenerator-com.aspose.slides.IShape-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>writeShapeStart</h4>
<pre>public final&nbsp;void&nbsp;writeShapeStart(<a href="../../../com/aspose/slides/IHtmlGenerator.html" title="interface in com.aspose.slides">IHtmlGenerator</a>&nbsp;generator,
                                  <a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a>&nbsp;shape)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../com/aspose/slides/IHtmlFormattingController.html#writeShapeStart-com.aspose.slides.IHtmlGenerator-com.aspose.slides.IShape-">IHtmlFormattingController</a></code></span></div>
<div class="block"><p>
 Called before shape's rendering. Called once per each of shape. If this function writes anything to generator, current slide image generation will be finished, added html fragment inserted and new image will be started atop of the previous.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IHtmlFormattingController.html#writeShapeStart-com.aspose.slides.IHtmlGenerator-com.aspose.slides.IShape-">writeShapeStart</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IHtmlFormattingController.html" title="interface in com.aspose.slides">IHtmlFormattingController</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>generator</code> - Output object.</dd>
<dd><code>shape</code> - Shape which is about to render.</dd>
</dl>
</li>
</ul>
<a name="writeShapeEnd-com.aspose.slides.IHtmlGenerator-com.aspose.slides.IShape-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>writeShapeEnd</h4>
<pre>public final&nbsp;void&nbsp;writeShapeEnd(<a href="../../../com/aspose/slides/IHtmlGenerator.html" title="interface in com.aspose.slides">IHtmlGenerator</a>&nbsp;generator,
                                <a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a>&nbsp;shape)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../com/aspose/slides/IHtmlFormattingController.html#writeShapeEnd-com.aspose.slides.IHtmlGenerator-com.aspose.slides.IShape-">IHtmlFormattingController</a></code></span></div>
<div class="block"><p>
 Called before shape's rendering. Called once per each of shape. If this function writes anything to generator, current slide image generation will be finished, added html fragment inserted and new image will be started atop of the previous.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IHtmlFormattingController.html#writeShapeEnd-com.aspose.slides.IHtmlGenerator-com.aspose.slides.IShape-">writeShapeEnd</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IHtmlFormattingController.html" title="interface in com.aspose.slides">IHtmlFormattingController</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>generator</code> - Output object.</dd>
<dd><code>shape</code> - Shape which is rendered last.</dd>
</dl>
</li>
</ul>
<a name="formatShape-com.aspose.slides.ISvgShape-com.aspose.slides.IShape-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>formatShape</h4>
<pre>public final&nbsp;void&nbsp;formatShape(<a href="../../../com/aspose/slides/ISvgShape.html" title="interface in com.aspose.slides">ISvgShape</a>&nbsp;svgShape,
                              <a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a>&nbsp;shape)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../com/aspose/slides/ISvgShapeFormattingController.html#formatShape-com.aspose.slides.ISvgShape-com.aspose.slides.IShape-">ISvgShapeFormattingController</a></code></span></div>
<div class="block"><p>
 This function is called before rendering of shape to SVG to allow user to control resulting SVG.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISvgShapeFormattingController.html#formatShape-com.aspose.slides.ISvgShape-com.aspose.slides.IShape-">formatShape</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISvgShapeFormattingController.html" title="interface in com.aspose.slides">ISvgShapeFormattingController</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>svgShape</code> - Object to control SVG shape generation.</dd>
<dd><code>shape</code> - Source shape.</dd>
</dl>
</li>
</ul>
<a name="getObjectStoringLocation-int-byte:A-java.lang.String-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getObjectStoringLocation</h4>
<pre>public final&nbsp;int&nbsp;getObjectStoringLocation(int&nbsp;id,
                                          byte[]&nbsp;entityData,
                                          java.lang.String&nbsp;semanticName,
                                          java.lang.String&nbsp;contentType,
                                          java.lang.String&nbsp;recomendedExtension)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../com/aspose/slides/ILinkEmbedController.html#getObjectStoringLocation-int-byte:A-java.lang.String-java.lang.String-java.lang.String-">ILinkEmbedController</a></code></span></div>
<div class="block"><p>
 Determines where object should be stored.
 This method is called once for each object id.
 It is not guaranteed that there won't be two objects with same data, semanticName and contentType but with different id.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ILinkEmbedController.html#getObjectStoringLocation-int-byte:A-java.lang.String-java.lang.String-java.lang.String-">getObjectStoringLocation</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ILinkEmbedController.html" title="interface in com.aspose.slides">ILinkEmbedController</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>id</code> - Object id. This id is saving operation-wide unique.</dd>
<dd><code>entityData</code> - Object binary data. This parameter can be null, if object binary data is not generated yet.</dd>
<dd><code>semanticName</code> - Some short text, describing meaning of object. Controller may use this as a part of external object name, but it is up to dispatcher to ensure that names will be unique and contain only allowed characters.</dd>
<dd><code>contentType</code> - MIME type of object.</dd>
<dd><code>recomendedExtension</code> - File name extension, recommended for this MIME type.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Decision</dd>
</dl>
</li>
</ul>
<a name="getUrl-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getUrl</h4>
<pre>public final&nbsp;java.lang.String&nbsp;getUrl(int&nbsp;id,
                                     int&nbsp;referrer)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../com/aspose/slides/ILinkEmbedController.html#getUrl-int-int-">ILinkEmbedController</a></code></span></div>
<div class="block"><p>
 Returns an URL to an external object.
 This method always called if <a href="../../../com/aspose/slides/ILinkEmbedController.html#getObjectStoringLocation-int-byte:A-java.lang.String-java.lang.String-java.lang.String-"><code>ILinkEmbedController.getObjectStoringLocation(int,byte[],String,String,String)</code></a> returned <a href="../../../com/aspose/slides/LinkEmbedDecision.html#Link"><code>LinkEmbedDecision.Link</code></a> and may be called if <a href="../../../com/aspose/slides/ILinkEmbedController.html#getObjectStoringLocation-int-byte:A-java.lang.String-java.lang.String-java.lang.String-"><code>ILinkEmbedController.getObjectStoringLocation(int,byte[],String,String,String)</code></a> returned <a href="../../../com/aspose/slides/LinkEmbedDecision.html#Embed"><code>LinkEmbedDecision.Embed</code></a> but embedding is impossible.
 Can be called multiple time for same object id.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ILinkEmbedController.html#getUrl-int-int-">getUrl</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ILinkEmbedController.html" title="interface in com.aspose.slides">ILinkEmbedController</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>id</code> - Object id. This id is saving operation-wide unique.</dd>
<dd><code>referrer</code> - id of referrencing object or 0, if object is referrenced by the root document. May be used to generate relative link.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Url of external object or null if this object should be ignored.</dd>
</dl>
</li>
</ul>
<a name="saveExternal-int-byte:A-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>saveExternal</h4>
<pre>public final&nbsp;void&nbsp;saveExternal(int&nbsp;id,
                               byte[]&nbsp;entityData)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../com/aspose/slides/ILinkEmbedController.html#saveExternal-int-byte:A-">ILinkEmbedController</a></code></span></div>
<div class="block"><p>
 Saves external object.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ILinkEmbedController.html#saveExternal-int-byte:A-">saveExternal</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ILinkEmbedController.html" title="interface in com.aspose.slides">ILinkEmbedController</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>id</code> - Object id. This id is saving operation-wide unique.</dd>
<dd><code>entityData</code> - Object binary data. This parameter cannot be null.</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/VideoFrame.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/VideoPlayerHtmlControllerFactory.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/VideoPlayerHtmlController.html" target="_top">Frames</a></li>
<li><a href="VideoPlayerHtmlController.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><i>Copyright &copy; 2004-2025 Aspose Pty Ltd. All Rights Reserved.</i></small></p>
</body>
</html>
