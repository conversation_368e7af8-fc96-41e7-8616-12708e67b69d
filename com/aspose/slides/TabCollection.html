<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_421) on Fri Jul 11 05:32:44 CDT 2025 -->
<title>TabCollection (Aspose.Slides for Java (25.7) API Documentation)</title>
<meta name="date" content="2025-07-11">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="TabCollection (Aspose.Slides for Java (25.7) API Documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/TabAlignment.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/TabFactory.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/TabCollection.html" target="_top">Frames</a></li>
<li><a href="TabCollection.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.aspose.slides</div>
<h2 title="Class TabCollection" class="title">Class TabCollection</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.aspose.slides.TabCollection</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd>com.aspose.ms.System.Collections.Generic.IGenericEnumerable&lt;<a href="../../../com/aspose/slides/ITab.html" title="interface in com.aspose.slides">ITab</a>&gt;, com.aspose.ms.System.Collections.ICollection&lt;<a href="../../../com/aspose/slides/ITab.html" title="interface in com.aspose.slides">ITab</a>&gt;, com.aspose.ms.System.Collections.IEnumerable&lt;<a href="../../../com/aspose/slides/ITab.html" title="interface in com.aspose.slides">ITab</a>&gt;, <a href="../../../com/aspose/slides/IGenericCollection.html" title="interface in com.aspose.slides">IGenericCollection</a>&lt;<a href="../../../com/aspose/slides/ITab.html" title="interface in com.aspose.slides">ITab</a>&gt;, <a href="../../../com/aspose/slides/ITabCollection.html" title="interface in com.aspose.slides">ITabCollection</a>, java.lang.Iterable&lt;<a href="../../../com/aspose/slides/ITab.html" title="interface in com.aspose.slides">ITab</a>&gt;</dd>
</dl>
<hr>
<br>
<pre>public final class <span class="typeNameLabel">TabCollection</span>
extends java.lang.Object
implements <a href="../../../com/aspose/slides/ITabCollection.html" title="interface in com.aspose.slides">ITabCollection</a></pre>
<div class="block"><p>
 Represents a collection of tabs.
 </p></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/ITab.html" title="interface in com.aspose.slides">ITab</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TabCollection.html#add-double-int-">add</a></span>(double&nbsp;position,
   int&nbsp;align)</code>
<div class="block">
 Adds a Tab to the collection.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TabCollection.html#add-com.aspose.slides.ITab-">add</a></span>(<a href="../../../com/aspose/slides/ITab.html" title="interface in com.aspose.slides">ITab</a>&nbsp;value)</code>
<div class="block">
 Adds a Tab to the collection.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TabCollection.html#clear--">clear</a></span>()</code>
<div class="block">
 Removes all elements from the collection.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TabCollection.html#copyTo-com.aspose.ms.System.Array-int-">copyTo</a></span>(com.aspose.ms.System.Array&nbsp;array,
      int&nbsp;index)</code>
<div class="block">
 Copies all elements from the collection to the specified array.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TabCollection.html#equals-java.lang.Object-">equals</a></span>(java.lang.Object&nbsp;obj)</code>
<div class="block">
 Determines whether two TabsEx instances are equal.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/ITab.html" title="interface in com.aspose.slides">ITab</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TabCollection.html#get_Item-int-">get_Item</a></span>(int&nbsp;index)</code>
<div class="block">
 Gets the element at the specified index.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>com.aspose.slides.IDOMObject</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TabCollection.html#getParent_Immediate--">getParent_Immediate</a></span>()</code>
<div class="block">
 Returns Parent_Immediate object.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>java.lang.Object</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TabCollection.html#getSyncRoot--">getSyncRoot</a></span>()</code>
<div class="block">
 Returns a synchronization root.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TabCollection.html#isSynchronized--">isSynchronized</a></span>()</code>
<div class="block">
 Returns a value indicating whether access to the collection is synchronized (thread-safe).</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>com.aspose.ms.System.Collections.Generic.IGenericEnumerator&lt;<a href="../../../com/aspose/slides/ITab.html" title="interface in com.aspose.slides">ITab</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TabCollection.html#iterator--">iterator</a></span>()</code>
<div class="block">
 Returns an enumerator that iterates through the collection.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>com.aspose.ms.System.Collections.Generic.IGenericEnumerator&lt;<a href="../../../com/aspose/slides/ITab.html" title="interface in com.aspose.slides">ITab</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TabCollection.html#iteratorJava--">iteratorJava</a></span>()</code>
<div class="block">
 Returns a java iterator for the entire collection.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TabCollection.html#removeAt-int-">removeAt</a></span>(int&nbsp;index)</code>
<div class="block">
 Removes the element at the specified index of the collection.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TabCollection.html#size--">size</a></span>()</code>
<div class="block">
 Gets the number of elements actually contained in the collection.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Iterable">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;java.lang.Iterable</h3>
<code>forEach, spliterator</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="size--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>size</h4>
<pre>public final&nbsp;int&nbsp;size()</pre>
<div class="block"><p>
 Gets the number of elements actually contained in the collection.
 Read-only <code>int</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>size</code>&nbsp;in interface&nbsp;<code>com.aspose.ms.System.Collections.ICollection&lt;<a href="../../../com/aspose/slides/ITab.html" title="interface in com.aspose.slides">ITab</a>&gt;</code></dd>
</dl>
</li>
</ul>
<a name="get_Item-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_Item</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/ITab.html" title="interface in com.aspose.slides">ITab</a>&nbsp;get_Item(int&nbsp;index)</pre>
<div class="block"><p>
 Gets the element at the specified index.
 Read-only <a href="../../../com/aspose/slides/Tab.html" title="class in com.aspose.slides"><code>Tab</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITabCollection.html#get_Item-int-">get_Item</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITabCollection.html" title="interface in com.aspose.slides">ITabCollection</a></code></dd>
</dl>
</li>
</ul>
<a name="add-double-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>add</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/ITab.html" title="interface in com.aspose.slides">ITab</a>&nbsp;add(double&nbsp;position,
                      int&nbsp;align)</pre>
<div class="block"><p>
 Adds a Tab to the collection.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITabCollection.html#add-double-int-">add</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITabCollection.html" title="interface in com.aspose.slides">ITabCollection</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>position</code> - Tab position.</dd>
<dd><code>align</code> - Tab alignment.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Added tab.</dd>
</dl>
</li>
</ul>
<a name="add-com.aspose.slides.ITab-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>add</h4>
<pre>public final&nbsp;int&nbsp;add(<a href="../../../com/aspose/slides/ITab.html" title="interface in com.aspose.slides">ITab</a>&nbsp;value)</pre>
<div class="block"><p>
 Adds a Tab to the collection.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITabCollection.html#add-com.aspose.slides.ITab-">add</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITabCollection.html" title="interface in com.aspose.slides">ITabCollection</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - The Tab object to be added at the end of the collection.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The index at which the tab was added.</dd>
</dl>
</li>
</ul>
<a name="clear--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>clear</h4>
<pre>public final&nbsp;void&nbsp;clear()</pre>
<div class="block"><p>
 Removes all elements from the collection.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITabCollection.html#clear--">clear</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITabCollection.html" title="interface in com.aspose.slides">ITabCollection</a></code></dd>
</dl>
</li>
</ul>
<a name="removeAt-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>removeAt</h4>
<pre>public final&nbsp;void&nbsp;removeAt(int&nbsp;index)</pre>
<div class="block"><p>
 Removes the element at the specified index of the collection.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITabCollection.html#removeAt-int-">removeAt</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITabCollection.html" title="interface in com.aspose.slides">ITabCollection</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - The zero-based index of the element to remove.</dd>
</dl>
</li>
</ul>
<a name="getParent_Immediate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getParent_Immediate</h4>
<pre>public final&nbsp;com.aspose.slides.IDOMObject&nbsp;getParent_Immediate()</pre>
<div class="block"><p>
 Returns Parent_Immediate object.
 Read-only <code>IDOMObject</code>.
 </p></div>
</li>
</ul>
<a name="equals-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>equals</h4>
<pre>public&nbsp;boolean&nbsp;equals(java.lang.Object&nbsp;obj)</pre>
<div class="block"><p>
 Determines whether two TabsEx instances are equal.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code>equals</code>&nbsp;in class&nbsp;<code>java.lang.Object</code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>obj</code> - The TabsEx to compare with the current TabsEx.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd><b>true</b> if the specified TabsEx is equal to the current TabsEx; otherwise, <b>false</b>.</dd>
</dl>
</li>
</ul>
<a name="iterator--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>iterator</h4>
<pre>public final&nbsp;com.aspose.ms.System.Collections.Generic.IGenericEnumerator&lt;<a href="../../../com/aspose/slides/ITab.html" title="interface in com.aspose.slides">ITab</a>&gt;&nbsp;iterator()</pre>
<div class="block"><p>
 Returns an enumerator that iterates through the collection.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>iterator</code>&nbsp;in interface&nbsp;<code>com.aspose.ms.System.Collections.Generic.IGenericEnumerable&lt;<a href="../../../com/aspose/slides/ITab.html" title="interface in com.aspose.slides">ITab</a>&gt;</code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>iterator</code>&nbsp;in interface&nbsp;<code>com.aspose.ms.System.Collections.IEnumerable&lt;<a href="../../../com/aspose/slides/ITab.html" title="interface in com.aspose.slides">ITab</a>&gt;</code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>iterator</code>&nbsp;in interface&nbsp;<code>java.lang.Iterable&lt;<a href="../../../com/aspose/slides/ITab.html" title="interface in com.aspose.slides">ITab</a>&gt;</code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>A <code>IGenericEnumerator</code> that can be used to iterate through the collection.</dd>
</dl>
</li>
</ul>
<a name="iteratorJava--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>iteratorJava</h4>
<pre>public final&nbsp;com.aspose.ms.System.Collections.Generic.IGenericEnumerator&lt;<a href="../../../com/aspose/slides/ITab.html" title="interface in com.aspose.slides">ITab</a>&gt;&nbsp;iteratorJava()</pre>
<div class="block"><p>
 Returns a java iterator for the entire collection.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IGenericCollection.html#iteratorJava--">iteratorJava</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IGenericCollection.html" title="interface in com.aspose.slides">IGenericCollection</a>&lt;<a href="../../../com/aspose/slides/ITab.html" title="interface in com.aspose.slides">ITab</a>&gt;</code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>An <code>Iterator</code> for the entire collection.</dd>
</dl>
</li>
</ul>
<a name="copyTo-com.aspose.ms.System.Array-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>copyTo</h4>
<pre>public final&nbsp;void&nbsp;copyTo(com.aspose.ms.System.Array&nbsp;array,
                         int&nbsp;index)</pre>
<div class="block"><p>
 Copies all elements from the collection to the specified array.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>copyTo</code>&nbsp;in interface&nbsp;<code>com.aspose.ms.System.Collections.ICollection&lt;<a href="../../../com/aspose/slides/ITab.html" title="interface in com.aspose.slides">ITab</a>&gt;</code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>array</code> - Target array.</dd>
<dd><code>index</code> - Starting index in the target array.</dd>
</dl>
</li>
</ul>
<a name="isSynchronized--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isSynchronized</h4>
<pre>public final&nbsp;boolean&nbsp;isSynchronized()</pre>
<div class="block"><p>
 Returns a value indicating whether access to the collection is synchronized (thread-safe).
 Read-only <code>boolean</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>isSynchronized</code>&nbsp;in interface&nbsp;<code>com.aspose.ms.System.Collections.ICollection&lt;<a href="../../../com/aspose/slides/ITab.html" title="interface in com.aspose.slides">ITab</a>&gt;</code></dd>
</dl>
</li>
</ul>
<a name="getSyncRoot--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getSyncRoot</h4>
<pre>public final&nbsp;java.lang.Object&nbsp;getSyncRoot()</pre>
<div class="block"><p>
 Returns a synchronization root.
 Read-only <code>Object</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>getSyncRoot</code>&nbsp;in interface&nbsp;<code>com.aspose.ms.System.Collections.ICollection&lt;<a href="../../../com/aspose/slides/ITab.html" title="interface in com.aspose.slides">ITab</a>&gt;</code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/TabAlignment.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/TabFactory.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/TabCollection.html" target="_top">Frames</a></li>
<li><a href="TabCollection.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><i>Copyright &copy; 2004-2025 Aspose Pty Ltd. All Rights Reserved.</i></small></p>
</body>
</html>
