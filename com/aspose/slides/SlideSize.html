<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_421) on Fri Jul 11 05:32:44 CDT 2025 -->
<title>SlideSize (Aspose.Slides for Java (25.7) API Documentation)</title>
<meta name="date" content="2025-07-11">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="SlideSize (Aspose.Slides for Java (25.7) API Documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/SlideShowType.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/SlideSizeScaleType.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/SlideSize.html" target="_top">Frames</a></li>
<li><a href="SlideSize.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.aspose.slides</div>
<h2 title="Class SlideSize" class="title">Class SlideSize</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../com/aspose/slides/DomObject.html" title="class in com.aspose.slides">com.aspose.slides.DomObject</a>&lt;<a href="../../../com/aspose/slides/Presentation.html" title="class in com.aspose.slides">Presentation</a>&gt;</li>
<li>
<ul class="inheritance">
<li>com.aspose.slides.SlideSize</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../com/aspose/slides/ISlideSize.html" title="interface in com.aspose.slides">ISlideSize</a></dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">SlideSize</span>
extends <a href="../../../com/aspose/slides/DomObject.html" title="class in com.aspose.slides">DomObject</a>&lt;<a href="../../../com/aspose/slides/Presentation.html" title="class in com.aspose.slides">Presentation</a>&gt;
implements <a href="../../../com/aspose/slides/ISlideSize.html" title="interface in com.aspose.slides">ISlideSize</a></pre>
<div class="block"><p>
 Represents the size and orientation of a slide.
 </p></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideSize.html#getOrientation--">getOrientation</a></span>()</code>
<div class="block">
 Gets or sets the slide orientation.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>java.awt.geom.Dimension2D</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideSize.html#getSize--">getSize</a></span>()</code>
<div class="block">
 Gets the slide dimensions in points.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideSize.html#getType--">getType</a></span>()</code>
<div class="block">
 Gets the slide size type.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideSize.html#setOrientation-int-">setOrientation</a></span>(int&nbsp;value)</code>
<div class="block">
 Gets or sets the slide orientation.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideSize.html#setSize-float-float-int-">setSize</a></span>(float&nbsp;width,
       float&nbsp;height,
       int&nbsp;scaleType)</code>
<div class="block">
 Sets the slide dimensions explicitly and scales existing content.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideSize.html#setSize-int-int-">setSize</a></span>(int&nbsp;type,
       int&nbsp;scaleType)</code>
<div class="block">
 Sets the slide size by type and scales existing content.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.aspose.slides.DomObject">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;com.aspose.slides.<a href="../../../com/aspose/slides/DomObject.html" title="class in com.aspose.slides">DomObject</a></h3>
<code><a href="../../../com/aspose/slides/DomObject.html#getParent_Immediate--">getParent_Immediate</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getSize--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSize</h4>
<pre>public final&nbsp;java.awt.geom.Dimension2D&nbsp;getSize()</pre>
<div class="block"><p>
 Gets the slide dimensions in points.
 </p><p><hr>
 Assigning a new value resets the <a href="../../../com/aspose/slides/SlideSize.html#getType--"><code>getType()</code></a> property to <a href="../../../com/aspose/slides/SlideSizeType.html#Custom"><code>SlideSizeType.Custom</code></a>
 and sets the <a href="../../../com/aspose/slides/SlideSize.html#getOrientation--"><code>getOrientation()</code></a>/<a href="../../../com/aspose/slides/SlideSize.html#setOrientation-int-"><code>setOrientation(int)</code></a>.
 </hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISlideSize.html#getSize--">getSize</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISlideSize.html" title="interface in com.aspose.slides">ISlideSize</a></code></dd>
</dl>
</li>
</ul>
<a name="getType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getType</h4>
<pre>public final&nbsp;int&nbsp;getType()</pre>
<div class="block"><p>
 Gets the slide size type.
 </p><p><hr>
 Assigning any value other than <a href="../../../com/aspose/slides/SlideSizeType.html#Custom"><code>SlideSizeType.Custom</code></a> adjusts the <a href="../../../com/aspose/slides/SlideSize.html#getSize--"><code>getSize()</code></a>
 according to the predefined dimensions, while retaining the current <a href="../../../com/aspose/slides/SlideSize.html#getOrientation--"><code>getOrientation()</code></a>/<a href="../../../com/aspose/slides/SlideSize.html#setOrientation-int-"><code>setOrientation(int)</code></a>.
 </hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISlideSize.html#getType--">getType</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISlideSize.html" title="interface in com.aspose.slides">ISlideSize</a></code></dd>
</dl>
</li>
</ul>
<a name="getOrientation--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getOrientation</h4>
<pre>public final&nbsp;int&nbsp;getOrientation()</pre>
<div class="block"><p>
 Gets or sets the slide orientation.
 </p><p><hr>
 Changing this value swaps the slide’s width and height.
 </hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISlideSize.html#getOrientation--">getOrientation</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISlideSize.html" title="interface in com.aspose.slides">ISlideSize</a></code></dd>
</dl>
</li>
</ul>
<a name="setOrientation-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setOrientation</h4>
<pre>public final&nbsp;void&nbsp;setOrientation(int&nbsp;value)</pre>
<div class="block"><p>
 Gets or sets the slide orientation.
 </p><p><hr>
 Changing this value swaps the slide’s width and height.
 </hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISlideSize.html#setOrientation-int-">setOrientation</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISlideSize.html" title="interface in com.aspose.slides">ISlideSize</a></code></dd>
</dl>
</li>
</ul>
<a name="setSize-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSize</h4>
<pre>public final&nbsp;void&nbsp;setSize(int&nbsp;type,
                          int&nbsp;scaleType)</pre>
<div class="block"><p>
 Sets the slide size by type and scales existing content.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISlideSize.html#setSize-int-int-">setSize</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISlideSize.html" title="interface in com.aspose.slides">ISlideSize</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>type</code> - The predefined slide size to apply.</dd>
<dd><code>scaleType</code> - The content scaling mode to use.
 <p><hr>
 Assigning any value other than <a href="../../../com/aspose/slides/SlideSizeType.html#Custom"><code>SlideSizeType.Custom</code></a> adjusts the <a href="../../../com/aspose/slides/SlideSize.html#getSize--"><code>getSize()</code></a>
 based on the selected type, while preserving <a href="../../../com/aspose/slides/SlideSize.html#getOrientation--"><code>getOrientation()</code></a>/<a href="../../../com/aspose/slides/SlideSize.html#setOrientation-int-"><code>setOrientation(int)</code></a>.
 </hr></p></dd>
</dl>
</li>
</ul>
<a name="setSize-float-float-int-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setSize</h4>
<pre>public final&nbsp;void&nbsp;setSize(float&nbsp;width,
                          float&nbsp;height,
                          int&nbsp;scaleType)</pre>
<div class="block"><p>
 Sets the slide dimensions explicitly and scales existing content.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISlideSize.html#setSize-float-float-int-">setSize</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISlideSize.html" title="interface in com.aspose.slides">ISlideSize</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>width</code> - The new slide width, in points.</dd>
<dd><code>height</code> - The new slide height, in points.</dd>
<dd><code>scaleType</code> - The content scaling mode to use.
 <p><hr>
 This resets the <a href="../../../com/aspose/slides/SlideSize.html#getType--"><code>getType()</code></a> property to <a href="../../../com/aspose/slides/SlideSizeType.html#Custom"><code>SlideSizeType.Custom</code></a>
 and sets the <a href="../../../com/aspose/slides/SlideSize.html#getOrientation--"><code>getOrientation()</code></a>/<a href="../../../com/aspose/slides/SlideSize.html#setOrientation-int-"><code>setOrientation(int)</code></a>.
 </hr></p></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/SlideShowType.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/SlideSizeScaleType.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/SlideSize.html" target="_top">Frames</a></li>
<li><a href="SlideSize.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><i>Copyright &copy; 2004-2025 Aspose Pty Ltd. All Rights Reserved.</i></small></p>
</body>
</html>
