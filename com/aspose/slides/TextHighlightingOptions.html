<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_421) on Fri Jul 11 05:32:44 CDT 2025 -->
<title>TextHighlightingOptions (Aspose.Slides for Java (25.7) API Documentation)</title>
<meta name="date" content="2025-07-11">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="TextHighlightingOptions (Aspose.Slides for Java (25.7) API Documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":42,"i1":42,"i2":42,"i3":42};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"],32:["t6","Deprecated Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/TextFrameFormat.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/TextInheritanceLimit.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/TextHighlightingOptions.html" target="_top">Frames</a></li>
<li><a href="TextHighlightingOptions.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.aspose.slides</div>
<h2 title="Class TextHighlightingOptions" class="title">Class TextHighlightingOptions</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.aspose.slides.TextHighlightingOptions</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../com/aspose/slides/ITextHighlightingOptions.html" title="interface in com.aspose.slides">ITextHighlightingOptions</a></dd>
</dl>
<hr>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;
<div class="block"><span class="deprecationComment">The interface ITextHighlightingOptions will be removed after release of version 24.10.</span></div>
</div>
<br>
<pre>@Deprecated
public class <span class="typeNameLabel">TextHighlightingOptions</span>
extends java.lang.Object
implements <a href="../../../com/aspose/slides/ITextHighlightingOptions.html" title="interface in com.aspose.slides">ITextHighlightingOptions</a></pre>
<div class="block"><p>
 Represents options which can be used to highlight text in text frame.
 </p></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TextHighlightingOptions.html#TextHighlightingOptions--">TextHighlightingOptions</a></span>()</code>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;</div>
<div class="block">
 Creates new default text highlighting options.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t6" class="tableTab"><span><a href="javascript:show(32);">Deprecated Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TextHighlightingOptions.html#getCaseSensitive--">getCaseSensitive</a></span>()</code>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;</div>
<div class="block">
 Set true to use case-sensitive search, false - otherwise.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TextHighlightingOptions.html#getWholeWordsOnly--">getWholeWordsOnly</a></span>()</code>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;</div>
<div class="block">
 Set true to match only whole words, false - otherwise.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TextHighlightingOptions.html#setCaseSensitive-boolean-">setCaseSensitive</a></span>(boolean&nbsp;value)</code>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;</div>
<div class="block">
 Set true to use case-sensitive search, false - otherwise.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TextHighlightingOptions.html#setWholeWordsOnly-boolean-">setWholeWordsOnly</a></span>(boolean&nbsp;value)</code>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;</div>
<div class="block">
 Set true to match only whole words, false - otherwise.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="TextHighlightingOptions--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>TextHighlightingOptions</h4>
<pre>public&nbsp;TextHighlightingOptions()</pre>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;</div>
<div class="block"><p>
 Creates new default text highlighting options.
 </p></div>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getCaseSensitive--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCaseSensitive</h4>
<pre>public final&nbsp;boolean&nbsp;getCaseSensitive()</pre>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;</div>
<div class="block"><p>
 Set true to use case-sensitive search, false - otherwise.
 Read/write <code>boolean</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITextHighlightingOptions.html#getCaseSensitive--">getCaseSensitive</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITextHighlightingOptions.html" title="interface in com.aspose.slides">ITextHighlightingOptions</a></code></dd>
</dl>
</li>
</ul>
<a name="setCaseSensitive-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCaseSensitive</h4>
<pre>public final&nbsp;void&nbsp;setCaseSensitive(boolean&nbsp;value)</pre>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;</div>
<div class="block"><p>
 Set true to use case-sensitive search, false - otherwise.
 Read/write <code>boolean</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITextHighlightingOptions.html#setCaseSensitive-boolean-">setCaseSensitive</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITextHighlightingOptions.html" title="interface in com.aspose.slides">ITextHighlightingOptions</a></code></dd>
</dl>
</li>
</ul>
<a name="getWholeWordsOnly--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWholeWordsOnly</h4>
<pre>public final&nbsp;boolean&nbsp;getWholeWordsOnly()</pre>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;</div>
<div class="block"><p>
 Set true to match only whole words, false - otherwise.
 Read/write <code>boolean</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITextHighlightingOptions.html#getWholeWordsOnly--">getWholeWordsOnly</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITextHighlightingOptions.html" title="interface in com.aspose.slides">ITextHighlightingOptions</a></code></dd>
</dl>
</li>
</ul>
<a name="setWholeWordsOnly-boolean-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setWholeWordsOnly</h4>
<pre>public final&nbsp;void&nbsp;setWholeWordsOnly(boolean&nbsp;value)</pre>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;</div>
<div class="block"><p>
 Set true to match only whole words, false - otherwise.
 Read/write <code>boolean</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITextHighlightingOptions.html#setWholeWordsOnly-boolean-">setWholeWordsOnly</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITextHighlightingOptions.html" title="interface in com.aspose.slides">ITextHighlightingOptions</a></code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/TextFrameFormat.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/TextInheritanceLimit.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/TextHighlightingOptions.html" target="_top">Frames</a></li>
<li><a href="TextHighlightingOptions.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><i>Copyright &copy; 2004-2025 Aspose Pty Ltd. All Rights Reserved.</i></small></p>
</body>
</html>
