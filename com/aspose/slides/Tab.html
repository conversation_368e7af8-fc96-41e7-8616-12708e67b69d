<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_421) on Fri Jul 11 05:32:44 CDT 2025 -->
<title>Tab (Aspose.Slides for Java (25.7) API Documentation)</title>
<meta name="date" content="2025-07-11">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Tab (Aspose.Slides for Java (25.7) API Documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/SystemColor.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/TabAlignment.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/Tab.html" target="_top">Frames</a></li>
<li><a href="Tab.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.aspose.slides</div>
<h2 title="Class Tab" class="title">Class Tab</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../com/aspose/slides/PVIObject.html" title="class in com.aspose.slides">com.aspose.slides.PVIObject</a></li>
<li>
<ul class="inheritance">
<li>com.aspose.slides.Tab</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../com/aspose/slides/IPresentationComponent.html" title="interface in com.aspose.slides">IPresentationComponent</a>, <a href="../../../com/aspose/slides/ISlideComponent.html" title="interface in com.aspose.slides">ISlideComponent</a>, <a href="../../../com/aspose/slides/ITab.html" title="interface in com.aspose.slides">ITab</a>, java.lang.Comparable</dd>
</dl>
<hr>
<br>
<pre>public final class <span class="typeNameLabel">Tab</span>
extends <a href="../../../com/aspose/slides/PVIObject.html" title="class in com.aspose.slides">PVIObject</a>
implements <a href="../../../com/aspose/slides/ITab.html" title="interface in com.aspose.slides">ITab</a></pre>
<div class="block"><p>
 Represents a tabulation for a text.
 </p></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Tab.html#Tab-double-int-">Tab</a></span>(double&nbsp;position,
   int&nbsp;align)</code>
<div class="block">
 Creates new Tab</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Tab.html#compareTo-java.lang.Object-">compareTo</a></span>(java.lang.Object&nbsp;obj)</code>
<div class="block">
 Compares the current instance with another object of the same type.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Tab.html#getAlignment--">getAlignment</a></span>()</code>
<div class="block">
 Returns or sets align style of a tab.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Tab.html#getPosition--">getPosition</a></span>()</code>
<div class="block">
 Returns or sets position of a tab.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Tab.html#getVersion--">getVersion</a></span>()</code>
<div class="block">
 Version.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Tab.html#setAlignment-int-">setAlignment</a></span>(int&nbsp;value)</code>
<div class="block">
 Returns or sets align style of a tab.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Tab.html#setPosition-double-">setPosition</a></span>(double&nbsp;value)</code>
<div class="block">
 Returns or sets position of a tab.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.aspose.slides.PVIObject">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;com.aspose.slides.<a href="../../../com/aspose/slides/PVIObject.html" title="class in com.aspose.slides">PVIObject</a></h3>
<code><a href="../../../com/aspose/slides/PVIObject.html#equals-java.lang.Object-">equals</a>, <a href="../../../com/aspose/slides/PVIObject.html#getParent_Immediate--">getParent_Immediate</a>, <a href="../../../com/aspose/slides/PVIObject.html#getParent_IPresentationComponent--">getParent_IPresentationComponent</a>, <a href="../../../com/aspose/slides/PVIObject.html#getParent_ISlideComponent--">getParent_ISlideComponent</a>, <a href="../../../com/aspose/slides/PVIObject.html#getPresentation--">getPresentation</a>, <a href="../../../com/aspose/slides/PVIObject.html#getSlide--">getSlide</a>, <a href="../../../com/aspose/slides/PVIObject.html#hashCode--">hashCode</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>getClass, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="Tab-double-int-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>Tab</h4>
<pre>public&nbsp;Tab(double&nbsp;position,
           int&nbsp;align)</pre>
<div class="block"><p>
 Creates new Tab
 </p></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>position</code> - Tab position.</dd>
<dd><code>align</code> - Align.</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getVersion--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVersion</h4>
<pre>public&nbsp;long&nbsp;getVersion()</pre>
<div class="block"><p>
 Version.
 Read-only <code>long</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../com/aspose/slides/PVIObject.html#getVersion--">getVersion</a></code>&nbsp;in class&nbsp;<code><a href="../../../com/aspose/slides/PVIObject.html" title="class in com.aspose.slides">PVIObject</a></code></dd>
</dl>
</li>
</ul>
<a name="getPosition--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPosition</h4>
<pre>public final&nbsp;double&nbsp;getPosition()</pre>
<div class="block"><p>
 Returns or sets position of a tab.
 Assigning this property can change tab's index in collection and invalidate Enumerator.
 Read/write <code>double</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITab.html#getPosition--">getPosition</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITab.html" title="interface in com.aspose.slides">ITab</a></code></dd>
</dl>
</li>
</ul>
<a name="setPosition-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPosition</h4>
<pre>public final&nbsp;void&nbsp;setPosition(double&nbsp;value)</pre>
<div class="block"><p>
 Returns or sets position of a tab.
 Assigning this property can change tab's index in collection and invalidate Enumerator.
 Read/write <code>double</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITab.html#setPosition-double-">setPosition</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITab.html" title="interface in com.aspose.slides">ITab</a></code></dd>
</dl>
</li>
</ul>
<a name="getAlignment--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAlignment</h4>
<pre>public final&nbsp;int&nbsp;getAlignment()</pre>
<div class="block"><p>
 Returns or sets align style of a tab.
 Read/write <a href="../../../com/aspose/slides/TabAlignment.html" title="class in com.aspose.slides"><code>TabAlignment</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITab.html#getAlignment--">getAlignment</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITab.html" title="interface in com.aspose.slides">ITab</a></code></dd>
</dl>
</li>
</ul>
<a name="setAlignment-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAlignment</h4>
<pre>public final&nbsp;void&nbsp;setAlignment(int&nbsp;value)</pre>
<div class="block"><p>
 Returns or sets align style of a tab.
 Read/write <a href="../../../com/aspose/slides/TabAlignment.html" title="class in com.aspose.slides"><code>TabAlignment</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITab.html#setAlignment-int-">setAlignment</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITab.html" title="interface in com.aspose.slides">ITab</a></code></dd>
</dl>
</li>
</ul>
<a name="compareTo-java.lang.Object-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>compareTo</h4>
<pre>public final&nbsp;int&nbsp;compareTo(java.lang.Object&nbsp;obj)</pre>
<div class="block"><p>
 Compares the current instance with another object of the same type.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>compareTo</code>&nbsp;in interface&nbsp;<code>java.lang.Comparable</code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>obj</code> - An object to compare with this instance.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>A 32-bit integer that indicates the relative order of the comparands.
 The return value has these meanings:
 <code>&lt;UL&gt;
 &lt;LI&gt; &amp;lt; 0 - This instance is less than obj.&lt;/LI&gt;
 &lt;LI&gt; = 0 - This instance is equal to obj.&lt;/LI&gt;
 &lt;LI&gt; &amp;gt; 0 - This instance is greater than obj.&lt;/LI&gt;
 &lt;/UL&gt;</code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/SystemColor.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/TabAlignment.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/Tab.html" target="_top">Frames</a></li>
<li><a href="Tab.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><i>Copyright &copy; 2004-2025 Aspose Pty Ltd. All Rights Reserved.</i></small></p>
</body>
</html>
