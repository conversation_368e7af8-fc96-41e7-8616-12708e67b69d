<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_421) on Fri Jul 11 05:32:44 CDT 2025 -->
<title>SwfOptions (Aspose.Slides for Java (25.7) API Documentation)</title>
<meta name="date" content="2025-07-11">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="SwfOptions (Aspose.Slides for Java (25.7) API Documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":42,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10,"i26":10,"i27":10,"i28":10,"i29":10,"i30":10,"i31":10,"i32":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"],32:["t6","Deprecated Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/SvgTSpan.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/SystemColor.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/SwfOptions.html" target="_top">Frames</a></li>
<li><a href="SwfOptions.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.aspose.slides</div>
<h2 title="Class SwfOptions" class="title">Class SwfOptions</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../com/aspose/slides/SaveOptions.html" title="class in com.aspose.slides">com.aspose.slides.SaveOptions</a></li>
<li>
<ul class="inheritance">
<li>com.aspose.slides.SwfOptions</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../com/aspose/slides/ISaveOptions.html" title="interface in com.aspose.slides">ISaveOptions</a>, <a href="../../../com/aspose/slides/ISwfOptions.html" title="interface in com.aspose.slides">ISwfOptions</a></dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">SwfOptions</span>
extends <a href="../../../com/aspose/slides/SaveOptions.html" title="class in com.aspose.slides">SaveOptions</a>
implements <a href="../../../com/aspose/slides/ISwfOptions.html" title="interface in com.aspose.slides">ISwfOptions</a></pre>
<div class="block"><p>
 Provides options that control how a presentation is saved in Swf format.
 </p><p><hr><blockquote><pre>
 The following example shows how to convert PowerPoint to SWF Flash.
 <pre>
 // Instantiate a Presentation object that represents a presentation file
 Presentation pres = new Presentation("HelloWorld.pptx");
 try {
     SwfOptions swfOptions = new SwfOptions();
     swfOptions.setViewerIncluded(false);
     INotesCommentsLayoutingOptions notesOptions = swfOptions.getNotesCommentsLayouting();
     notesOptions.setNotesPosition(NotesPositions.BottomFull);
     // Saving presentation and notes pages
     pres.save("SaveAsSwf_out.swf", SaveFormat.Swf, swfOptions);
     swfOptions.setViewerIncluded(true);
     pres.save("SaveNotes_out.swf", SaveFormat.Swf, swfOptions);
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SwfOptions.html#SwfOptions--">SwfOptions</a></span>()</code>
<div class="block">
 Default constructor.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t6" class="tableTab"><span><a href="javascript:show(32);">Deprecated Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SwfOptions.html#getCompressed--">getCompressed</a></span>()</code>
<div class="block">
 Specifies whether the generated SWF document should be compressed or not.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SwfOptions.html#getEnableContextMenu--">getEnableContextMenu</a></span>()</code>
<div class="block">
 Enable/disable context menu.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SwfOptions.html#getJpegQuality--">getJpegQuality</a></span>()</code>
<div class="block">
 Specifies the quality of JPEG images.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SwfOptions.html#getLogoImageBytes--">getLogoImageBytes</a></span>()</code>
<div class="block">
 Image that will be displayed as logo in the top right corner of the viewer.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SwfOptions.html#getLogoLink--">getLogoLink</a></span>()</code>
<div class="block">
 Gets or sets the full hyperlink address for a logo.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/INotesCommentsLayoutingOptions.html" title="interface in com.aspose.slides">INotesCommentsLayoutingOptions</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SwfOptions.html#getNotesCommentsLayouting--">getNotesCommentsLayouting</a></span>()</code>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;
<div class="block"><span class="deprecationComment">Use SlidesLayoutOptions property. The property NotesCommentsLayouting will be removed after release of version 25.8.</span></div>
</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SwfOptions.html#getShowBottomPane--">getShowBottomPane</a></span>()</code>
<div class="block">
 Show/hide bottom pane.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SwfOptions.html#getShowFullScreen--">getShowFullScreen</a></span>()</code>
<div class="block">
 Show/hide fullscreen button.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SwfOptions.html#getShowHiddenSlides--">getShowHiddenSlides</a></span>()</code>
<div class="block">
 Specifies whether the generated document should include hidden slides or not.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SwfOptions.html#getShowLeftPane--">getShowLeftPane</a></span>()</code>
<div class="block">
 Show/hide left pane.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SwfOptions.html#getShowPageBorder--">getShowPageBorder</a></span>()</code>
<div class="block">
 Specifies whether border around pages should be shown.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SwfOptions.html#getShowPageStepper--">getShowPageStepper</a></span>()</code>
<div class="block">
 Show/hide page stepper.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SwfOptions.html#getShowSearch--">getShowSearch</a></span>()</code>
<div class="block">
 Show/hide search section.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SwfOptions.html#getShowTopPane--">getShowTopPane</a></span>()</code>
<div class="block">
 Show/hide whole top pane.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/ISlidesLayoutOptions.html" title="interface in com.aspose.slides">ISlidesLayoutOptions</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SwfOptions.html#getSlidesLayoutOptions--">getSlidesLayoutOptions</a></span>()</code>
<div class="block">
 Gets or sets the mode in which slides are placed on the page when exporting a presentation <a href="../../../com/aspose/slides/ISlidesLayoutOptions.html" title="interface in com.aspose.slides"><code>ISlidesLayoutOptions</code></a>.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SwfOptions.html#getStartOpenLeftPane--">getStartOpenLeftPane</a></span>()</code>
<div class="block">
 Start with opened left pane.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SwfOptions.html#getViewerIncluded--">getViewerIncluded</a></span>()</code>
<div class="block">
 Specifies whether the generated SWF document should include the integrated document viewer or not.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SwfOptions.html#setCompressed-boolean-">setCompressed</a></span>(boolean&nbsp;value)</code>
<div class="block">
 Specifies whether the generated SWF document should be compressed or not.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SwfOptions.html#setEnableContextMenu-boolean-">setEnableContextMenu</a></span>(boolean&nbsp;value)</code>
<div class="block">
 Enable/disable context menu.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SwfOptions.html#setJpegQuality-int-">setJpegQuality</a></span>(int&nbsp;value)</code>
<div class="block">
 Specifies the quality of JPEG images.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SwfOptions.html#setLogoImageBytes-byte:A-">setLogoImageBytes</a></span>(byte[]&nbsp;value)</code>
<div class="block">
 Image that will be displayed as logo in the top right corner of the viewer.</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SwfOptions.html#setLogoLink-java.lang.String-">setLogoLink</a></span>(java.lang.String&nbsp;value)</code>
<div class="block">
 Gets or sets the full hyperlink address for a logo.</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SwfOptions.html#setShowBottomPane-boolean-">setShowBottomPane</a></span>(boolean&nbsp;value)</code>
<div class="block">
 Show/hide bottom pane.</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SwfOptions.html#setShowFullScreen-boolean-">setShowFullScreen</a></span>(boolean&nbsp;value)</code>
<div class="block">
 Show/hide fullscreen button.</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SwfOptions.html#setShowHiddenSlides-boolean-">setShowHiddenSlides</a></span>(boolean&nbsp;value)</code>
<div class="block">
 Specifies whether the generated document should include hidden slides or not.</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SwfOptions.html#setShowLeftPane-boolean-">setShowLeftPane</a></span>(boolean&nbsp;value)</code>
<div class="block">
 Show/hide left pane.</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SwfOptions.html#setShowPageBorder-boolean-">setShowPageBorder</a></span>(boolean&nbsp;value)</code>
<div class="block">
 Specifies whether border around pages should be shown.</div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SwfOptions.html#setShowPageStepper-boolean-">setShowPageStepper</a></span>(boolean&nbsp;value)</code>
<div class="block">
 Show/hide page stepper.</div>
</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SwfOptions.html#setShowSearch-boolean-">setShowSearch</a></span>(boolean&nbsp;value)</code>
<div class="block">
 Show/hide search section.</div>
</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SwfOptions.html#setShowTopPane-boolean-">setShowTopPane</a></span>(boolean&nbsp;value)</code>
<div class="block">
 Show/hide whole top pane.</div>
</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SwfOptions.html#setSlidesLayoutOptions-com.aspose.slides.ISlidesLayoutOptions-">setSlidesLayoutOptions</a></span>(<a href="../../../com/aspose/slides/ISlidesLayoutOptions.html" title="interface in com.aspose.slides">ISlidesLayoutOptions</a>&nbsp;value)</code>
<div class="block">
 Gets or sets the mode in which slides are placed on the page when exporting a presentation <a href="../../../com/aspose/slides/ISlidesLayoutOptions.html" title="interface in com.aspose.slides"><code>ISlidesLayoutOptions</code></a>.</div>
</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SwfOptions.html#setStartOpenLeftPane-boolean-">setStartOpenLeftPane</a></span>(boolean&nbsp;value)</code>
<div class="block">
 Start with opened left pane.</div>
</td>
</tr>
<tr id="i32" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SwfOptions.html#setViewerIncluded-boolean-">setViewerIncluded</a></span>(boolean&nbsp;value)</code>
<div class="block">
 Specifies whether the generated SWF document should include the integrated document viewer or not.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.aspose.slides.SaveOptions">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;com.aspose.slides.<a href="../../../com/aspose/slides/SaveOptions.html" title="class in com.aspose.slides">SaveOptions</a></h3>
<code><a href="../../../com/aspose/slides/SaveOptions.html#getDefaultRegularFont--">getDefaultRegularFont</a>, <a href="../../../com/aspose/slides/SaveOptions.html#getGradientStyle--">getGradientStyle</a>, <a href="../../../com/aspose/slides/SaveOptions.html#getProgressCallback--">getProgressCallback</a>, <a href="../../../com/aspose/slides/SaveOptions.html#getSkipJavaScriptLinks--">getSkipJavaScriptLinks</a>, <a href="../../../com/aspose/slides/SaveOptions.html#getWarningCallback--">getWarningCallback</a>, <a href="../../../com/aspose/slides/SaveOptions.html#setDefaultRegularFont-java.lang.String-">setDefaultRegularFont</a>, <a href="../../../com/aspose/slides/SaveOptions.html#setGradientStyle-int-">setGradientStyle</a>, <a href="../../../com/aspose/slides/SaveOptions.html#setProgressCallback-com.aspose.slides.IProgressCallback-">setProgressCallback</a>, <a href="../../../com/aspose/slides/SaveOptions.html#setSkipJavaScriptLinks-boolean-">setSkipJavaScriptLinks</a>, <a href="../../../com/aspose/slides/SaveOptions.html#setWarningCallback-com.aspose.slides.IWarningCallback-">setWarningCallback</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.aspose.slides.ISaveOptions">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;com.aspose.slides.<a href="../../../com/aspose/slides/ISaveOptions.html" title="interface in com.aspose.slides">ISaveOptions</a></h3>
<code><a href="../../../com/aspose/slides/ISaveOptions.html#getDefaultRegularFont--">getDefaultRegularFont</a>, <a href="../../../com/aspose/slides/ISaveOptions.html#getGradientStyle--">getGradientStyle</a>, <a href="../../../com/aspose/slides/ISaveOptions.html#getProgressCallback--">getProgressCallback</a>, <a href="../../../com/aspose/slides/ISaveOptions.html#getSkipJavaScriptLinks--">getSkipJavaScriptLinks</a>, <a href="../../../com/aspose/slides/ISaveOptions.html#getWarningCallback--">getWarningCallback</a>, <a href="../../../com/aspose/slides/ISaveOptions.html#setDefaultRegularFont-java.lang.String-">setDefaultRegularFont</a>, <a href="../../../com/aspose/slides/ISaveOptions.html#setGradientStyle-int-">setGradientStyle</a>, <a href="../../../com/aspose/slides/ISaveOptions.html#setProgressCallback-com.aspose.slides.IProgressCallback-">setProgressCallback</a>, <a href="../../../com/aspose/slides/ISaveOptions.html#setSkipJavaScriptLinks-boolean-">setSkipJavaScriptLinks</a>, <a href="../../../com/aspose/slides/ISaveOptions.html#setWarningCallback-com.aspose.slides.IWarningCallback-">setWarningCallback</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="SwfOptions--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>SwfOptions</h4>
<pre>public&nbsp;SwfOptions()</pre>
<div class="block"><p>
 Default constructor.
 </p></div>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getShowHiddenSlides--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getShowHiddenSlides</h4>
<pre>public final&nbsp;boolean&nbsp;getShowHiddenSlides()</pre>
<div class="block"><p>
 Specifies whether the generated document should include hidden slides or not.
 Default is false.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISwfOptions.html#getShowHiddenSlides--">getShowHiddenSlides</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISwfOptions.html" title="interface in com.aspose.slides">ISwfOptions</a></code></dd>
</dl>
</li>
</ul>
<a name="setShowHiddenSlides-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setShowHiddenSlides</h4>
<pre>public final&nbsp;void&nbsp;setShowHiddenSlides(boolean&nbsp;value)</pre>
<div class="block"><p>
 Specifies whether the generated document should include hidden slides or not.
 Default is false.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISwfOptions.html#setShowHiddenSlides-boolean-">setShowHiddenSlides</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISwfOptions.html" title="interface in com.aspose.slides">ISwfOptions</a></code></dd>
</dl>
</li>
</ul>
<a name="getCompressed--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCompressed</h4>
<pre>public final&nbsp;boolean&nbsp;getCompressed()</pre>
<div class="block"><p>
 Specifies whether the generated SWF document should be compressed or not.
 Default is true.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISwfOptions.html#getCompressed--">getCompressed</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISwfOptions.html" title="interface in com.aspose.slides">ISwfOptions</a></code></dd>
</dl>
</li>
</ul>
<a name="setCompressed-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCompressed</h4>
<pre>public final&nbsp;void&nbsp;setCompressed(boolean&nbsp;value)</pre>
<div class="block"><p>
 Specifies whether the generated SWF document should be compressed or not.
 Default is true.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISwfOptions.html#setCompressed-boolean-">setCompressed</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISwfOptions.html" title="interface in com.aspose.slides">ISwfOptions</a></code></dd>
</dl>
</li>
</ul>
<a name="getViewerIncluded--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getViewerIncluded</h4>
<pre>public final&nbsp;boolean&nbsp;getViewerIncluded()</pre>
<div class="block"><p>
 Specifies whether the generated SWF document should include the integrated document viewer or not.
 Default is true.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISwfOptions.html#getViewerIncluded--">getViewerIncluded</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISwfOptions.html" title="interface in com.aspose.slides">ISwfOptions</a></code></dd>
</dl>
</li>
</ul>
<a name="setViewerIncluded-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setViewerIncluded</h4>
<pre>public final&nbsp;void&nbsp;setViewerIncluded(boolean&nbsp;value)</pre>
<div class="block"><p>
 Specifies whether the generated SWF document should include the integrated document viewer or not.
 Default is true.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISwfOptions.html#setViewerIncluded-boolean-">setViewerIncluded</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISwfOptions.html" title="interface in com.aspose.slides">ISwfOptions</a></code></dd>
</dl>
</li>
</ul>
<a name="getShowPageBorder--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getShowPageBorder</h4>
<pre>public final&nbsp;boolean&nbsp;getShowPageBorder()</pre>
<div class="block"><p>
 Specifies whether border around pages should be shown. Default is true. 
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISwfOptions.html#getShowPageBorder--">getShowPageBorder</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISwfOptions.html" title="interface in com.aspose.slides">ISwfOptions</a></code></dd>
</dl>
</li>
</ul>
<a name="setShowPageBorder-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setShowPageBorder</h4>
<pre>public final&nbsp;void&nbsp;setShowPageBorder(boolean&nbsp;value)</pre>
<div class="block"><p>
 Specifies whether border around pages should be shown. Default is true. 
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISwfOptions.html#setShowPageBorder-boolean-">setShowPageBorder</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISwfOptions.html" title="interface in com.aspose.slides">ISwfOptions</a></code></dd>
</dl>
</li>
</ul>
<a name="getShowFullScreen--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getShowFullScreen</h4>
<pre>public final&nbsp;boolean&nbsp;getShowFullScreen()</pre>
<div class="block"><p>
 Show/hide fullscreen button. Can be overridden in flashvars. Default is true. 
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISwfOptions.html#getShowFullScreen--">getShowFullScreen</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISwfOptions.html" title="interface in com.aspose.slides">ISwfOptions</a></code></dd>
</dl>
</li>
</ul>
<a name="setShowFullScreen-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setShowFullScreen</h4>
<pre>public final&nbsp;void&nbsp;setShowFullScreen(boolean&nbsp;value)</pre>
<div class="block"><p>
 Show/hide fullscreen button. Can be overridden in flashvars. Default is true. 
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISwfOptions.html#setShowFullScreen-boolean-">setShowFullScreen</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISwfOptions.html" title="interface in com.aspose.slides">ISwfOptions</a></code></dd>
</dl>
</li>
</ul>
<a name="getShowPageStepper--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getShowPageStepper</h4>
<pre>public final&nbsp;boolean&nbsp;getShowPageStepper()</pre>
<div class="block"><p>
 Show/hide page stepper. Can be overridden in flashvars. Default is true. 
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISwfOptions.html#getShowPageStepper--">getShowPageStepper</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISwfOptions.html" title="interface in com.aspose.slides">ISwfOptions</a></code></dd>
</dl>
</li>
</ul>
<a name="setShowPageStepper-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setShowPageStepper</h4>
<pre>public final&nbsp;void&nbsp;setShowPageStepper(boolean&nbsp;value)</pre>
<div class="block"><p>
 Show/hide page stepper. Can be overridden in flashvars. Default is true. 
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISwfOptions.html#setShowPageStepper-boolean-">setShowPageStepper</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISwfOptions.html" title="interface in com.aspose.slides">ISwfOptions</a></code></dd>
</dl>
</li>
</ul>
<a name="getShowSearch--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getShowSearch</h4>
<pre>public final&nbsp;boolean&nbsp;getShowSearch()</pre>
<div class="block"><p>
 Show/hide search section. Can be overridden in flashvars. Default is true. 
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISwfOptions.html#getShowSearch--">getShowSearch</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISwfOptions.html" title="interface in com.aspose.slides">ISwfOptions</a></code></dd>
</dl>
</li>
</ul>
<a name="setShowSearch-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setShowSearch</h4>
<pre>public final&nbsp;void&nbsp;setShowSearch(boolean&nbsp;value)</pre>
<div class="block"><p>
 Show/hide search section. Can be overridden in flashvars. Default is true. 
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISwfOptions.html#setShowSearch-boolean-">setShowSearch</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISwfOptions.html" title="interface in com.aspose.slides">ISwfOptions</a></code></dd>
</dl>
</li>
</ul>
<a name="getShowTopPane--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getShowTopPane</h4>
<pre>public final&nbsp;boolean&nbsp;getShowTopPane()</pre>
<div class="block"><p>
 Show/hide whole top pane. Can be overridden in flashvars. Default is true. 
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISwfOptions.html#getShowTopPane--">getShowTopPane</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISwfOptions.html" title="interface in com.aspose.slides">ISwfOptions</a></code></dd>
</dl>
</li>
</ul>
<a name="setShowTopPane-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setShowTopPane</h4>
<pre>public final&nbsp;void&nbsp;setShowTopPane(boolean&nbsp;value)</pre>
<div class="block"><p>
 Show/hide whole top pane. Can be overridden in flashvars. Default is true. 
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISwfOptions.html#setShowTopPane-boolean-">setShowTopPane</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISwfOptions.html" title="interface in com.aspose.slides">ISwfOptions</a></code></dd>
</dl>
</li>
</ul>
<a name="getShowBottomPane--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getShowBottomPane</h4>
<pre>public final&nbsp;boolean&nbsp;getShowBottomPane()</pre>
<div class="block"><p>
 Show/hide bottom pane. Can be overridden in flashvars. Default is true. 
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISwfOptions.html#getShowBottomPane--">getShowBottomPane</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISwfOptions.html" title="interface in com.aspose.slides">ISwfOptions</a></code></dd>
</dl>
</li>
</ul>
<a name="setShowBottomPane-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setShowBottomPane</h4>
<pre>public final&nbsp;void&nbsp;setShowBottomPane(boolean&nbsp;value)</pre>
<div class="block"><p>
 Show/hide bottom pane. Can be overridden in flashvars. Default is true. 
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISwfOptions.html#setShowBottomPane-boolean-">setShowBottomPane</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISwfOptions.html" title="interface in com.aspose.slides">ISwfOptions</a></code></dd>
</dl>
</li>
</ul>
<a name="getShowLeftPane--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getShowLeftPane</h4>
<pre>public final&nbsp;boolean&nbsp;getShowLeftPane()</pre>
<div class="block"><p>
 Show/hide left pane. Can be overridden in flashvars. Default is true. 
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISwfOptions.html#getShowLeftPane--">getShowLeftPane</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISwfOptions.html" title="interface in com.aspose.slides">ISwfOptions</a></code></dd>
</dl>
</li>
</ul>
<a name="setShowLeftPane-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setShowLeftPane</h4>
<pre>public final&nbsp;void&nbsp;setShowLeftPane(boolean&nbsp;value)</pre>
<div class="block"><p>
 Show/hide left pane. Can be overridden in flashvars. Default is true. 
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISwfOptions.html#setShowLeftPane-boolean-">setShowLeftPane</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISwfOptions.html" title="interface in com.aspose.slides">ISwfOptions</a></code></dd>
</dl>
</li>
</ul>
<a name="getStartOpenLeftPane--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStartOpenLeftPane</h4>
<pre>public final&nbsp;boolean&nbsp;getStartOpenLeftPane()</pre>
<div class="block"><p>
 Start with opened left pane. Can be overridden in flashvars. Default is false. 
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISwfOptions.html#getStartOpenLeftPane--">getStartOpenLeftPane</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISwfOptions.html" title="interface in com.aspose.slides">ISwfOptions</a></code></dd>
</dl>
</li>
</ul>
<a name="setStartOpenLeftPane-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setStartOpenLeftPane</h4>
<pre>public final&nbsp;void&nbsp;setStartOpenLeftPane(boolean&nbsp;value)</pre>
<div class="block"><p>
 Start with opened left pane. Can be overridden in flashvars. Default is false. 
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISwfOptions.html#setStartOpenLeftPane-boolean-">setStartOpenLeftPane</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISwfOptions.html" title="interface in com.aspose.slides">ISwfOptions</a></code></dd>
</dl>
</li>
</ul>
<a name="getEnableContextMenu--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEnableContextMenu</h4>
<pre>public final&nbsp;boolean&nbsp;getEnableContextMenu()</pre>
<div class="block"><p>
 Enable/disable context menu. Default is true. 
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISwfOptions.html#getEnableContextMenu--">getEnableContextMenu</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISwfOptions.html" title="interface in com.aspose.slides">ISwfOptions</a></code></dd>
</dl>
</li>
</ul>
<a name="setEnableContextMenu-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEnableContextMenu</h4>
<pre>public final&nbsp;void&nbsp;setEnableContextMenu(boolean&nbsp;value)</pre>
<div class="block"><p>
 Enable/disable context menu. Default is true. 
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISwfOptions.html#setEnableContextMenu-boolean-">setEnableContextMenu</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISwfOptions.html" title="interface in com.aspose.slides">ISwfOptions</a></code></dd>
</dl>
</li>
</ul>
<a name="getLogoImageBytes--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLogoImageBytes</h4>
<pre>public final&nbsp;byte[]&nbsp;getLogoImageBytes()</pre>
<div class="block"><p>
 Image that will be displayed as logo in the top right corner of the viewer.
 Image should be 32x64 pixels PNG image, otherwise logo can be displayed improperly. 
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISwfOptions.html#getLogoImageBytes--">getLogoImageBytes</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISwfOptions.html" title="interface in com.aspose.slides">ISwfOptions</a></code></dd>
</dl>
</li>
</ul>
<a name="setLogoImageBytes-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLogoImageBytes</h4>
<pre>public final&nbsp;void&nbsp;setLogoImageBytes(byte[]&nbsp;value)</pre>
<div class="block"><p>
 Image that will be displayed as logo in the top right corner of the viewer.
 Image should be 32x64 pixels PNG image, otherwise logo can be displayed improperly. 
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISwfOptions.html#setLogoImageBytes-byte:A-">setLogoImageBytes</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISwfOptions.html" title="interface in com.aspose.slides">ISwfOptions</a></code></dd>
</dl>
</li>
</ul>
<a name="getLogoLink--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLogoLink</h4>
<pre>public final&nbsp;java.lang.String&nbsp;getLogoLink()</pre>
<div class="block"><p>
 Gets or sets the full hyperlink address for a logo.
 Has an effect only if a (<a href="../../../com/aspose/slides/SwfOptions.html#getLogoImageBytes--"><code>getLogoImageBytes()</code></a>/<a href="../../../com/aspose/slides/SwfOptions.html#setLogoImageBytes-byte:A-"><code>setLogoImageBytes(byte[])</code></a>) is specified.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISwfOptions.html#getLogoLink--">getLogoLink</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISwfOptions.html" title="interface in com.aspose.slides">ISwfOptions</a></code></dd>
</dl>
</li>
</ul>
<a name="setLogoLink-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLogoLink</h4>
<pre>public final&nbsp;void&nbsp;setLogoLink(java.lang.String&nbsp;value)</pre>
<div class="block"><p>
 Gets or sets the full hyperlink address for a logo.
 Has an effect only if a (<a href="../../../com/aspose/slides/SwfOptions.html#getLogoImageBytes--"><code>getLogoImageBytes()</code></a>/<a href="../../../com/aspose/slides/SwfOptions.html#setLogoImageBytes-byte:A-"><code>setLogoImageBytes(byte[])</code></a>) is specified.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISwfOptions.html#setLogoLink-java.lang.String-">setLogoLink</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISwfOptions.html" title="interface in com.aspose.slides">ISwfOptions</a></code></dd>
</dl>
</li>
</ul>
<a name="getJpegQuality--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getJpegQuality</h4>
<pre>public final&nbsp;int&nbsp;getJpegQuality()</pre>
<div class="block"><p>
 Specifies the quality of JPEG images.
 Default is 95.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISwfOptions.html#getJpegQuality--">getJpegQuality</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISwfOptions.html" title="interface in com.aspose.slides">ISwfOptions</a></code></dd>
</dl>
</li>
</ul>
<a name="setJpegQuality-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setJpegQuality</h4>
<pre>public final&nbsp;void&nbsp;setJpegQuality(int&nbsp;value)</pre>
<div class="block"><p>
 Specifies the quality of JPEG images.
 Default is 95.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISwfOptions.html#setJpegQuality-int-">setJpegQuality</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISwfOptions.html" title="interface in com.aspose.slides">ISwfOptions</a></code></dd>
</dl>
</li>
</ul>
<a name="getNotesCommentsLayouting--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNotesCommentsLayouting</h4>
<pre>@Deprecated
public final&nbsp;<a href="../../../com/aspose/slides/INotesCommentsLayoutingOptions.html" title="interface in com.aspose.slides">INotesCommentsLayoutingOptions</a>&nbsp;getNotesCommentsLayouting()</pre>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;<span class="deprecationComment">Use SlidesLayoutOptions property. The property NotesCommentsLayouting will be removed after release of version 25.8.</span></div>
<div class="block"><p>
 Provides options that control how notes and comments is placed in exported document.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISwfOptions.html#getNotesCommentsLayouting--">getNotesCommentsLayouting</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISwfOptions.html" title="interface in com.aspose.slides">ISwfOptions</a></code></dd>
</dl>
</li>
</ul>
<a name="getSlidesLayoutOptions--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSlidesLayoutOptions</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/ISlidesLayoutOptions.html" title="interface in com.aspose.slides">ISlidesLayoutOptions</a>&nbsp;getSlidesLayoutOptions()</pre>
<div class="block"><p>
 Gets or sets the mode in which slides are placed on the page when exporting a presentation <a href="../../../com/aspose/slides/ISlidesLayoutOptions.html" title="interface in com.aspose.slides"><code>ISlidesLayoutOptions</code></a>. 
 This property doesn't support assigning objects of type <a href="../../../com/aspose/slides/HandoutLayoutingOptions.html" title="class in com.aspose.slides"><code>HandoutLayoutingOptions</code></a>
 </p><p><hr><blockquote><pre>Example:
 <pre>
 Presentation pres = new Presentation("pres.pptx");
 try {
     NotesCommentsLayoutingOptions notesOptions = new NotesCommentsLayoutingOptions();
     notesOptions.setCommentsPosition(CommentsPositions.Right);

     SwfOptions options = new SwfOptions();
     options.setSlidesLayoutOptions(notesOptions);

     pres.save("pres.swf", SaveFormat.Swf, options);
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISwfOptions.html#getSlidesLayoutOptions--">getSlidesLayoutOptions</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISwfOptions.html" title="interface in com.aspose.slides">ISwfOptions</a></code></dd>
</dl>
</li>
</ul>
<a name="setSlidesLayoutOptions-com.aspose.slides.ISlidesLayoutOptions-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setSlidesLayoutOptions</h4>
<pre>public final&nbsp;void&nbsp;setSlidesLayoutOptions(<a href="../../../com/aspose/slides/ISlidesLayoutOptions.html" title="interface in com.aspose.slides">ISlidesLayoutOptions</a>&nbsp;value)</pre>
<div class="block"><p>
 Gets or sets the mode in which slides are placed on the page when exporting a presentation <a href="../../../com/aspose/slides/ISlidesLayoutOptions.html" title="interface in com.aspose.slides"><code>ISlidesLayoutOptions</code></a>. 
 This property doesn't support assigning objects of type <a href="../../../com/aspose/slides/HandoutLayoutingOptions.html" title="class in com.aspose.slides"><code>HandoutLayoutingOptions</code></a>.
 </p><p><hr><blockquote><pre>Example:
 <pre>
 Presentation pres = new Presentation("pres.pptx");
 try {
     NotesCommentsLayoutingOptions notesOptions = new NotesCommentsLayoutingOptions();
     notesOptions.setCommentsPosition(CommentsPositions.Right);

     SwfOptions options = new SwfOptions();
     options.setSlidesLayoutOptions(notesOptions);

     pres.save("pres.swf", SaveFormat.Swf, options);
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISwfOptions.html#setSlidesLayoutOptions-com.aspose.slides.ISlidesLayoutOptions-">setSlidesLayoutOptions</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISwfOptions.html" title="interface in com.aspose.slides">ISwfOptions</a></code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/SvgTSpan.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/SystemColor.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/SwfOptions.html" target="_top">Frames</a></li>
<li><a href="SwfOptions.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><i>Copyright &copy; 2004-2025 Aspose Pty Ltd. All Rights Reserved.</i></small></p>
</body>
</html>
