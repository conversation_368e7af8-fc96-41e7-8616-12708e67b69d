<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_421) on Fri Jul 11 05:32:44 CDT 2025 -->
<title>TableStylePreset (Aspose.Slides for Java (25.7) API Documentation)</title>
<meta name="date" content="2025-07-11">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="TableStylePreset (Aspose.Slides for Java (25.7) API Documentation)";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/TableFormat.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/TagCollection.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/TableStylePreset.html" target="_top">Frames</a></li>
<li><a href="TableStylePreset.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.classes.inherited.from.class.com.aspose.ms.System.Enum">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#methods.inherited.from.class.com.aspose.ms.System.Enum">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li>Method</li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.aspose.slides</div>
<h2 title="Class TableStylePreset" class="title">Class TableStylePreset</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.aspose.ms.System.ValueType&lt;com.aspose.ms.System.Enum&gt;</li>
<li>
<ul class="inheritance">
<li>com.aspose.ms.System.Enum</li>
<li>
<ul class="inheritance">
<li>com.aspose.slides.TableStylePreset</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public final class <span class="typeNameLabel">TableStylePreset</span>
extends com.aspose.ms.System.Enum</pre>
<div class="block"><p>
 Represents builtin table styles.
 </p></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.com.aspose.ms.System.Enum">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from class&nbsp;com.aspose.ms.System.Enum</h3>
<code>com.aspose.ms.System.Enum.AbstractEnum, com.aspose.ms.System.Enum.FlaggedEnum, com.aspose.ms.System.Enum.ObjectEnum, com.aspose.ms.System.Enum.SimpleEnum</code></li>
</ul>
</li>
</ul>
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TableStylePreset.html#Custom">Custom</a></span></code>
<div class="block">
 Table has a custom style.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TableStylePreset.html#DarkStyle1">DarkStyle1</a></span></code>
<div class="block">
 Dark Style 1.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TableStylePreset.html#DarkStyle1Accent1">DarkStyle1Accent1</a></span></code>
<div class="block">
 Dark Style 1 - Accent 1.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TableStylePreset.html#DarkStyle1Accent2">DarkStyle1Accent2</a></span></code>
<div class="block">
 Dark Style 1 - Accent 2.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TableStylePreset.html#DarkStyle1Accent3">DarkStyle1Accent3</a></span></code>
<div class="block">
 Dark Style 1 - Accent 3.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TableStylePreset.html#DarkStyle1Accent4">DarkStyle1Accent4</a></span></code>
<div class="block">
 Dark Style 1 - Accent 4.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TableStylePreset.html#DarkStyle1Accent5">DarkStyle1Accent5</a></span></code>
<div class="block">
 Dark Style 1 - Accent 5.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TableStylePreset.html#DarkStyle1Accent6">DarkStyle1Accent6</a></span></code>
<div class="block">
 Dark Style 1 - Accent 6.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TableStylePreset.html#DarkStyle2">DarkStyle2</a></span></code>
<div class="block">
 Dark Style 2.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TableStylePreset.html#DarkStyle2Accent1Accent2">DarkStyle2Accent1Accent2</a></span></code>
<div class="block">
 Dark Style 2 - Accent 1/Accent 2.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TableStylePreset.html#DarkStyle2Accent3Accent4">DarkStyle2Accent3Accent4</a></span></code>
<div class="block">
 Dark Style 2 - Accent 3/Accent 4.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TableStylePreset.html#DarkStyle2Accent5Accent6">DarkStyle2Accent5Accent6</a></span></code>
<div class="block">
 Dark Style 2 - Accent 5/Accent 6.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TableStylePreset.html#LightStyle1">LightStyle1</a></span></code>
<div class="block">
 Light Style 1</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TableStylePreset.html#LightStyle1Accent1">LightStyle1Accent1</a></span></code>
<div class="block">
 Light Style 1 - Accent 1.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TableStylePreset.html#LightStyle1Accent2">LightStyle1Accent2</a></span></code>
<div class="block">
 Light Style 1 - Accent 2.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TableStylePreset.html#LightStyle1Accent3">LightStyle1Accent3</a></span></code>
<div class="block">
 Light Style 1 - Accent 3.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TableStylePreset.html#LightStyle1Accent4">LightStyle1Accent4</a></span></code>
<div class="block">
 Light Style 1 - Accent 4.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TableStylePreset.html#LightStyle1Accent5">LightStyle1Accent5</a></span></code>
<div class="block">
 Light Style 1 - Accent 5.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TableStylePreset.html#LightStyle1Accent6">LightStyle1Accent6</a></span></code>
<div class="block">
 Light Style 1 - Accent 6.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TableStylePreset.html#LightStyle2">LightStyle2</a></span></code>
<div class="block">
 Light Style 2.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TableStylePreset.html#LightStyle2Accent1">LightStyle2Accent1</a></span></code>
<div class="block">
 Light Style 2 - Accent 1.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TableStylePreset.html#LightStyle2Accent2">LightStyle2Accent2</a></span></code>
<div class="block">
 Light Style 2 - Accent 2.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TableStylePreset.html#LightStyle2Accent3">LightStyle2Accent3</a></span></code>
<div class="block">
 Light Style 2 - Accent 3.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TableStylePreset.html#LightStyle2Accent4">LightStyle2Accent4</a></span></code>
<div class="block">
 Light Style 2 - Accent 4.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TableStylePreset.html#LightStyle2Accent5">LightStyle2Accent5</a></span></code>
<div class="block">
 Light Style 2 - Accent 5.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TableStylePreset.html#LightStyle2Accent6">LightStyle2Accent6</a></span></code>
<div class="block">
 Light Style 2 - Accent 6.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TableStylePreset.html#LightStyle3">LightStyle3</a></span></code>
<div class="block">
 Light Style 3.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TableStylePreset.html#LightStyle3Accent1">LightStyle3Accent1</a></span></code>
<div class="block">
 Light Style 3 - Accent 1.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TableStylePreset.html#LightStyle3Accent2">LightStyle3Accent2</a></span></code>
<div class="block">
 Light Style 3 - Accent 2.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TableStylePreset.html#LightStyle3Accent3">LightStyle3Accent3</a></span></code>
<div class="block">
 Light Style 3 - Accent 3.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TableStylePreset.html#LightStyle3Accent4">LightStyle3Accent4</a></span></code>
<div class="block">
 Light Style 3 - Accent 4.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TableStylePreset.html#LightStyle3Accent5">LightStyle3Accent5</a></span></code>
<div class="block">
 Light Style 3 - Accent 5.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TableStylePreset.html#LightStyle3Accent6">LightStyle3Accent6</a></span></code>
<div class="block">
 Light Style 3 - Accent 6.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TableStylePreset.html#MediumStyle1">MediumStyle1</a></span></code>
<div class="block">
 Medium Style 1.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TableStylePreset.html#MediumStyle1Accent1">MediumStyle1Accent1</a></span></code>
<div class="block">
 Medium Style 1 - Accent 1.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TableStylePreset.html#MediumStyle1Accent2">MediumStyle1Accent2</a></span></code>
<div class="block">
 Medium Style 1 - Accent 2.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TableStylePreset.html#MediumStyle1Accent3">MediumStyle1Accent3</a></span></code>
<div class="block">
 Medium Style 1 - Accent 3.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TableStylePreset.html#MediumStyle1Accent4">MediumStyle1Accent4</a></span></code>
<div class="block">
 Medium Style 1 - Accent 4.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TableStylePreset.html#MediumStyle1Accent5">MediumStyle1Accent5</a></span></code>
<div class="block">
 Medium Style 1 - Accent 5.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TableStylePreset.html#MediumStyle1Accent6">MediumStyle1Accent6</a></span></code>
<div class="block">
 Medium Style 1 - Accent 6.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TableStylePreset.html#MediumStyle2">MediumStyle2</a></span></code>
<div class="block">
 Medium Style 2.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TableStylePreset.html#MediumStyle2Accent1">MediumStyle2Accent1</a></span></code>
<div class="block">
 Medium Style 2 - Accent 1.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TableStylePreset.html#MediumStyle2Accent2">MediumStyle2Accent2</a></span></code>
<div class="block">
 Medium Style 2 - Accent 2.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TableStylePreset.html#MediumStyle2Accent3">MediumStyle2Accent3</a></span></code>
<div class="block">
 Medium Style 2 - Accent 3.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TableStylePreset.html#MediumStyle2Accent4">MediumStyle2Accent4</a></span></code>
<div class="block">
 Medium Style 2 - Accent 4.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TableStylePreset.html#MediumStyle2Accent5">MediumStyle2Accent5</a></span></code>
<div class="block">
 Medium Style 2 - Accent 5.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TableStylePreset.html#MediumStyle2Accent6">MediumStyle2Accent6</a></span></code>
<div class="block">
 Medium Style 2 - Accent 6.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TableStylePreset.html#MediumStyle3">MediumStyle3</a></span></code>
<div class="block">
 Medium Style 3.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TableStylePreset.html#MediumStyle3Accent1">MediumStyle3Accent1</a></span></code>
<div class="block">
 Medium Style 3 - Accent 1.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TableStylePreset.html#MediumStyle3Accent2">MediumStyle3Accent2</a></span></code>
<div class="block">
 Medium Style 3 - Accent 2.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TableStylePreset.html#MediumStyle3Accent3">MediumStyle3Accent3</a></span></code>
<div class="block">
 Medium Style 3 - Accent 3.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TableStylePreset.html#MediumStyle3Accent4">MediumStyle3Accent4</a></span></code>
<div class="block">
 Medium Style 3 - Accent 4.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TableStylePreset.html#MediumStyle3Accent5">MediumStyle3Accent5</a></span></code>
<div class="block">
 Medium Style 3 - Accent 5.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TableStylePreset.html#MediumStyle3Accent6">MediumStyle3Accent6</a></span></code>
<div class="block">
 Medium Style 3 - Accent 6.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TableStylePreset.html#MediumStyle4">MediumStyle4</a></span></code>
<div class="block">
 Medium Style 4.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TableStylePreset.html#MediumStyle4Accent1">MediumStyle4Accent1</a></span></code>
<div class="block">
 Medium Style 4 - Accent 1.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TableStylePreset.html#MediumStyle4Accent2">MediumStyle4Accent2</a></span></code>
<div class="block">
 Medium Style 4 - Accent 2.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TableStylePreset.html#MediumStyle4Accent3">MediumStyle4Accent3</a></span></code>
<div class="block">
 Medium Style 4 - Accent 3.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TableStylePreset.html#MediumStyle4Accent4">MediumStyle4Accent4</a></span></code>
<div class="block">
 Medium Style 4 - Accent 4.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TableStylePreset.html#MediumStyle4Accent5">MediumStyle4Accent5</a></span></code>
<div class="block">
 Medium Style 4 - Accent 5.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TableStylePreset.html#MediumStyle4Accent6">MediumStyle4Accent6</a></span></code>
<div class="block">
 Medium Style 4 - Accent 6.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TableStylePreset.html#None">None</a></span></code>
<div class="block">
 No style.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TableStylePreset.html#NoStyleNoGrid">NoStyleNoGrid</a></span></code>
<div class="block">
 No Style, No Grid.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TableStylePreset.html#NoStyleTableGrid">NoStyleTableGrid</a></span></code>
<div class="block">
 No Style, Table Grid.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TableStylePreset.html#ThemedStyle1Accent1">ThemedStyle1Accent1</a></span></code>
<div class="block">
 Themed Style 1 - Accent 1.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TableStylePreset.html#ThemedStyle1Accent2">ThemedStyle1Accent2</a></span></code>
<div class="block">
 Themed Style 1 - Accent 2.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TableStylePreset.html#ThemedStyle1Accent3">ThemedStyle1Accent3</a></span></code>
<div class="block">
 Themed Style 1 - Accent 3.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TableStylePreset.html#ThemedStyle1Accent4">ThemedStyle1Accent4</a></span></code>
<div class="block">
 Themed Style 1 - Accent 4.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TableStylePreset.html#ThemedStyle1Accent5">ThemedStyle1Accent5</a></span></code>
<div class="block">
 Themed Style 1 - Accent 5.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TableStylePreset.html#ThemedStyle1Accent6">ThemedStyle1Accent6</a></span></code>
<div class="block">
 Themed Style 1 - Accent 6.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TableStylePreset.html#ThemedStyle2Accent1">ThemedStyle2Accent1</a></span></code>
<div class="block">
 Themed Style 2 - Accent 1.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TableStylePreset.html#ThemedStyle2Accent2">ThemedStyle2Accent2</a></span></code>
<div class="block">
 Themed Style 2 - Accent 2.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TableStylePreset.html#ThemedStyle2Accent3">ThemedStyle2Accent3</a></span></code>
<div class="block">
 Themed Style 2 - Accent 3.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TableStylePreset.html#ThemedStyle2Accent4">ThemedStyle2Accent4</a></span></code>
<div class="block">
 Themed Style 2 - Accent 4.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TableStylePreset.html#ThemedStyle2Accent5">ThemedStyle2Accent5</a></span></code>
<div class="block">
 Themed Style 2 - Accent 5.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TableStylePreset.html#ThemedStyle2Accent6">ThemedStyle2Accent6</a></span></code>
<div class="block">
 Themed Style 2 - Accent 6.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.com.aspose.ms.System.Enum">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;com.aspose.ms.System.Enum</h3>
<code>EnumSeparatorCharArray</code></li>
</ul>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.aspose.ms.System.Enum">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;com.aspose.ms.System.Enum</h3>
<code>Clone, CloneTo, format, format, get_Caption, get_Value, getName, getName, getNames, getNames, getUnderlyingType, getUnderlyingType, getValue, getValues, isDefined, isDefined, isDefined, isDefined, parse, parse, parse, parse, register, toObject, toString</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="Custom">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Custom</h4>
<pre>public static final&nbsp;int Custom</pre>
<div class="block"><p>
 Table has a custom style.
 This is return-only value.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TableStylePreset.Custom">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="None">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>None</h4>
<pre>public static final&nbsp;int None</pre>
<div class="block"><p>
 No style.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TableStylePreset.None">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MediumStyle2Accent1">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MediumStyle2Accent1</h4>
<pre>public static final&nbsp;int MediumStyle2Accent1</pre>
<div class="block"><p>
 Medium Style 2 - Accent 1.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TableStylePreset.MediumStyle2Accent1">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MediumStyle2">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MediumStyle2</h4>
<pre>public static final&nbsp;int MediumStyle2</pre>
<div class="block"><p>
 Medium Style 2.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TableStylePreset.MediumStyle2">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="NoStyleNoGrid">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NoStyleNoGrid</h4>
<pre>public static final&nbsp;int NoStyleNoGrid</pre>
<div class="block"><p>
 No Style, No Grid.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TableStylePreset.NoStyleNoGrid">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="ThemedStyle1Accent1">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ThemedStyle1Accent1</h4>
<pre>public static final&nbsp;int ThemedStyle1Accent1</pre>
<div class="block"><p>
 Themed Style 1 - Accent 1.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TableStylePreset.ThemedStyle1Accent1">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="ThemedStyle1Accent2">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ThemedStyle1Accent2</h4>
<pre>public static final&nbsp;int ThemedStyle1Accent2</pre>
<div class="block"><p>
 Themed Style 1 - Accent 2.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TableStylePreset.ThemedStyle1Accent2">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="ThemedStyle1Accent3">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ThemedStyle1Accent3</h4>
<pre>public static final&nbsp;int ThemedStyle1Accent3</pre>
<div class="block"><p>
 Themed Style 1 - Accent 3.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TableStylePreset.ThemedStyle1Accent3">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="ThemedStyle1Accent4">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ThemedStyle1Accent4</h4>
<pre>public static final&nbsp;int ThemedStyle1Accent4</pre>
<div class="block"><p>
 Themed Style 1 - Accent 4.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TableStylePreset.ThemedStyle1Accent4">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="ThemedStyle1Accent5">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ThemedStyle1Accent5</h4>
<pre>public static final&nbsp;int ThemedStyle1Accent5</pre>
<div class="block"><p>
 Themed Style 1 - Accent 5.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TableStylePreset.ThemedStyle1Accent5">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="ThemedStyle1Accent6">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ThemedStyle1Accent6</h4>
<pre>public static final&nbsp;int ThemedStyle1Accent6</pre>
<div class="block"><p>
 Themed Style 1 - Accent 6.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TableStylePreset.ThemedStyle1Accent6">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="NoStyleTableGrid">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NoStyleTableGrid</h4>
<pre>public static final&nbsp;int NoStyleTableGrid</pre>
<div class="block"><p>
 No Style, Table Grid.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TableStylePreset.NoStyleTableGrid">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="ThemedStyle2Accent1">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ThemedStyle2Accent1</h4>
<pre>public static final&nbsp;int ThemedStyle2Accent1</pre>
<div class="block"><p>
 Themed Style 2 - Accent 1.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TableStylePreset.ThemedStyle2Accent1">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="ThemedStyle2Accent2">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ThemedStyle2Accent2</h4>
<pre>public static final&nbsp;int ThemedStyle2Accent2</pre>
<div class="block"><p>
 Themed Style 2 - Accent 2.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TableStylePreset.ThemedStyle2Accent2">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="ThemedStyle2Accent3">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ThemedStyle2Accent3</h4>
<pre>public static final&nbsp;int ThemedStyle2Accent3</pre>
<div class="block"><p>
 Themed Style 2 - Accent 3.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TableStylePreset.ThemedStyle2Accent3">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="ThemedStyle2Accent4">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ThemedStyle2Accent4</h4>
<pre>public static final&nbsp;int ThemedStyle2Accent4</pre>
<div class="block"><p>
 Themed Style 2 - Accent 4.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TableStylePreset.ThemedStyle2Accent4">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="ThemedStyle2Accent5">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ThemedStyle2Accent5</h4>
<pre>public static final&nbsp;int ThemedStyle2Accent5</pre>
<div class="block"><p>
 Themed Style 2 - Accent 5.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TableStylePreset.ThemedStyle2Accent5">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="ThemedStyle2Accent6">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ThemedStyle2Accent6</h4>
<pre>public static final&nbsp;int ThemedStyle2Accent6</pre>
<div class="block"><p>
 Themed Style 2 - Accent 6.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TableStylePreset.ThemedStyle2Accent6">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="LightStyle1">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LightStyle1</h4>
<pre>public static final&nbsp;int LightStyle1</pre>
<div class="block"><p>
 Light Style 1
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TableStylePreset.LightStyle1">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="LightStyle1Accent1">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LightStyle1Accent1</h4>
<pre>public static final&nbsp;int LightStyle1Accent1</pre>
<div class="block"><p>
 Light Style 1 - Accent 1.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TableStylePreset.LightStyle1Accent1">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="LightStyle1Accent2">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LightStyle1Accent2</h4>
<pre>public static final&nbsp;int LightStyle1Accent2</pre>
<div class="block"><p>
 Light Style 1 - Accent 2.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TableStylePreset.LightStyle1Accent2">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="LightStyle1Accent3">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LightStyle1Accent3</h4>
<pre>public static final&nbsp;int LightStyle1Accent3</pre>
<div class="block"><p>
 Light Style 1 - Accent 3.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TableStylePreset.LightStyle1Accent3">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="LightStyle1Accent4">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LightStyle1Accent4</h4>
<pre>public static final&nbsp;int LightStyle1Accent4</pre>
<div class="block"><p>
 Light Style 1 - Accent 4.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TableStylePreset.LightStyle1Accent4">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="LightStyle2Accent5">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LightStyle2Accent5</h4>
<pre>public static final&nbsp;int LightStyle2Accent5</pre>
<div class="block"><p>
 Light Style 2 - Accent 5.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TableStylePreset.LightStyle2Accent5">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="LightStyle1Accent6">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LightStyle1Accent6</h4>
<pre>public static final&nbsp;int LightStyle1Accent6</pre>
<div class="block"><p>
 Light Style 1 - Accent 6.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TableStylePreset.LightStyle1Accent6">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="LightStyle2">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LightStyle2</h4>
<pre>public static final&nbsp;int LightStyle2</pre>
<div class="block"><p>
 Light Style 2.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TableStylePreset.LightStyle2">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="LightStyle2Accent1">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LightStyle2Accent1</h4>
<pre>public static final&nbsp;int LightStyle2Accent1</pre>
<div class="block"><p>
 Light Style 2 - Accent 1.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TableStylePreset.LightStyle2Accent1">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="LightStyle2Accent2">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LightStyle2Accent2</h4>
<pre>public static final&nbsp;int LightStyle2Accent2</pre>
<div class="block"><p>
 Light Style 2 - Accent 2.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TableStylePreset.LightStyle2Accent2">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="LightStyle2Accent3">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LightStyle2Accent3</h4>
<pre>public static final&nbsp;int LightStyle2Accent3</pre>
<div class="block"><p>
 Light Style 2 - Accent 3.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TableStylePreset.LightStyle2Accent3">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MediumStyle2Accent3">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MediumStyle2Accent3</h4>
<pre>public static final&nbsp;int MediumStyle2Accent3</pre>
<div class="block"><p>
 Medium Style 2 - Accent 3.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TableStylePreset.MediumStyle2Accent3">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MediumStyle2Accent4">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MediumStyle2Accent4</h4>
<pre>public static final&nbsp;int MediumStyle2Accent4</pre>
<div class="block"><p>
 Medium Style 2 - Accent 4.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TableStylePreset.MediumStyle2Accent4">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MediumStyle2Accent5">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MediumStyle2Accent5</h4>
<pre>public static final&nbsp;int MediumStyle2Accent5</pre>
<div class="block"><p>
 Medium Style 2 - Accent 5.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TableStylePreset.MediumStyle2Accent5">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="LightStyle2Accent6">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LightStyle2Accent6</h4>
<pre>public static final&nbsp;int LightStyle2Accent6</pre>
<div class="block"><p>
 Light Style 2 - Accent 6.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TableStylePreset.LightStyle2Accent6">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="LightStyle2Accent4">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LightStyle2Accent4</h4>
<pre>public static final&nbsp;int LightStyle2Accent4</pre>
<div class="block"><p>
 Light Style 2 - Accent 4.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TableStylePreset.LightStyle2Accent4">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="LightStyle3">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LightStyle3</h4>
<pre>public static final&nbsp;int LightStyle3</pre>
<div class="block"><p>
 Light Style 3.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TableStylePreset.LightStyle3">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="LightStyle3Accent1">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LightStyle3Accent1</h4>
<pre>public static final&nbsp;int LightStyle3Accent1</pre>
<div class="block"><p>
 Light Style 3 - Accent 1.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TableStylePreset.LightStyle3Accent1">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MediumStyle2Accent2">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MediumStyle2Accent2</h4>
<pre>public static final&nbsp;int MediumStyle2Accent2</pre>
<div class="block"><p>
 Medium Style 2 - Accent 2.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TableStylePreset.MediumStyle2Accent2">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="LightStyle3Accent2">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LightStyle3Accent2</h4>
<pre>public static final&nbsp;int LightStyle3Accent2</pre>
<div class="block"><p>
 Light Style 3 - Accent 2.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TableStylePreset.LightStyle3Accent2">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="LightStyle3Accent3">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LightStyle3Accent3</h4>
<pre>public static final&nbsp;int LightStyle3Accent3</pre>
<div class="block"><p>
 Light Style 3 - Accent 3.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TableStylePreset.LightStyle3Accent3">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="LightStyle3Accent4">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LightStyle3Accent4</h4>
<pre>public static final&nbsp;int LightStyle3Accent4</pre>
<div class="block"><p>
 Light Style 3 - Accent 4.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TableStylePreset.LightStyle3Accent4">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="LightStyle3Accent5">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LightStyle3Accent5</h4>
<pre>public static final&nbsp;int LightStyle3Accent5</pre>
<div class="block"><p>
 Light Style 3 - Accent 5.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TableStylePreset.LightStyle3Accent5">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="LightStyle3Accent6">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LightStyle3Accent6</h4>
<pre>public static final&nbsp;int LightStyle3Accent6</pre>
<div class="block"><p>
 Light Style 3 - Accent 6.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TableStylePreset.LightStyle3Accent6">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MediumStyle1">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MediumStyle1</h4>
<pre>public static final&nbsp;int MediumStyle1</pre>
<div class="block"><p>
 Medium Style 1.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TableStylePreset.MediumStyle1">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MediumStyle1Accent1">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MediumStyle1Accent1</h4>
<pre>public static final&nbsp;int MediumStyle1Accent1</pre>
<div class="block"><p>
 Medium Style 1 - Accent 1.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TableStylePreset.MediumStyle1Accent1">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MediumStyle1Accent2">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MediumStyle1Accent2</h4>
<pre>public static final&nbsp;int MediumStyle1Accent2</pre>
<div class="block"><p>
 Medium Style 1 - Accent 2.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TableStylePreset.MediumStyle1Accent2">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MediumStyle1Accent3">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MediumStyle1Accent3</h4>
<pre>public static final&nbsp;int MediumStyle1Accent3</pre>
<div class="block"><p>
 Medium Style 1 - Accent 3.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TableStylePreset.MediumStyle1Accent3">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MediumStyle1Accent4">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MediumStyle1Accent4</h4>
<pre>public static final&nbsp;int MediumStyle1Accent4</pre>
<div class="block"><p>
 Medium Style 1 - Accent 4.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TableStylePreset.MediumStyle1Accent4">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MediumStyle1Accent5">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MediumStyle1Accent5</h4>
<pre>public static final&nbsp;int MediumStyle1Accent5</pre>
<div class="block"><p>
 Medium Style 1 - Accent 5.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TableStylePreset.MediumStyle1Accent5">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MediumStyle1Accent6">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MediumStyle1Accent6</h4>
<pre>public static final&nbsp;int MediumStyle1Accent6</pre>
<div class="block"><p>
 Medium Style 1 - Accent 6.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TableStylePreset.MediumStyle1Accent6">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MediumStyle2Accent6">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MediumStyle2Accent6</h4>
<pre>public static final&nbsp;int MediumStyle2Accent6</pre>
<div class="block"><p>
 Medium Style 2 - Accent 6.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TableStylePreset.MediumStyle2Accent6">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MediumStyle3">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MediumStyle3</h4>
<pre>public static final&nbsp;int MediumStyle3</pre>
<div class="block"><p>
 Medium Style 3.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TableStylePreset.MediumStyle3">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MediumStyle3Accent1">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MediumStyle3Accent1</h4>
<pre>public static final&nbsp;int MediumStyle3Accent1</pre>
<div class="block"><p>
 Medium Style 3 - Accent 1.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TableStylePreset.MediumStyle3Accent1">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MediumStyle3Accent2">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MediumStyle3Accent2</h4>
<pre>public static final&nbsp;int MediumStyle3Accent2</pre>
<div class="block"><p>
 Medium Style 3 - Accent 2.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TableStylePreset.MediumStyle3Accent2">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MediumStyle3Accent3">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MediumStyle3Accent3</h4>
<pre>public static final&nbsp;int MediumStyle3Accent3</pre>
<div class="block"><p>
 Medium Style 3 - Accent 3.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TableStylePreset.MediumStyle3Accent3">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MediumStyle3Accent4">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MediumStyle3Accent4</h4>
<pre>public static final&nbsp;int MediumStyle3Accent4</pre>
<div class="block"><p>
 Medium Style 3 - Accent 4.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TableStylePreset.MediumStyle3Accent4">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MediumStyle3Accent5">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MediumStyle3Accent5</h4>
<pre>public static final&nbsp;int MediumStyle3Accent5</pre>
<div class="block"><p>
 Medium Style 3 - Accent 5.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TableStylePreset.MediumStyle3Accent5">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MediumStyle3Accent6">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MediumStyle3Accent6</h4>
<pre>public static final&nbsp;int MediumStyle3Accent6</pre>
<div class="block"><p>
 Medium Style 3 - Accent 6.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TableStylePreset.MediumStyle3Accent6">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MediumStyle4">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MediumStyle4</h4>
<pre>public static final&nbsp;int MediumStyle4</pre>
<div class="block"><p>
 Medium Style 4.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TableStylePreset.MediumStyle4">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MediumStyle4Accent1">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MediumStyle4Accent1</h4>
<pre>public static final&nbsp;int MediumStyle4Accent1</pre>
<div class="block"><p>
 Medium Style 4 - Accent 1.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TableStylePreset.MediumStyle4Accent1">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MediumStyle4Accent2">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MediumStyle4Accent2</h4>
<pre>public static final&nbsp;int MediumStyle4Accent2</pre>
<div class="block"><p>
 Medium Style 4 - Accent 2.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TableStylePreset.MediumStyle4Accent2">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MediumStyle4Accent3">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MediumStyle4Accent3</h4>
<pre>public static final&nbsp;int MediumStyle4Accent3</pre>
<div class="block"><p>
 Medium Style 4 - Accent 3.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TableStylePreset.MediumStyle4Accent3">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MediumStyle4Accent4">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MediumStyle4Accent4</h4>
<pre>public static final&nbsp;int MediumStyle4Accent4</pre>
<div class="block"><p>
 Medium Style 4 - Accent 4.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TableStylePreset.MediumStyle4Accent4">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MediumStyle4Accent5">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MediumStyle4Accent5</h4>
<pre>public static final&nbsp;int MediumStyle4Accent5</pre>
<div class="block"><p>
 Medium Style 4 - Accent 5.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TableStylePreset.MediumStyle4Accent5">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MediumStyle4Accent6">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MediumStyle4Accent6</h4>
<pre>public static final&nbsp;int MediumStyle4Accent6</pre>
<div class="block"><p>
 Medium Style 4 - Accent 6.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TableStylePreset.MediumStyle4Accent6">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DarkStyle1">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DarkStyle1</h4>
<pre>public static final&nbsp;int DarkStyle1</pre>
<div class="block"><p>
 Dark Style 1.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TableStylePreset.DarkStyle1">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DarkStyle1Accent1">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DarkStyle1Accent1</h4>
<pre>public static final&nbsp;int DarkStyle1Accent1</pre>
<div class="block"><p>
 Dark Style 1 - Accent 1.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TableStylePreset.DarkStyle1Accent1">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DarkStyle1Accent2">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DarkStyle1Accent2</h4>
<pre>public static final&nbsp;int DarkStyle1Accent2</pre>
<div class="block"><p>
 Dark Style 1 - Accent 2.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TableStylePreset.DarkStyle1Accent2">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DarkStyle1Accent3">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DarkStyle1Accent3</h4>
<pre>public static final&nbsp;int DarkStyle1Accent3</pre>
<div class="block"><p>
 Dark Style 1 - Accent 3.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TableStylePreset.DarkStyle1Accent3">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DarkStyle1Accent4">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DarkStyle1Accent4</h4>
<pre>public static final&nbsp;int DarkStyle1Accent4</pre>
<div class="block"><p>
 Dark Style 1 - Accent 4.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TableStylePreset.DarkStyle1Accent4">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DarkStyle1Accent5">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DarkStyle1Accent5</h4>
<pre>public static final&nbsp;int DarkStyle1Accent5</pre>
<div class="block"><p>
 Dark Style 1 - Accent 5.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TableStylePreset.DarkStyle1Accent5">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DarkStyle1Accent6">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DarkStyle1Accent6</h4>
<pre>public static final&nbsp;int DarkStyle1Accent6</pre>
<div class="block"><p>
 Dark Style 1 - Accent 6.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TableStylePreset.DarkStyle1Accent6">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DarkStyle2">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DarkStyle2</h4>
<pre>public static final&nbsp;int DarkStyle2</pre>
<div class="block"><p>
 Dark Style 2.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TableStylePreset.DarkStyle2">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DarkStyle2Accent1Accent2">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DarkStyle2Accent1Accent2</h4>
<pre>public static final&nbsp;int DarkStyle2Accent1Accent2</pre>
<div class="block"><p>
 Dark Style 2 - Accent 1/Accent 2.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TableStylePreset.DarkStyle2Accent1Accent2">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DarkStyle2Accent3Accent4">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DarkStyle2Accent3Accent4</h4>
<pre>public static final&nbsp;int DarkStyle2Accent3Accent4</pre>
<div class="block"><p>
 Dark Style 2 - Accent 3/Accent 4.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TableStylePreset.DarkStyle2Accent3Accent4">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DarkStyle2Accent5Accent6">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DarkStyle2Accent5Accent6</h4>
<pre>public static final&nbsp;int DarkStyle2Accent5Accent6</pre>
<div class="block"><p>
 Dark Style 2 - Accent 5/Accent 6.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TableStylePreset.DarkStyle2Accent5Accent6">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="LightStyle1Accent5">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>LightStyle1Accent5</h4>
<pre>public static final&nbsp;int LightStyle1Accent5</pre>
<div class="block"><p>
 Light Style 1 - Accent 5.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.TableStylePreset.LightStyle1Accent5">Constant Field Values</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/TableFormat.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/TagCollection.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/TableStylePreset.html" target="_top">Frames</a></li>
<li><a href="TableStylePreset.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.classes.inherited.from.class.com.aspose.ms.System.Enum">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#methods.inherited.from.class.com.aspose.ms.System.Enum">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li>Method</li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><i>Copyright &copy; 2004-2025 Aspose Pty Ltd. All Rights Reserved.</i></small></p>
</body>
</html>
