<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_421) on Fri Jul 11 05:32:44 CDT 2025 -->
<title>ShapeType (Aspose.Slides for Java (25.7) API Documentation)</title>
<meta name="date" content="2025-07-11">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ShapeType (Aspose.Slides for Java (25.7) API Documentation)";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/ShapeThumbnailBounds.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/ShapeUtil.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/ShapeType.html" target="_top">Frames</a></li>
<li><a href="ShapeType.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.classes.inherited.from.class.com.aspose.ms.System.Enum">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#methods.inherited.from.class.com.aspose.ms.System.Enum">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li>Method</li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.aspose.slides</div>
<h2 title="Class ShapeType" class="title">Class ShapeType</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.aspose.ms.System.ValueType&lt;com.aspose.ms.System.Enum&gt;</li>
<li>
<ul class="inheritance">
<li>com.aspose.ms.System.Enum</li>
<li>
<ul class="inheritance">
<li>com.aspose.slides.ShapeType</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public final class <span class="typeNameLabel">ShapeType</span>
extends com.aspose.ms.System.Enum</pre>
<div class="block"><p>
 Represents preset geometry of geometry shapes.
 </p></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.com.aspose.ms.System.Enum">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from class&nbsp;com.aspose.ms.System.Enum</h3>
<code>com.aspose.ms.System.Enum.AbstractEnum, com.aspose.ms.System.Enum.FlaggedEnum, com.aspose.ms.System.Enum.ObjectEnum, com.aspose.ms.System.Enum.SimpleEnum</code></li>
</ul>
</li>
</ul>
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#AlternateProcessFlow">AlternateProcessFlow</a></span></code>
<div class="block">
 Alternate Process Flow Shape.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#BackOrPreviousButton">BackOrPreviousButton</a></span></code>
<div class="block">
 Back or Previous Button Shape.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#BeginningButton">BeginningButton</a></span></code>
<div class="block">
 Beginning Button Shape.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#BentArrow">BentArrow</a></span></code>
<div class="block">
 Bent Arrow Shape.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#BentConnector2">BentConnector2</a></span></code>
<div class="block">
 Bent Connector 2 Shape.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#BentConnector3">BentConnector3</a></span></code>
<div class="block">
 Bent Connector 3 Shape.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#BentConnector4">BentConnector4</a></span></code>
<div class="block">
 Bent Connector 4 Shape.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#BentConnector5">BentConnector5</a></span></code>
<div class="block">
 Bent Connector 5 Shape.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#BentUpArrow">BentUpArrow</a></span></code>
<div class="block">
 Bent Up Arrow Shape.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#Bevel">Bevel</a></span></code>
<div class="block">
 Bevel Shape.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#BlankButton">BlankButton</a></span></code>
<div class="block">
 Blank Button Shape.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#BlockArc">BlockArc</a></span></code>
<div class="block">
 Block Arc Shape.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#BracePair">BracePair</a></span></code>
<div class="block">
 Brace Pair Shape.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#BracketPair">BracketPair</a></span></code>
<div class="block">
 Bracket Pair Shape.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#Callout1">Callout1</a></span></code>
<div class="block">
 Callout 1 Shape.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#Callout1WithAccent">Callout1WithAccent</a></span></code>
<div class="block">
 Callout 1 with Accent Shape.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#Callout1WithBorder">Callout1WithBorder</a></span></code>
<div class="block">
 Callout 1 with Border Shape.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#Callout1WithBorderAndAccent">Callout1WithBorderAndAccent</a></span></code>
<div class="block">
 Callout 1 with Border and Accent Shape.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#Callout2">Callout2</a></span></code>
<div class="block">
 Callout 2 Shape.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#Callout2WithAccent">Callout2WithAccent</a></span></code>
<div class="block">
 Callout 2 with Accent Shape.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#Callout2WithBorder">Callout2WithBorder</a></span></code>
<div class="block">
 Callout 2 with Border Shape.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#Callout2WithBorderAndAccent">Callout2WithBorderAndAccent</a></span></code>
<div class="block">
 Callout 2 with Border and Accent Shape.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#Callout3">Callout3</a></span></code>
<div class="block">
 Callout 3 Shape.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#Callout3WithAccent">Callout3WithAccent</a></span></code>
<div class="block">
 Callout 3 with Accent Shape.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#Callout3WithBorder">Callout3WithBorder</a></span></code>
<div class="block">
 Callout 3 with Border Shape.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#Callout3WithBorderAndAccent">Callout3WithBorderAndAccent</a></span></code>
<div class="block">
 Callout 3 with Border and Accent Shape.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#CalloutCloud">CalloutCloud</a></span></code>
<div class="block">
 Callout Cloud Shape.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#CalloutDownArrow">CalloutDownArrow</a></span></code>
<div class="block">
 Callout Down Arrow Shape.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#CalloutLeftArrow">CalloutLeftArrow</a></span></code>
<div class="block">
 Callout Left Arrow Shape.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#CalloutLeftRightArrow">CalloutLeftRightArrow</a></span></code>
<div class="block">
 Callout Left Right Arrow Shape.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#CalloutQuadArrow">CalloutQuadArrow</a></span></code>
<div class="block">
 Callout Quad-Arrow Shape.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#CalloutRightArrow">CalloutRightArrow</a></span></code>
<div class="block">
 Callout Right Arrow Shape.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#CalloutUpArrow">CalloutUpArrow</a></span></code>
<div class="block">
 Callout Up Arrow Shape.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#CalloutUpDownArrow">CalloutUpDownArrow</a></span></code>
<div class="block">
 Callout Up Down Arrow Shape.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#CalloutWedgeEllipse">CalloutWedgeEllipse</a></span></code>
<div class="block">
 Callout Wedge Ellipse Shape.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#CalloutWedgeRectangle">CalloutWedgeRectangle</a></span></code>
<div class="block">
 Callout Wedge Rectangle Shape.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#CalloutWedgeRoundRectangle">CalloutWedgeRoundRectangle</a></span></code>
<div class="block">
 Callout Wedge Round Rectangle Shape.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#Can">Can</a></span></code>
<div class="block">
 Can Shape.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#ChartPlus">ChartPlus</a></span></code>
<div class="block">
 Chart Plus Shape.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#ChartStar">ChartStar</a></span></code>
<div class="block">
 Chart Star Shape.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#ChartX">ChartX</a></span></code>
<div class="block">
 Chart X Shape.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#Chevron">Chevron</a></span></code>
<div class="block">
 Chevron Shape.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#Chord">Chord</a></span></code>
<div class="block">
 Chord Shape.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#CircularArrow">CircularArrow</a></span></code>
<div class="block">
 Circular Arrow Shape.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#Cloud">Cloud</a></span></code>
<div class="block">
 Cloud Shape.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#CollateFlow">CollateFlow</a></span></code>
<div class="block">
 Collate Flow Shape.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#ConnectorFlow">ConnectorFlow</a></span></code>
<div class="block">
 Connector Flow Shape.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#Corner">Corner</a></span></code>
<div class="block">
 Corner Shape.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#CornerTabs">CornerTabs</a></span></code>
<div class="block">
 Corner Tabs Shape.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#Cube">Cube</a></span></code>
<div class="block">
 Cube Shape.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#CurvedArc">CurvedArc</a></span></code>
<div class="block">
 Curved Arc Shape.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#CurvedConnector2">CurvedConnector2</a></span></code>
<div class="block">
 Curved Connector 2 Shape.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#CurvedConnector3">CurvedConnector3</a></span></code>
<div class="block">
 Curved Connector 3 Shape.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#CurvedConnector4">CurvedConnector4</a></span></code>
<div class="block">
 Curved Connector 4 Shape.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#CurvedConnector5">CurvedConnector5</a></span></code>
<div class="block">
 Curved Connector 5 Shape.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#CurvedDownArrow">CurvedDownArrow</a></span></code>
<div class="block">
 Curved Down Arrow Shape.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#CurvedLeftArrow">CurvedLeftArrow</a></span></code>
<div class="block">
 Curved Left Arrow Shape.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#CurvedRightArrow">CurvedRightArrow</a></span></code>
<div class="block">
 Curved Right Arrow Shape.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#CurvedUpArrow">CurvedUpArrow</a></span></code>
<div class="block">
 Curved Up Arrow Shape.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#Custom">Custom</a></span></code>
<div class="block">
 Custom shape.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#Decagon">Decagon</a></span></code>
<div class="block">
 Decagon Shape.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#DecisionFlow">DecisionFlow</a></span></code>
<div class="block">
 Decision Flow Shape.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#DelayFlow">DelayFlow</a></span></code>
<div class="block">
 Delay Flow Shape.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#DiagonalStripe">DiagonalStripe</a></span></code>
<div class="block">
 Diagonal Stripe Shape.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#Diamond">Diamond</a></span></code>
<div class="block">
 Diamond Shape.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#DisplayFlow">DisplayFlow</a></span></code>
<div class="block">
 Display Flow Shape.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#DivideMath">DivideMath</a></span></code>
<div class="block">
 Divide Math Shape.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#DocumentButton">DocumentButton</a></span></code>
<div class="block">
 Document Button Shape.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#DocumentFlow">DocumentFlow</a></span></code>
<div class="block">
 Document Flow Shape.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#Dodecagon">Dodecagon</a></span></code>
<div class="block">
 Dodecagon Shape.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#Donut">Donut</a></span></code>
<div class="block">
 Donut Shape.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#DoubleWave">DoubleWave</a></span></code>
<div class="block">
 Double Wave Shape.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#DownArrow">DownArrow</a></span></code>
<div class="block">
 Down Arrow Shape.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#EightPointedStar">EightPointedStar</a></span></code>
<div class="block">
 Eight Pointed Star Shape.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#Ellipse">Ellipse</a></span></code>
<div class="block">
 Ellipse Shape.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#EllipseRibbon">EllipseRibbon</a></span></code>
<div class="block">
 Ellipse Ribbon Shape.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#EllipseRibbon2">EllipseRibbon2</a></span></code>
<div class="block">
 Ellipse Ribbon 2 Shape.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#EndButton">EndButton</a></span></code>
<div class="block">
 End Button Shape.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#EqualMath">EqualMath</a></span></code>
<div class="block">
 Equal Math Shape.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#ExtractFlow">ExtractFlow</a></span></code>
<div class="block">
 Extract Flow Shape.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#FivePointedStar">FivePointedStar</a></span></code>
<div class="block">
 Five Pointed Star Shape.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#FoldedCorner">FoldedCorner</a></span></code>
<div class="block">
 Folded Corner Shape.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#ForwardOrNextButton">ForwardOrNextButton</a></span></code>
<div class="block">
 Forward or Next Button Shape.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#FourPointedStar">FourPointedStar</a></span></code>
<div class="block">
 Four Pointed Star Shape.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#Frame">Frame</a></span></code>
<div class="block">
 Frame Shape.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#Funnel">Funnel</a></span></code>
<div class="block">
 Funnel Shape.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#Gear6">Gear6</a></span></code>
<div class="block">
 Gear 6 Shape.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#Gear9">Gear9</a></span></code>
<div class="block">
 Gear 9 Shape.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#HalfFrame">HalfFrame</a></span></code>
<div class="block">
 Half Frame Shape.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#Heart">Heart</a></span></code>
<div class="block">
 Heart Shape.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#HelpButton">HelpButton</a></span></code>
<div class="block">
 Help Button Shape.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#Heptagon">Heptagon</a></span></code>
<div class="block">
 Heptagon Shape.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#Hexagon">Hexagon</a></span></code>
<div class="block">
 Hexagon Shape.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#HomeButton">HomeButton</a></span></code>
<div class="block">
 Home Button Shape.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#HomePlate">HomePlate</a></span></code>
<div class="block">
 Home Plate Shape.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#HorizontalScroll">HorizontalScroll</a></span></code>
<div class="block">
 Horizontal Scroll Shape.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#InformationButton">InformationButton</a></span></code>
<div class="block">
 Information Button Shape.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#InputOutputFlow">InputOutputFlow</a></span></code>
<div class="block">
 Input Output Flow Shape.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#InternalStorageFlow">InternalStorageFlow</a></span></code>
<div class="block">
 Internal Storage Flow Shape.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#IrregularSeal1">IrregularSeal1</a></span></code>
<div class="block">
 Irregular Seal 1 Shape.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#IrregularSeal2">IrregularSeal2</a></span></code>
<div class="block">
 Irregular Seal 2 Shape.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#LeftArrow">LeftArrow</a></span></code>
<div class="block">
 Left Arrow Shape.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#LeftBrace">LeftBrace</a></span></code>
<div class="block">
 Left Brace Shape.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#LeftBracket">LeftBracket</a></span></code>
<div class="block">
 Left Bracket Shape.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#LeftCircularArrow">LeftCircularArrow</a></span></code>
<div class="block">
 Left Circular Arrow Shape.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#LeftRightArrow">LeftRightArrow</a></span></code>
<div class="block">
 Left Right Arrow Shape.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#LeftRightCircularArrow">LeftRightCircularArrow</a></span></code>
<div class="block">
 Left Right Circular Arrow Shape.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#LeftRightRibbon">LeftRightRibbon</a></span></code>
<div class="block">
 Left Right Ribbon Shape.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#LeftRightUpArrow">LeftRightUpArrow</a></span></code>
<div class="block">
 Left Right Up Arrow Shape.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#LeftUpArrow">LeftUpArrow</a></span></code>
<div class="block">
 Left Up Arrow Shape.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#LightningBolt">LightningBolt</a></span></code>
<div class="block">
 Lightning Bolt Shape.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#Line">Line</a></span></code>
<div class="block">
 Line Shape.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#LineInverse">LineInverse</a></span></code>
<div class="block">
 Line Inverse Shape.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#MagneticDiskFlow">MagneticDiskFlow</a></span></code>
<div class="block">
 Magnetic Disk Flow Shape.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#MagneticDrumFlow">MagneticDrumFlow</a></span></code>
<div class="block">
 Magnetic Drum Flow Shape.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#MagneticTapeFlow">MagneticTapeFlow</a></span></code>
<div class="block">
 Magnetic Tape Flow Shape.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#ManualInputFlow">ManualInputFlow</a></span></code>
<div class="block">
 Manual Input Flow Shape.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#ManualOperationFlow">ManualOperationFlow</a></span></code>
<div class="block">
 Manual Operation Flow Shape.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#MergeFlow">MergeFlow</a></span></code>
<div class="block">
 Merge Flow Shape.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#MinusMath">MinusMath</a></span></code>
<div class="block">
 Minus Math Shape.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#Moon">Moon</a></span></code>
<div class="block">
 Moon Shape.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#MovieButton">MovieButton</a></span></code>
<div class="block">
 Movie Button Shape.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#MultiDocumentFlow">MultiDocumentFlow</a></span></code>
<div class="block">
 Multi-Document Flow Shape.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#MultiplyMath">MultiplyMath</a></span></code>
<div class="block">
 Multiply Math Shape.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#NonIsoscelesTrapezoid">NonIsoscelesTrapezoid</a></span></code>
<div class="block">
 Non-Isosceles Trapezoid Shape.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#NoSmoking">NoSmoking</a></span></code>
<div class="block">
 No Smoking Shape.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#NotchedRightArrow">NotchedRightArrow</a></span></code>
<div class="block">
 Notched Right Arrow Shape.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#NotDefined">NotDefined</a></span></code>
<div class="block">
 Not defined.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#NotEqualMath">NotEqualMath</a></span></code>
<div class="block">
 Not Equal Math Shape.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#Octagon">Octagon</a></span></code>
<div class="block">
 Octagon Shape.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#OfflineStorageFlow">OfflineStorageFlow</a></span></code>
<div class="block">
 Offline Storage Flow Shape.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#OffPageConnectorFlow">OffPageConnectorFlow</a></span></code>
<div class="block">
 Off-Page Connector Flow Shape.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#OneRoundCornerRectangle">OneRoundCornerRectangle</a></span></code>
<div class="block">
 One Round Corner Rectangle Shape.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#OneSnipCornerRectangle">OneSnipCornerRectangle</a></span></code>
<div class="block">
 One Snip Corner Rectangle Shape.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#OneSnipOneRoundCornerRectangle">OneSnipOneRoundCornerRectangle</a></span></code>
<div class="block">
 One Snip One Round Corner Rectangle Shape.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#OnlineStorageFlow">OnlineStorageFlow</a></span></code>
<div class="block">
 Online Storage Flow Shape.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#OrFlow">OrFlow</a></span></code>
<div class="block">
 Or Flow Shape.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#Parallelogram">Parallelogram</a></span></code>
<div class="block">
 Parallelogram Shape.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#Pentagon">Pentagon</a></span></code>
<div class="block">
 Pentagon Shape.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#Pie">Pie</a></span></code>
<div class="block">
 Pie Shape.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#PieWedge">PieWedge</a></span></code>
<div class="block">
 Pie Wedge Shape.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#Plaque">Plaque</a></span></code>
<div class="block">
 Plaque Shape.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#PlaqueTabs">PlaqueTabs</a></span></code>
<div class="block">
 Plaque Tabs Shape.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#Plus">Plus</a></span></code>
<div class="block">
 Plus Shape.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#PlusMath">PlusMath</a></span></code>
<div class="block">
 Plus Math Shape.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#PredefinedProcessFlow">PredefinedProcessFlow</a></span></code>
<div class="block">
 Predefined Process Flow Shape.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#PreparationFlow">PreparationFlow</a></span></code>
<div class="block">
 Preparation Flow Shape.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#ProcessFlow">ProcessFlow</a></span></code>
<div class="block">
 Process Flow Shape.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#PunchedCardFlow">PunchedCardFlow</a></span></code>
<div class="block">
 Punched Card Flow Shape.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#PunchedTapeFlow">PunchedTapeFlow</a></span></code>
<div class="block">
 Punched Tape Flow Shape.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#QuadArrow">QuadArrow</a></span></code>
<div class="block">
 Quad-Arrow Shape.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#Rectangle">Rectangle</a></span></code>
<div class="block">
 Rectangle Shape.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#ReturnButton">ReturnButton</a></span></code>
<div class="block">
 Return Button Shape.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#Ribbon">Ribbon</a></span></code>
<div class="block">
 Ribbon Shape.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#Ribbon2">Ribbon2</a></span></code>
<div class="block">
 Ribbon 2 Shape.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#RightArrow">RightArrow</a></span></code>
<div class="block">
 Right Arrow Shape.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#RightBrace">RightBrace</a></span></code>
<div class="block">
 Right Brace Shape.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#RightBracket">RightBracket</a></span></code>
<div class="block">
 Right Bracket Shape.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#RightTriangle">RightTriangle</a></span></code>
<div class="block">
 Right Triangle Shape.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#RoundCornerRectangle">RoundCornerRectangle</a></span></code>
<div class="block">
 Round Corner Rectangle Shape.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#SevenPointedStar">SevenPointedStar</a></span></code>
<div class="block">
 Seven Pointed Star Shape.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#SixPointedStar">SixPointedStar</a></span></code>
<div class="block">
 Six Pointed Star Shape.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#SixteenPointedStar">SixteenPointedStar</a></span></code>
<div class="block">
 Sixteen Pointed Star Shape.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#SmileyFace">SmileyFace</a></span></code>
<div class="block">
 Smiley Face Shape.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#SortFlow">SortFlow</a></span></code>
<div class="block">
 Sort Flow Shape.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#SoundButton">SoundButton</a></span></code>
<div class="block">
 Sound Button Shape.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#SquareTabs">SquareTabs</a></span></code>
<div class="block">
 Square Tabs Shape.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#StraightConnector1">StraightConnector1</a></span></code>
<div class="block">
 Straight Connector 1 Shape.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#StripedRightArrow">StripedRightArrow</a></span></code>
<div class="block">
 Striped Right Arrow Shape.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#SummingJunctionFlow">SummingJunctionFlow</a></span></code>
<div class="block">
 Summing Junction Flow Shape.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#Sun">Sun</a></span></code>
<div class="block">
 Sun Shape.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#SwooshArrow">SwooshArrow</a></span></code>
<div class="block">
 Swoosh Arrow Shape.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#Teardrop">Teardrop</a></span></code>
<div class="block">
 Teardrop Shape.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#TenPointedStar">TenPointedStar</a></span></code>
<div class="block">
 Ten Pointed Star Shape.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#TerminatorFlow">TerminatorFlow</a></span></code>
<div class="block">
 Terminator Flow Shape.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#ThirtyTwoPointedStar">ThirtyTwoPointedStar</a></span></code>
<div class="block">
 Thirty Two Pointed Star Shape.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#Trapezoid">Trapezoid</a></span></code>
<div class="block">
 Trapezoid Shape.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#Triangle">Triangle</a></span></code>
<div class="block">
 Triangle Shape.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#TwelvePointedStar">TwelvePointedStar</a></span></code>
<div class="block">
 Twelve Pointed Star Shape.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#TwentyFourPointedStar">TwentyFourPointedStar</a></span></code>
<div class="block">
 Twenty Four Pointed Star Shape.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#TwoDiagonalRoundCornerRectangle">TwoDiagonalRoundCornerRectangle</a></span></code>
<div class="block">
 Two Diagonal Round Corner Rectangle Shape.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#TwoDiagonalSnipCornerRectangle">TwoDiagonalSnipCornerRectangle</a></span></code>
<div class="block">
 Two Diagonal Snip Corner Rectangle Shape.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#TwoSamesideRoundCornerRectangle">TwoSamesideRoundCornerRectangle</a></span></code>
<div class="block">
 Two Same-side Round Corner Rectangle Shape.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#TwoSamesideSnipCornerRectangle">TwoSamesideSnipCornerRectangle</a></span></code>
<div class="block">
 Two Same-side Snip Corner Rectangle Shape.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#UpArrow">UpArrow</a></span></code>
<div class="block">
 Up Arrow Shape.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#UpDownArrow">UpDownArrow</a></span></code>
<div class="block">
 Up Down Arrow Shape.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#UTurnArrow">UTurnArrow</a></span></code>
<div class="block">
 U-Turn Arrow Shape.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#VerticalScroll">VerticalScroll</a></span></code>
<div class="block">
 Vertical Scroll Shape.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShapeType.html#Wave">Wave</a></span></code>
<div class="block">
 Wave Shape.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.com.aspose.ms.System.Enum">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;com.aspose.ms.System.Enum</h3>
<code>EnumSeparatorCharArray</code></li>
</ul>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.aspose.ms.System.Enum">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;com.aspose.ms.System.Enum</h3>
<code>Clone, CloneTo, format, format, get_Caption, get_Value, getName, getName, getNames, getNames, getUnderlyingType, getUnderlyingType, getValue, getValues, isDefined, isDefined, isDefined, isDefined, parse, parse, parse, parse, register, toObject, toString</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="NotDefined">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NotDefined</h4>
<pre>public static final&nbsp;int NotDefined</pre>
<div class="block"><p>
 Not defined.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.NotDefined">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Custom">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Custom</h4>
<pre>public static final&nbsp;int Custom</pre>
<div class="block"><p>
 Custom shape.
 This is return-only value.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.Custom">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Line">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Line</h4>
<pre>public static final&nbsp;int Line</pre>
<div class="block"><p>
 Line Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.Line">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="LineInverse">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LineInverse</h4>
<pre>public static final&nbsp;int LineInverse</pre>
<div class="block"><p>
 Line Inverse Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.LineInverse">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Triangle">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Triangle</h4>
<pre>public static final&nbsp;int Triangle</pre>
<div class="block"><p>
 Triangle Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.Triangle">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="RightTriangle">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RightTriangle</h4>
<pre>public static final&nbsp;int RightTriangle</pre>
<div class="block"><p>
 Right Triangle Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.RightTriangle">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Rectangle">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Rectangle</h4>
<pre>public static final&nbsp;int Rectangle</pre>
<div class="block"><p>
 Rectangle Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.Rectangle">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Diamond">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Diamond</h4>
<pre>public static final&nbsp;int Diamond</pre>
<div class="block"><p>
 Diamond Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.Diamond">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Parallelogram">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Parallelogram</h4>
<pre>public static final&nbsp;int Parallelogram</pre>
<div class="block"><p>
 Parallelogram Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.Parallelogram">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Trapezoid">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Trapezoid</h4>
<pre>public static final&nbsp;int Trapezoid</pre>
<div class="block"><p>
 Trapezoid Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.Trapezoid">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="NonIsoscelesTrapezoid">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NonIsoscelesTrapezoid</h4>
<pre>public static final&nbsp;int NonIsoscelesTrapezoid</pre>
<div class="block"><p>
 Non-Isosceles Trapezoid Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.NonIsoscelesTrapezoid">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Pentagon">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Pentagon</h4>
<pre>public static final&nbsp;int Pentagon</pre>
<div class="block"><p>
 Pentagon Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.Pentagon">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Hexagon">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Hexagon</h4>
<pre>public static final&nbsp;int Hexagon</pre>
<div class="block"><p>
 Hexagon Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.Hexagon">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Heptagon">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Heptagon</h4>
<pre>public static final&nbsp;int Heptagon</pre>
<div class="block"><p>
 Heptagon Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.Heptagon">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Octagon">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Octagon</h4>
<pre>public static final&nbsp;int Octagon</pre>
<div class="block"><p>
 Octagon Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.Octagon">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Decagon">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Decagon</h4>
<pre>public static final&nbsp;int Decagon</pre>
<div class="block"><p>
 Decagon Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.Decagon">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Dodecagon">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Dodecagon</h4>
<pre>public static final&nbsp;int Dodecagon</pre>
<div class="block"><p>
 Dodecagon Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.Dodecagon">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="FourPointedStar">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FourPointedStar</h4>
<pre>public static final&nbsp;int FourPointedStar</pre>
<div class="block"><p>
 Four Pointed Star Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.FourPointedStar">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="FivePointedStar">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FivePointedStar</h4>
<pre>public static final&nbsp;int FivePointedStar</pre>
<div class="block"><p>
 Five Pointed Star Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.FivePointedStar">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="SixPointedStar">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SixPointedStar</h4>
<pre>public static final&nbsp;int SixPointedStar</pre>
<div class="block"><p>
 Six Pointed Star Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.SixPointedStar">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="SevenPointedStar">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SevenPointedStar</h4>
<pre>public static final&nbsp;int SevenPointedStar</pre>
<div class="block"><p>
 Seven Pointed Star Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.SevenPointedStar">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="EightPointedStar">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>EightPointedStar</h4>
<pre>public static final&nbsp;int EightPointedStar</pre>
<div class="block"><p>
 Eight Pointed Star Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.EightPointedStar">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="TenPointedStar">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TenPointedStar</h4>
<pre>public static final&nbsp;int TenPointedStar</pre>
<div class="block"><p>
 Ten Pointed Star Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.TenPointedStar">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="TwelvePointedStar">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TwelvePointedStar</h4>
<pre>public static final&nbsp;int TwelvePointedStar</pre>
<div class="block"><p>
 Twelve Pointed Star Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.TwelvePointedStar">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="SixteenPointedStar">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SixteenPointedStar</h4>
<pre>public static final&nbsp;int SixteenPointedStar</pre>
<div class="block"><p>
 Sixteen Pointed Star Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.SixteenPointedStar">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="TwentyFourPointedStar">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TwentyFourPointedStar</h4>
<pre>public static final&nbsp;int TwentyFourPointedStar</pre>
<div class="block"><p>
 Twenty Four Pointed Star Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.TwentyFourPointedStar">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="ThirtyTwoPointedStar">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ThirtyTwoPointedStar</h4>
<pre>public static final&nbsp;int ThirtyTwoPointedStar</pre>
<div class="block"><p>
 Thirty Two Pointed Star Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.ThirtyTwoPointedStar">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="RoundCornerRectangle">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RoundCornerRectangle</h4>
<pre>public static final&nbsp;int RoundCornerRectangle</pre>
<div class="block"><p>
 Round Corner Rectangle Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.RoundCornerRectangle">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="OneRoundCornerRectangle">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OneRoundCornerRectangle</h4>
<pre>public static final&nbsp;int OneRoundCornerRectangle</pre>
<div class="block"><p>
 One Round Corner Rectangle Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.OneRoundCornerRectangle">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="TwoSamesideRoundCornerRectangle">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TwoSamesideRoundCornerRectangle</h4>
<pre>public static final&nbsp;int TwoSamesideRoundCornerRectangle</pre>
<div class="block"><p>
 Two Same-side Round Corner Rectangle Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.TwoSamesideRoundCornerRectangle">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="TwoDiagonalRoundCornerRectangle">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TwoDiagonalRoundCornerRectangle</h4>
<pre>public static final&nbsp;int TwoDiagonalRoundCornerRectangle</pre>
<div class="block"><p>
 Two Diagonal Round Corner Rectangle Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.TwoDiagonalRoundCornerRectangle">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="OneSnipOneRoundCornerRectangle">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OneSnipOneRoundCornerRectangle</h4>
<pre>public static final&nbsp;int OneSnipOneRoundCornerRectangle</pre>
<div class="block"><p>
 One Snip One Round Corner Rectangle Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.OneSnipOneRoundCornerRectangle">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="OneSnipCornerRectangle">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OneSnipCornerRectangle</h4>
<pre>public static final&nbsp;int OneSnipCornerRectangle</pre>
<div class="block"><p>
 One Snip Corner Rectangle Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.OneSnipCornerRectangle">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="TwoSamesideSnipCornerRectangle">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TwoSamesideSnipCornerRectangle</h4>
<pre>public static final&nbsp;int TwoSamesideSnipCornerRectangle</pre>
<div class="block"><p>
 Two Same-side Snip Corner Rectangle Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.TwoSamesideSnipCornerRectangle">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="TwoDiagonalSnipCornerRectangle">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TwoDiagonalSnipCornerRectangle</h4>
<pre>public static final&nbsp;int TwoDiagonalSnipCornerRectangle</pre>
<div class="block"><p>
 Two Diagonal Snip Corner Rectangle Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.TwoDiagonalSnipCornerRectangle">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Plaque">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Plaque</h4>
<pre>public static final&nbsp;int Plaque</pre>
<div class="block"><p>
 Plaque Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.Plaque">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Ellipse">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Ellipse</h4>
<pre>public static final&nbsp;int Ellipse</pre>
<div class="block"><p>
 Ellipse Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.Ellipse">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Teardrop">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Teardrop</h4>
<pre>public static final&nbsp;int Teardrop</pre>
<div class="block"><p>
 Teardrop Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.Teardrop">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="HomePlate">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>HomePlate</h4>
<pre>public static final&nbsp;int HomePlate</pre>
<div class="block"><p>
 Home Plate Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.HomePlate">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Chevron">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Chevron</h4>
<pre>public static final&nbsp;int Chevron</pre>
<div class="block"><p>
 Chevron Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.Chevron">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="PieWedge">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PieWedge</h4>
<pre>public static final&nbsp;int PieWedge</pre>
<div class="block"><p>
 Pie Wedge Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.PieWedge">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Pie">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Pie</h4>
<pre>public static final&nbsp;int Pie</pre>
<div class="block"><p>
 Pie Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.Pie">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="BlockArc">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BlockArc</h4>
<pre>public static final&nbsp;int BlockArc</pre>
<div class="block"><p>
 Block Arc Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.BlockArc">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Donut">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Donut</h4>
<pre>public static final&nbsp;int Donut</pre>
<div class="block"><p>
 Donut Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.Donut">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="NoSmoking">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NoSmoking</h4>
<pre>public static final&nbsp;int NoSmoking</pre>
<div class="block"><p>
 No Smoking Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.NoSmoking">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="RightArrow">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RightArrow</h4>
<pre>public static final&nbsp;int RightArrow</pre>
<div class="block"><p>
 Right Arrow Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.RightArrow">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="LeftArrow">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LeftArrow</h4>
<pre>public static final&nbsp;int LeftArrow</pre>
<div class="block"><p>
 Left Arrow Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.LeftArrow">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="UpArrow">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UpArrow</h4>
<pre>public static final&nbsp;int UpArrow</pre>
<div class="block"><p>
 Up Arrow Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.UpArrow">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DownArrow">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DownArrow</h4>
<pre>public static final&nbsp;int DownArrow</pre>
<div class="block"><p>
 Down Arrow Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.DownArrow">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="StripedRightArrow">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>StripedRightArrow</h4>
<pre>public static final&nbsp;int StripedRightArrow</pre>
<div class="block"><p>
 Striped Right Arrow Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.StripedRightArrow">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="NotchedRightArrow">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NotchedRightArrow</h4>
<pre>public static final&nbsp;int NotchedRightArrow</pre>
<div class="block"><p>
 Notched Right Arrow Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.NotchedRightArrow">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="BentUpArrow">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BentUpArrow</h4>
<pre>public static final&nbsp;int BentUpArrow</pre>
<div class="block"><p>
 Bent Up Arrow Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.BentUpArrow">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="LeftRightArrow">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LeftRightArrow</h4>
<pre>public static final&nbsp;int LeftRightArrow</pre>
<div class="block"><p>
 Left Right Arrow Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.LeftRightArrow">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="UpDownArrow">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UpDownArrow</h4>
<pre>public static final&nbsp;int UpDownArrow</pre>
<div class="block"><p>
 Up Down Arrow Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.UpDownArrow">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="LeftUpArrow">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LeftUpArrow</h4>
<pre>public static final&nbsp;int LeftUpArrow</pre>
<div class="block"><p>
 Left Up Arrow Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.LeftUpArrow">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="LeftRightUpArrow">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LeftRightUpArrow</h4>
<pre>public static final&nbsp;int LeftRightUpArrow</pre>
<div class="block"><p>
 Left Right Up Arrow Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.LeftRightUpArrow">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="QuadArrow">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>QuadArrow</h4>
<pre>public static final&nbsp;int QuadArrow</pre>
<div class="block"><p>
 Quad-Arrow Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.QuadArrow">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CalloutLeftArrow">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CalloutLeftArrow</h4>
<pre>public static final&nbsp;int CalloutLeftArrow</pre>
<div class="block"><p>
 Callout Left Arrow Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.CalloutLeftArrow">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CalloutRightArrow">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CalloutRightArrow</h4>
<pre>public static final&nbsp;int CalloutRightArrow</pre>
<div class="block"><p>
 Callout Right Arrow Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.CalloutRightArrow">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CalloutUpArrow">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CalloutUpArrow</h4>
<pre>public static final&nbsp;int CalloutUpArrow</pre>
<div class="block"><p>
 Callout Up Arrow Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.CalloutUpArrow">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CalloutDownArrow">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CalloutDownArrow</h4>
<pre>public static final&nbsp;int CalloutDownArrow</pre>
<div class="block"><p>
 Callout Down Arrow Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.CalloutDownArrow">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CalloutLeftRightArrow">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CalloutLeftRightArrow</h4>
<pre>public static final&nbsp;int CalloutLeftRightArrow</pre>
<div class="block"><p>
 Callout Left Right Arrow Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.CalloutLeftRightArrow">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CalloutUpDownArrow">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CalloutUpDownArrow</h4>
<pre>public static final&nbsp;int CalloutUpDownArrow</pre>
<div class="block"><p>
 Callout Up Down Arrow Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.CalloutUpDownArrow">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CalloutQuadArrow">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CalloutQuadArrow</h4>
<pre>public static final&nbsp;int CalloutQuadArrow</pre>
<div class="block"><p>
 Callout Quad-Arrow Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.CalloutQuadArrow">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="BentArrow">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BentArrow</h4>
<pre>public static final&nbsp;int BentArrow</pre>
<div class="block"><p>
 Bent Arrow Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.BentArrow">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="UTurnArrow">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UTurnArrow</h4>
<pre>public static final&nbsp;int UTurnArrow</pre>
<div class="block"><p>
 U-Turn Arrow Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.UTurnArrow">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CircularArrow">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CircularArrow</h4>
<pre>public static final&nbsp;int CircularArrow</pre>
<div class="block"><p>
 Circular Arrow Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.CircularArrow">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="LeftCircularArrow">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LeftCircularArrow</h4>
<pre>public static final&nbsp;int LeftCircularArrow</pre>
<div class="block"><p>
 Left Circular Arrow Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.LeftCircularArrow">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="LeftRightCircularArrow">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LeftRightCircularArrow</h4>
<pre>public static final&nbsp;int LeftRightCircularArrow</pre>
<div class="block"><p>
 Left Right Circular Arrow Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.LeftRightCircularArrow">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CurvedRightArrow">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CurvedRightArrow</h4>
<pre>public static final&nbsp;int CurvedRightArrow</pre>
<div class="block"><p>
 Curved Right Arrow Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.CurvedRightArrow">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CurvedLeftArrow">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CurvedLeftArrow</h4>
<pre>public static final&nbsp;int CurvedLeftArrow</pre>
<div class="block"><p>
 Curved Left Arrow Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.CurvedLeftArrow">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CurvedUpArrow">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CurvedUpArrow</h4>
<pre>public static final&nbsp;int CurvedUpArrow</pre>
<div class="block"><p>
 Curved Up Arrow Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.CurvedUpArrow">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CurvedDownArrow">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CurvedDownArrow</h4>
<pre>public static final&nbsp;int CurvedDownArrow</pre>
<div class="block"><p>
 Curved Down Arrow Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.CurvedDownArrow">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="SwooshArrow">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SwooshArrow</h4>
<pre>public static final&nbsp;int SwooshArrow</pre>
<div class="block"><p>
 Swoosh Arrow Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.SwooshArrow">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Cube">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Cube</h4>
<pre>public static final&nbsp;int Cube</pre>
<div class="block"><p>
 Cube Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.Cube">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Can">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Can</h4>
<pre>public static final&nbsp;int Can</pre>
<div class="block"><p>
 Can Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.Can">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="LightningBolt">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LightningBolt</h4>
<pre>public static final&nbsp;int LightningBolt</pre>
<div class="block"><p>
 Lightning Bolt Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.LightningBolt">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Heart">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Heart</h4>
<pre>public static final&nbsp;int Heart</pre>
<div class="block"><p>
 Heart Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.Heart">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Sun">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Sun</h4>
<pre>public static final&nbsp;int Sun</pre>
<div class="block"><p>
 Sun Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.Sun">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Moon">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Moon</h4>
<pre>public static final&nbsp;int Moon</pre>
<div class="block"><p>
 Moon Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.Moon">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="SmileyFace">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SmileyFace</h4>
<pre>public static final&nbsp;int SmileyFace</pre>
<div class="block"><p>
 Smiley Face Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.SmileyFace">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="IrregularSeal1">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IrregularSeal1</h4>
<pre>public static final&nbsp;int IrregularSeal1</pre>
<div class="block"><p>
 Irregular Seal 1 Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.IrregularSeal1">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="IrregularSeal2">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IrregularSeal2</h4>
<pre>public static final&nbsp;int IrregularSeal2</pre>
<div class="block"><p>
 Irregular Seal 2 Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.IrregularSeal2">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="FoldedCorner">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FoldedCorner</h4>
<pre>public static final&nbsp;int FoldedCorner</pre>
<div class="block"><p>
 Folded Corner Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.FoldedCorner">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Bevel">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Bevel</h4>
<pre>public static final&nbsp;int Bevel</pre>
<div class="block"><p>
 Bevel Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.Bevel">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Frame">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Frame</h4>
<pre>public static final&nbsp;int Frame</pre>
<div class="block"><p>
 Frame Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.Frame">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="HalfFrame">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>HalfFrame</h4>
<pre>public static final&nbsp;int HalfFrame</pre>
<div class="block"><p>
 Half Frame Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.HalfFrame">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Corner">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Corner</h4>
<pre>public static final&nbsp;int Corner</pre>
<div class="block"><p>
 Corner Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.Corner">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DiagonalStripe">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DiagonalStripe</h4>
<pre>public static final&nbsp;int DiagonalStripe</pre>
<div class="block"><p>
 Diagonal Stripe Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.DiagonalStripe">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Chord">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Chord</h4>
<pre>public static final&nbsp;int Chord</pre>
<div class="block"><p>
 Chord Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.Chord">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CurvedArc">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CurvedArc</h4>
<pre>public static final&nbsp;int CurvedArc</pre>
<div class="block"><p>
 Curved Arc Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.CurvedArc">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="LeftBracket">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LeftBracket</h4>
<pre>public static final&nbsp;int LeftBracket</pre>
<div class="block"><p>
 Left Bracket Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.LeftBracket">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="RightBracket">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RightBracket</h4>
<pre>public static final&nbsp;int RightBracket</pre>
<div class="block"><p>
 Right Bracket Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.RightBracket">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="LeftBrace">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LeftBrace</h4>
<pre>public static final&nbsp;int LeftBrace</pre>
<div class="block"><p>
 Left Brace Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.LeftBrace">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="RightBrace">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RightBrace</h4>
<pre>public static final&nbsp;int RightBrace</pre>
<div class="block"><p>
 Right Brace Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.RightBrace">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="BracketPair">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BracketPair</h4>
<pre>public static final&nbsp;int BracketPair</pre>
<div class="block"><p>
 Bracket Pair Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.BracketPair">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="BracePair">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BracePair</h4>
<pre>public static final&nbsp;int BracePair</pre>
<div class="block"><p>
 Brace Pair Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.BracePair">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="StraightConnector1">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>StraightConnector1</h4>
<pre>public static final&nbsp;int StraightConnector1</pre>
<div class="block"><p>
 Straight Connector 1 Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.StraightConnector1">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="BentConnector2">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BentConnector2</h4>
<pre>public static final&nbsp;int BentConnector2</pre>
<div class="block"><p>
 Bent Connector 2 Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.BentConnector2">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="BentConnector3">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BentConnector3</h4>
<pre>public static final&nbsp;int BentConnector3</pre>
<div class="block"><p>
 Bent Connector 3 Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.BentConnector3">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="BentConnector4">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BentConnector4</h4>
<pre>public static final&nbsp;int BentConnector4</pre>
<div class="block"><p>
 Bent Connector 4 Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.BentConnector4">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="BentConnector5">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BentConnector5</h4>
<pre>public static final&nbsp;int BentConnector5</pre>
<div class="block"><p>
 Bent Connector 5 Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.BentConnector5">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CurvedConnector2">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CurvedConnector2</h4>
<pre>public static final&nbsp;int CurvedConnector2</pre>
<div class="block"><p>
 Curved Connector 2 Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.CurvedConnector2">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CurvedConnector3">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CurvedConnector3</h4>
<pre>public static final&nbsp;int CurvedConnector3</pre>
<div class="block"><p>
 Curved Connector 3 Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.CurvedConnector3">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CurvedConnector4">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CurvedConnector4</h4>
<pre>public static final&nbsp;int CurvedConnector4</pre>
<div class="block"><p>
 Curved Connector 4 Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.CurvedConnector4">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CurvedConnector5">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CurvedConnector5</h4>
<pre>public static final&nbsp;int CurvedConnector5</pre>
<div class="block"><p>
 Curved Connector 5 Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.CurvedConnector5">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Callout1">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Callout1</h4>
<pre>public static final&nbsp;int Callout1</pre>
<div class="block"><p>
 Callout 1 Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.Callout1">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Callout2">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Callout2</h4>
<pre>public static final&nbsp;int Callout2</pre>
<div class="block"><p>
 Callout 2 Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.Callout2">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Callout3">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Callout3</h4>
<pre>public static final&nbsp;int Callout3</pre>
<div class="block"><p>
 Callout 3 Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.Callout3">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Callout1WithAccent">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Callout1WithAccent</h4>
<pre>public static final&nbsp;int Callout1WithAccent</pre>
<div class="block"><p>
 Callout 1 with Accent Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.Callout1WithAccent">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Callout2WithAccent">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Callout2WithAccent</h4>
<pre>public static final&nbsp;int Callout2WithAccent</pre>
<div class="block"><p>
 Callout 2 with Accent Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.Callout2WithAccent">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Callout3WithAccent">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Callout3WithAccent</h4>
<pre>public static final&nbsp;int Callout3WithAccent</pre>
<div class="block"><p>
 Callout 3 with Accent Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.Callout3WithAccent">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Callout1WithBorder">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Callout1WithBorder</h4>
<pre>public static final&nbsp;int Callout1WithBorder</pre>
<div class="block"><p>
 Callout 1 with Border Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.Callout1WithBorder">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Callout2WithBorder">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Callout2WithBorder</h4>
<pre>public static final&nbsp;int Callout2WithBorder</pre>
<div class="block"><p>
 Callout 2 with Border Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.Callout2WithBorder">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Callout3WithBorder">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Callout3WithBorder</h4>
<pre>public static final&nbsp;int Callout3WithBorder</pre>
<div class="block"><p>
 Callout 3 with Border Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.Callout3WithBorder">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Callout1WithBorderAndAccent">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Callout1WithBorderAndAccent</h4>
<pre>public static final&nbsp;int Callout1WithBorderAndAccent</pre>
<div class="block"><p>
 Callout 1 with Border and Accent Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.Callout1WithBorderAndAccent">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Callout2WithBorderAndAccent">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Callout2WithBorderAndAccent</h4>
<pre>public static final&nbsp;int Callout2WithBorderAndAccent</pre>
<div class="block"><p>
 Callout 2 with Border and Accent Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.Callout2WithBorderAndAccent">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Callout3WithBorderAndAccent">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Callout3WithBorderAndAccent</h4>
<pre>public static final&nbsp;int Callout3WithBorderAndAccent</pre>
<div class="block"><p>
 Callout 3 with Border and Accent Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.Callout3WithBorderAndAccent">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CalloutWedgeRectangle">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CalloutWedgeRectangle</h4>
<pre>public static final&nbsp;int CalloutWedgeRectangle</pre>
<div class="block"><p>
 Callout Wedge Rectangle Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.CalloutWedgeRectangle">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CalloutWedgeRoundRectangle">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CalloutWedgeRoundRectangle</h4>
<pre>public static final&nbsp;int CalloutWedgeRoundRectangle</pre>
<div class="block"><p>
 Callout Wedge Round Rectangle Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.CalloutWedgeRoundRectangle">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CalloutWedgeEllipse">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CalloutWedgeEllipse</h4>
<pre>public static final&nbsp;int CalloutWedgeEllipse</pre>
<div class="block"><p>
 Callout Wedge Ellipse Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.CalloutWedgeEllipse">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CalloutCloud">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CalloutCloud</h4>
<pre>public static final&nbsp;int CalloutCloud</pre>
<div class="block"><p>
 Callout Cloud Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.CalloutCloud">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Cloud">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Cloud</h4>
<pre>public static final&nbsp;int Cloud</pre>
<div class="block"><p>
 Cloud Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.Cloud">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Ribbon">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Ribbon</h4>
<pre>public static final&nbsp;int Ribbon</pre>
<div class="block"><p>
 Ribbon Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.Ribbon">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Ribbon2">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Ribbon2</h4>
<pre>public static final&nbsp;int Ribbon2</pre>
<div class="block"><p>
 Ribbon 2 Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.Ribbon2">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="EllipseRibbon">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>EllipseRibbon</h4>
<pre>public static final&nbsp;int EllipseRibbon</pre>
<div class="block"><p>
 Ellipse Ribbon Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.EllipseRibbon">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="EllipseRibbon2">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>EllipseRibbon2</h4>
<pre>public static final&nbsp;int EllipseRibbon2</pre>
<div class="block"><p>
 Ellipse Ribbon 2 Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.EllipseRibbon2">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="LeftRightRibbon">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LeftRightRibbon</h4>
<pre>public static final&nbsp;int LeftRightRibbon</pre>
<div class="block"><p>
 Left Right Ribbon Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.LeftRightRibbon">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="VerticalScroll">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>VerticalScroll</h4>
<pre>public static final&nbsp;int VerticalScroll</pre>
<div class="block"><p>
 Vertical Scroll Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.VerticalScroll">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="HorizontalScroll">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>HorizontalScroll</h4>
<pre>public static final&nbsp;int HorizontalScroll</pre>
<div class="block"><p>
 Horizontal Scroll Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.HorizontalScroll">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Wave">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Wave</h4>
<pre>public static final&nbsp;int Wave</pre>
<div class="block"><p>
 Wave Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.Wave">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DoubleWave">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DoubleWave</h4>
<pre>public static final&nbsp;int DoubleWave</pre>
<div class="block"><p>
 Double Wave Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.DoubleWave">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Plus">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Plus</h4>
<pre>public static final&nbsp;int Plus</pre>
<div class="block"><p>
 Plus Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.Plus">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="ProcessFlow">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ProcessFlow</h4>
<pre>public static final&nbsp;int ProcessFlow</pre>
<div class="block"><p>
 Process Flow Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.ProcessFlow">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DecisionFlow">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DecisionFlow</h4>
<pre>public static final&nbsp;int DecisionFlow</pre>
<div class="block"><p>
 Decision Flow Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.DecisionFlow">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="InputOutputFlow">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>InputOutputFlow</h4>
<pre>public static final&nbsp;int InputOutputFlow</pre>
<div class="block"><p>
 Input Output Flow Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.InputOutputFlow">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="PredefinedProcessFlow">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PredefinedProcessFlow</h4>
<pre>public static final&nbsp;int PredefinedProcessFlow</pre>
<div class="block"><p>
 Predefined Process Flow Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.PredefinedProcessFlow">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="InternalStorageFlow">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>InternalStorageFlow</h4>
<pre>public static final&nbsp;int InternalStorageFlow</pre>
<div class="block"><p>
 Internal Storage Flow Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.InternalStorageFlow">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DocumentFlow">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DocumentFlow</h4>
<pre>public static final&nbsp;int DocumentFlow</pre>
<div class="block"><p>
 Document Flow Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.DocumentFlow">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MultiDocumentFlow">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MultiDocumentFlow</h4>
<pre>public static final&nbsp;int MultiDocumentFlow</pre>
<div class="block"><p>
 Multi-Document Flow Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.MultiDocumentFlow">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="TerminatorFlow">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TerminatorFlow</h4>
<pre>public static final&nbsp;int TerminatorFlow</pre>
<div class="block"><p>
 Terminator Flow Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.TerminatorFlow">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="PreparationFlow">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PreparationFlow</h4>
<pre>public static final&nbsp;int PreparationFlow</pre>
<div class="block"><p>
 Preparation Flow Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.PreparationFlow">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="ManualInputFlow">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ManualInputFlow</h4>
<pre>public static final&nbsp;int ManualInputFlow</pre>
<div class="block"><p>
 Manual Input Flow Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.ManualInputFlow">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="ManualOperationFlow">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ManualOperationFlow</h4>
<pre>public static final&nbsp;int ManualOperationFlow</pre>
<div class="block"><p>
 Manual Operation Flow Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.ManualOperationFlow">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="ConnectorFlow">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ConnectorFlow</h4>
<pre>public static final&nbsp;int ConnectorFlow</pre>
<div class="block"><p>
 Connector Flow Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.ConnectorFlow">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="PunchedCardFlow">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PunchedCardFlow</h4>
<pre>public static final&nbsp;int PunchedCardFlow</pre>
<div class="block"><p>
 Punched Card Flow Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.PunchedCardFlow">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="PunchedTapeFlow">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PunchedTapeFlow</h4>
<pre>public static final&nbsp;int PunchedTapeFlow</pre>
<div class="block"><p>
 Punched Tape Flow Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.PunchedTapeFlow">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="SummingJunctionFlow">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SummingJunctionFlow</h4>
<pre>public static final&nbsp;int SummingJunctionFlow</pre>
<div class="block"><p>
 Summing Junction Flow Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.SummingJunctionFlow">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="OrFlow">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OrFlow</h4>
<pre>public static final&nbsp;int OrFlow</pre>
<div class="block"><p>
 Or Flow Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.OrFlow">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CollateFlow">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CollateFlow</h4>
<pre>public static final&nbsp;int CollateFlow</pre>
<div class="block"><p>
 Collate Flow Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.CollateFlow">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="SortFlow">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SortFlow</h4>
<pre>public static final&nbsp;int SortFlow</pre>
<div class="block"><p>
 Sort Flow Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.SortFlow">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="ExtractFlow">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ExtractFlow</h4>
<pre>public static final&nbsp;int ExtractFlow</pre>
<div class="block"><p>
 Extract Flow Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.ExtractFlow">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MergeFlow">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MergeFlow</h4>
<pre>public static final&nbsp;int MergeFlow</pre>
<div class="block"><p>
 Merge Flow Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.MergeFlow">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="OfflineStorageFlow">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OfflineStorageFlow</h4>
<pre>public static final&nbsp;int OfflineStorageFlow</pre>
<div class="block"><p>
 Offline Storage Flow Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.OfflineStorageFlow">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="OnlineStorageFlow">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OnlineStorageFlow</h4>
<pre>public static final&nbsp;int OnlineStorageFlow</pre>
<div class="block"><p>
 Online Storage Flow Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.OnlineStorageFlow">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MagneticTapeFlow">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MagneticTapeFlow</h4>
<pre>public static final&nbsp;int MagneticTapeFlow</pre>
<div class="block"><p>
 Magnetic Tape Flow Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.MagneticTapeFlow">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MagneticDiskFlow">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MagneticDiskFlow</h4>
<pre>public static final&nbsp;int MagneticDiskFlow</pre>
<div class="block"><p>
 Magnetic Disk Flow Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.MagneticDiskFlow">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MagneticDrumFlow">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MagneticDrumFlow</h4>
<pre>public static final&nbsp;int MagneticDrumFlow</pre>
<div class="block"><p>
 Magnetic Drum Flow Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.MagneticDrumFlow">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DisplayFlow">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DisplayFlow</h4>
<pre>public static final&nbsp;int DisplayFlow</pre>
<div class="block"><p>
 Display Flow Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.DisplayFlow">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DelayFlow">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DelayFlow</h4>
<pre>public static final&nbsp;int DelayFlow</pre>
<div class="block"><p>
 Delay Flow Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.DelayFlow">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="AlternateProcessFlow">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>AlternateProcessFlow</h4>
<pre>public static final&nbsp;int AlternateProcessFlow</pre>
<div class="block"><p>
 Alternate Process Flow Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.AlternateProcessFlow">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="OffPageConnectorFlow">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OffPageConnectorFlow</h4>
<pre>public static final&nbsp;int OffPageConnectorFlow</pre>
<div class="block"><p>
 Off-Page Connector Flow Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.OffPageConnectorFlow">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="BlankButton">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BlankButton</h4>
<pre>public static final&nbsp;int BlankButton</pre>
<div class="block"><p>
 Blank Button Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.BlankButton">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="HomeButton">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>HomeButton</h4>
<pre>public static final&nbsp;int HomeButton</pre>
<div class="block"><p>
 Home Button Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.HomeButton">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="HelpButton">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>HelpButton</h4>
<pre>public static final&nbsp;int HelpButton</pre>
<div class="block"><p>
 Help Button Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.HelpButton">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="InformationButton">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>InformationButton</h4>
<pre>public static final&nbsp;int InformationButton</pre>
<div class="block"><p>
 Information Button Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.InformationButton">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="ForwardOrNextButton">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ForwardOrNextButton</h4>
<pre>public static final&nbsp;int ForwardOrNextButton</pre>
<div class="block"><p>
 Forward or Next Button Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.ForwardOrNextButton">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="BackOrPreviousButton">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BackOrPreviousButton</h4>
<pre>public static final&nbsp;int BackOrPreviousButton</pre>
<div class="block"><p>
 Back or Previous Button Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.BackOrPreviousButton">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="EndButton">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>EndButton</h4>
<pre>public static final&nbsp;int EndButton</pre>
<div class="block"><p>
 End Button Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.EndButton">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="BeginningButton">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BeginningButton</h4>
<pre>public static final&nbsp;int BeginningButton</pre>
<div class="block"><p>
 Beginning Button Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.BeginningButton">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="ReturnButton">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ReturnButton</h4>
<pre>public static final&nbsp;int ReturnButton</pre>
<div class="block"><p>
 Return Button Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.ReturnButton">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DocumentButton">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DocumentButton</h4>
<pre>public static final&nbsp;int DocumentButton</pre>
<div class="block"><p>
 Document Button Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.DocumentButton">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="SoundButton">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SoundButton</h4>
<pre>public static final&nbsp;int SoundButton</pre>
<div class="block"><p>
 Sound Button Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.SoundButton">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MovieButton">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MovieButton</h4>
<pre>public static final&nbsp;int MovieButton</pre>
<div class="block"><p>
 Movie Button Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.MovieButton">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Gear6">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Gear6</h4>
<pre>public static final&nbsp;int Gear6</pre>
<div class="block"><p>
 Gear 6 Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.Gear6">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Gear9">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Gear9</h4>
<pre>public static final&nbsp;int Gear9</pre>
<div class="block"><p>
 Gear 9 Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.Gear9">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Funnel">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Funnel</h4>
<pre>public static final&nbsp;int Funnel</pre>
<div class="block"><p>
 Funnel Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.Funnel">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="PlusMath">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PlusMath</h4>
<pre>public static final&nbsp;int PlusMath</pre>
<div class="block"><p>
 Plus Math Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.PlusMath">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MinusMath">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MinusMath</h4>
<pre>public static final&nbsp;int MinusMath</pre>
<div class="block"><p>
 Minus Math Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.MinusMath">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MultiplyMath">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MultiplyMath</h4>
<pre>public static final&nbsp;int MultiplyMath</pre>
<div class="block"><p>
 Multiply Math Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.MultiplyMath">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DivideMath">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DivideMath</h4>
<pre>public static final&nbsp;int DivideMath</pre>
<div class="block"><p>
 Divide Math Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.DivideMath">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="EqualMath">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>EqualMath</h4>
<pre>public static final&nbsp;int EqualMath</pre>
<div class="block"><p>
 Equal Math Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.EqualMath">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="NotEqualMath">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NotEqualMath</h4>
<pre>public static final&nbsp;int NotEqualMath</pre>
<div class="block"><p>
 Not Equal Math Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.NotEqualMath">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CornerTabs">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CornerTabs</h4>
<pre>public static final&nbsp;int CornerTabs</pre>
<div class="block"><p>
 Corner Tabs Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.CornerTabs">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="SquareTabs">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SquareTabs</h4>
<pre>public static final&nbsp;int SquareTabs</pre>
<div class="block"><p>
 Square Tabs Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.SquareTabs">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="PlaqueTabs">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PlaqueTabs</h4>
<pre>public static final&nbsp;int PlaqueTabs</pre>
<div class="block"><p>
 Plaque Tabs Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.PlaqueTabs">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="ChartX">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ChartX</h4>
<pre>public static final&nbsp;int ChartX</pre>
<div class="block"><p>
 Chart X Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.ChartX">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="ChartStar">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ChartStar</h4>
<pre>public static final&nbsp;int ChartStar</pre>
<div class="block"><p>
 Chart Star Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.ChartStar">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="ChartPlus">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>ChartPlus</h4>
<pre>public static final&nbsp;int ChartPlus</pre>
<div class="block"><p>
 Chart Plus Shape.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.ShapeType.ChartPlus">Constant Field Values</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/ShapeThumbnailBounds.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/ShapeUtil.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/ShapeType.html" target="_top">Frames</a></li>
<li><a href="ShapeType.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.classes.inherited.from.class.com.aspose.ms.System.Enum">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#methods.inherited.from.class.com.aspose.ms.System.Enum">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li>Method</li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><i>Copyright &copy; 2004-2025 Aspose Pty Ltd. All Rights Reserved.</i></small></p>
</body>
</html>
