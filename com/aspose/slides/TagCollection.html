<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_421) on Fri Jul 11 05:32:44 CDT 2025 -->
<title>TagCollection (Aspose.Slides for Java (25.7) API Documentation)</title>
<meta name="date" content="2025-07-11">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="TagCollection (Aspose.Slides for Java (25.7) API Documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/TableStylePreset.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/TemplateContext.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/TagCollection.html" target="_top">Frames</a></li>
<li><a href="TagCollection.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.aspose.slides</div>
<h2 title="Class TagCollection" class="title">Class TagCollection</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.aspose.slides.TagCollection</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd>com.aspose.ms.System.Collections.Generic.IGenericEnumerable&lt;com.aspose.ms.System.Collections.Generic.KeyValuePair&lt;java.lang.String,java.lang.String&gt;&gt;, com.aspose.ms.System.Collections.ICollection&lt;com.aspose.ms.System.Collections.Generic.KeyValuePair&lt;java.lang.String,java.lang.String&gt;&gt;, com.aspose.ms.System.Collections.IEnumerable&lt;com.aspose.ms.System.Collections.Generic.KeyValuePair&lt;java.lang.String,java.lang.String&gt;&gt;, <a href="../../../com/aspose/slides/IGenericCollection.html" title="interface in com.aspose.slides">IGenericCollection</a>&lt;com.aspose.ms.System.Collections.Generic.KeyValuePair&lt;java.lang.String,java.lang.String&gt;&gt;, <a href="../../../com/aspose/slides/ITagCollection.html" title="interface in com.aspose.slides">ITagCollection</a>, java.lang.Iterable&lt;com.aspose.ms.System.Collections.Generic.KeyValuePair&lt;java.lang.String,java.lang.String&gt;&gt;</dd>
</dl>
<hr>
<br>
<pre>public final class <span class="typeNameLabel">TagCollection</span>
extends java.lang.Object
implements <a href="../../../com/aspose/slides/ITagCollection.html" title="interface in com.aspose.slides">ITagCollection</a></pre>
<div class="block"><p>
 Represents the collection of tags (user defined pairs of strings)
 </p><p><hr><blockquote><pre>
 The following example shows how to add a tag to a PowerPoint Presentation.
 <pre>
 Presentation pres = new Presentation("pres.pptx");
 try {
     ITagCollection tags = pres.getCustomData().getTags();
     pres.getCustomData().getTags().add("MyTag", "My Tag Value");
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TagCollection.html#add-java.lang.String-java.lang.String-">add</a></span>(java.lang.String&nbsp;name,
   java.lang.String&nbsp;value)</code>
<div class="block">
 Adds a new tag to collection.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TagCollection.html#clear--">clear</a></span>()</code>
<div class="block">
 Removes all tags from the collection.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TagCollection.html#contains-java.lang.String-">contains</a></span>(java.lang.String&nbsp;name)</code>
<div class="block">
 Determines whether the collection contains a specific name.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TagCollection.html#copyTo-com.aspose.ms.System.Array-int-">copyTo</a></span>(com.aspose.ms.System.Array&nbsp;array,
      int&nbsp;index)</code>
<div class="block">
 Copies all elements from the collection into the specified array.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TagCollection.html#get_Item-java.lang.String-">get_Item</a></span>(java.lang.String&nbsp;name)</code>
<div class="block">
 Returns or sets a key and a value pair of a tag.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TagCollection.html#getNameByIndex-int-">getNameByIndex</a></span>(int&nbsp;index)</code>
<div class="block">
 Returns key of a tag at the specified index.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>java.lang.String[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TagCollection.html#getNamesOfTags--">getNamesOfTags</a></span>()</code>
<div class="block">
 Returns names of tags.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>java.lang.Object</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TagCollection.html#getSyncRoot--">getSyncRoot</a></span>()</code>
<div class="block">
 Returns a synchronization root.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TagCollection.html#getValueByIndex-int-">getValueByIndex</a></span>(int&nbsp;index)</code>
<div class="block">
 Returns value of a tag at the specified index.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TagCollection.html#indexOfName-java.lang.String-">indexOfName</a></span>(java.lang.String&nbsp;name)</code>
<div class="block">
 Returns the zero-based index of the specified key in the collection.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TagCollection.html#isSynchronized--">isSynchronized</a></span>()</code>
<div class="block">
 Returns a value indicating whether access to the collection is synchronized (thread-safe).</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>com.aspose.ms.System.Collections.Generic.IGenericEnumerator&lt;com.aspose.ms.System.Collections.Generic.KeyValuePair&lt;java.lang.String,java.lang.String&gt;&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TagCollection.html#iterator--">iterator</a></span>()</code>
<div class="block">
 Returns an enumerator that iterates through the collection.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>com.aspose.ms.System.Collections.Generic.IGenericEnumerator&lt;com.aspose.ms.System.Collections.Generic.KeyValuePair&lt;java.lang.String,java.lang.String&gt;&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TagCollection.html#iteratorJava--">iteratorJava</a></span>()</code>
<div class="block">
 Returns a java iterator for the entire collection.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TagCollection.html#remove-java.lang.String-">remove</a></span>(java.lang.String&nbsp;name)</code>
<div class="block">
 Removes the tag with a specified name from the collection.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TagCollection.html#removeAt-int-">removeAt</a></span>(int&nbsp;index)</code>
<div class="block">
 Removes the tag at the specified index.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TagCollection.html#set_Item-java.lang.String-java.lang.String-">set_Item</a></span>(java.lang.String&nbsp;name,
        java.lang.String&nbsp;value)</code>
<div class="block">
 Returns or sets a key and a value pair of a tag.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/TagCollection.html#size--">size</a></span>()</code>
<div class="block">
 Returns a number of tags in the collectoin.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Iterable">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;java.lang.Iterable</h3>
<code>forEach, spliterator</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="size--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>size</h4>
<pre>public final&nbsp;int&nbsp;size()</pre>
<div class="block"><p>
 Returns a number of tags in the collectoin.
 Read-only <code>int</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>size</code>&nbsp;in interface&nbsp;<code>com.aspose.ms.System.Collections.ICollection&lt;com.aspose.ms.System.Collections.Generic.KeyValuePair&lt;java.lang.String,java.lang.String&gt;&gt;</code></dd>
</dl>
</li>
</ul>
<a name="add-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>add</h4>
<pre>public final&nbsp;int&nbsp;add(java.lang.String&nbsp;name,
                     java.lang.String&nbsp;value)</pre>
<div class="block"><p>
 Adds a new tag to collection.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITagCollection.html#add-java.lang.String-java.lang.String-">add</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITagCollection.html" title="interface in com.aspose.slides">ITagCollection</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - The name of the tag.</dd>
<dd><code>value</code> - The value of the tag.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The index of the added tag.</dd>
</dl>
</li>
</ul>
<a name="remove-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>remove</h4>
<pre>public final&nbsp;void&nbsp;remove(java.lang.String&nbsp;name)</pre>
<div class="block"><p>
 Removes the tag with a specified name from the collection.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITagCollection.html#remove-java.lang.String-">remove</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITagCollection.html" title="interface in com.aspose.slides">ITagCollection</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - The name of tag to remove.</dd>
</dl>
</li>
</ul>
<a name="indexOfName-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>indexOfName</h4>
<pre>public final&nbsp;int&nbsp;indexOfName(java.lang.String&nbsp;name)</pre>
<div class="block"><p>
 Returns the zero-based index of the specified key in the collection.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITagCollection.html#indexOfName-java.lang.String-">indexOfName</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITagCollection.html" title="interface in com.aspose.slides">ITagCollection</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - The name to locate in the collection.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The zero-based index of key, if key is found in the collection; otherwise, -1.</dd>
</dl>
</li>
</ul>
<a name="contains-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>contains</h4>
<pre>public final&nbsp;boolean&nbsp;contains(java.lang.String&nbsp;name)</pre>
<div class="block"><p>
 Determines whether the collection contains a specific name.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITagCollection.html#contains-java.lang.String-">contains</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITagCollection.html" title="interface in com.aspose.slides">ITagCollection</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - The key to locate.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>True if the collection contains an tag with the specified key; otherwise, false.</dd>
</dl>
</li>
</ul>
<a name="removeAt-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>removeAt</h4>
<pre>public final&nbsp;void&nbsp;removeAt(int&nbsp;index)</pre>
<div class="block"><p>
 Removes the tag at the specified index.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITagCollection.html#removeAt-int-">removeAt</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITagCollection.html" title="interface in com.aspose.slides">ITagCollection</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - The zero-based index of the tag to remove.</dd>
</dl>
</li>
</ul>
<a name="clear--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>clear</h4>
<pre>public final&nbsp;void&nbsp;clear()</pre>
<div class="block"><p>
 Removes all tags from the collection.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITagCollection.html#clear--">clear</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITagCollection.html" title="interface in com.aspose.slides">ITagCollection</a></code></dd>
</dl>
</li>
</ul>
<a name="getValueByIndex-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getValueByIndex</h4>
<pre>public final&nbsp;java.lang.String&nbsp;getValueByIndex(int&nbsp;index)</pre>
<div class="block"><p>
 Returns value of a tag at the specified index.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITagCollection.html#getValueByIndex-int-">getValueByIndex</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITagCollection.html" title="interface in com.aspose.slides">ITagCollection</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - Index of a tag to return.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Value of a tag.</dd>
</dl>
</li>
</ul>
<a name="getNameByIndex-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNameByIndex</h4>
<pre>public final&nbsp;java.lang.String&nbsp;getNameByIndex(int&nbsp;index)</pre>
<div class="block"><p>
 Returns key of a tag at the specified index.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITagCollection.html#getNameByIndex-int-">getNameByIndex</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITagCollection.html" title="interface in com.aspose.slides">ITagCollection</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - Index of a tag to return.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Key of a tag.</dd>
</dl>
</li>
</ul>
<a name="getNamesOfTags--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNamesOfTags</h4>
<pre>public final&nbsp;java.lang.String[]&nbsp;getNamesOfTags()</pre>
<div class="block"><p>
 Returns names of tags.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITagCollection.html#getNamesOfTags--">getNamesOfTags</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITagCollection.html" title="interface in com.aspose.slides">ITagCollection</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Names of tags.</dd>
</dl>
</li>
</ul>
<a name="get_Item-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_Item</h4>
<pre>public final&nbsp;java.lang.String&nbsp;get_Item(java.lang.String&nbsp;name)</pre>
<div class="block"><p>
 Returns or sets a key and a value pair of a tag.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITagCollection.html#get_Item-java.lang.String-">get_Item</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITagCollection.html" title="interface in com.aspose.slides">ITagCollection</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - Key of a tag.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Value of a tag.</dd>
</dl>
</li>
</ul>
<a name="set_Item-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>set_Item</h4>
<pre>public final&nbsp;void&nbsp;set_Item(java.lang.String&nbsp;name,
                           java.lang.String&nbsp;value)</pre>
<div class="block"><p>
 Returns or sets a key and a value pair of a tag.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITagCollection.html#set_Item-java.lang.String-java.lang.String-">set_Item</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITagCollection.html" title="interface in com.aspose.slides">ITagCollection</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - Key of a tag.</dd>
</dl>
</li>
</ul>
<a name="copyTo-com.aspose.ms.System.Array-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>copyTo</h4>
<pre>public final&nbsp;void&nbsp;copyTo(com.aspose.ms.System.Array&nbsp;array,
                         int&nbsp;index)</pre>
<div class="block"><p>
 Copies all elements from the collection into the specified array.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>copyTo</code>&nbsp;in interface&nbsp;<code>com.aspose.ms.System.Collections.ICollection&lt;com.aspose.ms.System.Collections.Generic.KeyValuePair&lt;java.lang.String,java.lang.String&gt;&gt;</code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>array</code> - Array to fill.</dd>
<dd><code>index</code> - Starting position in target array.</dd>
</dl>
</li>
</ul>
<a name="isSynchronized--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isSynchronized</h4>
<pre>public final&nbsp;boolean&nbsp;isSynchronized()</pre>
<div class="block"><p>
 Returns a value indicating whether access to the collection is synchronized (thread-safe).
 Read-only <code>boolean</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>isSynchronized</code>&nbsp;in interface&nbsp;<code>com.aspose.ms.System.Collections.ICollection&lt;com.aspose.ms.System.Collections.Generic.KeyValuePair&lt;java.lang.String,java.lang.String&gt;&gt;</code></dd>
</dl>
</li>
</ul>
<a name="getSyncRoot--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSyncRoot</h4>
<pre>public final&nbsp;java.lang.Object&nbsp;getSyncRoot()</pre>
<div class="block"><p>
 Returns a synchronization root.
 Read-only <code>Object</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>getSyncRoot</code>&nbsp;in interface&nbsp;<code>com.aspose.ms.System.Collections.ICollection&lt;com.aspose.ms.System.Collections.Generic.KeyValuePair&lt;java.lang.String,java.lang.String&gt;&gt;</code></dd>
</dl>
</li>
</ul>
<a name="iterator--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>iterator</h4>
<pre>public final&nbsp;com.aspose.ms.System.Collections.Generic.IGenericEnumerator&lt;com.aspose.ms.System.Collections.Generic.KeyValuePair&lt;java.lang.String,java.lang.String&gt;&gt;&nbsp;iterator()</pre>
<div class="block"><p>
 Returns an enumerator that iterates through the collection.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>iterator</code>&nbsp;in interface&nbsp;<code>com.aspose.ms.System.Collections.Generic.IGenericEnumerable&lt;com.aspose.ms.System.Collections.Generic.KeyValuePair&lt;java.lang.String,java.lang.String&gt;&gt;</code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>iterator</code>&nbsp;in interface&nbsp;<code>com.aspose.ms.System.Collections.IEnumerable&lt;com.aspose.ms.System.Collections.Generic.KeyValuePair&lt;java.lang.String,java.lang.String&gt;&gt;</code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>iterator</code>&nbsp;in interface&nbsp;<code>java.lang.Iterable&lt;com.aspose.ms.System.Collections.Generic.KeyValuePair&lt;java.lang.String,java.lang.String&gt;&gt;</code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>A <code>IGenericEnumerator</code> that can be used to iterate through the collection.</dd>
</dl>
</li>
</ul>
<a name="iteratorJava--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>iteratorJava</h4>
<pre>public final&nbsp;com.aspose.ms.System.Collections.Generic.IGenericEnumerator&lt;com.aspose.ms.System.Collections.Generic.KeyValuePair&lt;java.lang.String,java.lang.String&gt;&gt;&nbsp;iteratorJava()</pre>
<div class="block"><p>
 Returns a java iterator for the entire collection.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IGenericCollection.html#iteratorJava--">iteratorJava</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IGenericCollection.html" title="interface in com.aspose.slides">IGenericCollection</a>&lt;com.aspose.ms.System.Collections.Generic.KeyValuePair&lt;java.lang.String,java.lang.String&gt;&gt;</code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>An <code>Iterator</code> for the entire collection.</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/TableStylePreset.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/TemplateContext.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/TagCollection.html" target="_top">Frames</a></li>
<li><a href="TagCollection.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><i>Copyright &copy; 2004-2025 Aspose Pty Ltd. All Rights Reserved.</i></small></p>
</body>
</html>
