<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_421) on Fri Jul 11 05:32:45 CDT 2025 -->
<title>Timing (Aspose.Slides for Java (25.7) API Documentation)</title>
<meta name="date" content="2025-07-11">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Timing (Aspose.Slides for Java (25.7) API Documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10,"i26":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/TimeUnitType.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/Tint.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/Timing.html" target="_top">Frames</a></li>
<li><a href="Timing.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.aspose.slides</div>
<h2 title="Class Timing" class="title">Class Timing</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.aspose.slides.Timing</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../com/aspose/slides/ITiming.html" title="interface in com.aspose.slides">ITiming</a></dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">Timing</span>
extends java.lang.Object
implements <a href="../../../com/aspose/slides/ITiming.html" title="interface in com.aspose.slides">ITiming</a></pre>
<div class="block"><p>
 Represents animation timing.
 </p></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Timing.html#getAccelerate--">getAccelerate</a></span>()</code>
<div class="block">
 Describes the percentage of duration accelerate behavior effect.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Timing.html#getAutoReverse--">getAutoReverse</a></span>()</code>
<div class="block">
 Describes whether to automatically play the animation in reverse after
 playing it in the forward direction.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Timing.html#getDecelerate--">getDecelerate</a></span>()</code>
<div class="block">
 Describes the percentage of duration decelerate behavior effect.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Timing.html#getDuration--">getDuration</a></span>()</code>
<div class="block">
 Describes the duration of animation effect.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>com.aspose.slides.IDOMObject</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Timing.html#getParent_Immediate--">getParent_Immediate</a></span>()</code>
<div class="block">
 Returns Parent_Immediate object.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Timing.html#getRepeatCount--">getRepeatCount</a></span>()</code>
<div class="block">
 Describes the number of times the effect should repeat.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Timing.html#getRepeatDuration--">getRepeatDuration</a></span>()</code>
<div class="block">
 Describes the number of times the effect should repeat.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Timing.html#getRepeatUntilEndSlide--">getRepeatUntilEndSlide</a></span>()</code>
<div class="block">
  This attribute specifies if the effect will repeat until the end of the slide.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Timing.html#getRepeatUntilNextClick--">getRepeatUntilNextClick</a></span>()</code>
<div class="block">
  This attribute specifies if the effect will repeat until the next click.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Timing.html#getRestart--">getRestart</a></span>()</code>
<div class="block">
 Specifies if a effect is to restart after complete.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Timing.html#getRewind--">getRewind</a></span>()</code>
<div class="block">
  This attribute specifies if the effect will rewind when done playing.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Timing.html#getSpeed--">getSpeed</a></span>()</code>
<div class="block">
 Specifies the percentage by which to speed up (or slow down) the timing.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Timing.html#getTriggerDelayTime--">getTriggerDelayTime</a></span>()</code>
<div class="block">
 Describes delay time after trigger.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Timing.html#getTriggerType--">getTriggerType</a></span>()</code>
<div class="block">
 Describes trigger type.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Timing.html#setAccelerate-float-">setAccelerate</a></span>(float&nbsp;value)</code>
<div class="block">
 Describes the percentage of duration accelerate behavior effect.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Timing.html#setAutoReverse-boolean-">setAutoReverse</a></span>(boolean&nbsp;value)</code>
<div class="block">
 Describes whether to automatically play the animation in reverse after
 playing it in the forward direction.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Timing.html#setDecelerate-float-">setDecelerate</a></span>(float&nbsp;value)</code>
<div class="block">
 Describes the percentage of duration decelerate behavior effect.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Timing.html#setDuration-float-">setDuration</a></span>(float&nbsp;value)</code>
<div class="block">
 Describes the duration of animation effect.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Timing.html#setRepeatCount-float-">setRepeatCount</a></span>(float&nbsp;value)</code>
<div class="block">
 Describes the number of times the effect should repeat.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Timing.html#setRepeatDuration-float-">setRepeatDuration</a></span>(float&nbsp;value)</code>
<div class="block">
 Describes the number of times the effect should repeat.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Timing.html#setRepeatUntilEndSlide-boolean-">setRepeatUntilEndSlide</a></span>(boolean&nbsp;value)</code>
<div class="block">
  This attribute specifies if the effect will repeat until the end of the slide.</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Timing.html#setRepeatUntilNextClick-boolean-">setRepeatUntilNextClick</a></span>(boolean&nbsp;value)</code>
<div class="block">
  This attribute specifies if the effect will repeat until the next click.</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Timing.html#setRestart-int-">setRestart</a></span>(int&nbsp;value)</code>
<div class="block">
 Specifies if a effect is to restart after complete.</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Timing.html#setRewind-boolean-">setRewind</a></span>(boolean&nbsp;value)</code>
<div class="block">
  This attribute specifies if the effect will rewind when done playing.</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Timing.html#setSpeed-float-">setSpeed</a></span>(float&nbsp;value)</code>
<div class="block">
 Specifies the percentage by which to speed up (or slow down) the timing.</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Timing.html#setTriggerDelayTime-float-">setTriggerDelayTime</a></span>(float&nbsp;value)</code>
<div class="block">
 Describes delay time after trigger.</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Timing.html#setTriggerType-int-">setTriggerType</a></span>(int&nbsp;value)</code>
<div class="block">
 Describes trigger type.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getAccelerate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAccelerate</h4>
<pre>public final&nbsp;float&nbsp;getAccelerate()</pre>
<div class="block"><p>
 Describes the percentage of duration accelerate behavior effect.
 Read/write <code>float</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITiming.html#getAccelerate--">getAccelerate</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITiming.html" title="interface in com.aspose.slides">ITiming</a></code></dd>
</dl>
</li>
</ul>
<a name="setAccelerate-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAccelerate</h4>
<pre>public final&nbsp;void&nbsp;setAccelerate(float&nbsp;value)</pre>
<div class="block"><p>
 Describes the percentage of duration accelerate behavior effect.
 Read/write <code>float</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITiming.html#setAccelerate-float-">setAccelerate</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITiming.html" title="interface in com.aspose.slides">ITiming</a></code></dd>
</dl>
</li>
</ul>
<a name="getDecelerate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDecelerate</h4>
<pre>public final&nbsp;float&nbsp;getDecelerate()</pre>
<div class="block"><p>
 Describes the percentage of duration decelerate behavior effect.
 Read/write <code>float</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITiming.html#getDecelerate--">getDecelerate</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITiming.html" title="interface in com.aspose.slides">ITiming</a></code></dd>
</dl>
</li>
</ul>
<a name="setDecelerate-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDecelerate</h4>
<pre>public final&nbsp;void&nbsp;setDecelerate(float&nbsp;value)</pre>
<div class="block"><p>
 Describes the percentage of duration decelerate behavior effect.
 Read/write <code>float</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITiming.html#setDecelerate-float-">setDecelerate</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITiming.html" title="interface in com.aspose.slides">ITiming</a></code></dd>
</dl>
</li>
</ul>
<a name="getAutoReverse--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAutoReverse</h4>
<pre>public final&nbsp;boolean&nbsp;getAutoReverse()</pre>
<div class="block"><p>
 Describes whether to automatically play the animation in reverse after
 playing it in the forward direction.
 Read/write <code>boolean</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITiming.html#getAutoReverse--">getAutoReverse</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITiming.html" title="interface in com.aspose.slides">ITiming</a></code></dd>
</dl>
</li>
</ul>
<a name="setAutoReverse-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAutoReverse</h4>
<pre>public final&nbsp;void&nbsp;setAutoReverse(boolean&nbsp;value)</pre>
<div class="block"><p>
 Describes whether to automatically play the animation in reverse after
 playing it in the forward direction.
 Read/write <code>boolean</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITiming.html#setAutoReverse-boolean-">setAutoReverse</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITiming.html" title="interface in com.aspose.slides">ITiming</a></code></dd>
</dl>
</li>
</ul>
<a name="getDuration--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDuration</h4>
<pre>public final&nbsp;float&nbsp;getDuration()</pre>
<div class="block"><p>
 Describes the duration of animation effect.
 Read/write <code>float</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITiming.html#getDuration--">getDuration</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITiming.html" title="interface in com.aspose.slides">ITiming</a></code></dd>
</dl>
</li>
</ul>
<a name="setDuration-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDuration</h4>
<pre>public final&nbsp;void&nbsp;setDuration(float&nbsp;value)</pre>
<div class="block"><p>
 Describes the duration of animation effect.
 Read/write <code>float</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITiming.html#setDuration-float-">setDuration</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITiming.html" title="interface in com.aspose.slides">ITiming</a></code></dd>
</dl>
</li>
</ul>
<a name="getRepeatCount--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRepeatCount</h4>
<pre>public final&nbsp;float&nbsp;getRepeatCount()</pre>
<div class="block"><p>
 Describes the number of times the effect should repeat.
 Read/write <code>float</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITiming.html#getRepeatCount--">getRepeatCount</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITiming.html" title="interface in com.aspose.slides">ITiming</a></code></dd>
</dl>
</li>
</ul>
<a name="setRepeatCount-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRepeatCount</h4>
<pre>public final&nbsp;void&nbsp;setRepeatCount(float&nbsp;value)</pre>
<div class="block"><p>
 Describes the number of times the effect should repeat.
 Read/write <code>float</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITiming.html#setRepeatCount-float-">setRepeatCount</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITiming.html" title="interface in com.aspose.slides">ITiming</a></code></dd>
</dl>
</li>
</ul>
<a name="getRepeatUntilEndSlide--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRepeatUntilEndSlide</h4>
<pre>public final&nbsp;boolean&nbsp;getRepeatUntilEndSlide()</pre>
<div class="block"><p>
  This attribute specifies if the effect will repeat until the end of the slide.
  Read/write <code>boolean</code>.
  </p><p><hr><blockquote><pre>
  <pre>
 Presentation presentation = new Presentation("demo.pptx");
 try {
     // Get the effects sequence for the first slide
     ISequence effectsSequence = presentation.getSlides().get_Item(0).getTimeline().getMainSequence();
     // Get the first effect of main sequence.
     IEffect effect = effectsSequence.get_Item(0);
     // Change the effect Timing/Repeat to "Until End of Slide"
     effect.getTiming().setRepeatUntilEndSlide(true);
 } finally {
     if (presentation != null) presentation.dispose();
 }
  </pre>
  </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITiming.html#getRepeatUntilEndSlide--">getRepeatUntilEndSlide</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITiming.html" title="interface in com.aspose.slides">ITiming</a></code></dd>
</dl>
</li>
</ul>
<a name="setRepeatUntilEndSlide-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRepeatUntilEndSlide</h4>
<pre>public final&nbsp;void&nbsp;setRepeatUntilEndSlide(boolean&nbsp;value)</pre>
<div class="block"><p>
  This attribute specifies if the effect will repeat until the end of the slide.
  Read/write <code>boolean</code>.
  </p><p><hr><blockquote><pre>
  <pre>
 Presentation presentation = new Presentation("demo.pptx");
 try {
     // Get the effects sequence for the first slide
     ISequence effectsSequence = presentation.getSlides().get_Item(0).getTimeline().getMainSequence();
     // Get the first effect of main sequence.
     IEffect effect = effectsSequence.get_Item(0);
     // Change the effect Timing/Repeat to "Until End of Slide"
     effect.getTiming().setRepeatUntilEndSlide(true);
 } finally {
     if (presentation != null) presentation.dispose();
 }
  </pre>
  </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITiming.html#setRepeatUntilEndSlide-boolean-">setRepeatUntilEndSlide</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITiming.html" title="interface in com.aspose.slides">ITiming</a></code></dd>
</dl>
</li>
</ul>
<a name="getRepeatUntilNextClick--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRepeatUntilNextClick</h4>
<pre>public final&nbsp;boolean&nbsp;getRepeatUntilNextClick()</pre>
<div class="block"><p>
  This attribute specifies if the effect will repeat until the next click.
  Read/write <code>boolean</code>.
  </p><p><hr><blockquote><pre>
  <pre>
 Presentation presentation = new Presentation("demo.pptx");
 try {
     // Get the effects sequence for the first slide
     ISequence effectsSequence = presentation.getSlides().get_Item(0).getTimeline().getMainSequence();
     // Get the first effect of main sequence.
     IEffect effect = effectsSequence.get_Item(0);
     // Change the effect Timing/Repeat to "Until Next Click"
     effect.getTiming().setRepeatUntilNextClick(true);
 } finally {
     if (presentation != null) presentation.dispose();
 }
  </pre>
  </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITiming.html#getRepeatUntilNextClick--">getRepeatUntilNextClick</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITiming.html" title="interface in com.aspose.slides">ITiming</a></code></dd>
</dl>
</li>
</ul>
<a name="setRepeatUntilNextClick-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRepeatUntilNextClick</h4>
<pre>public final&nbsp;void&nbsp;setRepeatUntilNextClick(boolean&nbsp;value)</pre>
<div class="block"><p>
  This attribute specifies if the effect will repeat until the next click.
  Read/write <code>boolean</code>.
  </p><p><hr><blockquote><pre>
  <pre>
 Presentation presentation = new Presentation("demo.pptx");
 try {
     // Get the effects sequence for the first slide
     ISequence effectsSequence = presentation.getSlides().get_Item(0).getTimeline().getMainSequence();
     // Get the first effect of main sequence.
     IEffect effect = effectsSequence.get_Item(0);
     // Change the effect Timing/Repeat to "Until Next Click"
     effect.getTiming().setRepeatUntilNextClick(true);
 } finally {
     if (presentation != null) presentation.dispose();
 }
  </pre>
  </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITiming.html#setRepeatUntilNextClick-boolean-">setRepeatUntilNextClick</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITiming.html" title="interface in com.aspose.slides">ITiming</a></code></dd>
</dl>
</li>
</ul>
<a name="getRepeatDuration--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRepeatDuration</h4>
<pre>public final&nbsp;float&nbsp;getRepeatDuration()</pre>
<div class="block"><p>
 Describes the number of times the effect should repeat.
 Read/write <code>float</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITiming.html#getRepeatDuration--">getRepeatDuration</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITiming.html" title="interface in com.aspose.slides">ITiming</a></code></dd>
</dl>
</li>
</ul>
<a name="setRepeatDuration-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRepeatDuration</h4>
<pre>public final&nbsp;void&nbsp;setRepeatDuration(float&nbsp;value)</pre>
<div class="block"><p>
 Describes the number of times the effect should repeat.
 Read/write <code>float</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITiming.html#setRepeatDuration-float-">setRepeatDuration</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITiming.html" title="interface in com.aspose.slides">ITiming</a></code></dd>
</dl>
</li>
</ul>
<a name="getRestart--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRestart</h4>
<pre>public final&nbsp;int&nbsp;getRestart()</pre>
<div class="block"><p>
 Specifies if a effect is to restart after complete.
 Read/write <a href="../../../com/aspose/slides/EffectRestartType.html" title="class in com.aspose.slides"><code>EffectRestartType</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITiming.html#getRestart--">getRestart</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITiming.html" title="interface in com.aspose.slides">ITiming</a></code></dd>
</dl>
</li>
</ul>
<a name="setRestart-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRestart</h4>
<pre>public final&nbsp;void&nbsp;setRestart(int&nbsp;value)</pre>
<div class="block"><p>
 Specifies if a effect is to restart after complete.
 Read/write <a href="../../../com/aspose/slides/EffectRestartType.html" title="class in com.aspose.slides"><code>EffectRestartType</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITiming.html#setRestart-int-">setRestart</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITiming.html" title="interface in com.aspose.slides">ITiming</a></code></dd>
</dl>
</li>
</ul>
<a name="getRewind--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRewind</h4>
<pre>public final&nbsp;boolean&nbsp;getRewind()</pre>
<div class="block"><p>
  This attribute specifies if the effect will rewind when done playing.
  Read/write <code>boolean</code>.
  </p><p><hr><blockquote><pre>
  <pre>
 Presentation presentation = new Presentation("demo.pptx");
 try {
     // Get the effects sequence for the first slide
     ISequence effectsSequence = presentation.getSlides().get_Item(0).getTimeline().getMainSequence();
     // Get the first effect of main sequence.
     IEffect effect = effectsSequence.get_Item(0);
     // Turn the effect Timing/Rewind on.
     effect.getTiming().setRewind(true);
 } finally {
     if (presentation != null) presentation.dispose();
 }
  </pre>
  </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITiming.html#getRewind--">getRewind</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITiming.html" title="interface in com.aspose.slides">ITiming</a></code></dd>
</dl>
</li>
</ul>
<a name="setRewind-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRewind</h4>
<pre>public final&nbsp;void&nbsp;setRewind(boolean&nbsp;value)</pre>
<div class="block"><p>
  This attribute specifies if the effect will rewind when done playing.
  Read/write <code>boolean</code>.
  </p><p><hr><blockquote><pre>
  <pre>
 Presentation presentation = new Presentation("demo.pptx");
 try {
     // Get the effects sequence for the first slide
     ISequence effectsSequence = presentation.getSlides().get_Item(0).getTimeline().getMainSequence();
     // Get the first effect of main sequence.
     IEffect effect = effectsSequence.get_Item(0);
     // Turn the effect Timing/Rewind on.
     effect.getTiming().setRewind(true);
 } finally {
     if (presentation != null) presentation.dispose();
 }
  </pre>
  </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITiming.html#setRewind-boolean-">setRewind</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITiming.html" title="interface in com.aspose.slides">ITiming</a></code></dd>
</dl>
</li>
</ul>
<a name="getSpeed--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSpeed</h4>
<pre>public final&nbsp;float&nbsp;getSpeed()</pre>
<div class="block"><p>
 Specifies the percentage by which to speed up (or slow down) the timing.
 Read/write <code>float</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITiming.html#getSpeed--">getSpeed</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITiming.html" title="interface in com.aspose.slides">ITiming</a></code></dd>
</dl>
</li>
</ul>
<a name="setSpeed-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSpeed</h4>
<pre>public final&nbsp;void&nbsp;setSpeed(float&nbsp;value)</pre>
<div class="block"><p>
 Specifies the percentage by which to speed up (or slow down) the timing.
 Read/write <code>float</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITiming.html#setSpeed-float-">setSpeed</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITiming.html" title="interface in com.aspose.slides">ITiming</a></code></dd>
</dl>
</li>
</ul>
<a name="getTriggerDelayTime--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTriggerDelayTime</h4>
<pre>public final&nbsp;float&nbsp;getTriggerDelayTime()</pre>
<div class="block"><p>
 Describes delay time after trigger.
 Read/write <code>float</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITiming.html#getTriggerDelayTime--">getTriggerDelayTime</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITiming.html" title="interface in com.aspose.slides">ITiming</a></code></dd>
</dl>
</li>
</ul>
<a name="setTriggerDelayTime-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTriggerDelayTime</h4>
<pre>public final&nbsp;void&nbsp;setTriggerDelayTime(float&nbsp;value)</pre>
<div class="block"><p>
 Describes delay time after trigger.
 Read/write <code>float</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITiming.html#setTriggerDelayTime-float-">setTriggerDelayTime</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITiming.html" title="interface in com.aspose.slides">ITiming</a></code></dd>
</dl>
</li>
</ul>
<a name="getTriggerType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTriggerType</h4>
<pre>public final&nbsp;int&nbsp;getTriggerType()</pre>
<div class="block"><p>
 Describes trigger type.
 Read/write <a href="../../../com/aspose/slides/EffectTriggerType.html" title="class in com.aspose.slides"><code>EffectTriggerType</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITiming.html#getTriggerType--">getTriggerType</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITiming.html" title="interface in com.aspose.slides">ITiming</a></code></dd>
</dl>
</li>
</ul>
<a name="setTriggerType-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTriggerType</h4>
<pre>public final&nbsp;void&nbsp;setTriggerType(int&nbsp;value)</pre>
<div class="block"><p>
 Describes trigger type.
 Read/write <a href="../../../com/aspose/slides/EffectTriggerType.html" title="class in com.aspose.slides"><code>EffectTriggerType</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ITiming.html#setTriggerType-int-">setTriggerType</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ITiming.html" title="interface in com.aspose.slides">ITiming</a></code></dd>
</dl>
</li>
</ul>
<a name="getParent_Immediate--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getParent_Immediate</h4>
<pre>public final&nbsp;com.aspose.slides.IDOMObject&nbsp;getParent_Immediate()</pre>
<div class="block"><p>
 Returns Parent_Immediate object.
 Read-only <code>IDOMObject</code>.
 </p></div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/TimeUnitType.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/Tint.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/Timing.html" target="_top">Frames</a></li>
<li><a href="Timing.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><i>Copyright &copy; 2004-2025 Aspose Pty Ltd. All Rights Reserved.</i></small></p>
</body>
</html>
