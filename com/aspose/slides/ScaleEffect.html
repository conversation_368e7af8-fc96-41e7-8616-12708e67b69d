<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_421) on Fri Jul 11 05:32:44 CDT 2025 -->
<title>ScaleEffect (Aspose.Slides for Java (25.7) API Documentation)</title>
<meta name="date" content="2025-07-11">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ScaleEffect (Aspose.Slides for Java (25.7) API Documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/SaveOptionsFactory.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/SchemeColor.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/ScaleEffect.html" target="_top">Frames</a></li>
<li><a href="ScaleEffect.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.aspose.slides</div>
<h2 title="Class ScaleEffect" class="title">Class ScaleEffect</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../com/aspose/slides/Behavior.html" title="class in com.aspose.slides">com.aspose.slides.Behavior</a></li>
<li>
<ul class="inheritance">
<li>com.aspose.slides.ScaleEffect</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../com/aspose/slides/IBehavior.html" title="interface in com.aspose.slides">IBehavior</a>, <a href="../../../com/aspose/slides/IScaleEffect.html" title="interface in com.aspose.slides">IScaleEffect</a></dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">ScaleEffect</span>
extends <a href="../../../com/aspose/slides/Behavior.html" title="class in com.aspose.slides">Behavior</a>
implements <a href="../../../com/aspose/slides/IScaleEffect.html" title="interface in com.aspose.slides">IScaleEffect</a></pre>
<div class="block"><p>
 Represents animation scale effect.
 </p></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ScaleEffect.html#ScaleEffect--">ScaleEffect</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>java.awt.geom.Point2D.Float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ScaleEffect.html#getBy--">getBy</a></span>()</code>
<div class="block">
 describes the relative offset value for the animation (in percents).</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>java.awt.geom.Point2D.Float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ScaleEffect.html#getFrom--">getFrom</a></span>()</code>
<div class="block">
 Specifies an x/y co-ordinate to start the animation from (in percents).</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>java.awt.geom.Point2D.Float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ScaleEffect.html#getTo--">getTo</a></span>()</code>
<div class="block">
 Specifies the target location for an animation scale effect (in percents).</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>byte</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ScaleEffect.html#getZoomContent--">getZoomContent</a></span>()</code>
<div class="block">
 Determines whether a content should be zoomed.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ScaleEffect.html#setBy-java.awt.geom.Point2D.Float-">setBy</a></span>(java.awt.geom.Point2D.Float&nbsp;value)</code>
<div class="block">
 describes the relative offset value for the animation (in percents).</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ScaleEffect.html#setFrom-java.awt.geom.Point2D.Float-">setFrom</a></span>(java.awt.geom.Point2D.Float&nbsp;value)</code>
<div class="block">
 Specifies an x/y co-ordinate to start the animation from (in percents).</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ScaleEffect.html#setTo-java.awt.geom.Point2D.Float-">setTo</a></span>(java.awt.geom.Point2D.Float&nbsp;value)</code>
<div class="block">
 Specifies the target location for an animation scale effect (in percents).</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ScaleEffect.html#setZoomContent-byte-">setZoomContent</a></span>(byte&nbsp;value)</code>
<div class="block">
 Determines whether a content should be zoomed.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.aspose.slides.Behavior">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;com.aspose.slides.<a href="../../../com/aspose/slides/Behavior.html" title="class in com.aspose.slides">Behavior</a></h3>
<code><a href="../../../com/aspose/slides/Behavior.html#getAccumulate--">getAccumulate</a>, <a href="../../../com/aspose/slides/Behavior.html#getAdditive--">getAdditive</a>, <a href="../../../com/aspose/slides/Behavior.html#getParent_Immediate--">getParent_Immediate</a>, <a href="../../../com/aspose/slides/Behavior.html#getProperties--">getProperties</a>, <a href="../../../com/aspose/slides/Behavior.html#getTiming--">getTiming</a>, <a href="../../../com/aspose/slides/Behavior.html#setAccumulate-byte-">setAccumulate</a>, <a href="../../../com/aspose/slides/Behavior.html#setAdditive-int-">setAdditive</a>, <a href="../../../com/aspose/slides/Behavior.html#setTiming-com.aspose.slides.ITiming-">setTiming</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.aspose.slides.IBehavior">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;com.aspose.slides.<a href="../../../com/aspose/slides/IBehavior.html" title="interface in com.aspose.slides">IBehavior</a></h3>
<code><a href="../../../com/aspose/slides/IBehavior.html#getAccumulate--">getAccumulate</a>, <a href="../../../com/aspose/slides/IBehavior.html#getAdditive--">getAdditive</a>, <a href="../../../com/aspose/slides/IBehavior.html#getProperties--">getProperties</a>, <a href="../../../com/aspose/slides/IBehavior.html#getTiming--">getTiming</a>, <a href="../../../com/aspose/slides/IBehavior.html#setAccumulate-byte-">setAccumulate</a>, <a href="../../../com/aspose/slides/IBehavior.html#setAdditive-int-">setAdditive</a>, <a href="../../../com/aspose/slides/IBehavior.html#setTiming-com.aspose.slides.ITiming-">setTiming</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="ScaleEffect--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>ScaleEffect</h4>
<pre>public&nbsp;ScaleEffect()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getZoomContent--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getZoomContent</h4>
<pre>public final&nbsp;byte&nbsp;getZoomContent()</pre>
<div class="block"><p>
 Determines whether a content should be zoomed.
 Read/write <a href="../../../com/aspose/slides/NullableBool.html" title="class in com.aspose.slides"><code>NullableBool</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IScaleEffect.html#getZoomContent--">getZoomContent</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IScaleEffect.html" title="interface in com.aspose.slides">IScaleEffect</a></code></dd>
</dl>
</li>
</ul>
<a name="setZoomContent-byte-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setZoomContent</h4>
<pre>public final&nbsp;void&nbsp;setZoomContent(byte&nbsp;value)</pre>
<div class="block"><p>
 Determines whether a content should be zoomed.
 Read/write <a href="../../../com/aspose/slides/NullableBool.html" title="class in com.aspose.slides"><code>NullableBool</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IScaleEffect.html#setZoomContent-byte-">setZoomContent</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IScaleEffect.html" title="interface in com.aspose.slides">IScaleEffect</a></code></dd>
</dl>
</li>
</ul>
<a name="getFrom--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFrom</h4>
<pre>public final&nbsp;java.awt.geom.Point2D.Float&nbsp;getFrom()</pre>
<div class="block"><p>
 Specifies an x/y co-ordinate to start the animation from (in percents).
 Read/write <code>Point2D.Float</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IScaleEffect.html#getFrom--">getFrom</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IScaleEffect.html" title="interface in com.aspose.slides">IScaleEffect</a></code></dd>
</dl>
</li>
</ul>
<a name="setFrom-java.awt.geom.Point2D.Float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFrom</h4>
<pre>public final&nbsp;void&nbsp;setFrom(java.awt.geom.Point2D.Float&nbsp;value)</pre>
<div class="block"><p>
 Specifies an x/y co-ordinate to start the animation from (in percents).
 Read/write <code>Point2D.Float</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IScaleEffect.html#setFrom-java.awt.geom.Point2D.Float-">setFrom</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IScaleEffect.html" title="interface in com.aspose.slides">IScaleEffect</a></code></dd>
</dl>
</li>
</ul>
<a name="getTo--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTo</h4>
<pre>public final&nbsp;java.awt.geom.Point2D.Float&nbsp;getTo()</pre>
<div class="block"><p>
 Specifies the target location for an animation scale effect (in percents).
 Read/write <code>Point2D.Float</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IScaleEffect.html#getTo--">getTo</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IScaleEffect.html" title="interface in com.aspose.slides">IScaleEffect</a></code></dd>
</dl>
</li>
</ul>
<a name="setTo-java.awt.geom.Point2D.Float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTo</h4>
<pre>public final&nbsp;void&nbsp;setTo(java.awt.geom.Point2D.Float&nbsp;value)</pre>
<div class="block"><p>
 Specifies the target location for an animation scale effect (in percents).
 Read/write <code>Point2D.Float</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IScaleEffect.html#setTo-java.awt.geom.Point2D.Float-">setTo</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IScaleEffect.html" title="interface in com.aspose.slides">IScaleEffect</a></code></dd>
</dl>
</li>
</ul>
<a name="getBy--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBy</h4>
<pre>public final&nbsp;java.awt.geom.Point2D.Float&nbsp;getBy()</pre>
<div class="block"><p>
 describes the relative offset value for the animation (in percents).
 Read/write <code>Point2D.Float</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IScaleEffect.html#getBy--">getBy</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IScaleEffect.html" title="interface in com.aspose.slides">IScaleEffect</a></code></dd>
</dl>
</li>
</ul>
<a name="setBy-java.awt.geom.Point2D.Float-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setBy</h4>
<pre>public final&nbsp;void&nbsp;setBy(java.awt.geom.Point2D.Float&nbsp;value)</pre>
<div class="block"><p>
 describes the relative offset value for the animation (in percents).
 Read/write <code>Point2D.Float</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IScaleEffect.html#setBy-java.awt.geom.Point2D.Float-">setBy</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IScaleEffect.html" title="interface in com.aspose.slides">IScaleEffect</a></code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/SaveOptionsFactory.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/SchemeColor.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/ScaleEffect.html" target="_top">Frames</a></li>
<li><a href="ScaleEffect.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><i>Copyright &copy; 2004-2025 Aspose Pty Ltd. All Rights Reserved.</i></small></p>
</body>
</html>
