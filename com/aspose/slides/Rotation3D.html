<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_421) on Fri Jul 11 05:32:44 CDT 2025 -->
<title>Rotation3D (Aspose.Slides for Java (25.7) API Documentation)</title>
<meta name="date" content="2025-07-11">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Rotation3D (Aspose.Slides for Java (25.7) API Documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/RippleTransition.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/RotationEffect.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/Rotation3D.html" target="_top">Frames</a></li>
<li><a href="Rotation3D.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.aspose.slides</div>
<h2 title="Class Rotation3D" class="title">Class Rotation3D</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.aspose.slides.Rotation3D</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../com/aspose/slides/IRotation3D.html" title="interface in com.aspose.slides">IRotation3D</a></dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">Rotation3D</span>
extends java.lang.Object
implements <a href="../../../com/aspose/slides/IRotation3D.html" title="interface in com.aspose.slides">IRotation3D</a></pre>
<div class="block"><p>
 Represents 3D rotation of a chart.
 </p></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Rotation3D.html#getDepthPercents--">getDepthPercents</a></span>()</code>
<div class="block">
 Returns or sets the depth of a 3D chart as a percentage of a chart width (between 20 and 2000 percent).</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Rotation3D.html#getHeightPercents--">getHeightPercents</a></span>()</code>
<div class="block">
 Specifies the height of a 3-D chart as a percentage of the chart width (between 5 and 500 percent).</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>com.aspose.slides.IDOMObject</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Rotation3D.html#getParent_Immediate--">getParent_Immediate</a></span>()</code>
<div class="block">
 Returns Parent_Immediate object.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>byte</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Rotation3D.html#getPerspective--">getPerspective</a></span>()</code>
<div class="block">
 Returns or sets the perspective value (field of view angle) for 3D charts (between 0 and 240).</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Rotation3D.html#getRightAngleAxes--">getRightAngleAxes</a></span>()</code>
<div class="block">
 Determines whether the chart axes are at right angles, rather than drawn in perspective.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>byte</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Rotation3D.html#getRotationX--">getRotationX</a></span>()</code>
<div class="block">
 Returns or sets the rotation degree around the X-axis, i.e.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Rotation3D.html#getRotationY--">getRotationY</a></span>()</code>
<div class="block">
 Returns or sets the rotation degree around the Y-axis, i.e.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Rotation3D.html#setDepthPercents-int-">setDepthPercents</a></span>(int&nbsp;value)</code>
<div class="block">
 Returns or sets the depth of a 3D chart as a percentage of a chart width (between 20 and 2000 percent).</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Rotation3D.html#setHeightPercents-int-">setHeightPercents</a></span>(int&nbsp;value)</code>
<div class="block">
 Specifies the height of a 3-D chart as a percentage of the chart width (between 5 and 500 percent).</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Rotation3D.html#setPerspective-byte-">setPerspective</a></span>(byte&nbsp;value)</code>
<div class="block">
 Returns or sets the perspective value (field of view angle) for 3D charts (between 0 and 240).</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Rotation3D.html#setRightAngleAxes-boolean-">setRightAngleAxes</a></span>(boolean&nbsp;value)</code>
<div class="block">
 Determines whether the chart axes are at right angles, rather than drawn in perspective.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Rotation3D.html#setRotationX-byte-">setRotationX</a></span>(byte&nbsp;value)</code>
<div class="block">
 Returns or sets the rotation degree around the X-axis, i.e.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Rotation3D.html#setRotationY-int-">setRotationY</a></span>(int&nbsp;value)</code>
<div class="block">
 Returns or sets the rotation degree around the Y-axis, i.e.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getRotationX--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRotationX</h4>
<pre>public final&nbsp;byte&nbsp;getRotationX()</pre>
<div class="block"><p>
 Returns or sets the rotation degree around the X-axis, i.e. in the Y direction for 3D charts (between -90 and 90 degrees).
 The property matches with the 21.2.2.157 rotX (X Rotation) item in ECMA-376 and with the "Y Rotation" option in PowerPoint 2007+.
 Read/write <code>byte</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IRotation3D.html#getRotationX--">getRotationX</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IRotation3D.html" title="interface in com.aspose.slides">IRotation3D</a></code></dd>
</dl>
</li>
</ul>
<a name="setRotationX-byte-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRotationX</h4>
<pre>public final&nbsp;void&nbsp;setRotationX(byte&nbsp;value)</pre>
<div class="block"><p>
 Returns or sets the rotation degree around the X-axis, i.e. in the Y direction for 3D charts (between -90 and 90 degrees).
 The property matches with the 21.2.2.157 rotX (X Rotation) item in ECMA-376 and with the "Y Rotation" option in PowerPoint 2007+.
 Read/write <code>byte</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IRotation3D.html#setRotationX-byte-">setRotationX</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IRotation3D.html" title="interface in com.aspose.slides">IRotation3D</a></code></dd>
</dl>
</li>
</ul>
<a name="getRotationY--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRotationY</h4>
<pre>public final&nbsp;int&nbsp;getRotationY()</pre>
<div class="block"><p>
 Returns or sets the rotation degree around the Y-axis, i.e. in the X direction for 3D charts (between 0 and 360 degrees).
 The property matches with the 21.2.2.158 rotY (Y Rotation) item in ECMA-376 and with the "X Rotation" option in PowerPoint 2007+.
 Read/write <code>int</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IRotation3D.html#getRotationY--">getRotationY</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IRotation3D.html" title="interface in com.aspose.slides">IRotation3D</a></code></dd>
</dl>
</li>
</ul>
<a name="setRotationY-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRotationY</h4>
<pre>public final&nbsp;void&nbsp;setRotationY(int&nbsp;value)</pre>
<div class="block"><p>
 Returns or sets the rotation degree around the Y-axis, i.e. in the X direction for 3D charts (between 0 and 360 degrees).
 The property matches with the 21.2.2.158 rotY (Y Rotation) item in ECMA-376 and with the "X Rotation" option in PowerPoint 2007+.
 Read/write <code>int</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IRotation3D.html#setRotationY-int-">setRotationY</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IRotation3D.html" title="interface in com.aspose.slides">IRotation3D</a></code></dd>
</dl>
</li>
</ul>
<a name="getPerspective--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPerspective</h4>
<pre>public final&nbsp;byte&nbsp;getPerspective()</pre>
<div class="block"><p>
 Returns or sets the perspective value (field of view angle) for 3D charts (between 0 and 240).
 Ignored if RightAngleAxes property value is true.
 Read/write <code>byte</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IRotation3D.html#getPerspective--">getPerspective</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IRotation3D.html" title="interface in com.aspose.slides">IRotation3D</a></code></dd>
</dl>
</li>
</ul>
<a name="setPerspective-byte-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPerspective</h4>
<pre>public final&nbsp;void&nbsp;setPerspective(byte&nbsp;value)</pre>
<div class="block"><p>
 Returns or sets the perspective value (field of view angle) for 3D charts (between 0 and 240).
 Ignored if RightAngleAxes property value is true.
 Read/write <code>byte</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IRotation3D.html#setPerspective-byte-">setPerspective</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IRotation3D.html" title="interface in com.aspose.slides">IRotation3D</a></code></dd>
</dl>
</li>
</ul>
<a name="getRightAngleAxes--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRightAngleAxes</h4>
<pre>public final&nbsp;boolean&nbsp;getRightAngleAxes()</pre>
<div class="block"><p>
 Determines whether the chart axes are at right angles, rather than drawn in perspective.
 In other words it determines whether the chart angles of axes are independent from chart 
 rotation or elevation.
 Read/write <code>boolean</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IRotation3D.html#getRightAngleAxes--">getRightAngleAxes</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IRotation3D.html" title="interface in com.aspose.slides">IRotation3D</a></code></dd>
</dl>
</li>
</ul>
<a name="setRightAngleAxes-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRightAngleAxes</h4>
<pre>public final&nbsp;void&nbsp;setRightAngleAxes(boolean&nbsp;value)</pre>
<div class="block"><p>
 Determines whether the chart axes are at right angles, rather than drawn in perspective.
 In other words it determines whether the chart angles of axes are independent from chart 
 rotation or elevation.
 Read/write <code>boolean</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IRotation3D.html#setRightAngleAxes-boolean-">setRightAngleAxes</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IRotation3D.html" title="interface in com.aspose.slides">IRotation3D</a></code></dd>
</dl>
</li>
</ul>
<a name="getDepthPercents--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDepthPercents</h4>
<pre>public final&nbsp;int&nbsp;getDepthPercents()</pre>
<div class="block"><p>
 Returns or sets the depth of a 3D chart as a percentage of a chart width (between 20 and 2000 percent).
 Read/write <code>int</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IRotation3D.html#getDepthPercents--">getDepthPercents</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IRotation3D.html" title="interface in com.aspose.slides">IRotation3D</a></code></dd>
</dl>
</li>
</ul>
<a name="setDepthPercents-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDepthPercents</h4>
<pre>public final&nbsp;void&nbsp;setDepthPercents(int&nbsp;value)</pre>
<div class="block"><p>
 Returns or sets the depth of a 3D chart as a percentage of a chart width (between 20 and 2000 percent).
 Read/write <code>int</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IRotation3D.html#setDepthPercents-int-">setDepthPercents</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IRotation3D.html" title="interface in com.aspose.slides">IRotation3D</a></code></dd>
</dl>
</li>
</ul>
<a name="getHeightPercents--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHeightPercents</h4>
<pre>public final&nbsp;int&nbsp;getHeightPercents()</pre>
<div class="block"><p>
 Specifies the height of a 3-D chart as a percentage of the chart width (between 5 and 500 percent).
 Read/write <code>int</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IRotation3D.html#getHeightPercents--">getHeightPercents</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IRotation3D.html" title="interface in com.aspose.slides">IRotation3D</a></code></dd>
</dl>
</li>
</ul>
<a name="setHeightPercents-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setHeightPercents</h4>
<pre>public final&nbsp;void&nbsp;setHeightPercents(int&nbsp;value)</pre>
<div class="block"><p>
 Specifies the height of a 3-D chart as a percentage of the chart width (between 5 and 500 percent).
 Read/write <code>int</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IRotation3D.html#setHeightPercents-int-">setHeightPercents</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IRotation3D.html" title="interface in com.aspose.slides">IRotation3D</a></code></dd>
</dl>
</li>
</ul>
<a name="getParent_Immediate--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getParent_Immediate</h4>
<pre>public final&nbsp;com.aspose.slides.IDOMObject&nbsp;getParent_Immediate()</pre>
<div class="block"><p>
 Returns Parent_Immediate object.
 Read-only <code>IDOMObject</code>.
 </p></div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/RippleTransition.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/RotationEffect.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/Rotation3D.html" target="_top">Frames</a></li>
<li><a href="Rotation3D.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><i>Copyright &copy; 2004-2025 Aspose Pty Ltd. All Rights Reserved.</i></small></p>
</body>
</html>
