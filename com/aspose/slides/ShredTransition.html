<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_421) on Fri Jul 11 05:32:44 CDT 2025 -->
<title>ShredTransition (Aspose.Slides for Java (25.7) API Documentation)</title>
<meta name="date" content="2025-07-11">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ShredTransition (Aspose.Slides for Java (25.7) API Documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/ShapeUtil.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/SideDirectionTransition.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/ShredTransition.html" target="_top">Frames</a></li>
<li><a href="ShredTransition.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.aspose.slides</div>
<h2 title="Class ShredTransition" class="title">Class ShredTransition</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../com/aspose/slides/TransitionValueBase.html" title="class in com.aspose.slides">com.aspose.slides.TransitionValueBase</a></li>
<li>
<ul class="inheritance">
<li>com.aspose.slides.ShredTransition</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../com/aspose/slides/IShredTransition.html" title="interface in com.aspose.slides">IShredTransition</a>, <a href="../../../com/aspose/slides/ITransitionValueBase.html" title="interface in com.aspose.slides">ITransitionValueBase</a></dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">ShredTransition</span>
extends <a href="../../../com/aspose/slides/TransitionValueBase.html" title="class in com.aspose.slides">TransitionValueBase</a>
implements <a href="../../../com/aspose/slides/IShredTransition.html" title="interface in com.aspose.slides">IShredTransition</a></pre>
<div class="block"><p>
 Shred slide transition effect.
 </p></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShredTransition.html#getDirection--">getDirection</a></span>()</code>
<div class="block">
 Direction of transition.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShredTransition.html#getPattern--">getPattern</a></span>()</code>
<div class="block">
 Specifies the shape of the visuals used during the transition.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShredTransition.html#setDirection-int-">setDirection</a></span>(int&nbsp;value)</code>
<div class="block">
 Direction of transition.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ShredTransition.html#setPattern-int-">setPattern</a></span>(int&nbsp;value)</code>
<div class="block">
 Specifies the shape of the visuals used during the transition.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.aspose.slides.TransitionValueBase">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;com.aspose.slides.<a href="../../../com/aspose/slides/TransitionValueBase.html" title="class in com.aspose.slides">TransitionValueBase</a></h3>
<code><a href="../../../com/aspose/slides/TransitionValueBase.html#equals-java.lang.Object-">equals</a>, <a href="../../../com/aspose/slides/TransitionValueBase.html#hashCode--">hashCode</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>getClass, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getDirection--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDirection</h4>
<pre>public final&nbsp;int&nbsp;getDirection()</pre>
<div class="block"><p>
 Direction of transition.
 Read/write <a href="../../../com/aspose/slides/TransitionInOutDirectionType.html" title="class in com.aspose.slides"><code>TransitionInOutDirectionType</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShredTransition.html#getDirection--">getDirection</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShredTransition.html" title="interface in com.aspose.slides">IShredTransition</a></code></dd>
</dl>
</li>
</ul>
<a name="setDirection-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDirection</h4>
<pre>public final&nbsp;void&nbsp;setDirection(int&nbsp;value)</pre>
<div class="block"><p>
 Direction of transition.
 Read/write <a href="../../../com/aspose/slides/TransitionInOutDirectionType.html" title="class in com.aspose.slides"><code>TransitionInOutDirectionType</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShredTransition.html#setDirection-int-">setDirection</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShredTransition.html" title="interface in com.aspose.slides">IShredTransition</a></code></dd>
</dl>
</li>
</ul>
<a name="getPattern--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPattern</h4>
<pre>public final&nbsp;int&nbsp;getPattern()</pre>
<div class="block"><p>
 Specifies the shape of the visuals used during the transition.
 Read/write <a href="../../../com/aspose/slides/TransitionShredPattern.html" title="class in com.aspose.slides"><code>TransitionShredPattern</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShredTransition.html#getPattern--">getPattern</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShredTransition.html" title="interface in com.aspose.slides">IShredTransition</a></code></dd>
</dl>
</li>
</ul>
<a name="setPattern-int-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setPattern</h4>
<pre>public final&nbsp;void&nbsp;setPattern(int&nbsp;value)</pre>
<div class="block"><p>
 Specifies the shape of the visuals used during the transition.
 Read/write <a href="../../../com/aspose/slides/TransitionShredPattern.html" title="class in com.aspose.slides"><code>TransitionShredPattern</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IShredTransition.html#setPattern-int-">setPattern</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IShredTransition.html" title="interface in com.aspose.slides">IShredTransition</a></code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/ShapeUtil.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/SideDirectionTransition.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/ShredTransition.html" target="_top">Frames</a></li>
<li><a href="ShredTransition.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><i>Copyright &copy; 2004-2025 Aspose Pty Ltd. All Rights Reserved.</i></small></p>
</body>
</html>
