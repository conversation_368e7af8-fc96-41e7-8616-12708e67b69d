<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_421) on Fri Jul 11 05:32:44 CDT 2025 -->
<title>ProtectionManager (Aspose.Slides for Java (25.7) API Documentation)</title>
<meta name="date" content="2025-07-11">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ProtectionManager (Aspose.Slides for Java (25.7) API Documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/PropertyValueType.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/PVIObject.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/ProtectionManager.html" target="_top">Frames</a></li>
<li><a href="ProtectionManager.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.aspose.slides</div>
<h2 title="Class ProtectionManager" class="title">Class ProtectionManager</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.aspose.slides.ProtectionManager</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../com/aspose/slides/IProtectionManager.html" title="interface in com.aspose.slides">IProtectionManager</a></dd>
</dl>
<hr>
<br>
<pre>public final class <span class="typeNameLabel">ProtectionManager</span>
extends java.lang.Object
implements <a href="../../../com/aspose/slides/IProtectionManager.html" title="interface in com.aspose.slides">IProtectionManager</a></pre>
<div class="block"><p>
 Presentation password protection management.
 </p></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ProtectionManager.html#checkWriteProtection-java.lang.String-">checkWriteProtection</a></span>(java.lang.String&nbsp;password)</code>
<div class="block">
 Determines whether a presentation is a password protected to modify.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ProtectionManager.html#encrypt-java.lang.String-">encrypt</a></span>(java.lang.String&nbsp;encryptionPassword)</code>
<div class="block">
 Encrypts Presentation with specified password.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ProtectionManager.html#getEncryptDocumentProperties--">getEncryptDocumentProperties</a></span>()</code>
<div class="block">
 This property makes sense, if presentation is password protected.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ProtectionManager.html#getEncryptionPassword--">getEncryptionPassword</a></span>()</code>
<div class="block">
 Gets the password which is used for presentation encryption.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ProtectionManager.html#getReadOnlyRecommended--">getReadOnlyRecommended</a></span>()</code>
<div class="block">
 Gets or sets read-only recommendation.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ProtectionManager.html#isEncrypted--">isEncrypted</a></span>()</code>
<div class="block">
 Gets a value indicating whether this instance is encrypted.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ProtectionManager.html#isOnlyDocumentPropertiesLoaded--">isOnlyDocumentPropertiesLoaded</a></span>()</code>
<div class="block">
 This property makes sense, if presentation file is password protected and document 
 properties of this file are public.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ProtectionManager.html#isWriteProtected--">isWriteProtected</a></span>()</code>
<div class="block">
 Gets a value indicating whether this presentation is write protected.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ProtectionManager.html#removeEncryption--">removeEncryption</a></span>()</code>
<div class="block">
 Removes the encryption.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ProtectionManager.html#removeWriteProtection--">removeWriteProtection</a></span>()</code>
<div class="block">
 Removes write protection for this presentation.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ProtectionManager.html#setEncryptDocumentProperties-boolean-">setEncryptDocumentProperties</a></span>(boolean&nbsp;value)</code>
<div class="block">
 This property makes sense, if presentation is password protected.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ProtectionManager.html#setReadOnlyRecommended-boolean-">setReadOnlyRecommended</a></span>(boolean&nbsp;value)</code>
<div class="block">
 Gets or sets read-only recommendation.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/ProtectionManager.html#setWriteProtection-java.lang.String-">setWriteProtection</a></span>(java.lang.String&nbsp;password)</code>
<div class="block">
 Set write protection for this presentation with specified password.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getEncryptDocumentProperties--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEncryptDocumentProperties</h4>
<pre>public final&nbsp;boolean&nbsp;getEncryptDocumentProperties()</pre>
<div class="block"><p>
 This property makes sense, if presentation is password protected.
 If true then document properties is encrypted in presentation file.
 If false then document properties is public while presentation is encrypted.
 Read/write <code>boolean</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IProtectionManager.html#getEncryptDocumentProperties--">getEncryptDocumentProperties</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IProtectionManager.html" title="interface in com.aspose.slides">IProtectionManager</a></code></dd>
</dl>
</li>
</ul>
<a name="setEncryptDocumentProperties-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEncryptDocumentProperties</h4>
<pre>public final&nbsp;void&nbsp;setEncryptDocumentProperties(boolean&nbsp;value)</pre>
<div class="block"><p>
 This property makes sense, if presentation is password protected.
 If true then document properties is encrypted in presentation file.
 If false then document properties is public while presentation is encrypted.
 Read/write <code>boolean</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IProtectionManager.html#setEncryptDocumentProperties-boolean-">setEncryptDocumentProperties</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IProtectionManager.html" title="interface in com.aspose.slides">IProtectionManager</a></code></dd>
</dl>
</li>
</ul>
<a name="isEncrypted--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isEncrypted</h4>
<pre>public final&nbsp;boolean&nbsp;isEncrypted()</pre>
<div class="block"><p>
 Gets a value indicating whether this instance is encrypted.
 Read-only <code>boolean</code>.
 </p>Value: 
 true if presentation was loaded from encrypted file or <a href="../../../com/aspose/slides/ProtectionManager.html#encrypt-java.lang.String-"><code>encrypt(String)</code></a>
 method was called ; otherwise, false.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IProtectionManager.html#isEncrypted--">isEncrypted</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IProtectionManager.html" title="interface in com.aspose.slides">IProtectionManager</a></code></dd>
</dl>
</li>
</ul>
<a name="isOnlyDocumentPropertiesLoaded--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isOnlyDocumentPropertiesLoaded</h4>
<pre>public final&nbsp;boolean&nbsp;isOnlyDocumentPropertiesLoaded()</pre>
<div class="block"><p>
 This property makes sense, if presentation file is password protected and document 
 properties of this file are public.
 Value of true means that only document properties are loaded from an encrypted 
 presentation file without use of password.
 Value of false means that entire encrypted presentation is loaded with use of right 
 password, not only document properties are loaded.
 If presentation isn't encrypted then property value is always false.
 If document properties of an encrypted file aren't public then property value is always false.
 If Presentation.EncryptDocumentProperties is true than IsOnlyDocumentPropertiesLoaded 
 property value is always false.
 Read-only <code>boolean</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IProtectionManager.html#isOnlyDocumentPropertiesLoaded--">isOnlyDocumentPropertiesLoaded</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IProtectionManager.html" title="interface in com.aspose.slides">IProtectionManager</a></code></dd>
</dl>
</li>
</ul>
<a name="isWriteProtected--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isWriteProtected</h4>
<pre>public final&nbsp;boolean&nbsp;isWriteProtected()</pre>
<div class="block"><p>
 Gets a value indicating whether this presentation is write protected.
 Read-only <code>boolean</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IProtectionManager.html#isWriteProtected--">isWriteProtected</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IProtectionManager.html" title="interface in com.aspose.slides">IProtectionManager</a></code></dd>
</dl>
</li>
</ul>
<a name="encrypt-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>encrypt</h4>
<pre>public final&nbsp;void&nbsp;encrypt(java.lang.String&nbsp;encryptionPassword)</pre>
<div class="block"><p>
 Encrypts Presentation with specified password.
 </p><p><hr><blockquote><pre>
 The following sample code shows you how to encrypt a PowerPoint Presentation.
 <pre>
 Presentation pres = new Presentation("pres.pptx");
 try {
     pres.getProtectionManager().encrypt("123123");
     pres.save("encrypted-pres.pptx", SaveFormat.Pptx);
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IProtectionManager.html#encrypt-java.lang.String-">encrypt</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IProtectionManager.html" title="interface in com.aspose.slides">IProtectionManager</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>encryptionPassword</code> - The password.</dd>
</dl>
</li>
</ul>
<a name="removeEncryption--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>removeEncryption</h4>
<pre>public final&nbsp;void&nbsp;removeEncryption()</pre>
<div class="block"><p>
 Removes the encryption.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IProtectionManager.html#removeEncryption--">removeEncryption</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IProtectionManager.html" title="interface in com.aspose.slides">IProtectionManager</a></code></dd>
</dl>
</li>
</ul>
<a name="setWriteProtection-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setWriteProtection</h4>
<pre>public final&nbsp;void&nbsp;setWriteProtection(java.lang.String&nbsp;password)</pre>
<div class="block"><p>
 Set write protection for this presentation with specified password.
 </p><p><hr><blockquote><pre>
 The following sample code shows you how to set a write protection to a presentation.
 <pre>
 Presentation pres = new Presentation("pres.pptx");
 try {
     pres.getProtectionManager().setWriteProtection("123123");
     pres.save("write-protected-pres.pptx", SaveFormat.Pptx);
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IProtectionManager.html#setWriteProtection-java.lang.String-">setWriteProtection</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IProtectionManager.html" title="interface in com.aspose.slides">IProtectionManager</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>password</code> - The password.</dd>
</dl>
</li>
</ul>
<a name="removeWriteProtection--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>removeWriteProtection</h4>
<pre>public final&nbsp;void&nbsp;removeWriteProtection()</pre>
<div class="block"><p>
 Removes write protection for this presentation.
 </p><p><hr><blockquote><pre>
 This sample code shows you how to remove the write protection from a PowerPoint Presentation.
 <pre>
 Presentation pres = new Presentation("pres.pptx");
 try {
     pres.getProtectionManager().removeWriteProtection();
     pres.save("write-protection-removed.pptx", SaveFormat.Pptx);
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IProtectionManager.html#removeWriteProtection--">removeWriteProtection</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IProtectionManager.html" title="interface in com.aspose.slides">IProtectionManager</a></code></dd>
</dl>
</li>
</ul>
<a name="checkWriteProtection-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>checkWriteProtection</h4>
<pre>public final&nbsp;boolean&nbsp;checkWriteProtection(java.lang.String&nbsp;password)</pre>
<div class="block"><p>
 Determines whether a presentation is a password protected to modify.
 </p><p><hr><blockquote><pre>
 <pre>
 Presentation presentation = new Presentation(presentationFilePath);
 try {
     boolean isWriteProtected = presentation.getProtectionManager().checkWriteProtection("my_password");
 } finally {
     if (presentation != null) presentation.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IProtectionManager.html#checkWriteProtection-java.lang.String-">checkWriteProtection</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IProtectionManager.html" title="interface in com.aspose.slides">IProtectionManager</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>password</code> - The password for checking.
 <p><hr>
 1. You should check the (<a href="../../../com/aspose/slides/ProtectionManager.html#isWriteProtected--"><code>isWriteProtected()</code></a>) property before calling this method.
 2. When the password is null or empty, this method returns false.
 </hr></p></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>True if the password is valid; otherwise, false.</dd>
</dl>
</li>
</ul>
<a name="getEncryptionPassword--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEncryptionPassword</h4>
<pre>public final&nbsp;java.lang.String&nbsp;getEncryptionPassword()</pre>
<div class="block"><p>
 Gets the password which is used for presentation encryption.
 Read-only <code>String</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IProtectionManager.html#getEncryptionPassword--">getEncryptionPassword</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IProtectionManager.html" title="interface in com.aspose.slides">IProtectionManager</a></code></dd>
</dl>
</li>
</ul>
<a name="getReadOnlyRecommended--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getReadOnlyRecommended</h4>
<pre>public final&nbsp;boolean&nbsp;getReadOnlyRecommended()</pre>
<div class="block"><p>
 Gets or sets read-only recommendation.
 Read/write <code>boolean</code>.
 </p><p><hr><blockquote><pre>
 <pre>
 Presentation pres = new Presentation();
 try {
     pres.getProtectionManager().setReadOnlyRecommended(true);
     pres.save("ReadOnlyPresentation.pptx", SaveFormat.Pptx);
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IProtectionManager.html#getReadOnlyRecommended--">getReadOnlyRecommended</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IProtectionManager.html" title="interface in com.aspose.slides">IProtectionManager</a></code></dd>
</dl>
</li>
</ul>
<a name="setReadOnlyRecommended-boolean-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setReadOnlyRecommended</h4>
<pre>public final&nbsp;void&nbsp;setReadOnlyRecommended(boolean&nbsp;value)</pre>
<div class="block"><p>
 Gets or sets read-only recommendation.
 Read/write <code>boolean</code>.
 </p><p><hr><blockquote><pre>
 <pre>
 Presentation pres = new Presentation();
 try {
     pres.getProtectionManager().setReadOnlyRecommended(true);
     pres.save("ReadOnlyPresentation.pptx", SaveFormat.Pptx);
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IProtectionManager.html#setReadOnlyRecommended-boolean-">setReadOnlyRecommended</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IProtectionManager.html" title="interface in com.aspose.slides">IProtectionManager</a></code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/PropertyValueType.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/PVIObject.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/ProtectionManager.html" target="_top">Frames</a></li>
<li><a href="ProtectionManager.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><i>Copyright &copy; 2004-2025 Aspose Pty Ltd. All Rights Reserved.</i></small></p>
</body>
</html>
