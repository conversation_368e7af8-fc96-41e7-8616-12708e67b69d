<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_421) on Fri Jul 11 05:32:44 CDT 2025 -->
<title>Reflection (Aspose.Slides for Java (25.7) API Documentation)</title>
<meta name="date" content="2025-07-11">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Reflection (Aspose.Slides for Java (25.7) API Documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10,"i26":10,"i27":10,"i28":10,"i29":10,"i30":10,"i31":10,"i32":10,"i33":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/RectangleAlignment.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/RenderingOptions.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/Reflection.html" target="_top">Frames</a></li>
<li><a href="Reflection.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.aspose.slides</div>
<h2 title="Class Reflection" class="title">Class Reflection</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.aspose.slides.Reflection</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../com/aspose/slides/IAccessiblePVIObject.html" title="interface in com.aspose.slides">IAccessiblePVIObject</a>&lt;<a href="../../../com/aspose/slides/IReflectionEffectiveData.html" title="interface in com.aspose.slides">IReflectionEffectiveData</a>&gt;, <a href="../../../com/aspose/slides/IImageTransformOperation.html" title="interface in com.aspose.slides">IImageTransformOperation</a>, <a href="../../../com/aspose/slides/IReflection.html" title="interface in com.aspose.slides">IReflection</a>, java.lang.Cloneable</dd>
</dl>
<hr>
<br>
<pre>public final class <span class="typeNameLabel">Reflection</span>
extends java.lang.Object
implements <a href="../../../com/aspose/slides/IReflection.html" title="interface in com.aspose.slides">IReflection</a>, java.lang.Cloneable</pre>
<div class="block"><p>
 Represents a Reflection effect.
 </p></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Reflection.html#equals-java.lang.Object-">equals</a></span>(java.lang.Object&nbsp;obj)</code>
<div class="block">
 Determines whether the specified <a href="../../../com/aspose/slides/Reflection.html" title="class in com.aspose.slides"><code>Reflection</code></a> is equal to the current <a href="../../../com/aspose/slides/Reflection.html" title="class in com.aspose.slides"><code>Reflection</code></a>.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Reflection.html#getBlurRadius--">getBlurRadius</a></span>()</code>
<div class="block">
 Blur radius.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Reflection.html#getDirection--">getDirection</a></span>()</code>
<div class="block">
 Direction of reflection.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Reflection.html#getDistance--">getDistance</a></span>()</code>
<div class="block">
 Distance of reflection.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IReflectionEffectiveData.html" title="interface in com.aspose.slides">IReflectionEffectiveData</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Reflection.html#getEffective--">getEffective</a></span>()</code>
<div class="block">
 Gets effective Reflection effect data with the inheritance applied.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Reflection.html#getEndPosAlpha--">getEndPosAlpha</a></span>()</code>
<div class="block">
 Specifies the end position (along the alpha gradient ramp) of the end alpha value (percents).</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Reflection.html#getEndReflectionOpacity--">getEndReflectionOpacity</a></span>()</code>
<div class="block">
 End reflection opacity.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Reflection.html#getFadeDirection--">getFadeDirection</a></span>()</code>
<div class="block">
 Specifies the direction to offset the reflection.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>com.aspose.slides.IDOMObject</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Reflection.html#getParent_Immediate--">getParent_Immediate</a></span>()</code>
<div class="block">
 Returns Parent_Immediate object.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IPresentationComponent.html" title="interface in com.aspose.slides">IPresentationComponent</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Reflection.html#getParent_IPresentationComponent--">getParent_IPresentationComponent</a></span>()</code>
<div class="block">
 Returns parent IPresentationComponent.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>byte</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Reflection.html#getRectangleAlign--">getRectangleAlign</a></span>()</code>
<div class="block">
 Rectangle alignment.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Reflection.html#getRotateShadowWithShape--">getRotateShadowWithShape</a></span>()</code>
<div class="block">
 Specifies whether the reflection should rotate with the shape if the shape is rotated.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Reflection.html#getScaleHorizontal--">getScaleHorizontal</a></span>()</code>
<div class="block">
 Specifies the horizontal scaling factor, negative scaling causes a flip.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Reflection.html#getScaleVertical--">getScaleVertical</a></span>()</code>
<div class="block">
 Specifies the vertical scaling factor, negative scaling causes a flip.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Reflection.html#getSkewHorizontal--">getSkewHorizontal</a></span>()</code>
<div class="block">
 Specifies the horizontal skew angle.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Reflection.html#getSkewVertical--">getSkewVertical</a></span>()</code>
<div class="block">
 Specifies the vertical skew angle.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Reflection.html#getStartPosAlpha--">getStartPosAlpha</a></span>()</code>
<div class="block">
 Specifies the start position (along the alpha gradient ramp) of the start alpha value (percents).</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Reflection.html#getStartReflectionOpacity--">getStartReflectionOpacity</a></span>()</code>
<div class="block">
 Starting reflection opacity.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Reflection.html#getVersion--">getVersion</a></span>()</code>
<div class="block">
 Version.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Reflection.html#hashCode--">hashCode</a></span>()</code>
<div class="block">
 Serves as a hash function for a particular type.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Reflection.html#setBlurRadius-double-">setBlurRadius</a></span>(double&nbsp;value)</code>
<div class="block">
 Blur radius.</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Reflection.html#setDirection-float-">setDirection</a></span>(float&nbsp;value)</code>
<div class="block">
 Direction of reflection.</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Reflection.html#setDistance-double-">setDistance</a></span>(double&nbsp;value)</code>
<div class="block">
 Distance of reflection.</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Reflection.html#setEndPosAlpha-float-">setEndPosAlpha</a></span>(float&nbsp;value)</code>
<div class="block">
 Specifies the end position (along the alpha gradient ramp) of the end alpha value (percents).</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Reflection.html#setEndReflectionOpacity-float-">setEndReflectionOpacity</a></span>(float&nbsp;value)</code>
<div class="block">
 End reflection opacity.</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Reflection.html#setFadeDirection-float-">setFadeDirection</a></span>(float&nbsp;value)</code>
<div class="block">
 Specifies the direction to offset the reflection.</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Reflection.html#setRectangleAlign-byte-">setRectangleAlign</a></span>(byte&nbsp;value)</code>
<div class="block">
 Rectangle alignment.</div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Reflection.html#setRotateShadowWithShape-boolean-">setRotateShadowWithShape</a></span>(boolean&nbsp;value)</code>
<div class="block">
 Specifies whether the reflection should rotate with the shape if the shape is rotated.</div>
</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Reflection.html#setScaleHorizontal-double-">setScaleHorizontal</a></span>(double&nbsp;value)</code>
<div class="block">
 Specifies the horizontal scaling factor, negative scaling causes a flip.</div>
</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Reflection.html#setScaleVertical-double-">setScaleVertical</a></span>(double&nbsp;value)</code>
<div class="block">
 Specifies the vertical scaling factor, negative scaling causes a flip.</div>
</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Reflection.html#setSkewHorizontal-double-">setSkewHorizontal</a></span>(double&nbsp;value)</code>
<div class="block">
 Specifies the horizontal skew angle.</div>
</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Reflection.html#setSkewVertical-double-">setSkewVertical</a></span>(double&nbsp;value)</code>
<div class="block">
 Specifies the vertical skew angle.</div>
</td>
</tr>
<tr id="i32" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Reflection.html#setStartPosAlpha-float-">setStartPosAlpha</a></span>(float&nbsp;value)</code>
<div class="block">
 Specifies the start position (along the alpha gradient ramp) of the start alpha value (percents).</div>
</td>
</tr>
<tr id="i33" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Reflection.html#setStartReflectionOpacity-float-">setStartReflectionOpacity</a></span>(float&nbsp;value)</code>
<div class="block">
 Starting reflection opacity.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>getClass, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getStartPosAlpha--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStartPosAlpha</h4>
<pre>public final&nbsp;float&nbsp;getStartPosAlpha()</pre>
<div class="block"><p>
 Specifies the start position (along the alpha gradient ramp) of the start alpha value (percents).
 Read/write <code>float</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IReflection.html#getStartPosAlpha--">getStartPosAlpha</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IReflection.html" title="interface in com.aspose.slides">IReflection</a></code></dd>
</dl>
</li>
</ul>
<a name="setStartPosAlpha-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setStartPosAlpha</h4>
<pre>public final&nbsp;void&nbsp;setStartPosAlpha(float&nbsp;value)</pre>
<div class="block"><p>
 Specifies the start position (along the alpha gradient ramp) of the start alpha value (percents).
 Read/write <code>float</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IReflection.html#setStartPosAlpha-float-">setStartPosAlpha</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IReflection.html" title="interface in com.aspose.slides">IReflection</a></code></dd>
</dl>
</li>
</ul>
<a name="getEndPosAlpha--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEndPosAlpha</h4>
<pre>public final&nbsp;float&nbsp;getEndPosAlpha()</pre>
<div class="block"><p>
 Specifies the end position (along the alpha gradient ramp) of the end alpha value (percents).
 Read/write <code>float</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IReflection.html#getEndPosAlpha--">getEndPosAlpha</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IReflection.html" title="interface in com.aspose.slides">IReflection</a></code></dd>
</dl>
</li>
</ul>
<a name="setEndPosAlpha-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEndPosAlpha</h4>
<pre>public final&nbsp;void&nbsp;setEndPosAlpha(float&nbsp;value)</pre>
<div class="block"><p>
 Specifies the end position (along the alpha gradient ramp) of the end alpha value (percents).
 Read/write <code>float</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IReflection.html#setEndPosAlpha-float-">setEndPosAlpha</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IReflection.html" title="interface in com.aspose.slides">IReflection</a></code></dd>
</dl>
</li>
</ul>
<a name="getFadeDirection--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFadeDirection</h4>
<pre>public final&nbsp;float&nbsp;getFadeDirection()</pre>
<div class="block"><p>
 Specifies the direction to offset the reflection. (angle).
 Read/write <code>float</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IReflection.html#getFadeDirection--">getFadeDirection</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IReflection.html" title="interface in com.aspose.slides">IReflection</a></code></dd>
</dl>
</li>
</ul>
<a name="setFadeDirection-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFadeDirection</h4>
<pre>public final&nbsp;void&nbsp;setFadeDirection(float&nbsp;value)</pre>
<div class="block"><p>
 Specifies the direction to offset the reflection. (angle).
 Read/write <code>float</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IReflection.html#setFadeDirection-float-">setFadeDirection</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IReflection.html" title="interface in com.aspose.slides">IReflection</a></code></dd>
</dl>
</li>
</ul>
<a name="getStartReflectionOpacity--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStartReflectionOpacity</h4>
<pre>public final&nbsp;float&nbsp;getStartReflectionOpacity()</pre>
<div class="block"><p>
 Starting reflection opacity. (percents).
 Read/write <code>float</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IReflection.html#getStartReflectionOpacity--">getStartReflectionOpacity</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IReflection.html" title="interface in com.aspose.slides">IReflection</a></code></dd>
</dl>
</li>
</ul>
<a name="setStartReflectionOpacity-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setStartReflectionOpacity</h4>
<pre>public final&nbsp;void&nbsp;setStartReflectionOpacity(float&nbsp;value)</pre>
<div class="block"><p>
 Starting reflection opacity. (percents).
 Read/write <code>float</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IReflection.html#setStartReflectionOpacity-float-">setStartReflectionOpacity</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IReflection.html" title="interface in com.aspose.slides">IReflection</a></code></dd>
</dl>
</li>
</ul>
<a name="getEndReflectionOpacity--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEndReflectionOpacity</h4>
<pre>public final&nbsp;float&nbsp;getEndReflectionOpacity()</pre>
<div class="block"><p>
 End reflection opacity. (percents).
 Read/write <code>float</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IReflection.html#getEndReflectionOpacity--">getEndReflectionOpacity</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IReflection.html" title="interface in com.aspose.slides">IReflection</a></code></dd>
</dl>
</li>
</ul>
<a name="setEndReflectionOpacity-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEndReflectionOpacity</h4>
<pre>public final&nbsp;void&nbsp;setEndReflectionOpacity(float&nbsp;value)</pre>
<div class="block"><p>
 End reflection opacity. (percents).
 Read/write <code>float</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IReflection.html#setEndReflectionOpacity-float-">setEndReflectionOpacity</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IReflection.html" title="interface in com.aspose.slides">IReflection</a></code></dd>
</dl>
</li>
</ul>
<a name="getBlurRadius--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBlurRadius</h4>
<pre>public final&nbsp;double&nbsp;getBlurRadius()</pre>
<div class="block"><p>
 Blur radius.
 Read/write <code>double</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IReflection.html#getBlurRadius--">getBlurRadius</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IReflection.html" title="interface in com.aspose.slides">IReflection</a></code></dd>
</dl>
</li>
</ul>
<a name="setBlurRadius-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBlurRadius</h4>
<pre>public final&nbsp;void&nbsp;setBlurRadius(double&nbsp;value)</pre>
<div class="block"><p>
 Blur radius.
 Read/write <code>double</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IReflection.html#setBlurRadius-double-">setBlurRadius</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IReflection.html" title="interface in com.aspose.slides">IReflection</a></code></dd>
</dl>
</li>
</ul>
<a name="getDirection--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDirection</h4>
<pre>public final&nbsp;float&nbsp;getDirection()</pre>
<div class="block"><p>
 Direction of reflection.
 Read/write <code>float</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IReflection.html#getDirection--">getDirection</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IReflection.html" title="interface in com.aspose.slides">IReflection</a></code></dd>
</dl>
</li>
</ul>
<a name="setDirection-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDirection</h4>
<pre>public final&nbsp;void&nbsp;setDirection(float&nbsp;value)</pre>
<div class="block"><p>
 Direction of reflection.
 Read/write <code>float</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IReflection.html#setDirection-float-">setDirection</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IReflection.html" title="interface in com.aspose.slides">IReflection</a></code></dd>
</dl>
</li>
</ul>
<a name="getDistance--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDistance</h4>
<pre>public final&nbsp;double&nbsp;getDistance()</pre>
<div class="block"><p>
 Distance of reflection.
 Read/write <code>double</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IReflection.html#getDistance--">getDistance</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IReflection.html" title="interface in com.aspose.slides">IReflection</a></code></dd>
</dl>
</li>
</ul>
<a name="setDistance-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDistance</h4>
<pre>public final&nbsp;void&nbsp;setDistance(double&nbsp;value)</pre>
<div class="block"><p>
 Distance of reflection.
 Read/write <code>double</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IReflection.html#setDistance-double-">setDistance</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IReflection.html" title="interface in com.aspose.slides">IReflection</a></code></dd>
</dl>
</li>
</ul>
<a name="getRectangleAlign--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRectangleAlign</h4>
<pre>public final&nbsp;byte&nbsp;getRectangleAlign()</pre>
<div class="block"><p>
 Rectangle alignment.
 Read/write <a href="../../../com/aspose/slides/RectangleAlignment.html" title="class in com.aspose.slides"><code>RectangleAlignment</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IReflection.html#getRectangleAlign--">getRectangleAlign</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IReflection.html" title="interface in com.aspose.slides">IReflection</a></code></dd>
</dl>
</li>
</ul>
<a name="setRectangleAlign-byte-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRectangleAlign</h4>
<pre>public final&nbsp;void&nbsp;setRectangleAlign(byte&nbsp;value)</pre>
<div class="block"><p>
 Rectangle alignment.
 Read/write <a href="../../../com/aspose/slides/RectangleAlignment.html" title="class in com.aspose.slides"><code>RectangleAlignment</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IReflection.html#setRectangleAlign-byte-">setRectangleAlign</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IReflection.html" title="interface in com.aspose.slides">IReflection</a></code></dd>
</dl>
</li>
</ul>
<a name="getSkewHorizontal--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSkewHorizontal</h4>
<pre>public final&nbsp;double&nbsp;getSkewHorizontal()</pre>
<div class="block"><p>
 Specifies the horizontal skew angle.
 Read/write <code>double</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IReflection.html#getSkewHorizontal--">getSkewHorizontal</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IReflection.html" title="interface in com.aspose.slides">IReflection</a></code></dd>
</dl>
</li>
</ul>
<a name="setSkewHorizontal-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSkewHorizontal</h4>
<pre>public final&nbsp;void&nbsp;setSkewHorizontal(double&nbsp;value)</pre>
<div class="block"><p>
 Specifies the horizontal skew angle.
 Read/write <code>double</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IReflection.html#setSkewHorizontal-double-">setSkewHorizontal</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IReflection.html" title="interface in com.aspose.slides">IReflection</a></code></dd>
</dl>
</li>
</ul>
<a name="getSkewVertical--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSkewVertical</h4>
<pre>public final&nbsp;double&nbsp;getSkewVertical()</pre>
<div class="block"><p>
 Specifies the vertical skew angle.
 Read/write <code>double</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IReflection.html#getSkewVertical--">getSkewVertical</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IReflection.html" title="interface in com.aspose.slides">IReflection</a></code></dd>
</dl>
</li>
</ul>
<a name="setSkewVertical-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSkewVertical</h4>
<pre>public final&nbsp;void&nbsp;setSkewVertical(double&nbsp;value)</pre>
<div class="block"><p>
 Specifies the vertical skew angle.
 Read/write <code>double</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IReflection.html#setSkewVertical-double-">setSkewVertical</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IReflection.html" title="interface in com.aspose.slides">IReflection</a></code></dd>
</dl>
</li>
</ul>
<a name="getRotateShadowWithShape--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRotateShadowWithShape</h4>
<pre>public final&nbsp;boolean&nbsp;getRotateShadowWithShape()</pre>
<div class="block"><p>
 Specifies whether the reflection should rotate with the shape if the shape is rotated.
 Read/write <code>boolean</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IReflection.html#getRotateShadowWithShape--">getRotateShadowWithShape</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IReflection.html" title="interface in com.aspose.slides">IReflection</a></code></dd>
</dl>
</li>
</ul>
<a name="setRotateShadowWithShape-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRotateShadowWithShape</h4>
<pre>public final&nbsp;void&nbsp;setRotateShadowWithShape(boolean&nbsp;value)</pre>
<div class="block"><p>
 Specifies whether the reflection should rotate with the shape if the shape is rotated.
 Read/write <code>boolean</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IReflection.html#setRotateShadowWithShape-boolean-">setRotateShadowWithShape</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IReflection.html" title="interface in com.aspose.slides">IReflection</a></code></dd>
</dl>
</li>
</ul>
<a name="getScaleHorizontal--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getScaleHorizontal</h4>
<pre>public final&nbsp;double&nbsp;getScaleHorizontal()</pre>
<div class="block"><p>
 Specifies the horizontal scaling factor, negative scaling causes a flip. (percents)
 Read/write <code>double</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IReflection.html#getScaleHorizontal--">getScaleHorizontal</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IReflection.html" title="interface in com.aspose.slides">IReflection</a></code></dd>
</dl>
</li>
</ul>
<a name="setScaleHorizontal-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setScaleHorizontal</h4>
<pre>public final&nbsp;void&nbsp;setScaleHorizontal(double&nbsp;value)</pre>
<div class="block"><p>
 Specifies the horizontal scaling factor, negative scaling causes a flip. (percents)
 Read/write <code>double</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IReflection.html#setScaleHorizontal-double-">setScaleHorizontal</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IReflection.html" title="interface in com.aspose.slides">IReflection</a></code></dd>
</dl>
</li>
</ul>
<a name="getScaleVertical--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getScaleVertical</h4>
<pre>public final&nbsp;double&nbsp;getScaleVertical()</pre>
<div class="block"><p>
 Specifies the vertical scaling factor, negative scaling causes a flip. (percents)
 Read/write <code>double</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IReflection.html#getScaleVertical--">getScaleVertical</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IReflection.html" title="interface in com.aspose.slides">IReflection</a></code></dd>
</dl>
</li>
</ul>
<a name="setScaleVertical-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setScaleVertical</h4>
<pre>public final&nbsp;void&nbsp;setScaleVertical(double&nbsp;value)</pre>
<div class="block"><p>
 Specifies the vertical scaling factor, negative scaling causes a flip. (percents)
 Read/write <code>double</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IReflection.html#setScaleVertical-double-">setScaleVertical</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IReflection.html" title="interface in com.aspose.slides">IReflection</a></code></dd>
</dl>
</li>
</ul>
<a name="getEffective--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEffective</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IReflectionEffectiveData.html" title="interface in com.aspose.slides">IReflectionEffectiveData</a>&nbsp;getEffective()</pre>
<div class="block"><p>
 Gets effective Reflection effect data with the inheritance applied.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IAccessiblePVIObject.html#getEffective--">getEffective</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IAccessiblePVIObject.html" title="interface in com.aspose.slides">IAccessiblePVIObject</a>&lt;<a href="../../../com/aspose/slides/IReflectionEffectiveData.html" title="interface in com.aspose.slides">IReflectionEffectiveData</a>&gt;</code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>A <a href="../../../com/aspose/slides/IReflectionEffectiveData.html" title="interface in com.aspose.slides"><code>IReflectionEffectiveData</code></a>.</dd>
</dl>
</li>
</ul>
<a name="getParent_Immediate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getParent_Immediate</h4>
<pre>public final&nbsp;com.aspose.slides.IDOMObject&nbsp;getParent_Immediate()</pre>
<div class="block"><p>
 Returns Parent_Immediate object.
 Read-only <code>IDOMObject</code>.
 </p></div>
</li>
</ul>
<a name="getVersion--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVersion</h4>
<pre>public final&nbsp;long&nbsp;getVersion()</pre>
<div class="block"><p>
 Version.
 Read-only <code>long</code>.
 </p></div>
</li>
</ul>
<a name="getParent_IPresentationComponent--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getParent_IPresentationComponent</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IPresentationComponent.html" title="interface in com.aspose.slides">IPresentationComponent</a>&nbsp;getParent_IPresentationComponent()</pre>
<div class="block"><p>
 Returns parent IPresentationComponent.
 Read-only <a href="../../../com/aspose/slides/IPresentationComponent.html" title="interface in com.aspose.slides"><code>IPresentationComponent</code></a>.
 </p></div>
</li>
</ul>
<a name="equals-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>equals</h4>
<pre>public&nbsp;boolean&nbsp;equals(java.lang.Object&nbsp;obj)</pre>
<div class="block"><p>
 Determines whether the specified <a href="../../../com/aspose/slides/Reflection.html" title="class in com.aspose.slides"><code>Reflection</code></a> is equal to the current <a href="../../../com/aspose/slides/Reflection.html" title="class in com.aspose.slides"><code>Reflection</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code>equals</code>&nbsp;in class&nbsp;<code>java.lang.Object</code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>obj</code> - The <a href="../../../com/aspose/slides/Reflection.html" title="class in com.aspose.slides"><code>Reflection</code></a> to compare.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true if objects are equal; otherwise, false.</dd>
</dl>
</li>
</ul>
<a name="hashCode--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>hashCode</h4>
<pre>public&nbsp;int&nbsp;hashCode()</pre>
<div class="block"><p>
 Serves as a hash function for a particular type.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code>hashCode</code>&nbsp;in class&nbsp;<code>java.lang.Object</code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>A hash code for the current object.</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/RectangleAlignment.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/RenderingOptions.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/Reflection.html" target="_top">Frames</a></li>
<li><a href="Reflection.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><i>Copyright &copy; 2004-2025 Aspose Pty Ltd. All Rights Reserved.</i></small></p>
</body>
</html>
