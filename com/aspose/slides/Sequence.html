<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_421) on Fri Jul 11 05:32:44 CDT 2025 -->
<title>Sequence (Aspose.Slides for Java (25.7) API Documentation)</title>
<meta name="date" content="2025-07-11">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Sequence (Aspose.Slides for Java (25.7) API Documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/SectionZoomFrame.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/SequenceCollection.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/Sequence.html" target="_top">Frames</a></li>
<li><a href="Sequence.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.aspose.slides</div>
<h2 title="Class Sequence" class="title">Class Sequence</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.aspose.slides.Sequence</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd>com.aspose.ms.System.Collections.Generic.IGenericEnumerable&lt;<a href="../../../com/aspose/slides/IEffect.html" title="interface in com.aspose.slides">IEffect</a>&gt;, com.aspose.ms.System.Collections.IEnumerable&lt;<a href="../../../com/aspose/slides/IEffect.html" title="interface in com.aspose.slides">IEffect</a>&gt;, <a href="../../../com/aspose/slides/ISequence.html" title="interface in com.aspose.slides">ISequence</a>, java.lang.Iterable&lt;<a href="../../../com/aspose/slides/IEffect.html" title="interface in com.aspose.slides">IEffect</a>&gt;</dd>
</dl>
<hr>
<br>
<pre>public final class <span class="typeNameLabel">Sequence</span>
extends java.lang.Object
implements <a href="../../../com/aspose/slides/ISequence.html" title="interface in com.aspose.slides">ISequence</a></pre>
<div class="block"><p>
 Represents sequence (collection of effects).
 </p></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IEffect.html" title="interface in com.aspose.slides">IEffect</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Sequence.html#addEffect-com.aspose.slides.IChart-int-int-int-int-int-">addEffect</a></span>(<a href="../../../com/aspose/slides/IChart.html" title="interface in com.aspose.slides">IChart</a>&nbsp;chart,
         int&nbsp;type,
         int&nbsp;index,
         int&nbsp;effectType,
         int&nbsp;subtype,
         int&nbsp;triggerType)</code>
<div class="block">
 Adds the new chart animation effect for category or series to the end of sequence.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IEffect.html" title="interface in com.aspose.slides">IEffect</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Sequence.html#addEffect-com.aspose.slides.IChart-int-int-int-int-int-int-">addEffect</a></span>(<a href="../../../com/aspose/slides/IChart.html" title="interface in com.aspose.slides">IChart</a>&nbsp;chart,
         int&nbsp;type,
         int&nbsp;seriesIndex,
         int&nbsp;categoriesIndex,
         int&nbsp;effectType,
         int&nbsp;subtype,
         int&nbsp;triggerType)</code>
<div class="block">
 Adds the new chart animation effect for elements in category or series to the end of sequence.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IEffect.html" title="interface in com.aspose.slides">IEffect</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Sequence.html#addEffect-com.aspose.slides.IParagraph-int-int-int-">addEffect</a></span>(<a href="../../../com/aspose/slides/IParagraph.html" title="interface in com.aspose.slides">IParagraph</a>&nbsp;paragraph,
         int&nbsp;effectType,
         int&nbsp;subtype,
         int&nbsp;triggerType)</code>
<div class="block">
  Add new animation effect for paragraph to the end of sequence.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IEffect.html" title="interface in com.aspose.slides">IEffect</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Sequence.html#addEffect-com.aspose.slides.IShape-int-int-int-">addEffect</a></span>(<a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a>&nbsp;shape,
         int&nbsp;effectType,
         int&nbsp;subtype,
         int&nbsp;triggerType)</code>
<div class="block">
 Add new effect to the end of sequence.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Sequence.html#clear--">clear</a></span>()</code>
<div class="block">
 Removes all effects from a collection.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IEffect.html" title="interface in com.aspose.slides">IEffect</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Sequence.html#get_Item-int-">get_Item</a></span>(int&nbsp;index)</code>
<div class="block">
 Returns an effect at the specified index.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Sequence.html#getCount--">getCount</a></span>()</code>
<div class="block">
 Returns the number of effects in a sequense.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Sequence.html#getCount-com.aspose.slides.IShape-">getCount</a></span>(<a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a>&nbsp;shape)</code>
<div class="block">
 Returns count of effects for the specified shape.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IEffect.html" title="interface in com.aspose.slides">IEffect</a>[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Sequence.html#getEffectsByParagraph-com.aspose.slides.IParagraph-">getEffectsByParagraph</a></span>(<a href="../../../com/aspose/slides/IParagraph.html" title="interface in com.aspose.slides">IParagraph</a>&nbsp;paragraph)</code>
<div class="block">
 Returns array of effects for the specified paragraph.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IEffect.html" title="interface in com.aspose.slides">IEffect</a>[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Sequence.html#getEffectsByShape-com.aspose.slides.IShape-">getEffectsByShape</a></span>(<a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a>&nbsp;shape)</code>
<div class="block">
 Returns array of effects for the specified shape.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Sequence.html#getTriggerShape--">getTriggerShape</a></span>()</code>
<div class="block">
 Returns or sets shape target for INTERACTIVE sequence.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>com.aspose.ms.System.Collections.Generic.IGenericEnumerator&lt;<a href="../../../com/aspose/slides/IEffect.html" title="interface in com.aspose.slides">IEffect</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Sequence.html#iterator--">iterator</a></span>()</code>
<div class="block">
 Returns an enumerator that iterates through the collection.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>com.aspose.ms.System.Collections.Generic.IGenericEnumerator&lt;<a href="../../../com/aspose/slides/IEffect.html" title="interface in com.aspose.slides">IEffect</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Sequence.html#iteratorJava--">iteratorJava</a></span>()</code>
<div class="block">
 Returns a java iterator for the entire collection.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Sequence.html#remove-com.aspose.slides.IEffect-">remove</a></span>(<a href="../../../com/aspose/slides/IEffect.html" title="interface in com.aspose.slides">IEffect</a>&nbsp;item)</code>
<div class="block">
 Removes specified effect from a collection.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Sequence.html#removeAt-int-">removeAt</a></span>(int&nbsp;index)</code>
<div class="block">
 Removes an effect from a collection.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Sequence.html#removeByShape-com.aspose.slides.IShape-">removeByShape</a></span>(<a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a>&nbsp;shape)</code>
<div class="block">
 Remove effect for the specified shape.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/Sequence.html#setTriggerShape-com.aspose.slides.IShape-">setTriggerShape</a></span>(<a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a>&nbsp;value)</code>
<div class="block">
 Returns or sets shape target for INTERACTIVE sequence.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Iterable">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;java.lang.Iterable</h3>
<code>forEach, spliterator</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getCount--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCount</h4>
<pre>public final&nbsp;int&nbsp;getCount()</pre>
<div class="block"><p>
 Returns the number of effects in a sequense.
 Read-only <code>int</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISequence.html#getCount--">getCount</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISequence.html" title="interface in com.aspose.slides">ISequence</a></code></dd>
</dl>
</li>
</ul>
<a name="remove-com.aspose.slides.IEffect-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>remove</h4>
<pre>public final&nbsp;void&nbsp;remove(<a href="../../../com/aspose/slides/IEffect.html" title="interface in com.aspose.slides">IEffect</a>&nbsp;item)</pre>
<div class="block"><p>
 Removes specified effect from a collection.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISequence.html#remove-com.aspose.slides.IEffect-">remove</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISequence.html" title="interface in com.aspose.slides">ISequence</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>item</code> - Effect to remove.</dd>
</dl>
</li>
</ul>
<a name="removeAt-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>removeAt</h4>
<pre>public final&nbsp;void&nbsp;removeAt(int&nbsp;index)</pre>
<div class="block"><p>
 Removes an effect from a collection.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISequence.html#removeAt-int-">removeAt</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISequence.html" title="interface in com.aspose.slides">ISequence</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - Index of a effect that should be deleted.</dd>
</dl>
</li>
</ul>
<a name="clear--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>clear</h4>
<pre>public final&nbsp;void&nbsp;clear()</pre>
<div class="block"><p>
 Removes all effects from a collection.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISequence.html#clear--">clear</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISequence.html" title="interface in com.aspose.slides">ISequence</a></code></dd>
</dl>
</li>
</ul>
<a name="get_Item-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_Item</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IEffect.html" title="interface in com.aspose.slides">IEffect</a>&nbsp;get_Item(int&nbsp;index)</pre>
<div class="block"><p>
 Returns an effect at the specified index.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISequence.html#get_Item-int-">get_Item</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISequence.html" title="interface in com.aspose.slides">ISequence</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - Index of element.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The <a href="../../../com/aspose/slides/IEffect.html" title="interface in com.aspose.slides"><code>IEffect</code></a> object.</dd>
</dl>
</li>
</ul>
<a name="iterator--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>iterator</h4>
<pre>public final&nbsp;com.aspose.ms.System.Collections.Generic.IGenericEnumerator&lt;<a href="../../../com/aspose/slides/IEffect.html" title="interface in com.aspose.slides">IEffect</a>&gt;&nbsp;iterator()</pre>
<div class="block"><p>
 Returns an enumerator that iterates through the collection.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>iterator</code>&nbsp;in interface&nbsp;<code>com.aspose.ms.System.Collections.Generic.IGenericEnumerable&lt;<a href="../../../com/aspose/slides/IEffect.html" title="interface in com.aspose.slides">IEffect</a>&gt;</code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>iterator</code>&nbsp;in interface&nbsp;<code>com.aspose.ms.System.Collections.IEnumerable&lt;<a href="../../../com/aspose/slides/IEffect.html" title="interface in com.aspose.slides">IEffect</a>&gt;</code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>iterator</code>&nbsp;in interface&nbsp;<code>java.lang.Iterable&lt;<a href="../../../com/aspose/slides/IEffect.html" title="interface in com.aspose.slides">IEffect</a>&gt;</code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>A <code>IGenericEnumerator</code> that can be used to iterate through the collection.</dd>
</dl>
</li>
</ul>
<a name="iteratorJava--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>iteratorJava</h4>
<pre>public final&nbsp;com.aspose.ms.System.Collections.Generic.IGenericEnumerator&lt;<a href="../../../com/aspose/slides/IEffect.html" title="interface in com.aspose.slides">IEffect</a>&gt;&nbsp;iteratorJava()</pre>
<div class="block"><p>
 Returns a java iterator for the entire collection.
 </p></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>An <code>Iterator</code> for the entire collection.</dd>
</dl>
</li>
</ul>
<a name="getTriggerShape--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTriggerShape</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a>&nbsp;getTriggerShape()</pre>
<div class="block"><p>
 Returns or sets shape target for INTERACTIVE sequence.
 If sequence is not interactive then returns null.
 Read/write <a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides"><code>IShape</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISequence.html#getTriggerShape--">getTriggerShape</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISequence.html" title="interface in com.aspose.slides">ISequence</a></code></dd>
</dl>
</li>
</ul>
<a name="setTriggerShape-com.aspose.slides.IShape-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTriggerShape</h4>
<pre>public final&nbsp;void&nbsp;setTriggerShape(<a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a>&nbsp;value)</pre>
<div class="block"><p>
 Returns or sets shape target for INTERACTIVE sequence.
 If sequence is not interactive then returns null.
 Read/write <a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides"><code>IShape</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISequence.html#setTriggerShape-com.aspose.slides.IShape-">setTriggerShape</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISequence.html" title="interface in com.aspose.slides">ISequence</a></code></dd>
</dl>
</li>
</ul>
<a name="removeByShape-com.aspose.slides.IShape-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>removeByShape</h4>
<pre>public final&nbsp;void&nbsp;removeByShape(<a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a>&nbsp;shape)</pre>
<div class="block"><p>
 Remove effect for the specified shape.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISequence.html#removeByShape-com.aspose.slides.IShape-">removeByShape</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISequence.html" title="interface in com.aspose.slides">ISequence</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>shape</code> - Shape object <a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides"><code>IShape</code></a></dd>
</dl>
</li>
</ul>
<a name="getEffectsByShape-com.aspose.slides.IShape-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEffectsByShape</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IEffect.html" title="interface in com.aspose.slides">IEffect</a>[]&nbsp;getEffectsByShape(<a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a>&nbsp;shape)</pre>
<div class="block"><p>
 Returns array of effects for the specified shape.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISequence.html#getEffectsByShape-com.aspose.slides.IShape-">getEffectsByShape</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISequence.html" title="interface in com.aspose.slides">ISequence</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>shape</code> - Shape object <a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides"><code>IShape</code></a></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Array of effects <a href="../../../com/aspose/slides/IEffect.html" title="interface in com.aspose.slides"><code>IEffect</code></a></dd>
</dl>
</li>
</ul>
<a name="getEffectsByParagraph-com.aspose.slides.IParagraph-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEffectsByParagraph</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IEffect.html" title="interface in com.aspose.slides">IEffect</a>[]&nbsp;getEffectsByParagraph(<a href="../../../com/aspose/slides/IParagraph.html" title="interface in com.aspose.slides">IParagraph</a>&nbsp;paragraph)</pre>
<div class="block"><p>
 Returns array of effects for the specified paragraph.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISequence.html#getEffectsByParagraph-com.aspose.slides.IParagraph-">getEffectsByParagraph</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISequence.html" title="interface in com.aspose.slides">ISequence</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>paragraph</code> - Paragraph object <a href="../../../com/aspose/slides/IParagraph.html" title="interface in com.aspose.slides"><code>IParagraph</code></a></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Array of effects <a href="../../../com/aspose/slides/IEffect.html" title="interface in com.aspose.slides"><code>IEffect</code></a></dd>
</dl>
</li>
</ul>
<a name="getCount-com.aspose.slides.IShape-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCount</h4>
<pre>public final&nbsp;int&nbsp;getCount(<a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a>&nbsp;shape)</pre>
<div class="block"><p>
 Returns count of effects for the specified shape.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISequence.html#getCount-com.aspose.slides.IShape-">getCount</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISequence.html" title="interface in com.aspose.slides">ISequence</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>shape</code> - Shape object <a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides"><code>IShape</code></a></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Count of effects <code>int</code></dd>
</dl>
</li>
</ul>
<a name="addEffect-com.aspose.slides.IShape-int-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addEffect</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IEffect.html" title="interface in com.aspose.slides">IEffect</a>&nbsp;addEffect(<a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides">IShape</a>&nbsp;shape,
                               int&nbsp;effectType,
                               int&nbsp;subtype,
                               int&nbsp;triggerType)</pre>
<div class="block"><p>
 Add new effect to the end of sequence.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISequence.html#addEffect-com.aspose.slides.IShape-int-int-int-">addEffect</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISequence.html" title="interface in com.aspose.slides">ISequence</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>shape</code> - Shape object <a href="../../../com/aspose/slides/IShape.html" title="interface in com.aspose.slides"><code>IShape</code></a> for adding an effect</dd>
<dd><code>effectType</code> - Type of an animation effect <a href="../../../com/aspose/slides/EffectType.html" title="class in com.aspose.slides"><code>EffectType</code></a></dd>
<dd><code>subtype</code> - Subtypes of animation effect <a href="../../../com/aspose/slides/EffectSubtype.html" title="class in com.aspose.slides"><code>EffectSubtype</code></a></dd>
<dd><code>triggerType</code> - Trigger type of effect <a href="../../../com/aspose/slides/EffectTriggerType.html" title="class in com.aspose.slides"><code>EffectTriggerType</code></a></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>New effect object <a href="../../../com/aspose/slides/IEffect.html" title="interface in com.aspose.slides"><code>IEffect</code></a></dd>
</dl>
</li>
</ul>
<a name="addEffect-com.aspose.slides.IParagraph-int-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addEffect</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IEffect.html" title="interface in com.aspose.slides">IEffect</a>&nbsp;addEffect(<a href="../../../com/aspose/slides/IParagraph.html" title="interface in com.aspose.slides">IParagraph</a>&nbsp;paragraph,
                               int&nbsp;effectType,
                               int&nbsp;subtype,
                               int&nbsp;triggerType)</pre>
<div class="block"><p>
  Add new animation effect for paragraph to the end of sequence.
  </p><p><hr><blockquote><pre>
  <pre>
  Presentation presentation = new Presentation(path + "input.pptx");
  try
  {        
     // select paragraph to add effect
     IAutoShape autoShape = (IAutoShape)presentation.getSlides().get_Item(0).getShapes().get_Item(0);
     IParagraph paragraph = autoShape.getTextFrame().getParagraphs().get_Item(0);
     // add Fly animation effect to selected paragraph
     IEffect effect = presentation.getSlides().get_Item(0).getTimeline().getMainSequence().addEffect(
     paragraph, EffectType.Fly, EffectSubtype.Left, EffectTriggerType.OnClick);
  }  finally {
     if (presentation != null) presentation.dispose();
  }
  </pre>
  </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISequence.html#addEffect-com.aspose.slides.IParagraph-int-int-int-">addEffect</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISequence.html" title="interface in com.aspose.slides">ISequence</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>paragraph</code> - Paragraph object <a href="../../../com/aspose/slides/IParagraph.html" title="interface in com.aspose.slides"><code>IParagraph</code></a></dd>
<dd><code>effectType</code> - Type of an animation effect <a href="../../../com/aspose/slides/EffectType.html" title="class in com.aspose.slides"><code>EffectType</code></a></dd>
<dd><code>subtype</code> - Subtypes of animation effect <a href="../../../com/aspose/slides/EffectSubtype.html" title="class in com.aspose.slides"><code>EffectSubtype</code></a></dd>
<dd><code>triggerType</code> - Trigger type of effect <a href="../../../com/aspose/slides/EffectTriggerType.html" title="class in com.aspose.slides"><code>EffectTriggerType</code></a></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>New effect object <a href="../../../com/aspose/slides/IEffect.html" title="interface in com.aspose.slides"><code>IEffect</code></a></dd>
</dl>
</li>
</ul>
<a name="addEffect-com.aspose.slides.IChart-int-int-int-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addEffect</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IEffect.html" title="interface in com.aspose.slides">IEffect</a>&nbsp;addEffect(<a href="../../../com/aspose/slides/IChart.html" title="interface in com.aspose.slides">IChart</a>&nbsp;chart,
                               int&nbsp;type,
                               int&nbsp;index,
                               int&nbsp;effectType,
                               int&nbsp;subtype,
                               int&nbsp;triggerType)</pre>
<div class="block"><p>
 Adds the new chart animation effect for category or series to the end of sequence.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISequence.html#addEffect-com.aspose.slides.IChart-int-int-int-int-int-">addEffect</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISequence.html" title="interface in com.aspose.slides">ISequence</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>chart</code> - Chart object <a href="../../../com/aspose/slides/IChart.html" title="interface in com.aspose.slides"><code>IChart</code></a></dd>
<dd><code>type</code> - Type of an animation effect <a href="../../../com/aspose/slides/EffectChartMinorGroupingType.html" title="class in com.aspose.slides"><code>EffectChartMinorGroupingType</code></a></dd>
<dd><code>index</code> - Index <code>int</code></dd>
<dd><code>effectType</code> - Type of an animation effect <a href="../../../com/aspose/slides/EffectType.html" title="class in com.aspose.slides"><code>EffectType</code></a></dd>
<dd><code>subtype</code> - Subtypes of animation effect <a href="../../../com/aspose/slides/EffectSubtype.html" title="class in com.aspose.slides"><code>EffectSubtype</code></a></dd>
<dd><code>triggerType</code> - Trigger type of effect <a href="../../../com/aspose/slides/EffectTriggerType.html" title="class in com.aspose.slides"><code>EffectTriggerType</code></a></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>New effect object <a href="../../../com/aspose/slides/IEffect.html" title="interface in com.aspose.slides"><code>IEffect</code></a></dd>
</dl>
</li>
</ul>
<a name="addEffect-com.aspose.slides.IChart-int-int-int-int-int-int-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>addEffect</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IEffect.html" title="interface in com.aspose.slides">IEffect</a>&nbsp;addEffect(<a href="../../../com/aspose/slides/IChart.html" title="interface in com.aspose.slides">IChart</a>&nbsp;chart,
                               int&nbsp;type,
                               int&nbsp;seriesIndex,
                               int&nbsp;categoriesIndex,
                               int&nbsp;effectType,
                               int&nbsp;subtype,
                               int&nbsp;triggerType)</pre>
<div class="block"><p>
 Adds the new chart animation effect for elements in category or series to the end of sequence.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISequence.html#addEffect-com.aspose.slides.IChart-int-int-int-int-int-int-">addEffect</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISequence.html" title="interface in com.aspose.slides">ISequence</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>chart</code> - Chart object <a href="../../../com/aspose/slides/IChart.html" title="interface in com.aspose.slides"><code>IChart</code></a></dd>
<dd><code>type</code> - Type of an animation effect <a href="../../../com/aspose/slides/EffectChartMinorGroupingType.html" title="class in com.aspose.slides"><code>EffectChartMinorGroupingType</code></a></dd>
<dd><code>seriesIndex</code> - Index of chart series <code>int</code></dd>
<dd><code>categoriesIndex</code> - Index of category <code>int</code></dd>
<dd><code>effectType</code> - Type of an animation effect <a href="../../../com/aspose/slides/EffectType.html" title="class in com.aspose.slides"><code>EffectType</code></a></dd>
<dd><code>subtype</code> - Subtypes of animation effect <a href="../../../com/aspose/slides/EffectSubtype.html" title="class in com.aspose.slides"><code>EffectSubtype</code></a></dd>
<dd><code>triggerType</code> - Trigger type of effect <a href="../../../com/aspose/slides/EffectTriggerType.html" title="class in com.aspose.slides"><code>EffectTriggerType</code></a></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>New effect object <a href="../../../com/aspose/slides/IEffect.html" title="interface in com.aspose.slides"><code>IEffect</code></a></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/SectionZoomFrame.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/SequenceCollection.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/Sequence.html" target="_top">Frames</a></li>
<li><a href="Sequence.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><i>Copyright &copy; 2004-2025 Aspose Pty Ltd. All Rights Reserved.</i></small></p>
</body>
</html>
