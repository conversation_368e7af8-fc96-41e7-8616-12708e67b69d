<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_421) on Fri Jul 11 05:32:43 CDT 2025 -->
<title>PresetColor (Aspose.Slides for Java (25.7) API Documentation)</title>
<meta name="date" content="2025-07-11">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="PresetColor (Aspose.Slides for Java (25.7) API Documentation)";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/PresentedBySpeaker.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/PresetShadow.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/PresetColor.html" target="_top">Frames</a></li>
<li><a href="PresetColor.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.classes.inherited.from.class.com.aspose.ms.System.Enum">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#methods.inherited.from.class.com.aspose.ms.System.Enum">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li>Method</li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.aspose.slides</div>
<h2 title="Class PresetColor" class="title">Class PresetColor</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.aspose.ms.System.ValueType&lt;com.aspose.ms.System.Enum&gt;</li>
<li>
<ul class="inheritance">
<li>com.aspose.ms.System.Enum</li>
<li>
<ul class="inheritance">
<li>com.aspose.slides.PresetColor</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public final class <span class="typeNameLabel">PresetColor</span>
extends com.aspose.ms.System.Enum</pre>
<div class="block"><p>
 Represents predefined color presets.
 </p></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.com.aspose.ms.System.Enum">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from class&nbsp;com.aspose.ms.System.Enum</h3>
<code>com.aspose.ms.System.Enum.AbstractEnum, com.aspose.ms.System.Enum.FlaggedEnum, com.aspose.ms.System.Enum.ObjectEnum, com.aspose.ms.System.Enum.SimpleEnum</code></li>
</ul>
</li>
</ul>
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#AliceBlue">AliceBlue</a></span></code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#AntiqueWhite">AntiqueWhite</a></span></code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#Aqua">Aqua</a></span></code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#Aquamarine">Aquamarine</a></span></code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#Azure">Azure</a></span></code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#Beige">Beige</a></span></code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#Bisque">Bisque</a></span></code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#Black">Black</a></span></code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#BlanchedAlmond">BlanchedAlmond</a></span></code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#Blue">Blue</a></span></code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#BlueViolet">BlueViolet</a></span></code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#Brown">Brown</a></span></code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#BurlyWood">BurlyWood</a></span></code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#CadetBlue">CadetBlue</a></span></code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#Chartreuse">Chartreuse</a></span></code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#Chocolate">Chocolate</a></span></code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#Coral">Coral</a></span></code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#CornflowerBlue">CornflowerBlue</a></span></code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#Cornsilk">Cornsilk</a></span></code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#Crimson">Crimson</a></span></code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#Cyan">Cyan</a></span></code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#DarkBlue">DarkBlue</a></span></code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#DarkCyan">DarkCyan</a></span></code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#DarkGoldenrod">DarkGoldenrod</a></span></code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#DarkGray">DarkGray</a></span></code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#DarkGreen">DarkGreen</a></span></code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#DarkKhaki">DarkKhaki</a></span></code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#DarkMagenta">DarkMagenta</a></span></code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#DarkOliveGreen">DarkOliveGreen</a></span></code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#DarkOrange">DarkOrange</a></span></code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#DarkOrchid">DarkOrchid</a></span></code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#DarkRed">DarkRed</a></span></code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#DarkSalmon">DarkSalmon</a></span></code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#DarkSeaGreen">DarkSeaGreen</a></span></code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#DarkSlateBlue">DarkSlateBlue</a></span></code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#DarkSlateGray">DarkSlateGray</a></span></code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#DarkTurquoise">DarkTurquoise</a></span></code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#DarkViolet">DarkViolet</a></span></code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#DeepPink">DeepPink</a></span></code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#DeepSkyBlue">DeepSkyBlue</a></span></code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#DimGray">DimGray</a></span></code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#DodgerBlue">DodgerBlue</a></span></code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#Firebrick">Firebrick</a></span></code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#FloralWhite">FloralWhite</a></span></code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#ForestGreen">ForestGreen</a></span></code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#Fuchsia">Fuchsia</a></span></code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#Gainsboro">Gainsboro</a></span></code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#GhostWhite">GhostWhite</a></span></code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#Gold">Gold</a></span></code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#Goldenrod">Goldenrod</a></span></code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#Gray">Gray</a></span></code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#Green">Green</a></span></code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#GreenYellow">GreenYellow</a></span></code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#Honeydew">Honeydew</a></span></code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#HotPink">HotPink</a></span></code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#IndianRed">IndianRed</a></span></code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#Indigo">Indigo</a></span></code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#Ivory">Ivory</a></span></code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#Khaki">Khaki</a></span></code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#Lavender">Lavender</a></span></code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#LavenderBlush">LavenderBlush</a></span></code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#LawnGreen">LawnGreen</a></span></code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#LemonChiffon">LemonChiffon</a></span></code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#LightBlue">LightBlue</a></span></code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#LightCoral">LightCoral</a></span></code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#LightCyan">LightCyan</a></span></code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#LightGoldenrodYellow">LightGoldenrodYellow</a></span></code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#LightGray">LightGray</a></span></code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#LightGreen">LightGreen</a></span></code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#LightPink">LightPink</a></span></code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#LightSalmon">LightSalmon</a></span></code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#LightSeaGreen">LightSeaGreen</a></span></code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#LightSkyBlue">LightSkyBlue</a></span></code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#LightSlateGray">LightSlateGray</a></span></code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#LightSteelBlue">LightSteelBlue</a></span></code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#LightYellow">LightYellow</a></span></code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#Lime">Lime</a></span></code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#LimeGreen">LimeGreen</a></span></code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#Linen">Linen</a></span></code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#Magenta">Magenta</a></span></code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#Maroon">Maroon</a></span></code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#MediumAquamarine">MediumAquamarine</a></span></code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#MediumBlue">MediumBlue</a></span></code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#MediumOrchid">MediumOrchid</a></span></code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#MediumPurple">MediumPurple</a></span></code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#MediumSeaGreen">MediumSeaGreen</a></span></code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#MediumSlateBlue">MediumSlateBlue</a></span></code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#MediumSpringGreen">MediumSpringGreen</a></span></code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#MediumTurquoise">MediumTurquoise</a></span></code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#MediumVioletRed">MediumVioletRed</a></span></code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#MidnightBlue">MidnightBlue</a></span></code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#MintCream">MintCream</a></span></code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#MistyRose">MistyRose</a></span></code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#Moccasin">Moccasin</a></span></code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#NavajoWhite">NavajoWhite</a></span></code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#Navy">Navy</a></span></code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#NotDefined">NotDefined</a></span></code>
<div class="block">
 Color preset is not defined.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#OldLace">OldLace</a></span></code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#Olive">Olive</a></span></code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#OliveDrab">OliveDrab</a></span></code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#Orange">Orange</a></span></code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#OrangeRed">OrangeRed</a></span></code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#Orchid">Orchid</a></span></code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#PaleGoldenrod">PaleGoldenrod</a></span></code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#PaleGreen">PaleGreen</a></span></code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#PaleTurquoise">PaleTurquoise</a></span></code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#PaleVioletRed">PaleVioletRed</a></span></code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#PapayaWhip">PapayaWhip</a></span></code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#PeachPuff">PeachPuff</a></span></code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#Peru">Peru</a></span></code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#Pink">Pink</a></span></code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#Plum">Plum</a></span></code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#PowderBlue">PowderBlue</a></span></code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#Purple">Purple</a></span></code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#Red">Red</a></span></code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#RosyBrown">RosyBrown</a></span></code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#RoyalBlue">RoyalBlue</a></span></code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#SaddleBrown">SaddleBrown</a></span></code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#Salmon">Salmon</a></span></code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#SandyBrown">SandyBrown</a></span></code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#SeaGreen">SeaGreen</a></span></code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#SeaShell">SeaShell</a></span></code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#Sienna">Sienna</a></span></code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#Silver">Silver</a></span></code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#SkyBlue">SkyBlue</a></span></code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#SlateBlue">SlateBlue</a></span></code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#SlateGray">SlateGray</a></span></code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#Snow">Snow</a></span></code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#SpringGreen">SpringGreen</a></span></code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#SteelBlue">SteelBlue</a></span></code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#Tan">Tan</a></span></code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#Teal">Teal</a></span></code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#Thistle">Thistle</a></span></code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#Tomato">Tomato</a></span></code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#Turquoise">Turquoise</a></span></code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#Violet">Violet</a></span></code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#Wheat">Wheat</a></span></code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#White">White</a></span></code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#WhiteSmoke">WhiteSmoke</a></span></code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#Yellow">Yellow</a></span></code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/PresetColor.html#YellowGreen">YellowGreen</a></span></code></td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.com.aspose.ms.System.Enum">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;com.aspose.ms.System.Enum</h3>
<code>EnumSeparatorCharArray</code></li>
</ul>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.aspose.ms.System.Enum">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;com.aspose.ms.System.Enum</h3>
<code>Clone, CloneTo, format, format, get_Caption, get_Value, getName, getName, getNames, getNames, getUnderlyingType, getUnderlyingType, getValue, getValues, isDefined, isDefined, isDefined, isDefined, parse, parse, parse, parse, register, toObject, toString</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="NotDefined">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NotDefined</h4>
<pre>public static final&nbsp;int NotDefined</pre>
<div class="block"><p>
 Color preset is not defined.
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.NotDefined">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="AliceBlue">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>AliceBlue</h4>
<pre>public static final&nbsp;int AliceBlue</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.AliceBlue">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="AntiqueWhite">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>AntiqueWhite</h4>
<pre>public static final&nbsp;int AntiqueWhite</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.AntiqueWhite">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Aqua">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Aqua</h4>
<pre>public static final&nbsp;int Aqua</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.Aqua">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Aquamarine">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Aquamarine</h4>
<pre>public static final&nbsp;int Aquamarine</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.Aquamarine">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Azure">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Azure</h4>
<pre>public static final&nbsp;int Azure</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.Azure">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Beige">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Beige</h4>
<pre>public static final&nbsp;int Beige</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.Beige">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Bisque">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Bisque</h4>
<pre>public static final&nbsp;int Bisque</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.Bisque">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Black">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Black</h4>
<pre>public static final&nbsp;int Black</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.Black">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="BlanchedAlmond">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BlanchedAlmond</h4>
<pre>public static final&nbsp;int BlanchedAlmond</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.BlanchedAlmond">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Blue">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Blue</h4>
<pre>public static final&nbsp;int Blue</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.Blue">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="BlueViolet">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BlueViolet</h4>
<pre>public static final&nbsp;int BlueViolet</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.BlueViolet">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Brown">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Brown</h4>
<pre>public static final&nbsp;int Brown</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.Brown">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="BurlyWood">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BurlyWood</h4>
<pre>public static final&nbsp;int BurlyWood</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.BurlyWood">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CadetBlue">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CadetBlue</h4>
<pre>public static final&nbsp;int CadetBlue</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.CadetBlue">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Chartreuse">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Chartreuse</h4>
<pre>public static final&nbsp;int Chartreuse</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.Chartreuse">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Chocolate">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Chocolate</h4>
<pre>public static final&nbsp;int Chocolate</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.Chocolate">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Coral">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Coral</h4>
<pre>public static final&nbsp;int Coral</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.Coral">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CornflowerBlue">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CornflowerBlue</h4>
<pre>public static final&nbsp;int CornflowerBlue</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.CornflowerBlue">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Cornsilk">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Cornsilk</h4>
<pre>public static final&nbsp;int Cornsilk</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.Cornsilk">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Crimson">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Crimson</h4>
<pre>public static final&nbsp;int Crimson</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.Crimson">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Cyan">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Cyan</h4>
<pre>public static final&nbsp;int Cyan</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.Cyan">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DarkBlue">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DarkBlue</h4>
<pre>public static final&nbsp;int DarkBlue</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.DarkBlue">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DarkCyan">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DarkCyan</h4>
<pre>public static final&nbsp;int DarkCyan</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.DarkCyan">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DarkGoldenrod">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DarkGoldenrod</h4>
<pre>public static final&nbsp;int DarkGoldenrod</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.DarkGoldenrod">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DarkGray">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DarkGray</h4>
<pre>public static final&nbsp;int DarkGray</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.DarkGray">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DarkGreen">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DarkGreen</h4>
<pre>public static final&nbsp;int DarkGreen</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.DarkGreen">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DarkKhaki">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DarkKhaki</h4>
<pre>public static final&nbsp;int DarkKhaki</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.DarkKhaki">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DarkMagenta">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DarkMagenta</h4>
<pre>public static final&nbsp;int DarkMagenta</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.DarkMagenta">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DarkOliveGreen">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DarkOliveGreen</h4>
<pre>public static final&nbsp;int DarkOliveGreen</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.DarkOliveGreen">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DarkOrange">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DarkOrange</h4>
<pre>public static final&nbsp;int DarkOrange</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.DarkOrange">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DarkOrchid">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DarkOrchid</h4>
<pre>public static final&nbsp;int DarkOrchid</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.DarkOrchid">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DarkRed">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DarkRed</h4>
<pre>public static final&nbsp;int DarkRed</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.DarkRed">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DarkSalmon">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DarkSalmon</h4>
<pre>public static final&nbsp;int DarkSalmon</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.DarkSalmon">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DarkSeaGreen">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DarkSeaGreen</h4>
<pre>public static final&nbsp;int DarkSeaGreen</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.DarkSeaGreen">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DarkSlateBlue">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DarkSlateBlue</h4>
<pre>public static final&nbsp;int DarkSlateBlue</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.DarkSlateBlue">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DarkSlateGray">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DarkSlateGray</h4>
<pre>public static final&nbsp;int DarkSlateGray</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.DarkSlateGray">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DarkTurquoise">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DarkTurquoise</h4>
<pre>public static final&nbsp;int DarkTurquoise</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.DarkTurquoise">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DarkViolet">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DarkViolet</h4>
<pre>public static final&nbsp;int DarkViolet</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.DarkViolet">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DeepPink">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DeepPink</h4>
<pre>public static final&nbsp;int DeepPink</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.DeepPink">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DeepSkyBlue">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DeepSkyBlue</h4>
<pre>public static final&nbsp;int DeepSkyBlue</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.DeepSkyBlue">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DimGray">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DimGray</h4>
<pre>public static final&nbsp;int DimGray</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.DimGray">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DodgerBlue">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DodgerBlue</h4>
<pre>public static final&nbsp;int DodgerBlue</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.DodgerBlue">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Firebrick">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Firebrick</h4>
<pre>public static final&nbsp;int Firebrick</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.Firebrick">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="FloralWhite">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FloralWhite</h4>
<pre>public static final&nbsp;int FloralWhite</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.FloralWhite">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="ForestGreen">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ForestGreen</h4>
<pre>public static final&nbsp;int ForestGreen</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.ForestGreen">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Fuchsia">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Fuchsia</h4>
<pre>public static final&nbsp;int Fuchsia</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.Fuchsia">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Gainsboro">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Gainsboro</h4>
<pre>public static final&nbsp;int Gainsboro</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.Gainsboro">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="GhostWhite">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>GhostWhite</h4>
<pre>public static final&nbsp;int GhostWhite</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.GhostWhite">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Gold">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Gold</h4>
<pre>public static final&nbsp;int Gold</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.Gold">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Goldenrod">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Goldenrod</h4>
<pre>public static final&nbsp;int Goldenrod</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.Goldenrod">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Gray">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Gray</h4>
<pre>public static final&nbsp;int Gray</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.Gray">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Green">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Green</h4>
<pre>public static final&nbsp;int Green</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.Green">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="GreenYellow">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>GreenYellow</h4>
<pre>public static final&nbsp;int GreenYellow</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.GreenYellow">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Honeydew">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Honeydew</h4>
<pre>public static final&nbsp;int Honeydew</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.Honeydew">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="HotPink">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>HotPink</h4>
<pre>public static final&nbsp;int HotPink</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.HotPink">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="IndianRed">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IndianRed</h4>
<pre>public static final&nbsp;int IndianRed</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.IndianRed">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Indigo">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Indigo</h4>
<pre>public static final&nbsp;int Indigo</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.Indigo">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Ivory">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Ivory</h4>
<pre>public static final&nbsp;int Ivory</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.Ivory">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Khaki">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Khaki</h4>
<pre>public static final&nbsp;int Khaki</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.Khaki">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Lavender">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Lavender</h4>
<pre>public static final&nbsp;int Lavender</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.Lavender">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="LavenderBlush">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LavenderBlush</h4>
<pre>public static final&nbsp;int LavenderBlush</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.LavenderBlush">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="LawnGreen">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LawnGreen</h4>
<pre>public static final&nbsp;int LawnGreen</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.LawnGreen">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="LemonChiffon">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LemonChiffon</h4>
<pre>public static final&nbsp;int LemonChiffon</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.LemonChiffon">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="LightBlue">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LightBlue</h4>
<pre>public static final&nbsp;int LightBlue</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.LightBlue">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="LightCoral">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LightCoral</h4>
<pre>public static final&nbsp;int LightCoral</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.LightCoral">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="LightCyan">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LightCyan</h4>
<pre>public static final&nbsp;int LightCyan</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.LightCyan">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="LightGoldenrodYellow">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LightGoldenrodYellow</h4>
<pre>public static final&nbsp;int LightGoldenrodYellow</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.LightGoldenrodYellow">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="LightGray">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LightGray</h4>
<pre>public static final&nbsp;int LightGray</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.LightGray">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="LightGreen">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LightGreen</h4>
<pre>public static final&nbsp;int LightGreen</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.LightGreen">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="LightPink">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LightPink</h4>
<pre>public static final&nbsp;int LightPink</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.LightPink">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="LightSalmon">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LightSalmon</h4>
<pre>public static final&nbsp;int LightSalmon</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.LightSalmon">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="LightSeaGreen">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LightSeaGreen</h4>
<pre>public static final&nbsp;int LightSeaGreen</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.LightSeaGreen">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="LightSkyBlue">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LightSkyBlue</h4>
<pre>public static final&nbsp;int LightSkyBlue</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.LightSkyBlue">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="LightSlateGray">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LightSlateGray</h4>
<pre>public static final&nbsp;int LightSlateGray</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.LightSlateGray">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="LightSteelBlue">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LightSteelBlue</h4>
<pre>public static final&nbsp;int LightSteelBlue</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.LightSteelBlue">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="LightYellow">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LightYellow</h4>
<pre>public static final&nbsp;int LightYellow</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.LightYellow">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Lime">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Lime</h4>
<pre>public static final&nbsp;int Lime</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.Lime">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="LimeGreen">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LimeGreen</h4>
<pre>public static final&nbsp;int LimeGreen</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.LimeGreen">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Linen">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Linen</h4>
<pre>public static final&nbsp;int Linen</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.Linen">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Magenta">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Magenta</h4>
<pre>public static final&nbsp;int Magenta</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.Magenta">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Maroon">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Maroon</h4>
<pre>public static final&nbsp;int Maroon</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.Maroon">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MediumAquamarine">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MediumAquamarine</h4>
<pre>public static final&nbsp;int MediumAquamarine</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.MediumAquamarine">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MediumBlue">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MediumBlue</h4>
<pre>public static final&nbsp;int MediumBlue</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.MediumBlue">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MediumOrchid">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MediumOrchid</h4>
<pre>public static final&nbsp;int MediumOrchid</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.MediumOrchid">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MediumPurple">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MediumPurple</h4>
<pre>public static final&nbsp;int MediumPurple</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.MediumPurple">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MediumSeaGreen">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MediumSeaGreen</h4>
<pre>public static final&nbsp;int MediumSeaGreen</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.MediumSeaGreen">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MediumSlateBlue">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MediumSlateBlue</h4>
<pre>public static final&nbsp;int MediumSlateBlue</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.MediumSlateBlue">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MediumSpringGreen">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MediumSpringGreen</h4>
<pre>public static final&nbsp;int MediumSpringGreen</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.MediumSpringGreen">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MediumTurquoise">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MediumTurquoise</h4>
<pre>public static final&nbsp;int MediumTurquoise</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.MediumTurquoise">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MediumVioletRed">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MediumVioletRed</h4>
<pre>public static final&nbsp;int MediumVioletRed</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.MediumVioletRed">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MidnightBlue">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MidnightBlue</h4>
<pre>public static final&nbsp;int MidnightBlue</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.MidnightBlue">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MintCream">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MintCream</h4>
<pre>public static final&nbsp;int MintCream</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.MintCream">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MistyRose">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MistyRose</h4>
<pre>public static final&nbsp;int MistyRose</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.MistyRose">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Moccasin">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Moccasin</h4>
<pre>public static final&nbsp;int Moccasin</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.Moccasin">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="NavajoWhite">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NavajoWhite</h4>
<pre>public static final&nbsp;int NavajoWhite</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.NavajoWhite">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Navy">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Navy</h4>
<pre>public static final&nbsp;int Navy</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.Navy">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="OldLace">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OldLace</h4>
<pre>public static final&nbsp;int OldLace</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.OldLace">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Olive">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Olive</h4>
<pre>public static final&nbsp;int Olive</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.Olive">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="OliveDrab">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OliveDrab</h4>
<pre>public static final&nbsp;int OliveDrab</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.OliveDrab">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Orange">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Orange</h4>
<pre>public static final&nbsp;int Orange</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.Orange">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="OrangeRed">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OrangeRed</h4>
<pre>public static final&nbsp;int OrangeRed</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.OrangeRed">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Orchid">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Orchid</h4>
<pre>public static final&nbsp;int Orchid</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.Orchid">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="PaleGoldenrod">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PaleGoldenrod</h4>
<pre>public static final&nbsp;int PaleGoldenrod</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.PaleGoldenrod">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="PaleGreen">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PaleGreen</h4>
<pre>public static final&nbsp;int PaleGreen</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.PaleGreen">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="PaleTurquoise">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PaleTurquoise</h4>
<pre>public static final&nbsp;int PaleTurquoise</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.PaleTurquoise">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="PaleVioletRed">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PaleVioletRed</h4>
<pre>public static final&nbsp;int PaleVioletRed</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.PaleVioletRed">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="PapayaWhip">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PapayaWhip</h4>
<pre>public static final&nbsp;int PapayaWhip</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.PapayaWhip">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="PeachPuff">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PeachPuff</h4>
<pre>public static final&nbsp;int PeachPuff</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.PeachPuff">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Peru">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Peru</h4>
<pre>public static final&nbsp;int Peru</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.Peru">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Pink">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Pink</h4>
<pre>public static final&nbsp;int Pink</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.Pink">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Plum">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Plum</h4>
<pre>public static final&nbsp;int Plum</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.Plum">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="PowderBlue">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PowderBlue</h4>
<pre>public static final&nbsp;int PowderBlue</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.PowderBlue">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Purple">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Purple</h4>
<pre>public static final&nbsp;int Purple</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.Purple">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Red">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Red</h4>
<pre>public static final&nbsp;int Red</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.Red">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="RosyBrown">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RosyBrown</h4>
<pre>public static final&nbsp;int RosyBrown</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.RosyBrown">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="RoyalBlue">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RoyalBlue</h4>
<pre>public static final&nbsp;int RoyalBlue</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.RoyalBlue">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="SaddleBrown">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SaddleBrown</h4>
<pre>public static final&nbsp;int SaddleBrown</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.SaddleBrown">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Salmon">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Salmon</h4>
<pre>public static final&nbsp;int Salmon</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.Salmon">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="SandyBrown">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SandyBrown</h4>
<pre>public static final&nbsp;int SandyBrown</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.SandyBrown">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="SeaGreen">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SeaGreen</h4>
<pre>public static final&nbsp;int SeaGreen</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.SeaGreen">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="SeaShell">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SeaShell</h4>
<pre>public static final&nbsp;int SeaShell</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.SeaShell">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Sienna">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Sienna</h4>
<pre>public static final&nbsp;int Sienna</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.Sienna">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Silver">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Silver</h4>
<pre>public static final&nbsp;int Silver</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.Silver">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="SkyBlue">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SkyBlue</h4>
<pre>public static final&nbsp;int SkyBlue</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.SkyBlue">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="SlateBlue">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SlateBlue</h4>
<pre>public static final&nbsp;int SlateBlue</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.SlateBlue">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="SlateGray">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SlateGray</h4>
<pre>public static final&nbsp;int SlateGray</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.SlateGray">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Snow">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Snow</h4>
<pre>public static final&nbsp;int Snow</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.Snow">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="SpringGreen">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SpringGreen</h4>
<pre>public static final&nbsp;int SpringGreen</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.SpringGreen">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="SteelBlue">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SteelBlue</h4>
<pre>public static final&nbsp;int SteelBlue</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.SteelBlue">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Tan">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Tan</h4>
<pre>public static final&nbsp;int Tan</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.Tan">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Teal">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Teal</h4>
<pre>public static final&nbsp;int Teal</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.Teal">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Thistle">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Thistle</h4>
<pre>public static final&nbsp;int Thistle</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.Thistle">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Tomato">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Tomato</h4>
<pre>public static final&nbsp;int Tomato</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.Tomato">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Turquoise">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Turquoise</h4>
<pre>public static final&nbsp;int Turquoise</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.Turquoise">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Violet">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Violet</h4>
<pre>public static final&nbsp;int Violet</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.Violet">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Wheat">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Wheat</h4>
<pre>public static final&nbsp;int Wheat</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.Wheat">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="White">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>White</h4>
<pre>public static final&nbsp;int White</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.White">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="WhiteSmoke">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>WhiteSmoke</h4>
<pre>public static final&nbsp;int WhiteSmoke</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.WhiteSmoke">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Yellow">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Yellow</h4>
<pre>public static final&nbsp;int Yellow</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.Yellow">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="YellowGreen">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>YellowGreen</h4>
<pre>public static final&nbsp;int YellowGreen</pre>
<div class="block"><p>
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.PresetColor.YellowGreen">Constant Field Values</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/PresentedBySpeaker.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/PresetShadow.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/PresetColor.html" target="_top">Frames</a></li>
<li><a href="PresetColor.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.classes.inherited.from.class.com.aspose.ms.System.Enum">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#methods.inherited.from.class.com.aspose.ms.System.Enum">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li>Method</li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><i>Copyright &copy; 2004-2025 Aspose Pty Ltd. All Rights Reserved.</i></small></p>
</body>
</html>
