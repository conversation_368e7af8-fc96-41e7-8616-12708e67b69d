<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_421) on Fri Jul 11 05:32:45 CDT 2025 -->
<title>VbaProject (Aspose.Slides for Java (25.7) API Documentation)</title>
<meta name="date" content="2025-07-11">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="VbaProject (Aspose.Slides for Java (25.7) API Documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/VbaModuleCollection.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/VbaProjectFactory.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/VbaProject.html" target="_top">Frames</a></li>
<li><a href="VbaProject.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.aspose.slides</div>
<h2 title="Class VbaProject" class="title">Class VbaProject</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.aspose.slides.VbaProject</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../com/aspose/slides/IVbaProject.html" title="interface in com.aspose.slides">IVbaProject</a></dd>
</dl>
<hr>
<br>
<pre>public final class <span class="typeNameLabel">VbaProject</span>
extends java.lang.Object
implements <a href="../../../com/aspose/slides/IVbaProject.html" title="interface in com.aspose.slides">IVbaProject</a></pre>
<div class="block"><p>
 Represents VBA project with presentation macros.
 </p></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/VbaProject.html#VbaProject--">VbaProject</a></span>()</code>
<div class="block">
 This constructor creates new VBA project from scratch.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/VbaProject.html#VbaProject-byte:A-">VbaProject</a></span>(byte[]&nbsp;data)</code>
<div class="block">
 This constructor loads VBA project from binary representation of OLE container.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IVbaModuleCollection.html" title="interface in com.aspose.slides">IVbaModuleCollection</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/VbaProject.html#getModules--">getModules</a></span>()</code>
<div class="block">
 Returns the list of all modules that are contained in the VBA project.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/VbaProject.html#getName--">getName</a></span>()</code>
<div class="block">
 Returns the name of the VBA project.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IVbaReferenceCollection.html" title="interface in com.aspose.slides">IVbaReferenceCollection</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/VbaProject.html#getReferences--">getReferences</a></span>()</code>
<div class="block">
 Returns the list of all references that are contained in the VBA project.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/VbaProject.html#isPasswordProtected--">isPasswordProtected</a></span>()</code>
<div class="block">
 Indicates whether the VBAProject is protected by a password to view project properties.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/VbaProject.html#toBinary--">toBinary</a></span>()</code>
<div class="block">
 Returns the binary representation of the VBA project as OLE container</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="VbaProject--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>VbaProject</h4>
<pre>public&nbsp;VbaProject()</pre>
<div class="block"><p>
 This constructor creates new VBA project from scratch.
 Project will be created in 1252 Windows Latin 1 (ANSI) codepage
 </p></div>
</li>
</ul>
<a name="VbaProject-byte:A-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>VbaProject</h4>
<pre>public&nbsp;VbaProject(byte[]&nbsp;data)</pre>
<div class="block"><p>
 This constructor loads VBA project from binary representation of OLE container.
 </p></div>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getName</h4>
<pre>public final&nbsp;java.lang.String&nbsp;getName()</pre>
<div class="block"><p>
 Returns the name of the VBA project.
 Read-only <code>String</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IVbaProject.html#getName--">getName</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IVbaProject.html" title="interface in com.aspose.slides">IVbaProject</a></code></dd>
</dl>
</li>
</ul>
<a name="getModules--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getModules</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IVbaModuleCollection.html" title="interface in com.aspose.slides">IVbaModuleCollection</a>&nbsp;getModules()</pre>
<div class="block"><p>
 Returns the list of all modules that are contained in the VBA project.
 Read-only <a href="../../../com/aspose/slides/IVbaModuleCollection.html" title="interface in com.aspose.slides"><code>IVbaModuleCollection</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IVbaProject.html#getModules--">getModules</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IVbaProject.html" title="interface in com.aspose.slides">IVbaProject</a></code></dd>
</dl>
</li>
</ul>
<a name="getReferences--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getReferences</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IVbaReferenceCollection.html" title="interface in com.aspose.slides">IVbaReferenceCollection</a>&nbsp;getReferences()</pre>
<div class="block"><p>
 Returns the list of all references that are contained in the VBA project.
 Read-only <a href="../../../com/aspose/slides/IVbaReferenceCollection.html" title="interface in com.aspose.slides"><code>IVbaReferenceCollection</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IVbaProject.html#getReferences--">getReferences</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IVbaProject.html" title="interface in com.aspose.slides">IVbaProject</a></code></dd>
</dl>
</li>
</ul>
<a name="toBinary--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>toBinary</h4>
<pre>public final&nbsp;byte[]&nbsp;toBinary()</pre>
<div class="block"><p>
 Returns the binary representation of the VBA project as OLE container
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IVbaProject.html#toBinary--">toBinary</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IVbaProject.html" title="interface in com.aspose.slides">IVbaProject</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Binary representation of the VBA project as OLE container</dd>
</dl>
</li>
</ul>
<a name="isPasswordProtected--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>isPasswordProtected</h4>
<pre>public final&nbsp;boolean&nbsp;isPasswordProtected()</pre>
<div class="block"><p>
 Indicates whether the VBAProject is protected by a password to view project properties.
 Read-only <code>boolean</code>.
 </p><p><hr><blockquote><pre>
 <pre>
 Presentation presentation = new Presentation("demo.pptm");
 try {
     if (presentation.getVbaProject().isPasswordProtected())
         System.out.println("The VBAProject '" + presentation.getVbaProject().getName() +
             "' is protected by password to view project properties.");
 } finally {
     if (presentation != null) presentation.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/IVbaProject.html#isPasswordProtected--">isPasswordProtected</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/IVbaProject.html" title="interface in com.aspose.slides">IVbaProject</a></code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/VbaModuleCollection.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/VbaProjectFactory.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/VbaProject.html" target="_top">Frames</a></li>
<li><a href="VbaProject.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><i>Copyright &copy; 2004-2025 Aspose Pty Ltd. All Rights Reserved.</i></small></p>
</body>
</html>
