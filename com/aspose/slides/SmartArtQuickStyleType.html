<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_421) on Fri Jul 11 05:32:44 CDT 2025 -->
<title>SmartArtQuickStyleType (Aspose.Slides for Java (25.7) API Documentation)</title>
<meta name="date" content="2025-07-11">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="SmartArtQuickStyleType (Aspose.Slides for Java (25.7) API Documentation)";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/SmartArtNodeCollection.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/SmartArtShape.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/SmartArtQuickStyleType.html" target="_top">Frames</a></li>
<li><a href="SmartArtQuickStyleType.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.classes.inherited.from.class.com.aspose.ms.System.Enum">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#methods.inherited.from.class.com.aspose.ms.System.Enum">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li>Method</li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.aspose.slides</div>
<h2 title="Class SmartArtQuickStyleType" class="title">Class SmartArtQuickStyleType</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.aspose.ms.System.ValueType&lt;com.aspose.ms.System.Enum&gt;</li>
<li>
<ul class="inheritance">
<li>com.aspose.ms.System.Enum</li>
<li>
<ul class="inheritance">
<li>com.aspose.slides.SmartArtQuickStyleType</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public final class <span class="typeNameLabel">SmartArtQuickStyleType</span>
extends com.aspose.ms.System.Enum</pre>
<div class="block"><p>
 Represents style scheme of a SmartArt diagram.
 </p></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.com.aspose.ms.System.Enum">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from class&nbsp;com.aspose.ms.System.Enum</h3>
<code>com.aspose.ms.System.Enum.AbstractEnum, com.aspose.ms.System.Enum.FlaggedEnum, com.aspose.ms.System.Enum.ObjectEnum, com.aspose.ms.System.Enum.SimpleEnum</code></li>
</ul>
</li>
</ul>
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtQuickStyleType.html#BirdsEyeScene">BirdsEyeScene</a></span></code>
<div class="block">
 BirdsEyeScene</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtQuickStyleType.html#BrickScene">BrickScene</a></span></code>
<div class="block">
 BrickScene</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtQuickStyleType.html#Cartoon">Cartoon</a></span></code>
<div class="block">
 Cartoon</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtQuickStyleType.html#FlatScene">FlatScene</a></span></code>
<div class="block">
 FlatScene</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtQuickStyleType.html#Inset">Inset</a></span></code>
<div class="block">
 Inset</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtQuickStyleType.html#IntenceEffect">IntenceEffect</a></span></code>
<div class="block">
 IntenceEffect</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtQuickStyleType.html#MetallicScene">MetallicScene</a></span></code>
<div class="block">
 MetallicScene</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtQuickStyleType.html#ModerateEffect">ModerateEffect</a></span></code>
<div class="block">
 ModerateEffect</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtQuickStyleType.html#Polished">Polished</a></span></code>
<div class="block">
 Polished</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtQuickStyleType.html#Powder">Powder</a></span></code>
<div class="block">
 Powder</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtQuickStyleType.html#SimpleFill">SimpleFill</a></span></code>
<div class="block">
 SimpleFill</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtQuickStyleType.html#SubtleEffect">SubtleEffect</a></span></code>
<div class="block">
 SubtleEffect</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtQuickStyleType.html#SunsetScene">SunsetScene</a></span></code>
<div class="block">
 SunsetScene</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SmartArtQuickStyleType.html#WhiteOutline">WhiteOutline</a></span></code>
<div class="block">
 WhiteOutline</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.com.aspose.ms.System.Enum">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;com.aspose.ms.System.Enum</h3>
<code>EnumSeparatorCharArray</code></li>
</ul>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.aspose.ms.System.Enum">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;com.aspose.ms.System.Enum</h3>
<code>Clone, CloneTo, format, format, get_Caption, get_Value, getName, getName, getNames, getNames, getUnderlyingType, getUnderlyingType, getValue, getValues, isDefined, isDefined, isDefined, isDefined, parse, parse, parse, parse, register, toObject, toString</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="SimpleFill">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SimpleFill</h4>
<pre>public static final&nbsp;int SimpleFill</pre>
<div class="block"><p>
 SimpleFill
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtQuickStyleType.SimpleFill">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="WhiteOutline">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>WhiteOutline</h4>
<pre>public static final&nbsp;int WhiteOutline</pre>
<div class="block"><p>
 WhiteOutline
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtQuickStyleType.WhiteOutline">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="SubtleEffect">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SubtleEffect</h4>
<pre>public static final&nbsp;int SubtleEffect</pre>
<div class="block"><p>
 SubtleEffect
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtQuickStyleType.SubtleEffect">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="ModerateEffect">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ModerateEffect</h4>
<pre>public static final&nbsp;int ModerateEffect</pre>
<div class="block"><p>
 ModerateEffect
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtQuickStyleType.ModerateEffect">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="IntenceEffect">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IntenceEffect</h4>
<pre>public static final&nbsp;int IntenceEffect</pre>
<div class="block"><p>
 IntenceEffect
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtQuickStyleType.IntenceEffect">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Polished">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Polished</h4>
<pre>public static final&nbsp;int Polished</pre>
<div class="block"><p>
 Polished
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtQuickStyleType.Polished">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Inset">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Inset</h4>
<pre>public static final&nbsp;int Inset</pre>
<div class="block"><p>
 Inset
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtQuickStyleType.Inset">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Cartoon">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Cartoon</h4>
<pre>public static final&nbsp;int Cartoon</pre>
<div class="block"><p>
 Cartoon
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtQuickStyleType.Cartoon">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Powder">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Powder</h4>
<pre>public static final&nbsp;int Powder</pre>
<div class="block"><p>
 Powder
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtQuickStyleType.Powder">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="BrickScene">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BrickScene</h4>
<pre>public static final&nbsp;int BrickScene</pre>
<div class="block"><p>
 BrickScene
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtQuickStyleType.BrickScene">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="FlatScene">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FlatScene</h4>
<pre>public static final&nbsp;int FlatScene</pre>
<div class="block"><p>
 FlatScene
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtQuickStyleType.FlatScene">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MetallicScene">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MetallicScene</h4>
<pre>public static final&nbsp;int MetallicScene</pre>
<div class="block"><p>
 MetallicScene
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtQuickStyleType.MetallicScene">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="SunsetScene">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SunsetScene</h4>
<pre>public static final&nbsp;int SunsetScene</pre>
<div class="block"><p>
 SunsetScene
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtQuickStyleType.SunsetScene">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="BirdsEyeScene">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>BirdsEyeScene</h4>
<pre>public static final&nbsp;int BirdsEyeScene</pre>
<div class="block"><p>
 BirdsEyeScene
 </p></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.aspose.slides.SmartArtQuickStyleType.BirdsEyeScene">Constant Field Values</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/SmartArtNodeCollection.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/SmartArtShape.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/SmartArtQuickStyleType.html" target="_top">Frames</a></li>
<li><a href="SmartArtQuickStyleType.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.classes.inherited.from.class.com.aspose.ms.System.Enum">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#methods.inherited.from.class.com.aspose.ms.System.Enum">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li>Method</li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><i>Copyright &copy; 2004-2025 Aspose Pty Ltd. All Rights Reserved.</i></small></p>
</body>
</html>
