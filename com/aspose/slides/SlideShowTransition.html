<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_421) on Fri Jul 11 05:32:44 CDT 2025 -->
<title>SlideShowTransition (Aspose.Slides for Java (25.7) API Documentation)</title>
<meta name="date" content="2025-07-11">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="SlideShowTransition (Aspose.Slides for Java (25.7) API Documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/SlideShowSettings.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/SlideShowType.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/SlideShowTransition.html" target="_top">Frames</a></li>
<li><a href="SlideShowTransition.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.aspose.slides</div>
<h2 title="Class SlideShowTransition" class="title">Class SlideShowTransition</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../com/aspose/slides/DomObject.html" title="class in com.aspose.slides">com.aspose.slides.DomObject</a>&lt;<a href="../../../com/aspose/slides/BaseSlide.html" title="class in com.aspose.slides">BaseSlide</a>&gt;</li>
<li>
<ul class="inheritance">
<li>com.aspose.slides.SlideShowTransition</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../com/aspose/slides/ISlideShowTransition.html" title="interface in com.aspose.slides">ISlideShowTransition</a></dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">SlideShowTransition</span>
extends <a href="../../../com/aspose/slides/DomObject.html" title="class in com.aspose.slides">DomObject</a>&lt;<a href="../../../com/aspose/slides/BaseSlide.html" title="class in com.aspose.slides">BaseSlide</a>&gt;
implements <a href="../../../com/aspose/slides/ISlideShowTransition.html" title="interface in com.aspose.slides">ISlideShowTransition</a></pre>
<div class="block"><p>
 Represents slide show transition.
 </p></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideShowTransition.html#equals-java.lang.Object-">equals</a></span>(java.lang.Object&nbsp;obj)</code>
<div class="block">
 Determines whether the two SlideShowTransition instances are equal.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideShowTransition.html#getAdvanceAfter--">getAdvanceAfter</a></span>()</code>
<div class="block">
 This attribute specifies if the slideshow will move to the next slide after a certain time.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideShowTransition.html#getAdvanceAfterTime--">getAdvanceAfterTime</a></span>()</code>
<div class="block">
 Specifies the time, in milliseconds, after which the transition should start.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideShowTransition.html#getAdvanceOnClick--">getAdvanceOnClick</a></span>()</code>
<div class="block">
 Specifies whether a mouse click will advance the slide or not.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/IAudio.html" title="interface in com.aspose.slides">IAudio</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideShowTransition.html#getSound--">getSound</a></span>()</code>
<div class="block">
 Returns or sets the embedded audio data.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideShowTransition.html#getSoundIsBuiltIn--">getSoundIsBuiltIn</a></span>()</code>
<div class="block">
 Specifies whether or not this sound is a built-in sound.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideShowTransition.html#getSoundLoop--">getSoundLoop</a></span>()</code>
<div class="block">
 This attribute specifies if the sound will loop until the next sound event occurs in
 slideshow.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideShowTransition.html#getSoundMode--">getSoundMode</a></span>()</code>
<div class="block">
 Set or returns sound mode for slide transition.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideShowTransition.html#getSoundName--">getSoundName</a></span>()</code>
<div class="block">
 Specifies a human readable name for the sound of the transition.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideShowTransition.html#getSpeed--">getSpeed</a></span>()</code>
<div class="block">
 Specifies the transition speed that is to be used when transitioning from the current slide
 to the next.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideShowTransition.html#getType--">getType</a></span>()</code>
<div class="block">
 Type of transition.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code><a href="../../../com/aspose/slides/ITransitionValueBase.html" title="interface in com.aspose.slides">ITransitionValueBase</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideShowTransition.html#getValue--">getValue</a></span>()</code>
<div class="block">
 Slide show transition value.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideShowTransition.html#hashCode--">hashCode</a></span>()</code>
<div class="block">
 Serves as a hash function for a particular type, suitable for use
 in hashing algorithms and data structures like a hash table.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideShowTransition.html#setAdvanceAfter-boolean-">setAdvanceAfter</a></span>(boolean&nbsp;value)</code>
<div class="block">
 This attribute specifies if the slideshow will move to the next slide after a certain time.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideShowTransition.html#setAdvanceAfterTime-long-">setAdvanceAfterTime</a></span>(long&nbsp;value)</code>
<div class="block">
 Specifies the time, in milliseconds, after which the transition should start.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideShowTransition.html#setAdvanceOnClick-boolean-">setAdvanceOnClick</a></span>(boolean&nbsp;value)</code>
<div class="block">
 Specifies whether a mouse click will advance the slide or not.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideShowTransition.html#setSound-com.aspose.slides.IAudio-">setSound</a></span>(<a href="../../../com/aspose/slides/IAudio.html" title="interface in com.aspose.slides">IAudio</a>&nbsp;value)</code>
<div class="block">
 Returns or sets the embedded audio data.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideShowTransition.html#setSoundIsBuiltIn-boolean-">setSoundIsBuiltIn</a></span>(boolean&nbsp;value)</code>
<div class="block">
 Specifies whether or not this sound is a built-in sound.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideShowTransition.html#setSoundLoop-boolean-">setSoundLoop</a></span>(boolean&nbsp;value)</code>
<div class="block">
 This attribute specifies if the sound will loop until the next sound event occurs in
 slideshow.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideShowTransition.html#setSoundMode-int-">setSoundMode</a></span>(int&nbsp;value)</code>
<div class="block">
 Set or returns sound mode for slide transition.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideShowTransition.html#setSoundName-java.lang.String-">setSoundName</a></span>(java.lang.String&nbsp;value)</code>
<div class="block">
 Specifies a human readable name for the sound of the transition.</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideShowTransition.html#setSpeed-int-">setSpeed</a></span>(int&nbsp;value)</code>
<div class="block">
 Specifies the transition speed that is to be used when transitioning from the current slide
 to the next.</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/aspose/slides/SlideShowTransition.html#setType-int-">setType</a></span>(int&nbsp;value)</code>
<div class="block">
 Type of transition.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.aspose.slides.DomObject">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;com.aspose.slides.<a href="../../../com/aspose/slides/DomObject.html" title="class in com.aspose.slides">DomObject</a></h3>
<code><a href="../../../com/aspose/slides/DomObject.html#getParent_Immediate--">getParent_Immediate</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>getClass, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getSound--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSound</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/IAudio.html" title="interface in com.aspose.slides">IAudio</a>&nbsp;getSound()</pre>
<div class="block"><p>
 Returns or sets the embedded audio data.
 Read/write <a href="../../../com/aspose/slides/IAudio.html" title="interface in com.aspose.slides"><code>IAudio</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISlideShowTransition.html#getSound--">getSound</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISlideShowTransition.html" title="interface in com.aspose.slides">ISlideShowTransition</a></code></dd>
</dl>
</li>
</ul>
<a name="setSound-com.aspose.slides.IAudio-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSound</h4>
<pre>public final&nbsp;void&nbsp;setSound(<a href="../../../com/aspose/slides/IAudio.html" title="interface in com.aspose.slides">IAudio</a>&nbsp;value)</pre>
<div class="block"><p>
 Returns or sets the embedded audio data.
 Read/write <a href="../../../com/aspose/slides/IAudio.html" title="interface in com.aspose.slides"><code>IAudio</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISlideShowTransition.html#setSound-com.aspose.slides.IAudio-">setSound</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISlideShowTransition.html" title="interface in com.aspose.slides">ISlideShowTransition</a></code></dd>
</dl>
</li>
</ul>
<a name="getSoundMode--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSoundMode</h4>
<pre>public final&nbsp;int&nbsp;getSoundMode()</pre>
<div class="block"><p>
 Set or returns sound mode for slide transition.
 Read/write <a href="../../../com/aspose/slides/TransitionSoundMode.html" title="class in com.aspose.slides"><code>TransitionSoundMode</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISlideShowTransition.html#getSoundMode--">getSoundMode</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISlideShowTransition.html" title="interface in com.aspose.slides">ISlideShowTransition</a></code></dd>
</dl>
</li>
</ul>
<a name="setSoundMode-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSoundMode</h4>
<pre>public final&nbsp;void&nbsp;setSoundMode(int&nbsp;value)</pre>
<div class="block"><p>
 Set or returns sound mode for slide transition.
 Read/write <a href="../../../com/aspose/slides/TransitionSoundMode.html" title="class in com.aspose.slides"><code>TransitionSoundMode</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISlideShowTransition.html#setSoundMode-int-">setSoundMode</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISlideShowTransition.html" title="interface in com.aspose.slides">ISlideShowTransition</a></code></dd>
</dl>
</li>
</ul>
<a name="getSoundLoop--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSoundLoop</h4>
<pre>public final&nbsp;boolean&nbsp;getSoundLoop()</pre>
<div class="block"><p>
 This attribute specifies if the sound will loop until the next sound event occurs in
 slideshow.
 Read/write <code>boolean</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISlideShowTransition.html#getSoundLoop--">getSoundLoop</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISlideShowTransition.html" title="interface in com.aspose.slides">ISlideShowTransition</a></code></dd>
</dl>
</li>
</ul>
<a name="setSoundLoop-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSoundLoop</h4>
<pre>public final&nbsp;void&nbsp;setSoundLoop(boolean&nbsp;value)</pre>
<div class="block"><p>
 This attribute specifies if the sound will loop until the next sound event occurs in
 slideshow.
 Read/write <code>boolean</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISlideShowTransition.html#setSoundLoop-boolean-">setSoundLoop</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISlideShowTransition.html" title="interface in com.aspose.slides">ISlideShowTransition</a></code></dd>
</dl>
</li>
</ul>
<a name="getAdvanceOnClick--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAdvanceOnClick</h4>
<pre>public final&nbsp;boolean&nbsp;getAdvanceOnClick()</pre>
<div class="block"><p>
 Specifies whether a mouse click will advance the slide or not. If this attribute is not
 specified then a value of true is assumed.
 Read/write <code>boolean</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISlideShowTransition.html#getAdvanceOnClick--">getAdvanceOnClick</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISlideShowTransition.html" title="interface in com.aspose.slides">ISlideShowTransition</a></code></dd>
</dl>
</li>
</ul>
<a name="setAdvanceOnClick-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAdvanceOnClick</h4>
<pre>public final&nbsp;void&nbsp;setAdvanceOnClick(boolean&nbsp;value)</pre>
<div class="block"><p>
 Specifies whether a mouse click will advance the slide or not. If this attribute is not
 specified then a value of true is assumed.
 Read/write <code>boolean</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISlideShowTransition.html#setAdvanceOnClick-boolean-">setAdvanceOnClick</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISlideShowTransition.html" title="interface in com.aspose.slides">ISlideShowTransition</a></code></dd>
</dl>
</li>
</ul>
<a name="getAdvanceAfter--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAdvanceAfter</h4>
<pre>public final&nbsp;boolean&nbsp;getAdvanceAfter()</pre>
<div class="block"><p>
 This attribute specifies if the slideshow will move to the next slide after a certain time.
 Read/write <code>boolean</code>.
 </p><p><hr><blockquote><pre>
 <pre>
 Presentation pres = new Presentation("demo.pptx");
 try {
     // Get the first slide Transition
     ISlideShowTransition slideTransition = pres.getSlides().get_Item(0).getSlideShowTransition();

     // Check if the Advance Slide After flag is checked
     if (slideTransition.getAdvanceAfter())
     {
         // Get the Advance Slide After Time value
         long advanceAfterTime = slideTransition.getAdvanceAfterTime();
     }
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISlideShowTransition.html#getAdvanceAfter--">getAdvanceAfter</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISlideShowTransition.html" title="interface in com.aspose.slides">ISlideShowTransition</a></code></dd>
</dl>
</li>
</ul>
<a name="setAdvanceAfter-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAdvanceAfter</h4>
<pre>public final&nbsp;void&nbsp;setAdvanceAfter(boolean&nbsp;value)</pre>
<div class="block"><p>
 This attribute specifies if the slideshow will move to the next slide after a certain time.
 Read/write <code>boolean</code>.
 </p><p><hr><blockquote><pre>
 <pre>
 Presentation pres = new Presentation("demo.pptx");
 try {
     // Get the first slide Transition
     ISlideShowTransition slideTransition = pres.getSlides().get_Item(0).getSlideShowTransition();

     // Check if the Advance Slide After flag is checked
     if (slideTransition.getAdvanceAfter())
     {
         // Get the Advance Slide After Time value
         long advanceAfterTime = slideTransition.getAdvanceAfterTime();
     }
 } finally {
     if (pres != null) pres.dispose();
 }
 </pre>
 </pre></blockquote></hr></p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISlideShowTransition.html#setAdvanceAfter-boolean-">setAdvanceAfter</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISlideShowTransition.html" title="interface in com.aspose.slides">ISlideShowTransition</a></code></dd>
</dl>
</li>
</ul>
<a name="getAdvanceAfterTime--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAdvanceAfterTime</h4>
<pre>public final&nbsp;long&nbsp;getAdvanceAfterTime()</pre>
<div class="block"><p>
 Specifies the time, in milliseconds, after which the transition should start. This setting
 may be used in conjunction with the advClick attribute. If this attribute is not specified
 then it is assumed that no auto-advance will occur.
 Read/write <code>long</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISlideShowTransition.html#getAdvanceAfterTime--">getAdvanceAfterTime</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISlideShowTransition.html" title="interface in com.aspose.slides">ISlideShowTransition</a></code></dd>
</dl>
</li>
</ul>
<a name="setAdvanceAfterTime-long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAdvanceAfterTime</h4>
<pre>public final&nbsp;void&nbsp;setAdvanceAfterTime(long&nbsp;value)</pre>
<div class="block"><p>
 Specifies the time, in milliseconds, after which the transition should start. This setting
 may be used in conjunction with the advClick attribute. If this attribute is not specified
 then it is assumed that no auto-advance will occur.
 Read/write <code>long</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISlideShowTransition.html#setAdvanceAfterTime-long-">setAdvanceAfterTime</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISlideShowTransition.html" title="interface in com.aspose.slides">ISlideShowTransition</a></code></dd>
</dl>
</li>
</ul>
<a name="getSpeed--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSpeed</h4>
<pre>public final&nbsp;int&nbsp;getSpeed()</pre>
<div class="block"><p>
 Specifies the transition speed that is to be used when transitioning from the current slide
 to the next.
 Read/write <a href="../../../com/aspose/slides/TransitionSpeed.html" title="class in com.aspose.slides"><code>TransitionSpeed</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISlideShowTransition.html#getSpeed--">getSpeed</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISlideShowTransition.html" title="interface in com.aspose.slides">ISlideShowTransition</a></code></dd>
</dl>
</li>
</ul>
<a name="setSpeed-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSpeed</h4>
<pre>public final&nbsp;void&nbsp;setSpeed(int&nbsp;value)</pre>
<div class="block"><p>
 Specifies the transition speed that is to be used when transitioning from the current slide
 to the next.
 Read/write <a href="../../../com/aspose/slides/TransitionSpeed.html" title="class in com.aspose.slides"><code>TransitionSpeed</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISlideShowTransition.html#setSpeed-int-">setSpeed</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISlideShowTransition.html" title="interface in com.aspose.slides">ISlideShowTransition</a></code></dd>
</dl>
</li>
</ul>
<a name="getValue--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getValue</h4>
<pre>public final&nbsp;<a href="../../../com/aspose/slides/ITransitionValueBase.html" title="interface in com.aspose.slides">ITransitionValueBase</a>&nbsp;getValue()</pre>
<div class="block"><p>
 Slide show transition value.
 Read-only <a href="../../../com/aspose/slides/ITransitionValueBase.html" title="interface in com.aspose.slides"><code>ITransitionValueBase</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISlideShowTransition.html#getValue--">getValue</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISlideShowTransition.html" title="interface in com.aspose.slides">ISlideShowTransition</a></code></dd>
</dl>
</li>
</ul>
<a name="getType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getType</h4>
<pre>public final&nbsp;int&nbsp;getType()</pre>
<div class="block"><p>
 Type of transition.
 Read/write <a href="../../../com/aspose/slides/TransitionType.html" title="class in com.aspose.slides"><code>TransitionType</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISlideShowTransition.html#getType--">getType</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISlideShowTransition.html" title="interface in com.aspose.slides">ISlideShowTransition</a></code></dd>
</dl>
</li>
</ul>
<a name="setType-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setType</h4>
<pre>public final&nbsp;void&nbsp;setType(int&nbsp;value)</pre>
<div class="block"><p>
 Type of transition.
 Read/write <a href="../../../com/aspose/slides/TransitionType.html" title="class in com.aspose.slides"><code>TransitionType</code></a>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISlideShowTransition.html#setType-int-">setType</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISlideShowTransition.html" title="interface in com.aspose.slides">ISlideShowTransition</a></code></dd>
</dl>
</li>
</ul>
<a name="getSoundIsBuiltIn--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSoundIsBuiltIn</h4>
<pre>public final&nbsp;boolean&nbsp;getSoundIsBuiltIn()</pre>
<div class="block"><p>
 Specifies whether or not this sound is a built-in sound. If this attribute is set to true then
 the generating application is alerted to check the name attribute specified for this sound
 in it's list of built-in sounds and can then surface a custom name or UI as needed.
 Read-write <code>boolean</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISlideShowTransition.html#getSoundIsBuiltIn--">getSoundIsBuiltIn</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISlideShowTransition.html" title="interface in com.aspose.slides">ISlideShowTransition</a></code></dd>
</dl>
</li>
</ul>
<a name="setSoundIsBuiltIn-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSoundIsBuiltIn</h4>
<pre>public final&nbsp;void&nbsp;setSoundIsBuiltIn(boolean&nbsp;value)</pre>
<div class="block"><p>
 Specifies whether or not this sound is a built-in sound. If this attribute is set to true then
 the generating application is alerted to check the name attribute specified for this sound
 in it's list of built-in sounds and can then surface a custom name or UI as needed.
 Read-write <code>boolean</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISlideShowTransition.html#setSoundIsBuiltIn-boolean-">setSoundIsBuiltIn</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISlideShowTransition.html" title="interface in com.aspose.slides">ISlideShowTransition</a></code></dd>
</dl>
</li>
</ul>
<a name="getSoundName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSoundName</h4>
<pre>public final&nbsp;java.lang.String&nbsp;getSoundName()</pre>
<div class="block"><p>
 Specifies a human readable name for the sound of the transition. The <code>Sound</code>(<a href="../../../com/aspose/slides/SlideShowTransition.html#getSound--"><code>getSound()</code></a>/<a href="../../../com/aspose/slides/SlideShowTransition.html#setSound-com.aspose.slides.IAudio-"><code>setSound(IAudio)</code></a>) property must be assigned to get or set the sound name.
 Read-write <code>String</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISlideShowTransition.html#getSoundName--">getSoundName</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISlideShowTransition.html" title="interface in com.aspose.slides">ISlideShowTransition</a></code></dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../com/aspose/slides/PptxException.html" title="class in com.aspose.slides">PptxException</a></code> - When <code>Sound</code>(<a href="../../../com/aspose/slides/SlideShowTransition.html#getSound--"><code>getSound()</code></a>/<a href="../../../com/aspose/slides/SlideShowTransition.html#setSound-com.aspose.slides.IAudio-"><code>setSound(IAudio)</code></a>) property is not assigned.
 <p><hr>This name appears in the PowerPoint user interface when configuring the transition sound manually.</hr></p></dd>
</dl>
</li>
</ul>
<a name="setSoundName-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSoundName</h4>
<pre>public final&nbsp;void&nbsp;setSoundName(java.lang.String&nbsp;value)</pre>
<div class="block"><p>
 Specifies a human readable name for the sound of the transition. The <code>Sound</code>(<a href="../../../com/aspose/slides/SlideShowTransition.html#getSound--"><code>getSound()</code></a>/<a href="../../../com/aspose/slides/SlideShowTransition.html#setSound-com.aspose.slides.IAudio-"><code>setSound(IAudio)</code></a>) property must be assigned to get or set the sound name.
 Read-write <code>String</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/aspose/slides/ISlideShowTransition.html#setSoundName-java.lang.String-">setSoundName</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/aspose/slides/ISlideShowTransition.html" title="interface in com.aspose.slides">ISlideShowTransition</a></code></dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../com/aspose/slides/PptxException.html" title="class in com.aspose.slides">PptxException</a></code> - When <code>Sound</code>(<a href="../../../com/aspose/slides/SlideShowTransition.html#getSound--"><code>getSound()</code></a>/<a href="../../../com/aspose/slides/SlideShowTransition.html#setSound-com.aspose.slides.IAudio-"><code>setSound(IAudio)</code></a>) property is not assigned.
 <p><hr>This name appears in the PowerPoint user interface when configuring the transition sound manually.</hr></p></dd>
</dl>
</li>
</ul>
<a name="equals-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>equals</h4>
<pre>public&nbsp;boolean&nbsp;equals(java.lang.Object&nbsp;obj)</pre>
<div class="block"><p>
 Determines whether the two SlideShowTransition instances are equal.
 Read/write <code>boolean</code>.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code>equals</code>&nbsp;in class&nbsp;<code>java.lang.Object</code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>obj</code> - The SlideShowTransition to compare with the current SlideShowTransition.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd><code>&lt;b&gt;true&lt;/b&gt;</code> if the specified SlideShowTransition is equal to the current SlideShowTransition; otherwise, <code>&lt;b&gt;false&lt;/b&gt;</code>.</dd>
</dl>
</li>
</ul>
<a name="hashCode--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>hashCode</h4>
<pre>public&nbsp;int&nbsp;hashCode()</pre>
<div class="block"><p>
 Serves as a hash function for a particular type, suitable for use
 in hashing algorithms and data structures like a hash table.
 </p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code>hashCode</code>&nbsp;in class&nbsp;<code>java.lang.Object</code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>23454
 <p><hr>
 Overriden to make compiler happy. Always returns constant because object is mutable.
 </hr></p></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../com/aspose/slides/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/aspose/slides/SlideShowSettings.html" title="class in com.aspose.slides"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/aspose/slides/SlideShowType.html" title="class in com.aspose.slides"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/aspose/slides/SlideShowTransition.html" target="_top">Frames</a></li>
<li><a href="SlideShowTransition.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><i>Copyright &copy; 2004-2025 Aspose Pty Ltd. All Rights Reserved.</i></small></p>
</body>
</html>
